<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

<!--    <context id="goods" targetRuntime="MyBatis3" defaultModelType="flat">-->
<!--        &lt;!&ndash; 数据库关键字冲突,自动处理 用反引号`` &ndash;&gt;-->
<!--        <property name="autoDelimitKeywords" value="true"/>-->
<!--        <property name="beginningDelimiter" value="`"/>-->
<!--        <property name="endingDelimiter" value="`"/>-->

<!--        &lt;!&ndash; 生成的entity实体引入lombok注解 Getter,Setter,Builder &ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>-->
<!--        &lt;!&ndash;生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin-->
<!--        3个插件，根据诉求决定使用哪个插件，具体区别见 https://docs.sankuai.com/dp/hbar/mdp-docs/master/mbg/ &ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>-->
<!--        &lt;!&ndash;<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>&ndash;&gt;-->
<!--        &lt;!&ndash; 每次执行插件生成的 xml 时通用的方法会覆盖的 &ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>-->
<!--        &lt;!&ndash; 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：-->
<!--        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"&ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>-->
<!--        &lt;!&ndash; 需要生成使用 RowBounds 的分页查询语句&ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>-->
<!--        &lt;!&ndash;分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator-->
<!--        type="XMLMAPPER、MIXEDMAPPER"&ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>-->
<!--        &lt;!&ndash; targetRuntime="Mybatis3"时需要，Example类存储路径 &ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">-->
<!--            <property name="targetPackage" value="com.sankuai.walle.carManage.example"/>-->
<!--        </plugin>-->
<!--        &lt;!&ndash; 从数据库中的字段的comment做为生成entity的属性注释 &ndash;&gt;-->
<!--        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">-->
<!--            <property name="suppressAllComments" value="true"/>-->
<!--            <property name="suppressDate" value="true"/>-->
<!--            <property name="addRemarkComments" value="true"/>-->
<!--        </commentGenerator>-->

<!--        &lt;!&ndash;使用前替换数据库名,账号密码&ndash;&gt;-->
<!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
<!--                        connectionURL="*********************************************************************************************************************"-->
<!--                        userId="mb_generator"-->
<!--                        password="m2tWrNusl6cnul">-->
<!--        </jdbcConnection>-->
<!--        &lt;!&ndash;替换成自己数据库的jdbcRef &ndash;&gt;-->
<!--        &lt;!&ndash;        <zebra jdbcRef="walle_mrm_manage_test"/>&ndash;&gt;-->

<!--        <javaTypeResolver>-->
<!--            <property name="forceBigDecimals" value="false"/>-->
<!--        </javaTypeResolver>-->

<!--        <javaModelGenerator targetPackage="com.sankuai.walle.carManage.entity" targetProject="src/main/java">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--            <property name="trimStrings" value="true"/>-->
<!--        </javaModelGenerator>-->

<!--        <sqlMapGenerator targetPackage="carManage" targetProject="src/main/resources/mappers">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </sqlMapGenerator>-->

<!--        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.walle.carManage.mapper"-->
<!--                             targetProject="src/main/java">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </javaClientGenerator>-->

<!--        &lt;!&ndash;配置需要生成的表&ndash;&gt;-->
<!--        &lt;!&ndash;        车辆管理的表  &ndash;&gt;-->
<!--        &lt;!&ndash;        <table tableName="car_objects" />&ndash;&gt;-->
<!--&lt;!&ndash;                <table tableName="device_types" />&ndash;&gt;-->

<!--&lt;!&ndash;        <table tableName="car_selects">&ndash;&gt;-->
<!--&lt;!&ndash;            <columnOverride column="is_deleted" javaType="java.lang.Boolean"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </table>&ndash;&gt;-->

<!--        <table tableName="vehicle_model_attributes">-->
<!--            <columnOverride column="is_deleted" javaType="java.lang.Boolean"/>-->
<!--        </table>-->
<!--&lt;!&ndash;        <table tableName="car_equipment"/>&ndash;&gt;-->
<!--        &lt;!&ndash;- <table tableName="auk_cert"/>&ndash;&gt;-->
<!--        &lt;!&ndash;- <table tableName="biz_handle_policy_info"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <table tableName="mis_group" />&ndash;&gt;-->

<!--        &lt;!&ndash;        <table tableName="car_operation" />&ndash;&gt;-->
<!--        &lt;!&ndash;        <table tableName="car_exec_word" />&ndash;&gt;-->
<!--        &lt;!&ndash;        配置管理的表 &ndash;&gt;-->
<!--        &lt;!&ndash;        <table tableName="car_device_config_extend" />&ndash;&gt;-->

<!--        &lt;!&ndash;<table tableName="remote_custom_config" domainObjectName="RemoteCustomConfigDO">-->
<!--            <columnOverride column="config_value" jdbcType="VARCHAR"/>-->
<!--        </table>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="remote_custom_config_subject" domainObjectName="RemoteCustomConfigSubjectDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="operation_log" domainObjectName="OperationLogDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="remote_objects" domainObjectName="RemoteObjectsDO"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <table tableName="remote_car_type" domainObjectName="RemoteCarTypeDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="tags" domainObjectName="TagsDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="tag_type" domainObjectName="TagTypeDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="remote_object_tags" domainObjectName="RemoteObjectTagsDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="remote_object_type" domainObjectName="RemoteObjectTypeDO"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<table tableName="remote_device_type" domainObjectName="RemoteDeviceTypeDO"/>&ndash;&gt;-->

<!--    </context>-->



    <context id="configs" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 数据库关键字冲突,自动处理 用反引号`` -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 生成的entity实体引入lombok注解 Getter,Setter,Builder -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!--生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin
        3个插件，根据诉求决定使用哪个插件，具体区别见 https://docs.sankuai.com/dp/hbar/mdp-docs/master/mbg/ -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>-->
        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：
        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>
        <!-- 需要生成使用 RowBounds 的分页查询语句-->
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>
        <!--分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator
        type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>
        <!-- targetRuntime="Mybatis3"时需要，Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.walle.dal.mrm_manage.example"/>
        </plugin>
        <!-- 从数据库中的字段的comment做为生成entity的属性注释 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--使用前替换数据库名,账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*********************************************************************************************************************"
                        userId="mb_generator"
                        password="m2tWrNusl6cnul">
        </jdbcConnection>
        <!--        connectionURL="*******************************************************************************************************************"-->
        <!--        userId="mb_generator"-->
        <!--        password="m2tWrNusl6cnul">-->


        <!--替换成自己数据库的jdbcRef -->
        <!--        <zebra jdbcRef="walle_mrm_manage_test"/>-->

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sankuai.walle.dal.mrm_manage.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetProject="src/main/resources/mappers" targetPackage="dal">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.walle.dal.mrm_manage.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <table tableName = "auto_config_content" />
        <!--配置需要生成的表-->
        <!--        车辆管理的表  -->
        <!--        <table tableName="car_objects" />-->
        <!--        <table tableName="car_assets" />-->
        <!--        <table tableName="car_selects"/>-->
        <!--        <table tableName="car_operation" />-->
        <!--        <table tableName="car_exec_word" />-->
        <!--        配置管理的表 -->
        <!--        <table tableName="car_device_config_extend" />-->
<!--        <table tableName="car_device_config" />-->

        <!--<table tableName="remote_custom_config" domainObjectName="RemoteCustomConfigDO">
            <columnOverride column="config_value" jdbcType="VARCHAR"/>
        </table>-->
        <!--<table tableName="remote_custom_config_subject" domainObjectName="RemoteCustomConfigSubjectDO"/>-->
        <!--<table tableName="operation_log" domainObjectName="OperationLogDO"/>-->
        <!--<table tableName="remote_objects" domainObjectName="RemoteObjectsDO"/>-->
<!--        <table tableName="remote_car_type" />-->
        <!--<table tableName="tags" domainObjectName="TagsDO"/>-->
        <!--<table tableName="tag_type" domainObjectName="TagTypeDO"/>-->
        <!--<table tableName="remote_object_tags" domainObjectName="RemoteObjectTagsDO"/>-->
<!--        <table tableName="remote_object_type" />-->
        <!--<table tableName="remote_device_type" domainObjectName="RemoteDeviceTypeDO"/>-->

    </context>


<!--    <context id="eve" targetRuntime="MyBatis3" defaultModelType="flat">-->
<!--        &lt;!&ndash; 数据库关键字冲突,自动处理 用反引号`` &ndash;&gt;-->
<!--        <property name="autoDelimitKeywords" value="true"/>-->
<!--        <property name="beginningDelimiter" value="`"/>-->
<!--        <property name="endingDelimiter" value="`"/>-->

<!--        &lt;!&ndash; 生成的entity实体引入lombok注解 Getter,Setter,Builder &ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>-->
<!--        &lt;!&ndash;生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin-->
<!--        3个插件，根据诉求决定使用哪个插件，具体区别见 https://docs.sankuai.com/dp/hbar/mdp-docs/master/mbg/ &ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>-->
<!--        &lt;!&ndash;<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>&ndash;&gt;-->
<!--        &lt;!&ndash;<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>&ndash;&gt;-->
<!--        &lt;!&ndash; 每次执行插件生成的 xml 时通用的方法会覆盖的 &ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>-->
<!--        &lt;!&ndash; 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：-->
<!--        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"&ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>-->
<!--        &lt;!&ndash; 需要生成使用 RowBounds 的分页查询语句&ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>-->
<!--        &lt;!&ndash;分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator-->
<!--        type="XMLMAPPER、MIXEDMAPPER"&ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>-->
<!--        &lt;!&ndash; targetRuntime="Mybatis3"时需要，Example类存储路径 &ndash;&gt;-->
<!--        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">-->
<!--            <property name="targetPackage" value="com.sankuai.walle.dal.eve.example"/>-->
<!--        </plugin>-->
<!--        &lt;!&ndash; 从数据库中的字段的comment做为生成entity的属性注释 &ndash;&gt;-->
<!--        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">-->
<!--            <property name="suppressAllComments" value="true"/>-->
<!--            <property name="suppressDate" value="true"/>-->
<!--            <property name="addRemarkComments" value="true"/>-->
<!--        </commentGenerator>-->

<!--        &lt;!&ndash;使用前替换数据库名,账号密码&ndash;&gt;-->
<!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
<!--                        connectionURL="**************************************************************************************************************"-->
<!--                        userId="rds_mybatis"-->
<!--                        password="B$vYS!OY5l$d%s">-->
<!--        </jdbcConnection>-->
<!--&lt;!&ndash;        <zebra></zebra>&ndash;&gt;-->

<!--        <javaTypeResolver>-->
<!--            <property name="forceBigDecimals" value="false"/>-->
<!--        </javaTypeResolver>-->

<!--        <javaModelGenerator targetPackage="com.sankuai.walle.dal.eve.entity" targetProject="src/main/java">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--            <property name="trimStrings" value="true"/>-->
<!--        </javaModelGenerator>-->

<!--        <sqlMapGenerator targetProject="src/main/resources/mappers" targetPackage="dal">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </sqlMapGenerator>-->

<!--        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.walle.dal.eve.mapper"-->
<!--                             targetProject="src/main/java">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </javaClientGenerator>-->

<!--        &lt;!&ndash;配置需要生成的表&ndash;&gt;-->
<!--        <table tableName="vehicle_base_info"/>-->
<!--&lt;!&ndash;        <table tableName="device_types" />&ndash;&gt;-->
<!--&lt;!&ndash;        <table tableName="vehicle_model"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <table tableName="cmdb_action_log"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <table tableName="config_permission"/>&ndash;&gt;-->

<!--    </context>-->

    <context id="battery_swap_cabinet" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 数据库关键字冲突,自动处理 用反引号`` -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 生成的entity实体引入lombok注解 Getter,Setter,Builder -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!--生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin
        3个插件，根据诉求决定使用哪个插件，具体区别见 https://docs.sankuai.com/dp/hbar/mdp-docs/master/mbg/ -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>-->
        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：
        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>
        <!-- 需要生成使用 RowBounds 的分页查询语句-->
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>
        <!--分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator
        type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>
        <!-- targetRuntime="Mybatis3"时需要，Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.walle.dal.battery.example"/>
        </plugin>
        <!-- 从数据库中的字段的comment做为生成entity的属性注释 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--使用前替换数据库名,账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*******************************************************************************************************************************"
                        userId="rds_battery"
                        password="UpT%Iu$wCJsmFH">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sankuai.walle.dal.battery.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetProject="src/main/resources/mappers" targetPackage="dal">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.walle.dal.battery.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!--配置需要生成的表-->
        <table tableName="battery_swap_cabinet_property"/>
        <table tableName="battery_cabinet_command"/>
    </context>

</generatorConfiguration>