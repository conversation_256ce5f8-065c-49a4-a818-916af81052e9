<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.battery.mapper.BatterySwapCabinetPropertyMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    <id column="battery_property_id" jdbcType="BIGINT" property="batteryPropertyId" />
    <result column="time" jdbcType="BIGINT" property="time" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="topic" jdbcType="VARCHAR" property="topic" />
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="device_key" jdbcType="VARCHAR" property="deviceKey" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="product_key" jdbcType="VARCHAR" property="productKey" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    <result column="arg" jdbcType="LONGVARCHAR" property="arg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    battery_property_id, `time`, `method`, topic, id, version, create_time, device_key, 
    message_id, product_key
  </sql>
  <sql id="Blob_Column_List">
    arg
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from battery_swap_cabinet_property
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from battery_swap_cabinet_property
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from battery_swap_cabinet_property
    where battery_property_id = #{batteryPropertyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from battery_swap_cabinet_property
    where battery_property_id = #{batteryPropertyId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample">
    delete from battery_swap_cabinet_property
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    insert into battery_swap_cabinet_property (battery_property_id, `time`, `method`, 
      topic, id, version, 
      create_time, device_key, message_id, 
      product_key, arg)
    values (#{batteryPropertyId,jdbcType=BIGINT}, #{time,jdbcType=BIGINT}, #{method,jdbcType=VARCHAR}, 
      #{topic,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{deviceKey,jdbcType=VARCHAR}, #{messageId,jdbcType=VARCHAR}, 
      #{productKey,jdbcType=VARCHAR}, #{arg,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    insert into battery_swap_cabinet_property
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batteryPropertyId != null">
        battery_property_id,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="method != null">
        `method`,
      </if>
      <if test="topic != null">
        topic,
      </if>
      <if test="id != null">
        id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="deviceKey != null">
        device_key,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="productKey != null">
        product_key,
      </if>
      <if test="arg != null">
        arg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batteryPropertyId != null">
        #{batteryPropertyId,jdbcType=BIGINT},
      </if>
      <if test="time != null">
        #{time,jdbcType=BIGINT},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="topic != null">
        #{topic,jdbcType=VARCHAR},
      </if>
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceKey != null">
        #{deviceKey,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="productKey != null">
        #{productKey,jdbcType=VARCHAR},
      </if>
      <if test="arg != null">
        #{arg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample" resultType="java.lang.Long">
    select count(*) from battery_swap_cabinet_property
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update battery_swap_cabinet_property
    <set>
      <if test="record.batteryPropertyId != null">
        battery_property_id = #{record.batteryPropertyId,jdbcType=BIGINT},
      </if>
      <if test="record.time != null">
        `time` = #{record.time,jdbcType=BIGINT},
      </if>
      <if test="record.method != null">
        `method` = #{record.method,jdbcType=VARCHAR},
      </if>
      <if test="record.topic != null">
        topic = #{record.topic,jdbcType=VARCHAR},
      </if>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deviceKey != null">
        device_key = #{record.deviceKey,jdbcType=VARCHAR},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.productKey != null">
        product_key = #{record.productKey,jdbcType=VARCHAR},
      </if>
      <if test="record.arg != null">
        arg = #{record.arg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update battery_swap_cabinet_property
    set battery_property_id = #{record.batteryPropertyId,jdbcType=BIGINT},
      `time` = #{record.time,jdbcType=BIGINT},
      `method` = #{record.method,jdbcType=VARCHAR},
      topic = #{record.topic,jdbcType=VARCHAR},
      id = #{record.id,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      device_key = #{record.deviceKey,jdbcType=VARCHAR},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      product_key = #{record.productKey,jdbcType=VARCHAR},
      arg = #{record.arg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update battery_swap_cabinet_property
    set battery_property_id = #{record.batteryPropertyId,jdbcType=BIGINT},
      `time` = #{record.time,jdbcType=BIGINT},
      `method` = #{record.method,jdbcType=VARCHAR},
      topic = #{record.topic,jdbcType=VARCHAR},
      id = #{record.id,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      device_key = #{record.deviceKey,jdbcType=VARCHAR},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      product_key = #{record.productKey,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    update battery_swap_cabinet_property
    <set>
      <if test="time != null">
        `time` = #{time,jdbcType=BIGINT},
      </if>
      <if test="method != null">
        `method` = #{method,jdbcType=VARCHAR},
      </if>
      <if test="topic != null">
        topic = #{topic,jdbcType=VARCHAR},
      </if>
      <if test="id != null">
        id = #{id,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceKey != null">
        device_key = #{deviceKey,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="productKey != null">
        product_key = #{productKey,jdbcType=VARCHAR},
      </if>
      <if test="arg != null">
        arg = #{arg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where battery_property_id = #{batteryPropertyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    update battery_swap_cabinet_property
    set `time` = #{time,jdbcType=BIGINT},
      `method` = #{method,jdbcType=VARCHAR},
      topic = #{topic,jdbcType=VARCHAR},
      id = #{id,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      device_key = #{deviceKey,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      product_key = #{productKey,jdbcType=VARCHAR},
      arg = #{arg,jdbcType=LONGVARCHAR}
    where battery_property_id = #{batteryPropertyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty">
    update battery_swap_cabinet_property
    set `time` = #{time,jdbcType=BIGINT},
      `method` = #{method,jdbcType=VARCHAR},
      topic = #{topic,jdbcType=VARCHAR},
      id = #{id,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      device_key = #{deviceKey,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      product_key = #{productKey,jdbcType=VARCHAR}
    where battery_property_id = #{batteryPropertyId,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into battery_swap_cabinet_property
    (battery_property_id, `time`, `method`, topic, id, version, create_time, device_key, 
      message_id, product_key, arg)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.batteryPropertyId,jdbcType=BIGINT}, #{item.time,jdbcType=BIGINT}, #{item.method,jdbcType=VARCHAR}, 
        #{item.topic,jdbcType=VARCHAR}, #{item.id,jdbcType=VARCHAR}, #{item.version,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.deviceKey,jdbcType=VARCHAR}, #{item.messageId,jdbcType=VARCHAR}, 
        #{item.productKey,jdbcType=VARCHAR}, #{item.arg,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from battery_swap_cabinet_property
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from battery_swap_cabinet_property
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>