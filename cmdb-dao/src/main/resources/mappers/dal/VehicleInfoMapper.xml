<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="CHAR" property="vehicleId" />
    <result column="vin" jdbcType="CHAR" property="vin" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="park" jdbcType="VARCHAR" property="park" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="vehicle_type" jdbcType="TINYINT" property="vehicleType" />
    <result column="vehicle_category" jdbcType="VARCHAR" property="vehicleCategory" />
    <result column="license_number" jdbcType="VARCHAR" property="licenseNumber" />
    <result column="licence" jdbcType="VARCHAR" property="licence" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="power_type" jdbcType="TINYINT" property="powerType" />
    <result column="operation_state" jdbcType="TINYINT" property="operationState" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="product" jdbcType="INTEGER" property="product" />
    <result column="purpose_id" jdbcType="TINYINT" property="purposeId" />
    <result column="is_dedicated" jdbcType="BIT" property="isDedicated" />
    <result column="submitter" jdbcType="VARCHAR" property="submitter" />
    <result column="activate_code" jdbcType="VARCHAR" property="activateCode" />
    <result column="access_secret" jdbcType="VARCHAR" property="accessSecret" />
    <result column="box_qrcode" jdbcType="VARCHAR" property="boxQrcode" />
    <result column="start_postion" jdbcType="VARCHAR" property="startPostion" />
    <result column="end_postion" jdbcType="VARCHAR" property="endPostion" />
    <result column="super_password" jdbcType="VARCHAR" property="superPassword" />
    <result column="usage_rights" jdbcType="VARCHAR" property="usageRights" />
    <result column="is_business" jdbcType="BIT" property="isBusiness" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs">
    <result column="image" jdbcType="LONGVARCHAR" property="image" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_id, vin, name, park, brand, type, vehicle_type, vehicle_category, license_number, 
    licence, year, power_type, operation_state, status, create_time, update_time, owner, 
    product, purpose_id, is_dedicated, submitter, activate_code, access_secret, box_qrcode, 
    start_postion, end_postion, super_password, usage_rights, is_business
  </sql>
  <sql id="Blob_Column_List">
    image, remark
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample">
    delete from vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs" useGeneratedKeys="true">
    insert into vehicle_info (vehicle_id, vin, name, 
      park, brand, type, 
      vehicle_type, vehicle_category, license_number, 
      licence, year, power_type, 
      operation_state, status, create_time, 
      update_time, owner, product, 
      purpose_id, is_dedicated, submitter, 
      activate_code, access_secret, box_qrcode, 
      start_postion, end_postion, super_password, 
      usage_rights, is_business, image, 
      remark)
    values (#{vehicleId,jdbcType=CHAR}, #{vin,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, 
      #{park,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{vehicleType,jdbcType=TINYINT}, #{vehicleCategory,jdbcType=VARCHAR}, #{licenseNumber,jdbcType=VARCHAR}, 
      #{licence,jdbcType=VARCHAR}, #{year,jdbcType=INTEGER}, #{powerType,jdbcType=TINYINT}, 
      #{operationState,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{owner,jdbcType=VARCHAR}, #{product,jdbcType=INTEGER}, 
      #{purposeId,jdbcType=TINYINT}, #{isDedicated,jdbcType=BIT}, #{submitter,jdbcType=VARCHAR}, 
      #{activateCode,jdbcType=VARCHAR}, #{accessSecret,jdbcType=VARCHAR}, #{boxQrcode,jdbcType=VARCHAR}, 
      #{startPostion,jdbcType=VARCHAR}, #{endPostion,jdbcType=VARCHAR}, #{superPassword,jdbcType=VARCHAR}, 
      #{usageRights,jdbcType=VARCHAR}, #{isBusiness,jdbcType=BIT}, #{image,jdbcType=LONGVARCHAR}, 
      #{remark,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs" useGeneratedKeys="true">
    insert into vehicle_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="park != null">
        park,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="vehicleCategory != null">
        vehicle_category,
      </if>
      <if test="licenseNumber != null">
        license_number,
      </if>
      <if test="licence != null">
        licence,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="powerType != null">
        power_type,
      </if>
      <if test="operationState != null">
        operation_state,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="product != null">
        product,
      </if>
      <if test="purposeId != null">
        purpose_id,
      </if>
      <if test="isDedicated != null">
        is_dedicated,
      </if>
      <if test="submitter != null">
        submitter,
      </if>
      <if test="activateCode != null">
        activate_code,
      </if>
      <if test="accessSecret != null">
        access_secret,
      </if>
      <if test="boxQrcode != null">
        box_qrcode,
      </if>
      <if test="startPostion != null">
        start_postion,
      </if>
      <if test="endPostion != null">
        end_postion,
      </if>
      <if test="superPassword != null">
        super_password,
      </if>
      <if test="usageRights != null">
        usage_rights,
      </if>
      <if test="isBusiness != null">
        is_business,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=CHAR},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="park != null">
        #{park,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=TINYINT},
      </if>
      <if test="vehicleCategory != null">
        #{vehicleCategory,jdbcType=VARCHAR},
      </if>
      <if test="licenseNumber != null">
        #{licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="licence != null">
        #{licence,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="powerType != null">
        #{powerType,jdbcType=TINYINT},
      </if>
      <if test="operationState != null">
        #{operationState,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="product != null">
        #{product,jdbcType=INTEGER},
      </if>
      <if test="purposeId != null">
        #{purposeId,jdbcType=TINYINT},
      </if>
      <if test="isDedicated != null">
        #{isDedicated,jdbcType=BIT},
      </if>
      <if test="submitter != null">
        #{submitter,jdbcType=VARCHAR},
      </if>
      <if test="activateCode != null">
        #{activateCode,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="boxQrcode != null">
        #{boxQrcode,jdbcType=VARCHAR},
      </if>
      <if test="startPostion != null">
        #{startPostion,jdbcType=VARCHAR},
      </if>
      <if test="endPostion != null">
        #{endPostion,jdbcType=VARCHAR},
      </if>
      <if test="superPassword != null">
        #{superPassword,jdbcType=VARCHAR},
      </if>
      <if test="usageRights != null">
        #{usageRights,jdbcType=VARCHAR},
      </if>
      <if test="isBusiness != null">
        #{isBusiness,jdbcType=BIT},
      </if>
      <if test="image != null">
        #{image,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample" resultType="java.lang.Long">
    select count(*) from vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=CHAR},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.park != null">
        park = #{record.park,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleType != null">
        vehicle_type = #{record.vehicleType,jdbcType=TINYINT},
      </if>
      <if test="record.vehicleCategory != null">
        vehicle_category = #{record.vehicleCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseNumber != null">
        license_number = #{record.licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.licence != null">
        licence = #{record.licence,jdbcType=VARCHAR},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.powerType != null">
        power_type = #{record.powerType,jdbcType=TINYINT},
      </if>
      <if test="record.operationState != null">
        operation_state = #{record.operationState,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.product != null">
        product = #{record.product,jdbcType=INTEGER},
      </if>
      <if test="record.purposeId != null">
        purpose_id = #{record.purposeId,jdbcType=TINYINT},
      </if>
      <if test="record.isDedicated != null">
        is_dedicated = #{record.isDedicated,jdbcType=BIT},
      </if>
      <if test="record.submitter != null">
        submitter = #{record.submitter,jdbcType=VARCHAR},
      </if>
      <if test="record.activateCode != null">
        activate_code = #{record.activateCode,jdbcType=VARCHAR},
      </if>
      <if test="record.accessSecret != null">
        access_secret = #{record.accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="record.boxQrcode != null">
        box_qrcode = #{record.boxQrcode,jdbcType=VARCHAR},
      </if>
      <if test="record.startPostion != null">
        start_postion = #{record.startPostion,jdbcType=VARCHAR},
      </if>
      <if test="record.endPostion != null">
        end_postion = #{record.endPostion,jdbcType=VARCHAR},
      </if>
      <if test="record.superPassword != null">
        super_password = #{record.superPassword,jdbcType=VARCHAR},
      </if>
      <if test="record.usageRights != null">
        usage_rights = #{record.usageRights,jdbcType=VARCHAR},
      </if>
      <if test="record.isBusiness != null">
        is_business = #{record.isBusiness,jdbcType=BIT},
      </if>
      <if test="record.image != null">
        image = #{record.image,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update vehicle_info
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=CHAR},
      vin = #{record.vin,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      park = #{record.park,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      vehicle_type = #{record.vehicleType,jdbcType=TINYINT},
      vehicle_category = #{record.vehicleCategory,jdbcType=VARCHAR},
      license_number = #{record.licenseNumber,jdbcType=VARCHAR},
      licence = #{record.licence,jdbcType=VARCHAR},
      year = #{record.year,jdbcType=INTEGER},
      power_type = #{record.powerType,jdbcType=TINYINT},
      operation_state = #{record.operationState,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      owner = #{record.owner,jdbcType=VARCHAR},
      product = #{record.product,jdbcType=INTEGER},
      purpose_id = #{record.purposeId,jdbcType=TINYINT},
      is_dedicated = #{record.isDedicated,jdbcType=BIT},
      submitter = #{record.submitter,jdbcType=VARCHAR},
      activate_code = #{record.activateCode,jdbcType=VARCHAR},
      access_secret = #{record.accessSecret,jdbcType=VARCHAR},
      box_qrcode = #{record.boxQrcode,jdbcType=VARCHAR},
      start_postion = #{record.startPostion,jdbcType=VARCHAR},
      end_postion = #{record.endPostion,jdbcType=VARCHAR},
      super_password = #{record.superPassword,jdbcType=VARCHAR},
      usage_rights = #{record.usageRights,jdbcType=VARCHAR},
      is_business = #{record.isBusiness,jdbcType=BIT},
      image = #{record.image,jdbcType=LONGVARCHAR},
      remark = #{record.remark,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_info
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=CHAR},
      vin = #{record.vin,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      park = #{record.park,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      vehicle_type = #{record.vehicleType,jdbcType=TINYINT},
      vehicle_category = #{record.vehicleCategory,jdbcType=VARCHAR},
      license_number = #{record.licenseNumber,jdbcType=VARCHAR},
      licence = #{record.licence,jdbcType=VARCHAR},
      year = #{record.year,jdbcType=INTEGER},
      power_type = #{record.powerType,jdbcType=TINYINT},
      operation_state = #{record.operationState,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      owner = #{record.owner,jdbcType=VARCHAR},
      product = #{record.product,jdbcType=INTEGER},
      purpose_id = #{record.purposeId,jdbcType=TINYINT},
      is_dedicated = #{record.isDedicated,jdbcType=BIT},
      submitter = #{record.submitter,jdbcType=VARCHAR},
      activate_code = #{record.activateCode,jdbcType=VARCHAR},
      access_secret = #{record.accessSecret,jdbcType=VARCHAR},
      box_qrcode = #{record.boxQrcode,jdbcType=VARCHAR},
      start_postion = #{record.startPostion,jdbcType=VARCHAR},
      end_postion = #{record.endPostion,jdbcType=VARCHAR},
      super_password = #{record.superPassword,jdbcType=VARCHAR},
      usage_rights = #{record.usageRights,jdbcType=VARCHAR},
      is_business = #{record.isBusiness,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs">
    update vehicle_info
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=CHAR},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="park != null">
        park = #{park,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=TINYINT},
      </if>
      <if test="vehicleCategory != null">
        vehicle_category = #{vehicleCategory,jdbcType=VARCHAR},
      </if>
      <if test="licenseNumber != null">
        license_number = #{licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="licence != null">
        licence = #{licence,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="powerType != null">
        power_type = #{powerType,jdbcType=TINYINT},
      </if>
      <if test="operationState != null">
        operation_state = #{operationState,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="product != null">
        product = #{product,jdbcType=INTEGER},
      </if>
      <if test="purposeId != null">
        purpose_id = #{purposeId,jdbcType=TINYINT},
      </if>
      <if test="isDedicated != null">
        is_dedicated = #{isDedicated,jdbcType=BIT},
      </if>
      <if test="submitter != null">
        submitter = #{submitter,jdbcType=VARCHAR},
      </if>
      <if test="activateCode != null">
        activate_code = #{activateCode,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        access_secret = #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="boxQrcode != null">
        box_qrcode = #{boxQrcode,jdbcType=VARCHAR},
      </if>
      <if test="startPostion != null">
        start_postion = #{startPostion,jdbcType=VARCHAR},
      </if>
      <if test="endPostion != null">
        end_postion = #{endPostion,jdbcType=VARCHAR},
      </if>
      <if test="superPassword != null">
        super_password = #{superPassword,jdbcType=VARCHAR},
      </if>
      <if test="usageRights != null">
        usage_rights = #{usageRights,jdbcType=VARCHAR},
      </if>
      <if test="isBusiness != null">
        is_business = #{isBusiness,jdbcType=BIT},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs">
    update vehicle_info
    set vehicle_id = #{vehicleId,jdbcType=CHAR},
      vin = #{vin,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      park = #{park,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=TINYINT},
      vehicle_category = #{vehicleCategory,jdbcType=VARCHAR},
      license_number = #{licenseNumber,jdbcType=VARCHAR},
      licence = #{licence,jdbcType=VARCHAR},
      year = #{year,jdbcType=INTEGER},
      power_type = #{powerType,jdbcType=TINYINT},
      operation_state = #{operationState,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      owner = #{owner,jdbcType=VARCHAR},
      product = #{product,jdbcType=INTEGER},
      purpose_id = #{purposeId,jdbcType=TINYINT},
      is_dedicated = #{isDedicated,jdbcType=BIT},
      submitter = #{submitter,jdbcType=VARCHAR},
      activate_code = #{activateCode,jdbcType=VARCHAR},
      access_secret = #{accessSecret,jdbcType=VARCHAR},
      box_qrcode = #{boxQrcode,jdbcType=VARCHAR},
      start_postion = #{startPostion,jdbcType=VARCHAR},
      end_postion = #{endPostion,jdbcType=VARCHAR},
      super_password = #{superPassword,jdbcType=VARCHAR},
      usage_rights = #{usageRights,jdbcType=VARCHAR},
      is_business = #{isBusiness,jdbcType=BIT},
      image = #{image,jdbcType=LONGVARCHAR},
      remark = #{remark,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.walle_data_center.entity.VehicleInfo">
    update vehicle_info
    set vehicle_id = #{vehicleId,jdbcType=CHAR},
      vin = #{vin,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      park = #{park,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=TINYINT},
      vehicle_category = #{vehicleCategory,jdbcType=VARCHAR},
      license_number = #{licenseNumber,jdbcType=VARCHAR},
      licence = #{licence,jdbcType=VARCHAR},
      year = #{year,jdbcType=INTEGER},
      power_type = #{powerType,jdbcType=TINYINT},
      operation_state = #{operationState,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      owner = #{owner,jdbcType=VARCHAR},
      product = #{product,jdbcType=INTEGER},
      purpose_id = #{purposeId,jdbcType=TINYINT},
      is_dedicated = #{isDedicated,jdbcType=BIT},
      submitter = #{submitter,jdbcType=VARCHAR},
      activate_code = #{activateCode,jdbcType=VARCHAR},
      access_secret = #{accessSecret,jdbcType=VARCHAR},
      box_qrcode = #{boxQrcode,jdbcType=VARCHAR},
      start_postion = #{startPostion,jdbcType=VARCHAR},
      end_postion = #{endPostion,jdbcType=VARCHAR},
      super_password = #{superPassword,jdbcType=VARCHAR},
      usage_rights = #{usageRights,jdbcType=VARCHAR},
      is_business = #{isBusiness,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into vehicle_info
    (vehicle_id, vin, name, park, brand, type, vehicle_type, vehicle_category, license_number, 
      licence, year, power_type, operation_state, status, create_time, update_time, owner, 
      product, purpose_id, is_dedicated, submitter, activate_code, access_secret, box_qrcode, 
      start_postion, end_postion, super_password, usage_rights, is_business, image, remark
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleId,jdbcType=CHAR}, #{item.vin,jdbcType=CHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.park,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.vehicleType,jdbcType=TINYINT}, #{item.vehicleCategory,jdbcType=VARCHAR}, 
        #{item.licenseNumber,jdbcType=VARCHAR}, #{item.licence,jdbcType=VARCHAR}, #{item.year,jdbcType=INTEGER}, 
        #{item.powerType,jdbcType=TINYINT}, #{item.operationState,jdbcType=TINYINT}, #{item.status,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.owner,jdbcType=VARCHAR}, 
        #{item.product,jdbcType=INTEGER}, #{item.purposeId,jdbcType=TINYINT}, #{item.isDedicated,jdbcType=BIT}, 
        #{item.submitter,jdbcType=VARCHAR}, #{item.activateCode,jdbcType=VARCHAR}, #{item.accessSecret,jdbcType=VARCHAR}, 
        #{item.boxQrcode,jdbcType=VARCHAR}, #{item.startPostion,jdbcType=VARCHAR}, #{item.endPostion,jdbcType=VARCHAR}, 
        #{item.superPassword,jdbcType=VARCHAR}, #{item.usageRights,jdbcType=VARCHAR}, #{item.isBusiness,jdbcType=BIT}, 
        #{item.image,jdbcType=LONGVARCHAR}, #{item.remark,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>