<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.eve.mapper.CarDevicesMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.eve.entity.CarDevices">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `type`, vin, sn, operator_id, add_time, update_time, mac
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.eve.example.CarDevicesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_devices
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_devices
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_devices
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.eve.example.CarDevicesExample">
    delete from car_devices
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.eve.entity.CarDevices">
    insert into car_devices (id, `type`, vin, 
      sn, operator_id, add_time, 
      update_time, mac)
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{vin,jdbcType=VARCHAR}, 
      #{sn,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{mac,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.eve.entity.CarDevices">
    insert into car_devices
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mac != null">
        mac,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mac != null">
        #{mac,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.eve.example.CarDevicesExample" resultType="java.lang.Long">
    select count(*) from car_devices
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_devices
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mac != null">
        mac = #{record.mac,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_devices
    set id = #{record.id,jdbcType=BIGINT},
      `type` = #{record.type,jdbcType=VARCHAR},
      vin = #{record.vin,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      mac = #{record.mac,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.eve.entity.CarDevices">
    update car_devices
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mac != null">
        mac = #{mac,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.eve.entity.CarDevices">
    update car_devices
    set `type` = #{type,jdbcType=VARCHAR},
      vin = #{vin,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      mac = #{mac,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_devices
    (id, `type`, vin, sn, operator_id, add_time, update_time, mac)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.type,jdbcType=VARCHAR}, #{item.vin,jdbcType=VARCHAR}, 
        #{item.sn,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.mac,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.eve.example.CarDevicesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_devices
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>