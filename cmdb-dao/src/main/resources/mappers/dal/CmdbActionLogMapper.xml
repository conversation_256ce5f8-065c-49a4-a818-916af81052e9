<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.eve.mapper.CmdbActionLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.eve.entity.CmdbActionLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mis" jdbcType="VARCHAR" property="mis" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="action_content" jdbcType="VARCHAR" property="actionContent" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mis, vin, action_content, add_time, `type`
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.eve.example.CmdbActionLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cmdb_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cmdb_action_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cmdb_action_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.eve.example.CmdbActionLogExample">
    delete from cmdb_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.eve.entity.CmdbActionLog">
    insert into cmdb_action_log (id, mis, vin, 
      action_content, add_time, `type`
      )
    values (#{id,jdbcType=BIGINT}, #{mis,jdbcType=VARCHAR}, #{vin,jdbcType=VARCHAR}, 
      #{actionContent,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{type,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.eve.entity.CmdbActionLog">
    insert into cmdb_action_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mis != null">
        mis,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="actionContent != null">
        action_content,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mis != null">
        #{mis,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="actionContent != null">
        #{actionContent,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.eve.example.CmdbActionLogExample" resultType="java.lang.Long">
    select count(*) from cmdb_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cmdb_action_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mis != null">
        mis = #{record.mis,jdbcType=VARCHAR},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.actionContent != null">
        action_content = #{record.actionContent,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cmdb_action_log
    set id = #{record.id,jdbcType=BIGINT},
      mis = #{record.mis,jdbcType=VARCHAR},
      vin = #{record.vin,jdbcType=VARCHAR},
      action_content = #{record.actionContent,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      `type` = #{record.type,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.eve.entity.CmdbActionLog">
    update cmdb_action_log
    <set>
      <if test="mis != null">
        mis = #{mis,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="actionContent != null">
        action_content = #{actionContent,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.eve.entity.CmdbActionLog">
    update cmdb_action_log
    set mis = #{mis,jdbcType=VARCHAR},
      vin = #{vin,jdbcType=VARCHAR},
      action_content = #{actionContent,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cmdb_action_log
    (id, mis, vin, action_content, add_time, `type`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.mis,jdbcType=VARCHAR}, #{item.vin,jdbcType=VARCHAR}, 
        #{item.actionContent,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.eve.example.CmdbActionLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cmdb_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>