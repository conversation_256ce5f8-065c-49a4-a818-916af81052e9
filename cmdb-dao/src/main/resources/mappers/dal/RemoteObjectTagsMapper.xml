<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectTagsMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="remote_object_id" jdbcType="BIGINT" property="remoteObjectId" />
    <result column="tag_id" jdbcType="BIGINT" property="tagId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, remote_object_id, tag_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectTagsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from remote_object_tags
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from remote_object_tags
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from remote_object_tags
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectTagsExample">
    delete from remote_object_tags
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags" useGeneratedKeys="true">
    insert into remote_object_tags (remote_object_id, tag_id)
    values (#{remoteObjectId,jdbcType=BIGINT}, #{tagId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags" useGeneratedKeys="true">
    insert into remote_object_tags
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="remoteObjectId != null">
        remote_object_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="remoteObjectId != null">
        #{remoteObjectId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectTagsExample" resultType="java.lang.Long">
    select count(*) from remote_object_tags
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update remote_object_tags
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.remoteObjectId != null">
        remote_object_id = #{record.remoteObjectId,jdbcType=BIGINT},
      </if>
      <if test="record.tagId != null">
        tag_id = #{record.tagId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update remote_object_tags
    set id = #{record.id,jdbcType=BIGINT},
      remote_object_id = #{record.remoteObjectId,jdbcType=BIGINT},
      tag_id = #{record.tagId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags">
    update remote_object_tags
    <set>
      <if test="remoteObjectId != null">
        remote_object_id = #{remoteObjectId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags">
    update remote_object_tags
    set remote_object_id = #{remoteObjectId,jdbcType=BIGINT},
      tag_id = #{tagId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into remote_object_tags
    (remote_object_id, tag_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.remoteObjectId,jdbcType=BIGINT}, #{item.tagId,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>