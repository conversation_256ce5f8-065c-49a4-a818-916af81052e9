<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarConfigQueryMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="config_version" jdbcType="VARCHAR" property="configVersion" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    <result column="config" jdbcType="LONGVARCHAR" property="config" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, `name`, file_name, config_version, update_time
  </sql>
  <sql id="Blob_Column_List">
    config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.classify.example.CarConfigQueryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigQueryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_config_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config_query
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_config_query
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigQueryExample">
    delete from car_config_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    insert into car_config_query (id, vin, `name`, 
      file_name, config_version, update_time, 
      config)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{configVersion,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{config,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    insert into car_config_query
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="configVersion != null">
        config_version,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="config != null">
        config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="configVersion != null">
        #{configVersion,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="config != null">
        #{config,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigQueryExample" resultType="java.lang.Long">
    select count(*) from car_config_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_config_query
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.configVersion != null">
        config_version = #{record.configVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.config != null">
        config = #{record.config,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update car_config_query
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      config_version = #{record.configVersion,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      config = #{record.config,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_config_query
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      config_version = #{record.configVersion,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    update car_config_query
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="configVersion != null">
        config_version = #{configVersion,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="config != null">
        config = #{config,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    update car_config_query
    set vin = #{vin,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      config_version = #{configVersion,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      config = #{config,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigQuery">
    update car_config_query
    set vin = #{vin,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      config_version = #{configVersion,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_config_query
    (id, vin, `name`, file_name, config_version, update_time, config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.fileName,jdbcType=VARCHAR}, #{item.configVersion,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.config,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.walle.dal.classify.example.CarConfigQueryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.classify.example.CarConfigQueryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_config_query
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>