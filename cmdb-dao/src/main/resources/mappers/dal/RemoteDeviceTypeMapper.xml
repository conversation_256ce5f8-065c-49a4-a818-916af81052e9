<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.RemoteDeviceTypeMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="friend_name" jdbcType="VARCHAR" property="friendName" />
    <result column="conf_content" jdbcType="CHAR" property="confContent" />
    <result column="latest_version_id" jdbcType="BIGINT" property="latestVersionId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type_name, friend_name, conf_content, latest_version_id, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteDeviceTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from remote_device_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from remote_device_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from remote_device_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteDeviceTypeExample">
    delete from remote_device_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType" useGeneratedKeys="true">
    insert into remote_device_type (type_name, friend_name, conf_content, 
      latest_version_id, add_time, update_time
      )
    values (#{typeName,jdbcType=VARCHAR}, #{friendName,jdbcType=VARCHAR}, #{confContent,jdbcType=CHAR}, 
      #{latestVersionId,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType" useGeneratedKeys="true">
    insert into remote_device_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="typeName != null">
        type_name,
      </if>
      <if test="friendName != null">
        friend_name,
      </if>
      <if test="confContent != null">
        conf_content,
      </if>
      <if test="latestVersionId != null">
        latest_version_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="friendName != null">
        #{friendName,jdbcType=VARCHAR},
      </if>
      <if test="confContent != null">
        #{confContent,jdbcType=CHAR},
      </if>
      <if test="latestVersionId != null">
        #{latestVersionId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteDeviceTypeExample" resultType="java.lang.Long">
    select count(*) from remote_device_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update remote_device_type
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.typeName != null">
        type_name = #{record.typeName,jdbcType=VARCHAR},
      </if>
      <if test="record.friendName != null">
        friend_name = #{record.friendName,jdbcType=VARCHAR},
      </if>
      <if test="record.confContent != null">
        conf_content = #{record.confContent,jdbcType=CHAR},
      </if>
      <if test="record.latestVersionId != null">
        latest_version_id = #{record.latestVersionId,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update remote_device_type
    set id = #{record.id,jdbcType=BIGINT},
      type_name = #{record.typeName,jdbcType=VARCHAR},
      friend_name = #{record.friendName,jdbcType=VARCHAR},
      conf_content = #{record.confContent,jdbcType=CHAR},
      latest_version_id = #{record.latestVersionId,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType">
    update remote_device_type
    <set>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="friendName != null">
        friend_name = #{friendName,jdbcType=VARCHAR},
      </if>
      <if test="confContent != null">
        conf_content = #{confContent,jdbcType=CHAR},
      </if>
      <if test="latestVersionId != null">
        latest_version_id = #{latestVersionId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType">
    update remote_device_type
    set type_name = #{typeName,jdbcType=VARCHAR},
      friend_name = #{friendName,jdbcType=VARCHAR},
      conf_content = #{confContent,jdbcType=CHAR},
      latest_version_id = #{latestVersionId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into remote_device_type
    (type_name, friend_name, conf_content, latest_version_id, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.typeName,jdbcType=VARCHAR}, #{item.friendName,jdbcType=VARCHAR}, #{item.confContent,jdbcType=CHAR}, 
        #{item.latestVersionId,jdbcType=BIGINT}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>