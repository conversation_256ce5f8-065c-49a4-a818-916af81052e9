<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.eve.mapper.VehicleBaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.eve.entity.VehicleBaseInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="first_class_model" jdbcType="VARCHAR" property="firstClassModel" />
    <result column="second_class_model" jdbcType="VARCHAR" property="secondClassModel" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="exec_word" jdbcType="CHAR" property="execWord" />
    <result column="car_size_type" jdbcType="TINYINT" property="carSizeType" />
    <result column="power_type" jdbcType="INTEGER" property="powerType" />
    <result column="refited" jdbcType="TINYINT" property="refited" />
    <result column="assembly_parts" jdbcType="CHAR" property="assemblyParts" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="registration_date" jdbcType="TIMESTAMP" property="registrationDate" />
    <result column="annual_inspection_date" jdbcType="TIMESTAMP" property="annualInspectionDate" />
    <result column="car_preduce_owner" jdbcType="VARCHAR" property="carPreduceOwner" />
    <result column="indicate_owner" jdbcType="VARCHAR" property="indicateOwner" />
    <result column="indicate_rental_duration" jdbcType="BIGINT" property="indicateRentalDuration" />
    <result column="rent_expense" jdbcType="BIGINT" property="rentExpense" />
    <result column="leasing_expense" jdbcType="BIGINT" property="leasingExpense" />
    <result column="leasing_duration" jdbcType="BIGINT" property="leasingDuration" />
    <result column="heavy_traffic_insurance" jdbcType="TIMESTAMP" property="heavyTrafficInsurance" />
    <result column="business_insurance_date" jdbcType="TIMESTAMP" property="businessInsuranceDate" />
    <result column="equipment_insurance_date" jdbcType="TIMESTAMP" property="equipmentInsuranceDate" />
    <result column="priority_insurance_date" jdbcType="TIMESTAMP" property="priorityInsuranceDate" />
    <result column="third_liability_insurance_date" jdbcType="TIMESTAMP" property="thirdLiabilityInsuranceDate" />
    <result column="key_num" jdbcType="BIGINT" property="keyNum" />
    <result column="back_key_owner" jdbcType="VARCHAR" property="backKeyOwner" />
    <result column="driving_permit_in_car" jdbcType="VARCHAR" property="drivingPermitInCar" />
    <result column="car_used_target" jdbcType="VARCHAR" property="carUsedTarget" />
    <result column="vehicle_name" jdbcType="VARCHAR" property="vehicleName" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="place" jdbcType="VARCHAR" property="place" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="ability" jdbcType="VARCHAR" property="ability" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="person_mis" jdbcType="VARCHAR" property="personMis" />
    <result column="owner_department" jdbcType="VARCHAR" property="ownerDepartment" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="car_belong_type" jdbcType="BIGINT" property="carBelongType" />
    <result column="car_owner" jdbcType="VARCHAR" property="carOwner" />
    <result column="scrap" jdbcType="BIT" property="scrap" />
    <result column="scrap_reason" jdbcType="VARCHAR" property="scrapReason" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, first_class_model, second_class_model, brand, exec_word, car_size_type, 
    power_type, refited, assembly_parts, license_no, registration_date, annual_inspection_date, 
    car_preduce_owner, indicate_owner, indicate_rental_duration, rent_expense, leasing_expense, 
    leasing_duration, heavy_traffic_insurance, business_insurance_date, equipment_insurance_date, 
    priority_insurance_date, third_liability_insurance_date, key_num, back_key_owner, 
    driving_permit_in_car, car_used_target, vehicle_name, city, place, business_name, 
    ability, `status`, person_mis, owner_department, `label`, sn, car_belong_type, car_owner, 
    scrap, scrap_reason, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.eve.example.VehicleBaseInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vehicle_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_base_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_base_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.eve.example.VehicleBaseInfoExample">
    delete from vehicle_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.eve.entity.VehicleBaseInfo">
    insert into vehicle_base_info (id, vin, first_class_model, 
      second_class_model, brand, exec_word, 
      car_size_type, power_type, refited, 
      assembly_parts, license_no, registration_date, 
      annual_inspection_date, car_preduce_owner, 
      indicate_owner, indicate_rental_duration, rent_expense, 
      leasing_expense, leasing_duration, heavy_traffic_insurance, 
      business_insurance_date, equipment_insurance_date, 
      priority_insurance_date, third_liability_insurance_date, 
      key_num, back_key_owner, driving_permit_in_car, 
      car_used_target, vehicle_name, city, 
      place, business_name, ability, 
      `status`, person_mis, owner_department, 
      `label`, sn, car_belong_type, 
      car_owner, scrap, scrap_reason, 
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{firstClassModel,jdbcType=VARCHAR}, 
      #{secondClassModel,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{execWord,jdbcType=CHAR}, 
      #{carSizeType,jdbcType=TINYINT}, #{powerType,jdbcType=INTEGER}, #{refited,jdbcType=TINYINT}, 
      #{assemblyParts,jdbcType=CHAR}, #{licenseNo,jdbcType=VARCHAR}, #{registrationDate,jdbcType=TIMESTAMP}, 
      #{annualInspectionDate,jdbcType=TIMESTAMP}, #{carPreduceOwner,jdbcType=VARCHAR}, 
      #{indicateOwner,jdbcType=VARCHAR}, #{indicateRentalDuration,jdbcType=BIGINT}, #{rentExpense,jdbcType=BIGINT}, 
      #{leasingExpense,jdbcType=BIGINT}, #{leasingDuration,jdbcType=BIGINT}, #{heavyTrafficInsurance,jdbcType=TIMESTAMP}, 
      #{businessInsuranceDate,jdbcType=TIMESTAMP}, #{equipmentInsuranceDate,jdbcType=TIMESTAMP}, 
      #{priorityInsuranceDate,jdbcType=TIMESTAMP}, #{thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP}, 
      #{keyNum,jdbcType=BIGINT}, #{backKeyOwner,jdbcType=VARCHAR}, #{drivingPermitInCar,jdbcType=VARCHAR}, 
      #{carUsedTarget,jdbcType=VARCHAR}, #{vehicleName,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{place,jdbcType=VARCHAR}, #{businessName,jdbcType=VARCHAR}, #{ability,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{personMis,jdbcType=VARCHAR}, #{ownerDepartment,jdbcType=VARCHAR}, 
      #{label,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{carBelongType,jdbcType=BIGINT}, 
      #{carOwner,jdbcType=VARCHAR}, #{scrap,jdbcType=BIT}, #{scrapReason,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.eve.entity.VehicleBaseInfo">
    insert into vehicle_base_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="firstClassModel != null">
        first_class_model,
      </if>
      <if test="secondClassModel != null">
        second_class_model,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="execWord != null">
        exec_word,
      </if>
      <if test="carSizeType != null">
        car_size_type,
      </if>
      <if test="powerType != null">
        power_type,
      </if>
      <if test="refited != null">
        refited,
      </if>
      <if test="assemblyParts != null">
        assembly_parts,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="registrationDate != null">
        registration_date,
      </if>
      <if test="annualInspectionDate != null">
        annual_inspection_date,
      </if>
      <if test="carPreduceOwner != null">
        car_preduce_owner,
      </if>
      <if test="indicateOwner != null">
        indicate_owner,
      </if>
      <if test="indicateRentalDuration != null">
        indicate_rental_duration,
      </if>
      <if test="rentExpense != null">
        rent_expense,
      </if>
      <if test="leasingExpense != null">
        leasing_expense,
      </if>
      <if test="leasingDuration != null">
        leasing_duration,
      </if>
      <if test="heavyTrafficInsurance != null">
        heavy_traffic_insurance,
      </if>
      <if test="businessInsuranceDate != null">
        business_insurance_date,
      </if>
      <if test="equipmentInsuranceDate != null">
        equipment_insurance_date,
      </if>
      <if test="priorityInsuranceDate != null">
        priority_insurance_date,
      </if>
      <if test="thirdLiabilityInsuranceDate != null">
        third_liability_insurance_date,
      </if>
      <if test="keyNum != null">
        key_num,
      </if>
      <if test="backKeyOwner != null">
        back_key_owner,
      </if>
      <if test="drivingPermitInCar != null">
        driving_permit_in_car,
      </if>
      <if test="carUsedTarget != null">
        car_used_target,
      </if>
      <if test="vehicleName != null">
        vehicle_name,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="place != null">
        place,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="ability != null">
        ability,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="personMis != null">
        person_mis,
      </if>
      <if test="ownerDepartment != null">
        owner_department,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="carBelongType != null">
        car_belong_type,
      </if>
      <if test="carOwner != null">
        car_owner,
      </if>
      <if test="scrap != null">
        scrap,
      </if>
      <if test="scrapReason != null">
        scrap_reason,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="firstClassModel != null">
        #{firstClassModel,jdbcType=VARCHAR},
      </if>
      <if test="secondClassModel != null">
        #{secondClassModel,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="execWord != null">
        #{execWord,jdbcType=CHAR},
      </if>
      <if test="carSizeType != null">
        #{carSizeType,jdbcType=TINYINT},
      </if>
      <if test="powerType != null">
        #{powerType,jdbcType=INTEGER},
      </if>
      <if test="refited != null">
        #{refited,jdbcType=TINYINT},
      </if>
      <if test="assemblyParts != null">
        #{assemblyParts,jdbcType=CHAR},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="registrationDate != null">
        #{registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="annualInspectionDate != null">
        #{annualInspectionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="carPreduceOwner != null">
        #{carPreduceOwner,jdbcType=VARCHAR},
      </if>
      <if test="indicateOwner != null">
        #{indicateOwner,jdbcType=VARCHAR},
      </if>
      <if test="indicateRentalDuration != null">
        #{indicateRentalDuration,jdbcType=BIGINT},
      </if>
      <if test="rentExpense != null">
        #{rentExpense,jdbcType=BIGINT},
      </if>
      <if test="leasingExpense != null">
        #{leasingExpense,jdbcType=BIGINT},
      </if>
      <if test="leasingDuration != null">
        #{leasingDuration,jdbcType=BIGINT},
      </if>
      <if test="heavyTrafficInsurance != null">
        #{heavyTrafficInsurance,jdbcType=TIMESTAMP},
      </if>
      <if test="businessInsuranceDate != null">
        #{businessInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="equipmentInsuranceDate != null">
        #{equipmentInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="priorityInsuranceDate != null">
        #{priorityInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdLiabilityInsuranceDate != null">
        #{thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="keyNum != null">
        #{keyNum,jdbcType=BIGINT},
      </if>
      <if test="backKeyOwner != null">
        #{backKeyOwner,jdbcType=VARCHAR},
      </if>
      <if test="drivingPermitInCar != null">
        #{drivingPermitInCar,jdbcType=VARCHAR},
      </if>
      <if test="carUsedTarget != null">
        #{carUsedTarget,jdbcType=VARCHAR},
      </if>
      <if test="vehicleName != null">
        #{vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="place != null">
        #{place,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="ability != null">
        #{ability,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="personMis != null">
        #{personMis,jdbcType=VARCHAR},
      </if>
      <if test="ownerDepartment != null">
        #{ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="carBelongType != null">
        #{carBelongType,jdbcType=BIGINT},
      </if>
      <if test="carOwner != null">
        #{carOwner,jdbcType=VARCHAR},
      </if>
      <if test="scrap != null">
        #{scrap,jdbcType=BIT},
      </if>
      <if test="scrapReason != null">
        #{scrapReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.eve.example.VehicleBaseInfoExample" resultType="java.lang.Long">
    select count(*) from vehicle_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_base_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.firstClassModel != null">
        first_class_model = #{record.firstClassModel,jdbcType=VARCHAR},
      </if>
      <if test="record.secondClassModel != null">
        second_class_model = #{record.secondClassModel,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.execWord != null">
        exec_word = #{record.execWord,jdbcType=CHAR},
      </if>
      <if test="record.carSizeType != null">
        car_size_type = #{record.carSizeType,jdbcType=TINYINT},
      </if>
      <if test="record.powerType != null">
        power_type = #{record.powerType,jdbcType=INTEGER},
      </if>
      <if test="record.refited != null">
        refited = #{record.refited,jdbcType=TINYINT},
      </if>
      <if test="record.assemblyParts != null">
        assembly_parts = #{record.assemblyParts,jdbcType=CHAR},
      </if>
      <if test="record.licenseNo != null">
        license_no = #{record.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registrationDate != null">
        registration_date = #{record.registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.annualInspectionDate != null">
        annual_inspection_date = #{record.annualInspectionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.carPreduceOwner != null">
        car_preduce_owner = #{record.carPreduceOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.indicateOwner != null">
        indicate_owner = #{record.indicateOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.indicateRentalDuration != null">
        indicate_rental_duration = #{record.indicateRentalDuration,jdbcType=BIGINT},
      </if>
      <if test="record.rentExpense != null">
        rent_expense = #{record.rentExpense,jdbcType=BIGINT},
      </if>
      <if test="record.leasingExpense != null">
        leasing_expense = #{record.leasingExpense,jdbcType=BIGINT},
      </if>
      <if test="record.leasingDuration != null">
        leasing_duration = #{record.leasingDuration,jdbcType=BIGINT},
      </if>
      <if test="record.heavyTrafficInsurance != null">
        heavy_traffic_insurance = #{record.heavyTrafficInsurance,jdbcType=TIMESTAMP},
      </if>
      <if test="record.businessInsuranceDate != null">
        business_insurance_date = #{record.businessInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.equipmentInsuranceDate != null">
        equipment_insurance_date = #{record.equipmentInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.priorityInsuranceDate != null">
        priority_insurance_date = #{record.priorityInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.thirdLiabilityInsuranceDate != null">
        third_liability_insurance_date = #{record.thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.keyNum != null">
        key_num = #{record.keyNum,jdbcType=BIGINT},
      </if>
      <if test="record.backKeyOwner != null">
        back_key_owner = #{record.backKeyOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.drivingPermitInCar != null">
        driving_permit_in_car = #{record.drivingPermitInCar,jdbcType=VARCHAR},
      </if>
      <if test="record.carUsedTarget != null">
        car_used_target = #{record.carUsedTarget,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleName != null">
        vehicle_name = #{record.vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.place != null">
        place = #{record.place,jdbcType=VARCHAR},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.ability != null">
        ability = #{record.ability,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.personMis != null">
        person_mis = #{record.personMis,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerDepartment != null">
        owner_department = #{record.ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null">
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.carBelongType != null">
        car_belong_type = #{record.carBelongType,jdbcType=BIGINT},
      </if>
      <if test="record.carOwner != null">
        car_owner = #{record.carOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.scrap != null">
        scrap = #{record.scrap,jdbcType=BIT},
      </if>
      <if test="record.scrapReason != null">
        scrap_reason = #{record.scrapReason,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_base_info
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      first_class_model = #{record.firstClassModel,jdbcType=VARCHAR},
      second_class_model = #{record.secondClassModel,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      exec_word = #{record.execWord,jdbcType=CHAR},
      car_size_type = #{record.carSizeType,jdbcType=TINYINT},
      power_type = #{record.powerType,jdbcType=INTEGER},
      refited = #{record.refited,jdbcType=TINYINT},
      assembly_parts = #{record.assemblyParts,jdbcType=CHAR},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      registration_date = #{record.registrationDate,jdbcType=TIMESTAMP},
      annual_inspection_date = #{record.annualInspectionDate,jdbcType=TIMESTAMP},
      car_preduce_owner = #{record.carPreduceOwner,jdbcType=VARCHAR},
      indicate_owner = #{record.indicateOwner,jdbcType=VARCHAR},
      indicate_rental_duration = #{record.indicateRentalDuration,jdbcType=BIGINT},
      rent_expense = #{record.rentExpense,jdbcType=BIGINT},
      leasing_expense = #{record.leasingExpense,jdbcType=BIGINT},
      leasing_duration = #{record.leasingDuration,jdbcType=BIGINT},
      heavy_traffic_insurance = #{record.heavyTrafficInsurance,jdbcType=TIMESTAMP},
      business_insurance_date = #{record.businessInsuranceDate,jdbcType=TIMESTAMP},
      equipment_insurance_date = #{record.equipmentInsuranceDate,jdbcType=TIMESTAMP},
      priority_insurance_date = #{record.priorityInsuranceDate,jdbcType=TIMESTAMP},
      third_liability_insurance_date = #{record.thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP},
      key_num = #{record.keyNum,jdbcType=BIGINT},
      back_key_owner = #{record.backKeyOwner,jdbcType=VARCHAR},
      driving_permit_in_car = #{record.drivingPermitInCar,jdbcType=VARCHAR},
      car_used_target = #{record.carUsedTarget,jdbcType=VARCHAR},
      vehicle_name = #{record.vehicleName,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      place = #{record.place,jdbcType=VARCHAR},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      ability = #{record.ability,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      person_mis = #{record.personMis,jdbcType=VARCHAR},
      owner_department = #{record.ownerDepartment,jdbcType=VARCHAR},
      `label` = #{record.label,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR},
      car_belong_type = #{record.carBelongType,jdbcType=BIGINT},
      car_owner = #{record.carOwner,jdbcType=VARCHAR},
      scrap = #{record.scrap,jdbcType=BIT},
      scrap_reason = #{record.scrapReason,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.eve.entity.VehicleBaseInfo">
    update vehicle_base_info
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="firstClassModel != null">
        first_class_model = #{firstClassModel,jdbcType=VARCHAR},
      </if>
      <if test="secondClassModel != null">
        second_class_model = #{secondClassModel,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="execWord != null">
        exec_word = #{execWord,jdbcType=CHAR},
      </if>
      <if test="carSizeType != null">
        car_size_type = #{carSizeType,jdbcType=TINYINT},
      </if>
      <if test="powerType != null">
        power_type = #{powerType,jdbcType=INTEGER},
      </if>
      <if test="refited != null">
        refited = #{refited,jdbcType=TINYINT},
      </if>
      <if test="assemblyParts != null">
        assembly_parts = #{assemblyParts,jdbcType=CHAR},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="registrationDate != null">
        registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="annualInspectionDate != null">
        annual_inspection_date = #{annualInspectionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="carPreduceOwner != null">
        car_preduce_owner = #{carPreduceOwner,jdbcType=VARCHAR},
      </if>
      <if test="indicateOwner != null">
        indicate_owner = #{indicateOwner,jdbcType=VARCHAR},
      </if>
      <if test="indicateRentalDuration != null">
        indicate_rental_duration = #{indicateRentalDuration,jdbcType=BIGINT},
      </if>
      <if test="rentExpense != null">
        rent_expense = #{rentExpense,jdbcType=BIGINT},
      </if>
      <if test="leasingExpense != null">
        leasing_expense = #{leasingExpense,jdbcType=BIGINT},
      </if>
      <if test="leasingDuration != null">
        leasing_duration = #{leasingDuration,jdbcType=BIGINT},
      </if>
      <if test="heavyTrafficInsurance != null">
        heavy_traffic_insurance = #{heavyTrafficInsurance,jdbcType=TIMESTAMP},
      </if>
      <if test="businessInsuranceDate != null">
        business_insurance_date = #{businessInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="equipmentInsuranceDate != null">
        equipment_insurance_date = #{equipmentInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="priorityInsuranceDate != null">
        priority_insurance_date = #{priorityInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdLiabilityInsuranceDate != null">
        third_liability_insurance_date = #{thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="keyNum != null">
        key_num = #{keyNum,jdbcType=BIGINT},
      </if>
      <if test="backKeyOwner != null">
        back_key_owner = #{backKeyOwner,jdbcType=VARCHAR},
      </if>
      <if test="drivingPermitInCar != null">
        driving_permit_in_car = #{drivingPermitInCar,jdbcType=VARCHAR},
      </if>
      <if test="carUsedTarget != null">
        car_used_target = #{carUsedTarget,jdbcType=VARCHAR},
      </if>
      <if test="vehicleName != null">
        vehicle_name = #{vehicleName,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="place != null">
        place = #{place,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="ability != null">
        ability = #{ability,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="personMis != null">
        person_mis = #{personMis,jdbcType=VARCHAR},
      </if>
      <if test="ownerDepartment != null">
        owner_department = #{ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="carBelongType != null">
        car_belong_type = #{carBelongType,jdbcType=BIGINT},
      </if>
      <if test="carOwner != null">
        car_owner = #{carOwner,jdbcType=VARCHAR},
      </if>
      <if test="scrap != null">
        scrap = #{scrap,jdbcType=BIT},
      </if>
      <if test="scrapReason != null">
        scrap_reason = #{scrapReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.eve.entity.VehicleBaseInfo">
    update vehicle_base_info
    set vin = #{vin,jdbcType=VARCHAR},
      first_class_model = #{firstClassModel,jdbcType=VARCHAR},
      second_class_model = #{secondClassModel,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      exec_word = #{execWord,jdbcType=CHAR},
      car_size_type = #{carSizeType,jdbcType=TINYINT},
      power_type = #{powerType,jdbcType=INTEGER},
      refited = #{refited,jdbcType=TINYINT},
      assembly_parts = #{assemblyParts,jdbcType=CHAR},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      registration_date = #{registrationDate,jdbcType=TIMESTAMP},
      annual_inspection_date = #{annualInspectionDate,jdbcType=TIMESTAMP},
      car_preduce_owner = #{carPreduceOwner,jdbcType=VARCHAR},
      indicate_owner = #{indicateOwner,jdbcType=VARCHAR},
      indicate_rental_duration = #{indicateRentalDuration,jdbcType=BIGINT},
      rent_expense = #{rentExpense,jdbcType=BIGINT},
      leasing_expense = #{leasingExpense,jdbcType=BIGINT},
      leasing_duration = #{leasingDuration,jdbcType=BIGINT},
      heavy_traffic_insurance = #{heavyTrafficInsurance,jdbcType=TIMESTAMP},
      business_insurance_date = #{businessInsuranceDate,jdbcType=TIMESTAMP},
      equipment_insurance_date = #{equipmentInsuranceDate,jdbcType=TIMESTAMP},
      priority_insurance_date = #{priorityInsuranceDate,jdbcType=TIMESTAMP},
      third_liability_insurance_date = #{thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP},
      key_num = #{keyNum,jdbcType=BIGINT},
      back_key_owner = #{backKeyOwner,jdbcType=VARCHAR},
      driving_permit_in_car = #{drivingPermitInCar,jdbcType=VARCHAR},
      car_used_target = #{carUsedTarget,jdbcType=VARCHAR},
      vehicle_name = #{vehicleName,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      place = #{place,jdbcType=VARCHAR},
      business_name = #{businessName,jdbcType=VARCHAR},
      ability = #{ability,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      person_mis = #{personMis,jdbcType=VARCHAR},
      owner_department = #{ownerDepartment,jdbcType=VARCHAR},
      `label` = #{label,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      car_belong_type = #{carBelongType,jdbcType=BIGINT},
      car_owner = #{carOwner,jdbcType=VARCHAR},
      scrap = #{scrap,jdbcType=BIT},
      scrap_reason = #{scrapReason,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into vehicle_base_info
    (id, vin, first_class_model, second_class_model, brand, exec_word, car_size_type, 
      power_type, refited, assembly_parts, license_no, registration_date, annual_inspection_date, 
      car_preduce_owner, indicate_owner, indicate_rental_duration, rent_expense, leasing_expense, 
      leasing_duration, heavy_traffic_insurance, business_insurance_date, equipment_insurance_date, 
      priority_insurance_date, third_liability_insurance_date, key_num, back_key_owner, 
      driving_permit_in_car, car_used_target, vehicle_name, city, place, business_name, 
      ability, `status`, person_mis, owner_department, `label`, sn, car_belong_type, 
      car_owner, scrap, scrap_reason, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.firstClassModel,jdbcType=VARCHAR}, 
        #{item.secondClassModel,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.execWord,jdbcType=CHAR}, 
        #{item.carSizeType,jdbcType=TINYINT}, #{item.powerType,jdbcType=INTEGER}, #{item.refited,jdbcType=TINYINT}, 
        #{item.assemblyParts,jdbcType=CHAR}, #{item.licenseNo,jdbcType=VARCHAR}, #{item.registrationDate,jdbcType=TIMESTAMP}, 
        #{item.annualInspectionDate,jdbcType=TIMESTAMP}, #{item.carPreduceOwner,jdbcType=VARCHAR}, 
        #{item.indicateOwner,jdbcType=VARCHAR}, #{item.indicateRentalDuration,jdbcType=BIGINT}, 
        #{item.rentExpense,jdbcType=BIGINT}, #{item.leasingExpense,jdbcType=BIGINT}, #{item.leasingDuration,jdbcType=BIGINT}, 
        #{item.heavyTrafficInsurance,jdbcType=TIMESTAMP}, #{item.businessInsuranceDate,jdbcType=TIMESTAMP}, 
        #{item.equipmentInsuranceDate,jdbcType=TIMESTAMP}, #{item.priorityInsuranceDate,jdbcType=TIMESTAMP}, 
        #{item.thirdLiabilityInsuranceDate,jdbcType=TIMESTAMP}, #{item.keyNum,jdbcType=BIGINT}, 
        #{item.backKeyOwner,jdbcType=VARCHAR}, #{item.drivingPermitInCar,jdbcType=VARCHAR}, 
        #{item.carUsedTarget,jdbcType=VARCHAR}, #{item.vehicleName,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, 
        #{item.place,jdbcType=VARCHAR}, #{item.businessName,jdbcType=VARCHAR}, #{item.ability,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.personMis,jdbcType=VARCHAR}, #{item.ownerDepartment,jdbcType=VARCHAR}, 
        #{item.label,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR}, #{item.carBelongType,jdbcType=BIGINT}, 
        #{item.carOwner,jdbcType=VARCHAR}, #{item.scrap,jdbcType=BIT}, #{item.scrapReason,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.eve.example.VehicleBaseInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vehicle_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>