<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarClassifyDeviceConfigViewMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="first_device_type_id" jdbcType="BIGINT" property="firstDeviceTypeId" />
    <result column="second_device_type_id" jdbcType="BIGINT" property="secondDeviceTypeId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="car_classify_id" jdbcType="BIGINT" property="carClassifyId" />
    <result column="car_classify_third_model_id" jdbcType="BIGINT" property="carClassifyThirdModelId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, first_device_type_id, second_device_type_id, config_id, car_classify_id, car_classify_third_model_id, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyDeviceConfigViewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_classify_device_config_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_classify_device_config_view
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_classify_device_config_view
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyDeviceConfigViewExample">
    delete from car_classify_device_config_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView" useGeneratedKeys="true">
    insert into car_classify_device_config_view (first_device_type_id, second_device_type_id, 
      config_id, car_classify_id, car_classify_third_model_id, 
      add_time, update_time)
    values (#{firstDeviceTypeId,jdbcType=BIGINT}, #{secondDeviceTypeId,jdbcType=BIGINT}, 
      #{configId,jdbcType=BIGINT}, #{carClassifyId,jdbcType=BIGINT}, #{carClassifyThirdModelId,jdbcType=BIGINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView" useGeneratedKeys="true">
    insert into car_classify_device_config_view
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firstDeviceTypeId != null">
        first_device_type_id,
      </if>
      <if test="secondDeviceTypeId != null">
        second_device_type_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="carClassifyId != null">
        car_classify_id,
      </if>
      <if test="carClassifyThirdModelId != null">
        car_classify_third_model_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firstDeviceTypeId != null">
        #{firstDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="secondDeviceTypeId != null">
        #{secondDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="carClassifyId != null">
        #{carClassifyId,jdbcType=BIGINT},
      </if>
      <if test="carClassifyThirdModelId != null">
        #{carClassifyThirdModelId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyDeviceConfigViewExample" resultType="java.lang.Long">
    select count(*) from car_classify_device_config_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_classify_device_config_view
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.firstDeviceTypeId != null">
        first_device_type_id = #{record.firstDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.secondDeviceTypeId != null">
        second_device_type_id = #{record.secondDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.configId != null">
        config_id = #{record.configId,jdbcType=BIGINT},
      </if>
      <if test="record.carClassifyId != null">
        car_classify_id = #{record.carClassifyId,jdbcType=BIGINT},
      </if>
      <if test="record.carClassifyThirdModelId != null">
        car_classify_third_model_id = #{record.carClassifyThirdModelId,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_classify_device_config_view
    set id = #{record.id,jdbcType=BIGINT},
      first_device_type_id = #{record.firstDeviceTypeId,jdbcType=BIGINT},
      second_device_type_id = #{record.secondDeviceTypeId,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      car_classify_id = #{record.carClassifyId,jdbcType=BIGINT},
      car_classify_third_model_id = #{record.carClassifyThirdModelId,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView">
    update car_classify_device_config_view
    <set>
      <if test="firstDeviceTypeId != null">
        first_device_type_id = #{firstDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="secondDeviceTypeId != null">
        second_device_type_id = #{secondDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="carClassifyId != null">
        car_classify_id = #{carClassifyId,jdbcType=BIGINT},
      </if>
      <if test="carClassifyThirdModelId != null">
        car_classify_third_model_id = #{carClassifyThirdModelId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView">
    update car_classify_device_config_view
    set first_device_type_id = #{firstDeviceTypeId,jdbcType=BIGINT},
      second_device_type_id = #{secondDeviceTypeId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      car_classify_id = #{carClassifyId,jdbcType=BIGINT},
      car_classify_third_model_id = #{carClassifyThirdModelId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into car_classify_device_config_view
    (first_device_type_id, second_device_type_id, config_id, car_classify_id, car_classify_third_model_id, 
      add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.firstDeviceTypeId,jdbcType=BIGINT}, #{item.secondDeviceTypeId,jdbcType=BIGINT}, 
        #{item.configId,jdbcType=BIGINT}, #{item.carClassifyId,jdbcType=BIGINT}, #{item.carClassifyThirdModelId,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>