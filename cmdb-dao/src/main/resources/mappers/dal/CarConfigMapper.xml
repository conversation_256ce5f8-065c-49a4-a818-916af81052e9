<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="config_version" jdbcType="BIGINT" property="configVersion" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="master_id" jdbcType="BIGINT" property="masterId" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.classify.entity.CarConfig">
    <result column="config" jdbcType="LONGVARCHAR" property="config" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, config_version, config_name, master_id, file_type
  </sql>
  <sql id="Blob_Column_List">
    config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.classify.example.CarConfigExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigExample">
    delete from car_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarConfig" useGeneratedKeys="true">
    insert into car_config (name, config_version, config_name, 
      master_id, file_type, config
      )
    values (#{name,jdbcType=VARCHAR}, #{configVersion,jdbcType=BIGINT}, #{configName,jdbcType=VARCHAR}, 
      #{masterId,jdbcType=BIGINT}, #{fileType,jdbcType=VARCHAR}, #{config,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarConfig" useGeneratedKeys="true">
    insert into car_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="configVersion != null">
        config_version,
      </if>
      <if test="configName != null">
        config_name,
      </if>
      <if test="masterId != null">
        master_id,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="config != null">
        config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="configVersion != null">
        #{configVersion,jdbcType=BIGINT},
      </if>
      <if test="configName != null">
        #{configName,jdbcType=VARCHAR},
      </if>
      <if test="masterId != null">
        #{masterId,jdbcType=BIGINT},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="config != null">
        #{config,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigExample" resultType="java.lang.Long">
    select count(*) from car_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.configVersion != null">
        config_version = #{record.configVersion,jdbcType=BIGINT},
      </if>
      <if test="record.configName != null">
        config_name = #{record.configName,jdbcType=VARCHAR},
      </if>
      <if test="record.masterId != null">
        master_id = #{record.masterId,jdbcType=BIGINT},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.config != null">
        config = #{record.config,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update car_config
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      config_version = #{record.configVersion,jdbcType=BIGINT},
      config_name = #{record.configName,jdbcType=VARCHAR},
      master_id = #{record.masterId,jdbcType=BIGINT},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      config = #{record.config,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_config
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      config_version = #{record.configVersion,jdbcType=BIGINT},
      config_name = #{record.configName,jdbcType=VARCHAR},
      master_id = #{record.masterId,jdbcType=BIGINT},
      file_type = #{record.fileType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarConfig">
    update car_config
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="configVersion != null">
        config_version = #{configVersion,jdbcType=BIGINT},
      </if>
      <if test="configName != null">
        config_name = #{configName,jdbcType=VARCHAR},
      </if>
      <if test="masterId != null">
        master_id = #{masterId,jdbcType=BIGINT},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="config != null">
        config = #{config,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.classify.entity.CarConfig">
    update car_config
    set name = #{name,jdbcType=VARCHAR},
      config_version = #{configVersion,jdbcType=BIGINT},
      config_name = #{configName,jdbcType=VARCHAR},
      master_id = #{masterId,jdbcType=BIGINT},
      file_type = #{fileType,jdbcType=VARCHAR},
      config = #{config,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarConfig">
    update car_config
    set name = #{name,jdbcType=VARCHAR},
      config_version = #{configVersion,jdbcType=BIGINT},
      config_name = #{configName,jdbcType=VARCHAR},
      master_id = #{masterId,jdbcType=BIGINT},
      file_type = #{fileType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into car_config
    (name, config_version, config_name, master_id, file_type, config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.configVersion,jdbcType=BIGINT}, #{item.configName,jdbcType=VARCHAR}, 
        #{item.masterId,jdbcType=BIGINT}, #{item.fileType,jdbcType=VARCHAR}, #{item.config,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
</mapper>