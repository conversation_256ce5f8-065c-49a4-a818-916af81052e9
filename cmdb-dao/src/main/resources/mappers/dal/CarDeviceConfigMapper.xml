<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="device_type_id" jdbcType="BIGINT" property="deviceTypeId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="config_id_last" jdbcType="BIGINT" property="configIdLast" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    <result column="config" jdbcType="LONGVARCHAR" property="config" />
    <result column="task_history_id" jdbcType="LONGVARCHAR" property="taskHistoryId" />
    <result column="config_id_history" jdbcType="LONGVARCHAR" property="configIdHistory" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, device_type_id, `name`, add_time, update_time, create_user, update_user, 
    task_id, config_id, config_id_last, `status`, file_type
  </sql>
  <sql id="Blob_Column_List">
    config, task_history_id, config_id_history
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.classify.example.CarDeviceConfigExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_device_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarDeviceConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_device_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_device_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_device_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarDeviceConfigExample">
    delete from car_device_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    insert into car_device_config (id, vin, device_type_id, 
      `name`, add_time, update_time, 
      create_user, update_user, task_id, 
      config_id, config_id_last, `status`, 
      file_type, config, task_history_id, 
      config_id_history)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{deviceTypeId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{taskId,jdbcType=BIGINT}, 
      #{configId,jdbcType=BIGINT}, #{configIdLast,jdbcType=BIGINT}, #{status,jdbcType=SMALLINT}, 
      #{fileType,jdbcType=VARCHAR}, #{config,jdbcType=LONGVARCHAR}, #{taskHistoryId,jdbcType=LONGVARCHAR}, 
      #{configIdHistory,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    insert into car_device_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="deviceTypeId != null">
        device_type_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="configIdLast != null">
        config_id_last,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="config != null">
        config,
      </if>
      <if test="taskHistoryId != null">
        task_history_id,
      </if>
      <if test="configIdHistory != null">
        config_id_history,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeId != null">
        #{deviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="configIdLast != null">
        #{configIdLast,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="config != null">
        #{config,jdbcType=LONGVARCHAR},
      </if>
      <if test="taskHistoryId != null">
        #{taskHistoryId,jdbcType=LONGVARCHAR},
      </if>
      <if test="configIdHistory != null">
        #{configIdHistory,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarDeviceConfigExample" resultType="java.lang.Long">
    select count(*) from car_device_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_device_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceTypeId != null">
        device_type_id = #{record.deviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.configId != null">
        config_id = #{record.configId,jdbcType=BIGINT},
      </if>
      <if test="record.configIdLast != null">
        config_id_last = #{record.configIdLast,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.config != null">
        config = #{record.config,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.taskHistoryId != null">
        task_history_id = #{record.taskHistoryId,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.configIdHistory != null">
        config_id_history = #{record.configIdHistory,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update car_device_config
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      device_type_id = #{record.deviceTypeId,jdbcType=BIGINT},
      `name` = #{record.name,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      config_id_last = #{record.configIdLast,jdbcType=BIGINT},
      `status` = #{record.status,jdbcType=SMALLINT},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      config = #{record.config,jdbcType=LONGVARCHAR},
      task_history_id = #{record.taskHistoryId,jdbcType=LONGVARCHAR},
      config_id_history = #{record.configIdHistory,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_device_config
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      device_type_id = #{record.deviceTypeId,jdbcType=BIGINT},
      `name` = #{record.name,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      config_id_last = #{record.configIdLast,jdbcType=BIGINT},
      `status` = #{record.status,jdbcType=SMALLINT},
      file_type = #{record.fileType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    update car_device_config
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeId != null">
        device_type_id = #{deviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="configIdLast != null">
        config_id_last = #{configIdLast,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=SMALLINT},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="config != null">
        config = #{config,jdbcType=LONGVARCHAR},
      </if>
      <if test="taskHistoryId != null">
        task_history_id = #{taskHistoryId,jdbcType=LONGVARCHAR},
      </if>
      <if test="configIdHistory != null">
        config_id_history = #{configIdHistory,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    update car_device_config
    set vin = #{vin,jdbcType=VARCHAR},
      device_type_id = #{deviceTypeId,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      config_id_last = #{configIdLast,jdbcType=BIGINT},
      `status` = #{status,jdbcType=SMALLINT},
      file_type = #{fileType,jdbcType=VARCHAR},
      config = #{config,jdbcType=LONGVARCHAR},
      task_history_id = #{taskHistoryId,jdbcType=LONGVARCHAR},
      config_id_history = #{configIdHistory,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarDeviceConfig">
    update car_device_config
    set vin = #{vin,jdbcType=VARCHAR},
      device_type_id = #{deviceTypeId,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      config_id_last = #{configIdLast,jdbcType=BIGINT},
      `status` = #{status,jdbcType=SMALLINT},
      file_type = #{fileType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_device_config
    (id, vin, device_type_id, `name`, add_time, update_time, create_user, update_user, 
      task_id, config_id, config_id_last, `status`, file_type, config, task_history_id, 
      config_id_history)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.deviceTypeId,jdbcType=BIGINT}, 
        #{item.name,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.updateUser,jdbcType=VARCHAR}, #{item.taskId,jdbcType=BIGINT}, 
        #{item.configId,jdbcType=BIGINT}, #{item.configIdLast,jdbcType=BIGINT}, #{item.status,jdbcType=SMALLINT}, 
        #{item.fileType,jdbcType=VARCHAR}, #{item.config,jdbcType=LONGVARCHAR}, #{item.taskHistoryId,jdbcType=LONGVARCHAR}, 
        #{item.configIdHistory,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.walle.dal.classify.example.CarDeviceConfigExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_device_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.classify.example.CarDeviceConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_device_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>