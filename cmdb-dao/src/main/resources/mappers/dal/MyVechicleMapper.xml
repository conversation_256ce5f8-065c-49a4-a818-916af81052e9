<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.MyVehicleMapper">
<!--  <resultMap id="BaseResultMap" type="com.sankuai.walle.rmanage.config.dal.mrm_manage.entity.MyTagsVin">-->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
<!--    <result column="vin" jdbcType="VARCHAR" property="vin" />-->
<!--    <result column="name" jdbcType="VARCHAR" property="name" />-->
<!--    <result column="color" jdbcType="VARCHAR" property="color" />-->
<!--    <result column="tag_type" jdbcType="BIGINT" property="tagType" />-->
<!--    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />-->
<!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
<!--  </resultMap>-->
  <resultMap id="BaseResultMap" type="string">

  </resultMap>
  <sql id="car_assets_Where_Clause">
    <where>
      <foreach collection="car_assets.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="car_operation_Where_Clause">
    <where>
      <foreach collection="car_operation.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="car_select_Where_Clause">
    <where>
      <foreach collection="car_tag.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.MyVehicleExampleList" resultType="java.lang.String">
    SELECT a.vin from car_objects a
    <if test="car_assets != null">
      INNER JOIN
      (SELECT vin from car_assets <include refid="car_assets_Where_Clause"/>
      ) b ON a.vin=b.vin
    </if>
    <if test="car_operation != null">
      INNER JOIN
      (SELECT vin from car_operation <include refid="car_operation_Where_Clause"/>
      ) c on a.vin=c.vin
    </if>
    <if test="car_tag != null">
      INNER JOIN
      (
      SELECT a.vin, a.name as name, b.name as typeName
      from car_objects a
      LEFT JOIN car_selects b ON a.car_type=b.type
      <include refid="car_select_Where_Clause"/>
      GROUP BY a.vin
      ) d ON a.vin=d.vin
    </if>
    ORDER BY a.id
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--    <if test="orderByClause != null">-->
<!--      order by ${orderByClause}-->
<!--    </if>-->
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>

  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.MyVehicleExampleList" resultType="java.lang.Long">
    SELECT count(a.vin) from car_objects a
    <if test="car_assets != null">
      INNER JOIN
      (SELECT vin from car_assets <include refid="car_assets_Where_Clause"/>
      ) b ON a.vin=b.vin
    </if>
    <if test="car_operation != null">
      INNER JOIN
      (SELECT vin from car_operation <include refid="car_operation_Where_Clause"/>
      ) c on a.vin=c.vin
    </if>
    <if test="car_tag != null">
      INNER JOIN
      (
      SELECT a.vin, a.name as name, b.name as typeName
      from car_objects a
      LEFT JOIN car_selects b ON a.car_type=b.type
      <include refid="car_select_Where_Clause"/>
      GROUP BY a.vin
      ) d ON a.vin=d.vin
    </if>
  </select>
</mapper>