<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarFirstcartypeSecondcartypeMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="first_car_id" jdbcType="BIGINT" property="firstCarId" />
    <result column="second_car_id" jdbcType="BIGINT" property="secondCarId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, first_car_id, second_car_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_firstCarType_secondCarType
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_firstCarType_secondCarType
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_firstCarType_secondCarType
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample">
    delete from car_firstCarType_secondCarType
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype" useGeneratedKeys="true">
    insert into car_firstCarType_secondCarType (first_car_id, second_car_id)
    values (#{firstCarId,jdbcType=BIGINT}, #{secondCarId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype" useGeneratedKeys="true">
    insert into car_firstCarType_secondCarType
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="firstCarId != null">
        first_car_id,
      </if>
      <if test="secondCarId != null">
        second_car_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="firstCarId != null">
        #{firstCarId,jdbcType=BIGINT},
      </if>
      <if test="secondCarId != null">
        #{secondCarId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample" resultType="java.lang.Long">
    select count(*) from car_firstCarType_secondCarType
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_firstCarType_secondCarType
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.firstCarId != null">
        first_car_id = #{record.firstCarId,jdbcType=BIGINT},
      </if>
      <if test="record.secondCarId != null">
        second_car_id = #{record.secondCarId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_firstCarType_secondCarType
    set id = #{record.id,jdbcType=BIGINT},
      first_car_id = #{record.firstCarId,jdbcType=BIGINT},
      second_car_id = #{record.secondCarId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype">
    update car_firstCarType_secondCarType
    <set>
      <if test="firstCarId != null">
        first_car_id = #{firstCarId,jdbcType=BIGINT},
      </if>
      <if test="secondCarId != null">
        second_car_id = #{secondCarId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype">
    update car_firstCarType_secondCarType
    set first_car_id = #{firstCarId,jdbcType=BIGINT},
      second_car_id = #{secondCarId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into car_firstCarType_secondCarType
    (first_car_id, second_car_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.firstCarId,jdbcType=BIGINT}, #{item.secondCarId,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>