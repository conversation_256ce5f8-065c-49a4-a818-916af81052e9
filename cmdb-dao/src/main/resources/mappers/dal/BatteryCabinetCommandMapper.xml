<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.battery.mapper.BatteryCabinetCommandMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="module_identifier" jdbcType="VARCHAR" property="moduleIdentifier" />
    <result column="command_identifier" jdbcType="VARCHAR" property="commandIdentifier" />
    <result column="command_content" jdbcType="VARCHAR" property="commandContent" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="status_msg" jdbcType="VARCHAR" property="statusMsg" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="mis" jdbcType="VARCHAR" property="mis" />
    <result column="rep" jdbcType="VARCHAR" property="rep" />
    <result column="device_key" jdbcType="VARCHAR" property="deviceKey" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_time, update_time, module_identifier, command_identifier, command_content, 
    `status`, status_msg, request_id, mis, rep, device_key
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.battery.example.BatteryCabinetCommandExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from battery_cabinet_command
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from battery_cabinet_command
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from battery_cabinet_command
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.battery.example.BatteryCabinetCommandExample">
    delete from battery_cabinet_command
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand">
    insert into battery_cabinet_command (id, create_time, update_time, 
      module_identifier, command_identifier, command_content, 
      `status`, status_msg, request_id, 
      mis, rep, device_key
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{moduleIdentifier,jdbcType=VARCHAR}, #{commandIdentifier,jdbcType=VARCHAR}, #{commandContent,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{statusMsg,jdbcType=VARCHAR}, #{requestId,jdbcType=VARCHAR}, 
      #{mis,jdbcType=VARCHAR}, #{rep,jdbcType=VARCHAR}, #{deviceKey,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand">
    insert into battery_cabinet_command
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="moduleIdentifier != null">
        module_identifier,
      </if>
      <if test="commandIdentifier != null">
        command_identifier,
      </if>
      <if test="commandContent != null">
        command_content,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="statusMsg != null">
        status_msg,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="mis != null">
        mis,
      </if>
      <if test="rep != null">
        rep,
      </if>
      <if test="deviceKey != null">
        device_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleIdentifier != null">
        #{moduleIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="commandIdentifier != null">
        #{commandIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="commandContent != null">
        #{commandContent,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="statusMsg != null">
        #{statusMsg,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="mis != null">
        #{mis,jdbcType=VARCHAR},
      </if>
      <if test="rep != null">
        #{rep,jdbcType=VARCHAR},
      </if>
      <if test="deviceKey != null">
        #{deviceKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.battery.example.BatteryCabinetCommandExample" resultType="java.lang.Long">
    select count(*) from battery_cabinet_command
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update battery_cabinet_command
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.moduleIdentifier != null">
        module_identifier = #{record.moduleIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="record.commandIdentifier != null">
        command_identifier = #{record.commandIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="record.commandContent != null">
        command_content = #{record.commandContent,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.statusMsg != null">
        status_msg = #{record.statusMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.requestId != null">
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.mis != null">
        mis = #{record.mis,jdbcType=VARCHAR},
      </if>
      <if test="record.rep != null">
        rep = #{record.rep,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceKey != null">
        device_key = #{record.deviceKey,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update battery_cabinet_command
    set id = #{record.id,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      module_identifier = #{record.moduleIdentifier,jdbcType=VARCHAR},
      command_identifier = #{record.commandIdentifier,jdbcType=VARCHAR},
      command_content = #{record.commandContent,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      status_msg = #{record.statusMsg,jdbcType=VARCHAR},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      mis = #{record.mis,jdbcType=VARCHAR},
      rep = #{record.rep,jdbcType=VARCHAR},
      device_key = #{record.deviceKey,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand">
    update battery_cabinet_command
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleIdentifier != null">
        module_identifier = #{moduleIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="commandIdentifier != null">
        command_identifier = #{commandIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="commandContent != null">
        command_content = #{commandContent,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="statusMsg != null">
        status_msg = #{statusMsg,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="mis != null">
        mis = #{mis,jdbcType=VARCHAR},
      </if>
      <if test="rep != null">
        rep = #{rep,jdbcType=VARCHAR},
      </if>
      <if test="deviceKey != null">
        device_key = #{deviceKey,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand">
    update battery_cabinet_command
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      module_identifier = #{moduleIdentifier,jdbcType=VARCHAR},
      command_identifier = #{commandIdentifier,jdbcType=VARCHAR},
      command_content = #{commandContent,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      status_msg = #{statusMsg,jdbcType=VARCHAR},
      request_id = #{requestId,jdbcType=VARCHAR},
      mis = #{mis,jdbcType=VARCHAR},
      rep = #{rep,jdbcType=VARCHAR},
      device_key = #{deviceKey,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into battery_cabinet_command
    (id, create_time, update_time, module_identifier, command_identifier, command_content, 
      `status`, status_msg, request_id, mis, rep, device_key)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.moduleIdentifier,jdbcType=VARCHAR}, #{item.commandIdentifier,jdbcType=VARCHAR}, 
        #{item.commandContent,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.statusMsg,jdbcType=VARCHAR}, 
        #{item.requestId,jdbcType=VARCHAR}, #{item.mis,jdbcType=VARCHAR}, #{item.rep,jdbcType=VARCHAR}, 
        #{item.deviceKey,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.battery.example.BatteryCabinetCommandExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from battery_cabinet_command
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>