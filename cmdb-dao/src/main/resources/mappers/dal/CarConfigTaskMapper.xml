<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarConfigTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarConfigTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.classify.entity.CarConfigTask">
    <result column="vins" jdbcType="LONGVARCHAR" property="vins" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_name, device_id, config_id, create_user, add_time
  </sql>
  <sql id="Blob_Column_List">
    vins
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.classify.example.CarConfigTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from car_config_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_config_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigTaskExample">
    delete from car_config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigTask" useGeneratedKeys="true">
    insert into car_config_task (task_name, device_id, config_id, 
      create_user, add_time, vins
      )
    values (#{taskName,jdbcType=VARCHAR}, #{deviceId,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, 
      #{createUser,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{vins,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigTask" useGeneratedKeys="true">
    insert into car_config_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskName != null">
        task_name,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="vins != null">
        vins,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vins != null">
        #{vins,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarConfigTaskExample" resultType="java.lang.Long">
    select count(*) from car_config_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_config_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceId != null">
        device_id = #{record.deviceId,jdbcType=BIGINT},
      </if>
      <if test="record.configId != null">
        config_id = #{record.configId,jdbcType=BIGINT},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.vins != null">
        vins = #{record.vins,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update car_config_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      device_id = #{record.deviceId,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      vins = #{record.vins,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_config_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      device_id = #{record.deviceId,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigTask">
    update car_config_task
    <set>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vins != null">
        vins = #{vins,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigTask">
    update car_config_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      device_id = #{deviceId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      vins = #{vins,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarConfigTask">
    update car_config_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      device_id = #{deviceId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into car_config_task
    (task_name, device_id, config_id, create_user, add_time, vins)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskName,jdbcType=VARCHAR}, #{item.deviceId,jdbcType=BIGINT}, #{item.configId,jdbcType=BIGINT}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.vins,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
</mapper>