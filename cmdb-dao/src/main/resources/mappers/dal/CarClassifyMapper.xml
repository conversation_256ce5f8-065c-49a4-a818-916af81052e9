<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarClassifyMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarClassify">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc" />
    <result column="first_car_model" jdbcType="BIGINT" property="firstCarModel" />
    <result column="second_car_model" jdbcType="BIGINT" property="secondCarModel" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, classify_desc, first_car_model, second_car_model, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_classify
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_classify
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_classify
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyExample">
    delete from car_classify
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarClassify" useGeneratedKeys="true">
    insert into car_classify (name, classify_desc, first_car_model, 
      second_car_model, add_time, update_time
      )
    values (#{name,jdbcType=VARCHAR}, #{classifyDesc,jdbcType=VARCHAR}, #{firstCarModel,jdbcType=BIGINT}, 
      #{secondCarModel,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarClassify" useGeneratedKeys="true">
    insert into car_classify
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="classifyDesc != null">
        classify_desc,
      </if>
      <if test="firstCarModel != null">
        first_car_model,
      </if>
      <if test="secondCarModel != null">
        second_car_model,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="classifyDesc != null">
        #{classifyDesc,jdbcType=VARCHAR},
      </if>
      <if test="firstCarModel != null">
        #{firstCarModel,jdbcType=BIGINT},
      </if>
      <if test="secondCarModel != null">
        #{secondCarModel,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyExample" resultType="java.lang.Long">
    select count(*) from car_classify
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_classify
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.classifyDesc != null">
        classify_desc = #{record.classifyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.firstCarModel != null">
        first_car_model = #{record.firstCarModel,jdbcType=BIGINT},
      </if>
      <if test="record.secondCarModel != null">
        second_car_model = #{record.secondCarModel,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_classify
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      classify_desc = #{record.classifyDesc,jdbcType=VARCHAR},
      first_car_model = #{record.firstCarModel,jdbcType=BIGINT},
      second_car_model = #{record.secondCarModel,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarClassify">
    update car_classify
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="classifyDesc != null">
        classify_desc = #{classifyDesc,jdbcType=VARCHAR},
      </if>
      <if test="firstCarModel != null">
        first_car_model = #{firstCarModel,jdbcType=BIGINT},
      </if>
      <if test="secondCarModel != null">
        second_car_model = #{secondCarModel,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarClassify">
    update car_classify
    set name = #{name,jdbcType=VARCHAR},
      classify_desc = #{classifyDesc,jdbcType=VARCHAR},
      first_car_model = #{firstCarModel,jdbcType=BIGINT},
      second_car_model = #{secondCarModel,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into car_classify
    (name, classify_desc, first_car_model, second_car_model, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.classifyDesc,jdbcType=VARCHAR}, #{item.firstCarModel,jdbcType=BIGINT}, 
        #{item.secondCarModel,jdbcType=BIGINT}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>