<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.BizHandlePolicyInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_table_name" jdbcType="VARCHAR" property="bizTableName" />
    <result column="biz_table_desc" jdbcType="VARCHAR" property="bizTableDesc" />
    <result column="policy_name" jdbcType="VARCHAR" property="policyName" />
    <result column="policy_desc" jdbcType="VARCHAR" property="policyDesc" />
    <result column="policy_value" jdbcType="CHAR" property="policyValue" />
    <result column="handle_method" jdbcType="CHAR" property="handleMethod" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="is_enable" jdbcType="BIT" property="isEnable" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_table_name, biz_table_desc, policy_name, policy_desc, policy_value, handle_method, 
    add_time, update_time, create_user, update_user, is_enable, is_deleted
  </sql>
  <select id="selectAll" resultMap="BaseResultMap">
    select *
    from biz_handle_policy_info
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.BizHandlePolicyInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_handle_policy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_handle_policy_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from biz_handle_policy_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.BizHandlePolicyInfoExample">
    delete from biz_handle_policy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo">
    insert into biz_handle_policy_info (id, biz_table_name, biz_table_desc, 
      policy_name, policy_desc, policy_value, 
      handle_method, add_time, update_time, 
      create_user, update_user, is_enable, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{bizTableName,jdbcType=VARCHAR}, #{bizTableDesc,jdbcType=VARCHAR}, 
      #{policyName,jdbcType=VARCHAR}, #{policyDesc,jdbcType=VARCHAR}, #{policyValue,jdbcType=CHAR}, 
      #{handleMethod,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{isEnable,jdbcType=BIT}, 
      #{isDeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo">
    insert into biz_handle_policy_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizTableName != null">
        biz_table_name,
      </if>
      <if test="bizTableDesc != null">
        biz_table_desc,
      </if>
      <if test="policyName != null">
        policy_name,
      </if>
      <if test="policyDesc != null">
        policy_desc,
      </if>
      <if test="policyValue != null">
        policy_value,
      </if>
      <if test="handleMethod != null">
        handle_method,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="isEnable != null">
        is_enable,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizTableName != null">
        #{bizTableName,jdbcType=VARCHAR},
      </if>
      <if test="bizTableDesc != null">
        #{bizTableDesc,jdbcType=VARCHAR},
      </if>
      <if test="policyName != null">
        #{policyName,jdbcType=VARCHAR},
      </if>
      <if test="policyDesc != null">
        #{policyDesc,jdbcType=VARCHAR},
      </if>
      <if test="policyValue != null">
        #{policyValue,jdbcType=CHAR},
      </if>
      <if test="handleMethod != null">
        #{handleMethod,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.BizHandlePolicyInfoExample" resultType="java.lang.Long">
    select count(*) from biz_handle_policy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update biz_handle_policy_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizTableName != null">
        biz_table_name = #{record.bizTableName,jdbcType=VARCHAR},
      </if>
      <if test="record.bizTableDesc != null">
        biz_table_desc = #{record.bizTableDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.policyName != null">
        policy_name = #{record.policyName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyDesc != null">
        policy_desc = #{record.policyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.policyValue != null">
        policy_value = #{record.policyValue,jdbcType=CHAR},
      </if>
      <if test="record.handleMethod != null">
        handle_method = #{record.handleMethod,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.isEnable != null">
        is_enable = #{record.isEnable,jdbcType=BIT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update biz_handle_policy_info
    set id = #{record.id,jdbcType=BIGINT},
      biz_table_name = #{record.bizTableName,jdbcType=VARCHAR},
      biz_table_desc = #{record.bizTableDesc,jdbcType=VARCHAR},
      policy_name = #{record.policyName,jdbcType=VARCHAR},
      policy_desc = #{record.policyDesc,jdbcType=VARCHAR},
      policy_value = #{record.policyValue,jdbcType=CHAR},
      handle_method = #{record.handleMethod,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      is_enable = #{record.isEnable,jdbcType=BIT},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo">
    update biz_handle_policy_info
    <set>
      <if test="bizTableName != null">
        biz_table_name = #{bizTableName,jdbcType=VARCHAR},
      </if>
      <if test="bizTableDesc != null">
        biz_table_desc = #{bizTableDesc,jdbcType=VARCHAR},
      </if>
      <if test="policyName != null">
        policy_name = #{policyName,jdbcType=VARCHAR},
      </if>
      <if test="policyDesc != null">
        policy_desc = #{policyDesc,jdbcType=VARCHAR},
      </if>
      <if test="policyValue != null">
        policy_value = #{policyValue,jdbcType=CHAR},
      </if>
      <if test="handleMethod != null">
        handle_method = #{handleMethod,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo">
    update biz_handle_policy_info
    set biz_table_name = #{bizTableName,jdbcType=VARCHAR},
      biz_table_desc = #{bizTableDesc,jdbcType=VARCHAR},
      policy_name = #{policyName,jdbcType=VARCHAR},
      policy_desc = #{policyDesc,jdbcType=VARCHAR},
      policy_value = #{policyValue,jdbcType=CHAR},
      handle_method = #{handleMethod,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=BIT},
      is_deleted = #{isDeleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into biz_handle_policy_info
    (id, biz_table_name, biz_table_desc, policy_name, policy_desc, policy_value, handle_method, 
      add_time, update_time, create_user, update_user, is_enable, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.bizTableName,jdbcType=VARCHAR}, #{item.bizTableDesc,jdbcType=VARCHAR}, 
        #{item.policyName,jdbcType=VARCHAR}, #{item.policyDesc,jdbcType=VARCHAR}, #{item.policyValue,jdbcType=CHAR}, 
        #{item.handleMethod,jdbcType=CHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.createUser,jdbcType=VARCHAR}, #{item.updateUser,jdbcType=VARCHAR}, #{item.isEnable,jdbcType=BIT}, 
        #{item.isDeleted,jdbcType=BIT})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.mrm_manage.example.BizHandlePolicyInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_handle_policy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>