<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.DeviceSoftwareMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="related_remote_device_type_id" jdbcType="BIGINT" property="relatedRemoteDeviceTypeId" />
    <result column="related_remote_software_type_id" jdbcType="BIGINT" property="relatedRemoteSoftwareTypeId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, related_remote_device_type_id, related_remote_software_type_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.DeviceSoftwareExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from device_software
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from device_software
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from device_software
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.DeviceSoftwareExample">
    delete from device_software
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware" useGeneratedKeys="true">
    insert into device_software (related_remote_device_type_id, related_remote_software_type_id
      )
    values (#{relatedRemoteDeviceTypeId,jdbcType=BIGINT}, #{relatedRemoteSoftwareTypeId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware" useGeneratedKeys="true">
    insert into device_software
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relatedRemoteDeviceTypeId != null">
        related_remote_device_type_id,
      </if>
      <if test="relatedRemoteSoftwareTypeId != null">
        related_remote_software_type_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relatedRemoteDeviceTypeId != null">
        #{relatedRemoteDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="relatedRemoteSoftwareTypeId != null">
        #{relatedRemoteSoftwareTypeId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.DeviceSoftwareExample" resultType="java.lang.Long">
    select count(*) from device_software
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update device_software
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.relatedRemoteDeviceTypeId != null">
        related_remote_device_type_id = #{record.relatedRemoteDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.relatedRemoteSoftwareTypeId != null">
        related_remote_software_type_id = #{record.relatedRemoteSoftwareTypeId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update device_software
    set id = #{record.id,jdbcType=BIGINT},
      related_remote_device_type_id = #{record.relatedRemoteDeviceTypeId,jdbcType=BIGINT},
      related_remote_software_type_id = #{record.relatedRemoteSoftwareTypeId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware">
    update device_software
    <set>
      <if test="relatedRemoteDeviceTypeId != null">
        related_remote_device_type_id = #{relatedRemoteDeviceTypeId,jdbcType=BIGINT},
      </if>
      <if test="relatedRemoteSoftwareTypeId != null">
        related_remote_software_type_id = #{relatedRemoteSoftwareTypeId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware">
    update device_software
    set related_remote_device_type_id = #{relatedRemoteDeviceTypeId,jdbcType=BIGINT},
      related_remote_software_type_id = #{relatedRemoteSoftwareTypeId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into device_software
    (related_remote_device_type_id, related_remote_software_type_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.relatedRemoteDeviceTypeId,jdbcType=BIGINT}, #{item.relatedRemoteSoftwareTypeId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
</mapper>