<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.AutoConfigContentMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="property_content" jdbcType="VARCHAR" property="propertyContent" />
    <result column="property_value" jdbcType="VARCHAR" property="propertyValue" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, property_content, property_value, file_type, `user`, add_time, update_time, 
    is_delete, device_name
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.AutoConfigContentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_config_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auto_config_content
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auto_config_content
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.AutoConfigContentExample">
    delete from auto_config_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent">
    insert into auto_config_content (id, vin, property_content, 
      property_value, file_type, `user`, 
      add_time, update_time, is_delete, 
      device_name)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{propertyContent,jdbcType=VARCHAR}, 
      #{propertyValue,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, #{user,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BIT}, 
      #{deviceName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent">
    insert into auto_config_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="propertyContent != null">
        property_content,
      </if>
      <if test="propertyValue != null">
        property_value,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="user != null">
        `user`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="deviceName != null">
        device_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="propertyContent != null">
        #{propertyContent,jdbcType=VARCHAR},
      </if>
      <if test="propertyValue != null">
        #{propertyValue,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="deviceName != null">
        #{deviceName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.AutoConfigContentExample" resultType="java.lang.Long">
    select count(*) from auto_config_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auto_config_content
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.propertyContent != null">
        property_content = #{record.propertyContent,jdbcType=VARCHAR},
      </if>
      <if test="record.propertyValue != null">
        property_value = #{record.propertyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.user != null">
        `user` = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.deviceName != null">
        device_name = #{record.deviceName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auto_config_content
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      property_content = #{record.propertyContent,jdbcType=VARCHAR},
      property_value = #{record.propertyValue,jdbcType=VARCHAR},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      `user` = #{record.user,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=BIT},
      device_name = #{record.deviceName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent">
    update auto_config_content
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="propertyContent != null">
        property_content = #{propertyContent,jdbcType=VARCHAR},
      </if>
      <if test="propertyValue != null">
        property_value = #{propertyValue,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        `user` = #{user,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="deviceName != null">
        device_name = #{deviceName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent">
    update auto_config_content
    set vin = #{vin,jdbcType=VARCHAR},
      property_content = #{propertyContent,jdbcType=VARCHAR},
      property_value = #{propertyValue,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=VARCHAR},
      `user` = #{user,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      device_name = #{deviceName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into auto_config_content
    (id, vin, property_content, property_value, file_type, `user`, add_time, update_time, 
      is_delete, device_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.propertyContent,jdbcType=VARCHAR}, 
        #{item.propertyValue,jdbcType=VARCHAR}, #{item.fileType,jdbcType=VARCHAR}, #{item.user,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDelete,jdbcType=BIT}, 
        #{item.deviceName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.mrm_manage.example.AutoConfigContentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_config_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>