<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.RemoteCarTypeMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="friend_name" jdbcType="VARCHAR" property="friendName" />
    <result column="conf_content" jdbcType="CHAR" property="confContent" />
    <result column="father_type" jdbcType="VARCHAR" property="fatherType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type_name, friend_name, conf_content, father_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from remote_car_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from remote_car_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from remote_car_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample">
    delete from remote_car_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType">
    insert into remote_car_type (id, type_name, friend_name, 
      conf_content, father_type)
    values (#{id,jdbcType=BIGINT}, #{typeName,jdbcType=VARCHAR}, #{friendName,jdbcType=VARCHAR}, 
      #{confContent,jdbcType=CHAR}, #{fatherType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType">
    insert into remote_car_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="friendName != null">
        friend_name,
      </if>
      <if test="confContent != null">
        conf_content,
      </if>
      <if test="fatherType != null">
        father_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="friendName != null">
        #{friendName,jdbcType=VARCHAR},
      </if>
      <if test="confContent != null">
        #{confContent,jdbcType=CHAR},
      </if>
      <if test="fatherType != null">
        #{fatherType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample" resultType="java.lang.Long">
    select count(*) from remote_car_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update remote_car_type
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.typeName != null">
        type_name = #{record.typeName,jdbcType=VARCHAR},
      </if>
      <if test="record.friendName != null">
        friend_name = #{record.friendName,jdbcType=VARCHAR},
      </if>
      <if test="record.confContent != null">
        conf_content = #{record.confContent,jdbcType=CHAR},
      </if>
      <if test="record.fatherType != null">
        father_type = #{record.fatherType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update remote_car_type
    set id = #{record.id,jdbcType=BIGINT},
      type_name = #{record.typeName,jdbcType=VARCHAR},
      friend_name = #{record.friendName,jdbcType=VARCHAR},
      conf_content = #{record.confContent,jdbcType=CHAR},
      father_type = #{record.fatherType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType">
    update remote_car_type
    <set>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="friendName != null">
        friend_name = #{friendName,jdbcType=VARCHAR},
      </if>
      <if test="confContent != null">
        conf_content = #{confContent,jdbcType=CHAR},
      </if>
      <if test="fatherType != null">
        father_type = #{fatherType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType">
    update remote_car_type
    set type_name = #{typeName,jdbcType=VARCHAR},
      friend_name = #{friendName,jdbcType=VARCHAR},
      conf_content = #{confContent,jdbcType=CHAR},
      father_type = #{fatherType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into remote_car_type
    (id, type_name, friend_name, conf_content, father_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.typeName,jdbcType=VARCHAR}, #{item.friendName,jdbcType=VARCHAR}, 
        #{item.confContent,jdbcType=CHAR}, #{item.fatherType,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from remote_car_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>