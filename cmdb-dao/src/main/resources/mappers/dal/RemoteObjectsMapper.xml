<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectsMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="login_user" jdbcType="VARCHAR" property="loginUser" />
    <result column="login_password" jdbcType="VARCHAR" property="loginPassword" />
    <result column="remote_object_type_id" jdbcType="BIGINT" property="remoteObjectTypeId" />
    <result column="remote_car_type_id" jdbcType="BIGINT" property="remoteCarTypeId" />
    <result column="remote_control_enable" jdbcType="BIT" property="remoteControlEnable" />
    <result column="remote_ssh_enable" jdbcType="BIT" property="remoteSshEnable" />
    <result column="remote_debug_enable" jdbcType="BIT" property="remoteDebugEnable" />
    <result column="stream_base_port" jdbcType="INTEGER" property="streamBasePort" />
    <result column="remote_object_configure" jdbcType="CHAR" property="remoteObjectConfigure" />
    <result column="remote_release" jdbcType="CHAR" property="remoteRelease" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects">
    <result column="desc" jdbcType="LONGVARCHAR" property="desc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, vin, license_no, login_user, login_password, remote_object_type_id, remote_car_type_id, 
    remote_control_enable, remote_ssh_enable, remote_debug_enable, stream_base_port, 
    remote_object_configure, remote_release, status
  </sql>
  <sql id="Blob_Column_List">
    desc
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from remote_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from remote_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from remote_objects
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from remote_objects
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample">
    delete from remote_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects" useGeneratedKeys="true">
    insert into remote_objects (name, vin, license_no, 
      login_user, login_password, remote_object_type_id, 
      remote_car_type_id, remote_control_enable, remote_ssh_enable, 
      remote_debug_enable, stream_base_port, remote_object_configure, 
      remote_release, status, desc
      )
    values (#{name,jdbcType=VARCHAR}, #{vin,jdbcType=VARCHAR}, #{licenseNo,jdbcType=VARCHAR}, 
      #{loginUser,jdbcType=VARCHAR}, #{loginPassword,jdbcType=VARCHAR}, #{remoteObjectTypeId,jdbcType=BIGINT}, 
      #{remoteCarTypeId,jdbcType=BIGINT}, #{remoteControlEnable,jdbcType=BIT}, #{remoteSshEnable,jdbcType=BIT}, 
      #{remoteDebugEnable,jdbcType=BIT}, #{streamBasePort,jdbcType=INTEGER}, #{remoteObjectConfigure,jdbcType=CHAR}, 
      #{remoteRelease,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{desc,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects" useGeneratedKeys="true">
    insert into remote_objects
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="loginUser != null">
        login_user,
      </if>
      <if test="loginPassword != null">
        login_password,
      </if>
      <if test="remoteObjectTypeId != null">
        remote_object_type_id,
      </if>
      <if test="remoteCarTypeId != null">
        remote_car_type_id,
      </if>
      <if test="remoteControlEnable != null">
        remote_control_enable,
      </if>
      <if test="remoteSshEnable != null">
        remote_ssh_enable,
      </if>
      <if test="remoteDebugEnable != null">
        remote_debug_enable,
      </if>
      <if test="streamBasePort != null">
        stream_base_port,
      </if>
      <if test="remoteObjectConfigure != null">
        remote_object_configure,
      </if>
      <if test="remoteRelease != null">
        remote_release,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="desc != null">
        desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="loginUser != null">
        #{loginUser,jdbcType=VARCHAR},
      </if>
      <if test="loginPassword != null">
        #{loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="remoteObjectTypeId != null">
        #{remoteObjectTypeId,jdbcType=BIGINT},
      </if>
      <if test="remoteCarTypeId != null">
        #{remoteCarTypeId,jdbcType=BIGINT},
      </if>
      <if test="remoteControlEnable != null">
        #{remoteControlEnable,jdbcType=BIT},
      </if>
      <if test="remoteSshEnable != null">
        #{remoteSshEnable,jdbcType=BIT},
      </if>
      <if test="remoteDebugEnable != null">
        #{remoteDebugEnable,jdbcType=BIT},
      </if>
      <if test="streamBasePort != null">
        #{streamBasePort,jdbcType=INTEGER},
      </if>
      <if test="remoteObjectConfigure != null">
        #{remoteObjectConfigure,jdbcType=CHAR},
      </if>
      <if test="remoteRelease != null">
        #{remoteRelease,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample" resultType="java.lang.Long">
    select count(*) from remote_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update remote_objects
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseNo != null">
        license_no = #{record.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.loginUser != null">
        login_user = #{record.loginUser,jdbcType=VARCHAR},
      </if>
      <if test="record.loginPassword != null">
        login_password = #{record.loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="record.remoteObjectTypeId != null">
        remote_object_type_id = #{record.remoteObjectTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.remoteCarTypeId != null">
        remote_car_type_id = #{record.remoteCarTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.remoteControlEnable != null">
        remote_control_enable = #{record.remoteControlEnable,jdbcType=BIT},
      </if>
      <if test="record.remoteSshEnable != null">
        remote_ssh_enable = #{record.remoteSshEnable,jdbcType=BIT},
      </if>
      <if test="record.remoteDebugEnable != null">
        remote_debug_enable = #{record.remoteDebugEnable,jdbcType=BIT},
      </if>
      <if test="record.streamBasePort != null">
        stream_base_port = #{record.streamBasePort,jdbcType=INTEGER},
      </if>
      <if test="record.remoteObjectConfigure != null">
        remote_object_configure = #{record.remoteObjectConfigure,jdbcType=CHAR},
      </if>
      <if test="record.remoteRelease != null">
        remote_release = #{record.remoteRelease,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.desc != null">
        desc = #{record.desc,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update remote_objects
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      vin = #{record.vin,jdbcType=VARCHAR},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      login_user = #{record.loginUser,jdbcType=VARCHAR},
      login_password = #{record.loginPassword,jdbcType=VARCHAR},
      remote_object_type_id = #{record.remoteObjectTypeId,jdbcType=BIGINT},
      remote_car_type_id = #{record.remoteCarTypeId,jdbcType=BIGINT},
      remote_control_enable = #{record.remoteControlEnable,jdbcType=BIT},
      remote_ssh_enable = #{record.remoteSshEnable,jdbcType=BIT},
      remote_debug_enable = #{record.remoteDebugEnable,jdbcType=BIT},
      stream_base_port = #{record.streamBasePort,jdbcType=INTEGER},
      remote_object_configure = #{record.remoteObjectConfigure,jdbcType=CHAR},
      remote_release = #{record.remoteRelease,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER},
      desc = #{record.desc,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update remote_objects
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      vin = #{record.vin,jdbcType=VARCHAR},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      login_user = #{record.loginUser,jdbcType=VARCHAR},
      login_password = #{record.loginPassword,jdbcType=VARCHAR},
      remote_object_type_id = #{record.remoteObjectTypeId,jdbcType=BIGINT},
      remote_car_type_id = #{record.remoteCarTypeId,jdbcType=BIGINT},
      remote_control_enable = #{record.remoteControlEnable,jdbcType=BIT},
      remote_ssh_enable = #{record.remoteSshEnable,jdbcType=BIT},
      remote_debug_enable = #{record.remoteDebugEnable,jdbcType=BIT},
      stream_base_port = #{record.streamBasePort,jdbcType=INTEGER},
      remote_object_configure = #{record.remoteObjectConfigure,jdbcType=CHAR},
      remote_release = #{record.remoteRelease,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects">
    update remote_objects
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="loginUser != null">
        login_user = #{loginUser,jdbcType=VARCHAR},
      </if>
      <if test="loginPassword != null">
        login_password = #{loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="remoteObjectTypeId != null">
        remote_object_type_id = #{remoteObjectTypeId,jdbcType=BIGINT},
      </if>
      <if test="remoteCarTypeId != null">
        remote_car_type_id = #{remoteCarTypeId,jdbcType=BIGINT},
      </if>
      <if test="remoteControlEnable != null">
        remote_control_enable = #{remoteControlEnable,jdbcType=BIT},
      </if>
      <if test="remoteSshEnable != null">
        remote_ssh_enable = #{remoteSshEnable,jdbcType=BIT},
      </if>
      <if test="remoteDebugEnable != null">
        remote_debug_enable = #{remoteDebugEnable,jdbcType=BIT},
      </if>
      <if test="streamBasePort != null">
        stream_base_port = #{streamBasePort,jdbcType=INTEGER},
      </if>
      <if test="remoteObjectConfigure != null">
        remote_object_configure = #{remoteObjectConfigure,jdbcType=CHAR},
      </if>
      <if test="remoteRelease != null">
        remote_release = #{remoteRelease,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="desc != null">
        desc = #{desc,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects">
    update remote_objects
    set name = #{name,jdbcType=VARCHAR},
      vin = #{vin,jdbcType=VARCHAR},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      login_user = #{loginUser,jdbcType=VARCHAR},
      login_password = #{loginPassword,jdbcType=VARCHAR},
      remote_object_type_id = #{remoteObjectTypeId,jdbcType=BIGINT},
      remote_car_type_id = #{remoteCarTypeId,jdbcType=BIGINT},
      remote_control_enable = #{remoteControlEnable,jdbcType=BIT},
      remote_ssh_enable = #{remoteSshEnable,jdbcType=BIT},
      remote_debug_enable = #{remoteDebugEnable,jdbcType=BIT},
      stream_base_port = #{streamBasePort,jdbcType=INTEGER},
      remote_object_configure = #{remoteObjectConfigure,jdbcType=CHAR},
      remote_release = #{remoteRelease,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER},
      desc = #{desc,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects">
    update remote_objects
    set name = #{name,jdbcType=VARCHAR},
      vin = #{vin,jdbcType=VARCHAR},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      login_user = #{loginUser,jdbcType=VARCHAR},
      login_password = #{loginPassword,jdbcType=VARCHAR},
      remote_object_type_id = #{remoteObjectTypeId,jdbcType=BIGINT},
      remote_car_type_id = #{remoteCarTypeId,jdbcType=BIGINT},
      remote_control_enable = #{remoteControlEnable,jdbcType=BIT},
      remote_ssh_enable = #{remoteSshEnable,jdbcType=BIT},
      remote_debug_enable = #{remoteDebugEnable,jdbcType=BIT},
      stream_base_port = #{streamBasePort,jdbcType=INTEGER},
      remote_object_configure = #{remoteObjectConfigure,jdbcType=CHAR},
      remote_release = #{remoteRelease,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into remote_objects
    (name, vin, license_no, login_user, login_password, remote_object_type_id, remote_car_type_id, 
      remote_control_enable, remote_ssh_enable, remote_debug_enable, stream_base_port, 
      remote_object_configure, remote_release, `status`, `desc`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.vin,jdbcType=VARCHAR}, #{item.licenseNo,jdbcType=VARCHAR}, 
        #{item.loginUser,jdbcType=VARCHAR}, #{item.loginPassword,jdbcType=VARCHAR}, #{item.remoteObjectTypeId,jdbcType=BIGINT}, 
        #{item.remoteCarTypeId,jdbcType=BIGINT}, #{item.remoteControlEnable,jdbcType=BIT}, 
        #{item.remoteSshEnable,jdbcType=BIT}, #{item.remoteDebugEnable,jdbcType=BIT}, #{item.streamBasePort,jdbcType=INTEGER}, 
        #{item.remoteObjectConfigure,jdbcType=CHAR}, #{item.remoteRelease,jdbcType=CHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.desc,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>