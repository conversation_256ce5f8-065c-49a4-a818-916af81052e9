<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.walle_data_center.mapper.TmpBdsVehicleInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="CHAR" property="vehicleId" />
    <result column="vehicle_device_id" jdbcType="CHAR" property="vehicleDeviceId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="engine_number" jdbcType="VARCHAR" property="engineNumber" />
    <result column="license_number" jdbcType="VARCHAR" property="licenseNumber" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="power_type" jdbcType="TINYINT" property="powerType" />
    <result column="operation_state" jdbcType="TINYINT" property="operationState" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="first_add_time" jdbcType="TIMESTAMP" property="firstAddTime" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="vid" jdbcType="CHAR" property="vid" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="product" jdbcType="INTEGER" property="product" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfoWithBLOBs">
    <result column="image" jdbcType="LONGVARCHAR" property="image" />
    <result column="extend" jdbcType="LONGVARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vehicle_id, vehicle_device_id, name, brand, type, engine_number, license_number, 
    year, power_type, operation_state, status, first_add_time, last_update_time, vid, 
    owner, product
  </sql>
  <sql id="Blob_Column_List">
    image, extend
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.walle_data_center.example.TmpBdsVehicleInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tmp_bds_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.TmpBdsVehicleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tmp_bds_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tmp_bds_vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tmp_bds_vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.TmpBdsVehicleInfoExample">
    delete from tmp_bds_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfoWithBLOBs" useGeneratedKeys="true">
    insert into tmp_bds_vehicle_info (vehicle_id, vehicle_device_id, name, 
      brand, type, engine_number, 
      license_number, year, power_type, 
      operation_state, status, first_add_time, 
      last_update_time, vid, owner, 
      product, image, extend
      )
    values (#{vehicleId,jdbcType=CHAR}, #{vehicleDeviceId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{engineNumber,jdbcType=VARCHAR}, 
      #{licenseNumber,jdbcType=VARCHAR}, #{year,jdbcType=INTEGER}, #{powerType,jdbcType=TINYINT}, 
      #{operationState,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{firstAddTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{vid,jdbcType=CHAR}, #{owner,jdbcType=VARCHAR}, 
      #{product,jdbcType=INTEGER}, #{image,jdbcType=LONGVARCHAR}, #{extend,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfoWithBLOBs" useGeneratedKeys="true">
    insert into tmp_bds_vehicle_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleDeviceId != null">
        vehicle_device_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="engineNumber != null">
        engine_number,
      </if>
      <if test="licenseNumber != null">
        license_number,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="powerType != null">
        power_type,
      </if>
      <if test="operationState != null">
        operation_state,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="firstAddTime != null">
        first_add_time,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="vid != null">
        vid,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="product != null">
        product,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=CHAR},
      </if>
      <if test="vehicleDeviceId != null">
        #{vehicleDeviceId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="engineNumber != null">
        #{engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="licenseNumber != null">
        #{licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="powerType != null">
        #{powerType,jdbcType=TINYINT},
      </if>
      <if test="operationState != null">
        #{operationState,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="firstAddTime != null">
        #{firstAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vid != null">
        #{vid,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="product != null">
        #{product,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        #{image,jdbcType=LONGVARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.TmpBdsVehicleInfoExample" resultType="java.lang.Long">
    select count(*) from tmp_bds_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tmp_bds_vehicle_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vehicleId != null">
        vehicle_id = #{record.vehicleId,jdbcType=CHAR},
      </if>
      <if test="record.vehicleDeviceId != null">
        vehicle_device_id = #{record.vehicleDeviceId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.engineNumber != null">
        engine_number = #{record.engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseNumber != null">
        license_number = #{record.licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.powerType != null">
        power_type = #{record.powerType,jdbcType=TINYINT},
      </if>
      <if test="record.operationState != null">
        operation_state = #{record.operationState,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.firstAddTime != null">
        first_add_time = #{record.firstAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.vid != null">
        vid = #{record.vid,jdbcType=CHAR},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.product != null">
        product = #{record.product,jdbcType=INTEGER},
      </if>
      <if test="record.image != null">
        image = #{record.image,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tmp_bds_vehicle_info
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=CHAR},
      vehicle_device_id = #{record.vehicleDeviceId,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      engine_number = #{record.engineNumber,jdbcType=VARCHAR},
      license_number = #{record.licenseNumber,jdbcType=VARCHAR},
      year = #{record.year,jdbcType=INTEGER},
      power_type = #{record.powerType,jdbcType=TINYINT},
      operation_state = #{record.operationState,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      first_add_time = #{record.firstAddTime,jdbcType=TIMESTAMP},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      vid = #{record.vid,jdbcType=CHAR},
      owner = #{record.owner,jdbcType=VARCHAR},
      product = #{record.product,jdbcType=INTEGER},
      image = #{record.image,jdbcType=LONGVARCHAR},
      extend = #{record.extend,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tmp_bds_vehicle_info
    set id = #{record.id,jdbcType=BIGINT},
      vehicle_id = #{record.vehicleId,jdbcType=CHAR},
      vehicle_device_id = #{record.vehicleDeviceId,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      engine_number = #{record.engineNumber,jdbcType=VARCHAR},
      license_number = #{record.licenseNumber,jdbcType=VARCHAR},
      year = #{record.year,jdbcType=INTEGER},
      power_type = #{record.powerType,jdbcType=TINYINT},
      operation_state = #{record.operationState,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      first_add_time = #{record.firstAddTime,jdbcType=TIMESTAMP},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      vid = #{record.vid,jdbcType=CHAR},
      owner = #{record.owner,jdbcType=VARCHAR},
      product = #{record.product,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfoWithBLOBs">
    update tmp_bds_vehicle_info
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=CHAR},
      </if>
      <if test="vehicleDeviceId != null">
        vehicle_device_id = #{vehicleDeviceId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="engineNumber != null">
        engine_number = #{engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="licenseNumber != null">
        license_number = #{licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="powerType != null">
        power_type = #{powerType,jdbcType=TINYINT},
      </if>
      <if test="operationState != null">
        operation_state = #{operationState,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="firstAddTime != null">
        first_add_time = #{firstAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vid != null">
        vid = #{vid,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="product != null">
        product = #{product,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=LONGVARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfoWithBLOBs">
    update tmp_bds_vehicle_info
    set vehicle_id = #{vehicleId,jdbcType=CHAR},
      vehicle_device_id = #{vehicleDeviceId,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      engine_number = #{engineNumber,jdbcType=VARCHAR},
      license_number = #{licenseNumber,jdbcType=VARCHAR},
      year = #{year,jdbcType=INTEGER},
      power_type = #{powerType,jdbcType=TINYINT},
      operation_state = #{operationState,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      first_add_time = #{firstAddTime,jdbcType=TIMESTAMP},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      vid = #{vid,jdbcType=CHAR},
      owner = #{owner,jdbcType=VARCHAR},
      product = #{product,jdbcType=INTEGER},
      image = #{image,jdbcType=LONGVARCHAR},
      extend = #{extend,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfo">
    update tmp_bds_vehicle_info
    set vehicle_id = #{vehicleId,jdbcType=CHAR},
      vehicle_device_id = #{vehicleDeviceId,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      engine_number = #{engineNumber,jdbcType=VARCHAR},
      license_number = #{licenseNumber,jdbcType=VARCHAR},
      year = #{year,jdbcType=INTEGER},
      power_type = #{powerType,jdbcType=TINYINT},
      operation_state = #{operationState,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      first_add_time = #{firstAddTime,jdbcType=TIMESTAMP},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      vid = #{vid,jdbcType=CHAR},
      owner = #{owner,jdbcType=VARCHAR},
      product = #{product,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into tmp_bds_vehicle_info
    (vehicle_id, vehicle_device_id, name, brand, type, engine_number, license_number, 
      year, power_type, operation_state, status, first_add_time, last_update_time, vid, 
      owner, product, image, extend)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vehicleId,jdbcType=CHAR}, #{item.vehicleDeviceId,jdbcType=CHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.brand,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.engineNumber,jdbcType=VARCHAR}, 
        #{item.licenseNumber,jdbcType=VARCHAR}, #{item.year,jdbcType=INTEGER}, #{item.powerType,jdbcType=TINYINT}, 
        #{item.operationState,jdbcType=TINYINT}, #{item.status,jdbcType=TINYINT}, #{item.firstAddTime,jdbcType=TIMESTAMP}, 
        #{item.lastUpdateTime,jdbcType=TIMESTAMP}, #{item.vid,jdbcType=CHAR}, #{item.owner,jdbcType=VARCHAR}, 
        #{item.product,jdbcType=INTEGER}, #{item.image,jdbcType=LONGVARCHAR}, #{item.extend,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
</mapper>