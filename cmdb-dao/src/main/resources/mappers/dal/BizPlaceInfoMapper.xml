<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.walle_data_center.mapper.BizPlaceInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="SMALLINT" property="type" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="task_type" jdbcType="CHAR" property="taskType" />
    <result column="area_code" jdbcType="CHAR" property="areaCode" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="affiliation" jdbcType="VARCHAR" property="affiliation" />
    <result column="is_self_place" jdbcType="BIT" property="isSelfPlace" />
    <result column="hd_map" jdbcType="VARCHAR" property="hdMap" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfoWithBLOBs">
    <result column="scope" jdbcType="LONGVARCHAR" property="scope" />
    <result column="comment" jdbcType="LONGVARCHAR" property="comment" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, name, status, city, city_code, create_time, update_time, type, parent_id, 
    task_type, area_code, alias, affiliation, is_self_place, hd_map
  </sql>
  <sql id="Blob_Column_List">
    scope, comment
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from biz_place_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_place_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from biz_place_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from biz_place_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample">
    delete from biz_place_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfoWithBLOBs" useGeneratedKeys="true">
    insert into biz_place_info (code, name, status, 
      city, city_code, create_time, 
      update_time, type, parent_id, 
      task_type, area_code, alias, 
      affiliation, is_self_place, hd_map, 
      scope, comment)
    values (#{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{city,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{type,jdbcType=SMALLINT}, #{parentId,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=CHAR}, #{areaCode,jdbcType=CHAR}, #{alias,jdbcType=VARCHAR}, 
      #{affiliation,jdbcType=VARCHAR}, #{isSelfPlace,jdbcType=BIT}, #{hdMap,jdbcType=VARCHAR}, 
      #{scope,jdbcType=LONGVARCHAR}, #{comment,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfoWithBLOBs" useGeneratedKeys="true">
    insert into biz_place_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="alias != null">
        alias,
      </if>
      <if test="affiliation != null">
        affiliation,
      </if>
      <if test="isSelfPlace != null">
        is_self_place,
      </if>
      <if test="hdMap != null">
        hd_map,
      </if>
      <if test="scope != null">
        scope,
      </if>
      <if test="comment != null">
        comment,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=SMALLINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=CHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=CHAR},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="affiliation != null">
        #{affiliation,jdbcType=VARCHAR},
      </if>
      <if test="isSelfPlace != null">
        #{isSelfPlace,jdbcType=BIT},
      </if>
      <if test="hdMap != null">
        #{hdMap,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=LONGVARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample" resultType="java.lang.Long">
    select count(*) from biz_place_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update biz_place_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=SMALLINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=CHAR},
      </if>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=CHAR},
      </if>
      <if test="record.alias != null">
        alias = #{record.alias,jdbcType=VARCHAR},
      </if>
      <if test="record.affiliation != null">
        affiliation = #{record.affiliation,jdbcType=VARCHAR},
      </if>
      <if test="record.isSelfPlace != null">
        is_self_place = #{record.isSelfPlace,jdbcType=BIT},
      </if>
      <if test="record.hdMap != null">
        hd_map = #{record.hdMap,jdbcType=VARCHAR},
      </if>
      <if test="record.scope != null">
        scope = #{record.scope,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update biz_place_info
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      city = #{record.city,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=SMALLINT},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=CHAR},
      area_code = #{record.areaCode,jdbcType=CHAR},
      alias = #{record.alias,jdbcType=VARCHAR},
      affiliation = #{record.affiliation,jdbcType=VARCHAR},
      is_self_place = #{record.isSelfPlace,jdbcType=BIT},
      hd_map = #{record.hdMap,jdbcType=VARCHAR},
      scope = #{record.scope,jdbcType=LONGVARCHAR},
      comment = #{record.comment,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update biz_place_info
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      city = #{record.city,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=SMALLINT},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=CHAR},
      area_code = #{record.areaCode,jdbcType=CHAR},
      alias = #{record.alias,jdbcType=VARCHAR},
      affiliation = #{record.affiliation,jdbcType=VARCHAR},
      is_self_place = #{record.isSelfPlace,jdbcType=BIT},
      hd_map = #{record.hdMap,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfoWithBLOBs">
    update biz_place_info
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=SMALLINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=CHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=CHAR},
      </if>
      <if test="alias != null">
        alias = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="affiliation != null">
        affiliation = #{affiliation,jdbcType=VARCHAR},
      </if>
      <if test="isSelfPlace != null">
        is_self_place = #{isSelfPlace,jdbcType=BIT},
      </if>
      <if test="hdMap != null">
        hd_map = #{hdMap,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        scope = #{scope,jdbcType=LONGVARCHAR},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfoWithBLOBs">
    update biz_place_info
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      city = #{city,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=SMALLINT},
      parent_id = #{parentId,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=CHAR},
      area_code = #{areaCode,jdbcType=CHAR},
      alias = #{alias,jdbcType=VARCHAR},
      affiliation = #{affiliation,jdbcType=VARCHAR},
      is_self_place = #{isSelfPlace,jdbcType=BIT},
      hd_map = #{hdMap,jdbcType=VARCHAR},
      scope = #{scope,jdbcType=LONGVARCHAR},
      comment = #{comment,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfo">
    update biz_place_info
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      city = #{city,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=SMALLINT},
      parent_id = #{parentId,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=CHAR},
      area_code = #{areaCode,jdbcType=CHAR},
      alias = #{alias,jdbcType=VARCHAR},
      affiliation = #{affiliation,jdbcType=VARCHAR},
      is_self_place = #{isSelfPlace,jdbcType=BIT},
      hd_map = #{hdMap,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into biz_place_info
    (code, name, status, city, city_code, create_time, update_time, type, parent_id, 
      task_type, area_code, alias, affiliation, is_self_place, hd_map, scope, comment
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, 
        #{item.city,jdbcType=VARCHAR}, #{item.cityCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=SMALLINT}, #{item.parentId,jdbcType=VARCHAR}, 
        #{item.taskType,jdbcType=CHAR}, #{item.areaCode,jdbcType=CHAR}, #{item.alias,jdbcType=VARCHAR}, 
        #{item.affiliation,jdbcType=VARCHAR}, #{item.isSelfPlace,jdbcType=BIT}, #{item.hdMap,jdbcType=VARCHAR}, 
        #{item.scope,jdbcType=LONGVARCHAR}, #{item.comment,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>