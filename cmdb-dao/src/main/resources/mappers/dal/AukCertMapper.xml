<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.AukCertMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="device_key" jdbcType="CHAR" property="deviceKey" />
    <result column="validity_period" jdbcType="INTEGER" property="validityPeriod" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="certId" jdbcType="CHAR" property="certid" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    <result column="privateKey" jdbcType="LONGVARCHAR" property="privatekey" />
    <result column="pem" jdbcType="LONGVARCHAR" property="pem" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, device_key, validity_period, expire_time, certId, add_time
  </sql>
  <sql id="Blob_Column_List">
    privateKey, pem
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walle.dal.mrm_manage.example.AukCertExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from auk_cert
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.AukCertExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auk_cert
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from auk_cert
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auk_cert
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.AukCertExample">
    delete from auk_cert
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    insert into auk_cert (id, device_key, validity_period, 
      expire_time, certId, add_time, 
      privateKey, pem)
    values (#{id,jdbcType=BIGINT}, #{deviceKey,jdbcType=CHAR}, #{validityPeriod,jdbcType=INTEGER}, 
      #{expireTime,jdbcType=TIMESTAMP}, #{certid,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{privatekey,jdbcType=LONGVARCHAR}, #{pem,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    insert into auk_cert
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deviceKey != null">
        device_key,
      </if>
      <if test="validityPeriod != null">
        validity_period,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="certid != null">
        certId,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="privatekey != null">
        privateKey,
      </if>
      <if test="pem != null">
        pem,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deviceKey != null">
        #{deviceKey,jdbcType=CHAR},
      </if>
      <if test="validityPeriod != null">
        #{validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="certid != null">
        #{certid,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="privatekey != null">
        #{privatekey,jdbcType=LONGVARCHAR},
      </if>
      <if test="pem != null">
        #{pem,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.AukCertExample" resultType="java.lang.Long">
    select count(*) from auk_cert
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auk_cert
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deviceKey != null">
        device_key = #{record.deviceKey,jdbcType=CHAR},
      </if>
      <if test="record.validityPeriod != null">
        validity_period = #{record.validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.expireTime != null">
        expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.certid != null">
        certId = #{record.certid,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.privatekey != null">
        privateKey = #{record.privatekey,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.pem != null">
        pem = #{record.pem,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update auk_cert
    set id = #{record.id,jdbcType=BIGINT},
      device_key = #{record.deviceKey,jdbcType=CHAR},
      validity_period = #{record.validityPeriod,jdbcType=INTEGER},
      expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      certId = #{record.certid,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      privateKey = #{record.privatekey,jdbcType=LONGVARCHAR},
      pem = #{record.pem,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auk_cert
    set id = #{record.id,jdbcType=BIGINT},
      device_key = #{record.deviceKey,jdbcType=CHAR},
      validity_period = #{record.validityPeriod,jdbcType=INTEGER},
      expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      certId = #{record.certid,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    update auk_cert
    <set>
      <if test="deviceKey != null">
        device_key = #{deviceKey,jdbcType=CHAR},
      </if>
      <if test="validityPeriod != null">
        validity_period = #{validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="certid != null">
        certId = #{certid,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="privatekey != null">
        privateKey = #{privatekey,jdbcType=LONGVARCHAR},
      </if>
      <if test="pem != null">
        pem = #{pem,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    update auk_cert
    set device_key = #{deviceKey,jdbcType=CHAR},
      validity_period = #{validityPeriod,jdbcType=INTEGER},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      certId = #{certid,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      privateKey = #{privatekey,jdbcType=LONGVARCHAR},
      pem = #{pem,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.AukCert">
    update auk_cert
    set device_key = #{deviceKey,jdbcType=CHAR},
      validity_period = #{validityPeriod,jdbcType=INTEGER},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      certId = #{certid,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into auk_cert
    (id, device_key, validity_period, expire_time, certId, add_time, privateKey, pem)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.deviceKey,jdbcType=CHAR}, #{item.validityPeriod,jdbcType=INTEGER}, 
        #{item.expireTime,jdbcType=TIMESTAMP}, #{item.certid,jdbcType=CHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.privatekey,jdbcType=LONGVARCHAR}, #{item.pem,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.walle.dal.mrm_manage.example.AukCertExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from auk_cert
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.mrm_manage.example.AukCertExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auk_cert
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>