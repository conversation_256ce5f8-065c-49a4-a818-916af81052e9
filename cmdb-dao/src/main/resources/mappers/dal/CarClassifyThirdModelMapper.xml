<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.classify.mapper.CarClassifyThirdModelMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.classify.entity.CarClassifyThirdModel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="remote_car_classify_id" jdbcType="BIGINT" property="remoteCarClassifyId" />
    <result column="third_model" jdbcType="SMALLINT" property="thirdModel" />
    <result column="third_model_next" jdbcType="VARCHAR" property="thirdModelNext" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, remote_car_classify_id, third_model, third_model_next, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyThirdModelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_classify_third_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_classify_third_model
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_classify_third_model
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyThirdModelExample">
    delete from car_classify_third_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyThirdModel" useGeneratedKeys="true">
    insert into car_classify_third_model (remote_car_classify_id, third_model, 
      third_model_next, add_time, update_time
      )
    values (#{remoteCarClassifyId,jdbcType=BIGINT}, #{thirdModel,jdbcType=SMALLINT}, 
      #{thirdModelNext,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyThirdModel" useGeneratedKeys="true">
    insert into car_classify_third_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="remoteCarClassifyId != null">
        remote_car_classify_id,
      </if>
      <if test="thirdModel != null">
        third_model,
      </if>
      <if test="thirdModelNext != null">
        third_model_next,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="remoteCarClassifyId != null">
        #{remoteCarClassifyId,jdbcType=BIGINT},
      </if>
      <if test="thirdModel != null">
        #{thirdModel,jdbcType=SMALLINT},
      </if>
      <if test="thirdModelNext != null">
        #{thirdModelNext,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.classify.example.CarClassifyThirdModelExample" resultType="java.lang.Long">
    select count(*) from car_classify_third_model
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_classify_third_model
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.remoteCarClassifyId != null">
        remote_car_classify_id = #{record.remoteCarClassifyId,jdbcType=BIGINT},
      </if>
      <if test="record.thirdModel != null">
        third_model = #{record.thirdModel,jdbcType=SMALLINT},
      </if>
      <if test="record.thirdModelNext != null">
        third_model_next = #{record.thirdModelNext,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_classify_third_model
    set id = #{record.id,jdbcType=BIGINT},
      remote_car_classify_id = #{record.remoteCarClassifyId,jdbcType=BIGINT},
      third_model = #{record.thirdModel,jdbcType=SMALLINT},
      third_model_next = #{record.thirdModelNext,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyThirdModel">
    update car_classify_third_model
    <set>
      <if test="remoteCarClassifyId != null">
        remote_car_classify_id = #{remoteCarClassifyId,jdbcType=BIGINT},
      </if>
      <if test="thirdModel != null">
        third_model = #{thirdModel,jdbcType=SMALLINT},
      </if>
      <if test="thirdModelNext != null">
        third_model_next = #{thirdModelNext,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.classify.entity.CarClassifyThirdModel">
    update car_classify_third_model
    set remote_car_classify_id = #{remoteCarClassifyId,jdbcType=BIGINT},
      third_model = #{thirdModel,jdbcType=SMALLINT},
      third_model_next = #{thirdModelNext,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into car_classify_third_model
    (remote_car_classify_id, third_model, third_model_next, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.remoteCarClassifyId,jdbcType=BIGINT}, #{item.thirdModel,jdbcType=SMALLINT}, 
        #{item.thirdModelNext,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>