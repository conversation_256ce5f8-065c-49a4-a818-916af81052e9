<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.wallevresv.mapper.VresvCityParkMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.wallevresv.entity.VresvCityPark">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="park" jdbcType="VARCHAR" property="park" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="first_add_time" jdbcType="TIMESTAMP" property="firstAddTime" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="code" jdbcType="VARCHAR" property="code" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, city, park, address, deleted, first_add_time, last_update_time, code
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.wallevresv.example.VresvCityParkExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vresv_city_park
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vresv_city_park
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from vresv_city_park
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.wallevresv.example.VresvCityParkExample">
    delete from vresv_city_park
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.wallevresv.entity.VresvCityPark" useGeneratedKeys="true">
    insert into vresv_city_park (city, park, address, 
      deleted, first_add_time, last_update_time, 
      code)
    values (#{city,jdbcType=VARCHAR}, #{park,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=BIT}, #{firstAddTime,jdbcType=TIMESTAMP}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{code,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.walle.dal.wallevresv.entity.VresvCityPark" useGeneratedKeys="true">
    insert into vresv_city_park
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="city != null">
        city,
      </if>
      <if test="park != null">
        park,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="firstAddTime != null">
        first_add_time,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="code != null">
        code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="park != null">
        #{park,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="firstAddTime != null">
        #{firstAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.wallevresv.example.VresvCityParkExample" resultType="java.lang.Long">
    select count(*) from vresv_city_park
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vresv_city_park
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.park != null">
        park = #{record.park,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.firstAddTime != null">
        first_add_time = #{record.firstAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vresv_city_park
    set id = #{record.id,jdbcType=INTEGER},
      city = #{record.city,jdbcType=VARCHAR},
      park = #{record.park,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      first_add_time = #{record.firstAddTime,jdbcType=TIMESTAMP},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      code = #{record.code,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.wallevresv.entity.VresvCityPark">
    update vresv_city_park
    <set>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="park != null">
        park = #{park,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="firstAddTime != null">
        first_add_time = #{firstAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.wallevresv.entity.VresvCityPark">
    update vresv_city_park
    set city = #{city,jdbcType=VARCHAR},
      park = #{park,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      first_add_time = #{firstAddTime,jdbcType=TIMESTAMP},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      code = #{code,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into vresv_city_park
    (city, park, address, deleted, first_add_time, last_update_time, code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.city,jdbcType=VARCHAR}, #{item.park,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, 
        #{item.deleted,jdbcType=BIT}, #{item.firstAddTime,jdbcType=TIMESTAMP}, #{item.lastUpdateTime,jdbcType=TIMESTAMP}, 
        #{item.code,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>