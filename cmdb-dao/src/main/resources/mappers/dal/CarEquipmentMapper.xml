<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.dal.mrm_manage.mapper.CarEquipmentMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.dal.mrm_manage.entity.CarEquipment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="big_type" jdbcType="VARCHAR" property="bigType" />
    <result column="small_type" jdbcType="VARCHAR" property="smallType" />
    <result column="mis" jdbcType="VARCHAR" property="mis" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="remote_type_name" jdbcType="VARCHAR" property="remoteTypeName" />
    <result column="scrap" jdbcType="BIT" property="scrap" />
    <result column="park" jdbcType="VARCHAR" property="park" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="exec_word" jdbcType="CHAR" property="execWord" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sn, `uid`, vin, `label`, big_type, small_type, mis, `status`, remote_type_name, 
    scrap, park, add_time, update_time, exec_word
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.CarEquipmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_equipment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_equipment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.CarEquipmentExample">
    delete from car_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.dal.mrm_manage.entity.CarEquipment">
    insert into car_equipment (id, sn, `uid`, vin, 
      `label`, big_type, small_type, 
      mis, `status`, remote_type_name, 
      scrap, park, add_time, 
      update_time, exec_word)
    values (#{id,jdbcType=BIGINT}, #{sn,jdbcType=VARCHAR}, #{uid,jdbcType=VARCHAR}, #{vin,jdbcType=VARCHAR}, 
      #{label,jdbcType=VARCHAR}, #{bigType,jdbcType=VARCHAR}, #{smallType,jdbcType=VARCHAR}, 
      #{mis,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{remoteTypeName,jdbcType=VARCHAR}, 
      #{scrap,jdbcType=BIT}, #{park,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{execWord,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.CarEquipment">
    insert into car_equipment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="uid != null">
        `uid`,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="bigType != null">
        big_type,
      </if>
      <if test="smallType != null">
        small_type,
      </if>
      <if test="mis != null">
        mis,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remoteTypeName != null">
        remote_type_name,
      </if>
      <if test="scrap != null">
        scrap,
      </if>
      <if test="park != null">
        park,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="execWord != null">
        exec_word,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="bigType != null">
        #{bigType,jdbcType=VARCHAR},
      </if>
      <if test="smallType != null">
        #{smallType,jdbcType=VARCHAR},
      </if>
      <if test="mis != null">
        #{mis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="remoteTypeName != null">
        #{remoteTypeName,jdbcType=VARCHAR},
      </if>
      <if test="scrap != null">
        #{scrap,jdbcType=BIT},
      </if>
      <if test="park != null">
        #{park,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execWord != null">
        #{execWord,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.dal.mrm_manage.example.CarEquipmentExample" resultType="java.lang.Long">
    select count(*) from car_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_equipment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.uid != null">
        `uid` = #{record.uid,jdbcType=VARCHAR},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null">
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.bigType != null">
        big_type = #{record.bigType,jdbcType=VARCHAR},
      </if>
      <if test="record.smallType != null">
        small_type = #{record.smallType,jdbcType=VARCHAR},
      </if>
      <if test="record.mis != null">
        mis = #{record.mis,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.remoteTypeName != null">
        remote_type_name = #{record.remoteTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.scrap != null">
        scrap = #{record.scrap,jdbcType=BIT},
      </if>
      <if test="record.park != null">
        park = #{record.park,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.execWord != null">
        exec_word = #{record.execWord,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_equipment
    set id = #{record.id,jdbcType=BIGINT},
      sn = #{record.sn,jdbcType=VARCHAR},
      `uid` = #{record.uid,jdbcType=VARCHAR},
      vin = #{record.vin,jdbcType=VARCHAR},
      `label` = #{record.label,jdbcType=VARCHAR},
      big_type = #{record.bigType,jdbcType=VARCHAR},
      small_type = #{record.smallType,jdbcType=VARCHAR},
      mis = #{record.mis,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      remote_type_name = #{record.remoteTypeName,jdbcType=VARCHAR},
      scrap = #{record.scrap,jdbcType=BIT},
      park = #{record.park,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      exec_word = #{record.execWord,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.dal.mrm_manage.entity.CarEquipment">
    update car_equipment
    <set>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        `uid` = #{uid,jdbcType=VARCHAR},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="bigType != null">
        big_type = #{bigType,jdbcType=VARCHAR},
      </if>
      <if test="smallType != null">
        small_type = #{smallType,jdbcType=VARCHAR},
      </if>
      <if test="mis != null">
        mis = #{mis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remoteTypeName != null">
        remote_type_name = #{remoteTypeName,jdbcType=VARCHAR},
      </if>
      <if test="scrap != null">
        scrap = #{scrap,jdbcType=BIT},
      </if>
      <if test="park != null">
        park = #{park,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execWord != null">
        exec_word = #{execWord,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.dal.mrm_manage.entity.CarEquipment">
    update car_equipment
    set sn = #{sn,jdbcType=VARCHAR},
      `uid` = #{uid,jdbcType=VARCHAR},
      vin = #{vin,jdbcType=VARCHAR},
      `label` = #{label,jdbcType=VARCHAR},
      big_type = #{bigType,jdbcType=VARCHAR},
      small_type = #{smallType,jdbcType=VARCHAR},
      mis = #{mis,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      remote_type_name = #{remoteTypeName,jdbcType=VARCHAR},
      scrap = #{scrap,jdbcType=BIT},
      park = #{park,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      exec_word = #{execWord,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_equipment
    (id, sn, `uid`, vin, `label`, big_type, small_type, mis, `status`, remote_type_name, 
      scrap, park, add_time, update_time, exec_word)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.sn,jdbcType=VARCHAR}, #{item.uid,jdbcType=VARCHAR}, 
        #{item.vin,jdbcType=VARCHAR}, #{item.label,jdbcType=VARCHAR}, #{item.bigType,jdbcType=VARCHAR}, 
        #{item.smallType,jdbcType=VARCHAR}, #{item.mis,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, 
        #{item.remoteTypeName,jdbcType=VARCHAR}, #{item.scrap,jdbcType=BIT}, #{item.park,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.execWord,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.dal.mrm_manage.example.CarEquipmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>