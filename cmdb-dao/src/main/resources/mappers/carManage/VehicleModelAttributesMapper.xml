<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.carManage.mapper.VehicleModelAttributesMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.carManage.entity.VehicleModelAttributes">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attribute_name" jdbcType="VARCHAR" property="attributeName" />
    <result column="attribute_value" jdbcType="VARCHAR" property="attributeValue" />
    <result column="editor" jdbcType="VARCHAR" property="editor" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, attribute_name, attribute_value, editor, create_time, update_time, is_deleted, 
    car_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.carManage.example.VehicleModelAttributesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vehicle_model_attributes
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vehicle_model_attributes
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vehicle_model_attributes
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.carManage.example.VehicleModelAttributesExample">
    delete from vehicle_model_attributes
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.carManage.entity.VehicleModelAttributes">
    insert into vehicle_model_attributes (id, attribute_name, attribute_value, 
      editor, create_time, update_time, 
      is_deleted, car_type)
    values (#{id,jdbcType=BIGINT}, #{attributeName,jdbcType=VARCHAR}, #{attributeValue,jdbcType=VARCHAR}, 
      #{editor,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=BIT}, #{carType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.carManage.entity.VehicleModelAttributes">
    insert into vehicle_model_attributes
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="attributeName != null">
        attribute_name,
      </if>
      <if test="attributeValue != null">
        attribute_value,
      </if>
      <if test="editor != null">
        editor,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="carType != null">
        car_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="attributeName != null">
        #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null">
        #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="editor != null">
        #{editor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="carType != null">
        #{carType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.carManage.example.VehicleModelAttributesExample" resultType="java.lang.Long">
    select count(*) from vehicle_model_attributes
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vehicle_model_attributes
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.attributeName != null">
        attribute_name = #{record.attributeName,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeValue != null">
        attribute_value = #{record.attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.editor != null">
        editor = #{record.editor,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.carType != null">
        car_type = #{record.carType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vehicle_model_attributes
    set id = #{record.id,jdbcType=BIGINT},
      attribute_name = #{record.attributeName,jdbcType=VARCHAR},
      attribute_value = #{record.attributeValue,jdbcType=VARCHAR},
      editor = #{record.editor,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      car_type = #{record.carType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.carManage.entity.VehicleModelAttributes">
    update vehicle_model_attributes
    <set>
      <if test="attributeName != null">
        attribute_name = #{attributeName,jdbcType=VARCHAR},
      </if>
      <if test="attributeValue != null">
        attribute_value = #{attributeValue,jdbcType=VARCHAR},
      </if>
      <if test="editor != null">
        editor = #{editor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="carType != null">
        car_type = #{carType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.carManage.entity.VehicleModelAttributes">
    update vehicle_model_attributes
    set attribute_name = #{attributeName,jdbcType=VARCHAR},
      attribute_value = #{attributeValue,jdbcType=VARCHAR},
      editor = #{editor,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT},
      car_type = #{carType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into vehicle_model_attributes
    (id, attribute_name, attribute_value, editor, create_time, update_time, is_deleted, 
      car_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.attributeName,jdbcType=VARCHAR}, #{item.attributeValue,jdbcType=VARCHAR}, 
        #{item.editor,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=BIT}, #{item.carType,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.carManage.example.VehicleModelAttributesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vehicle_model_attributes
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>