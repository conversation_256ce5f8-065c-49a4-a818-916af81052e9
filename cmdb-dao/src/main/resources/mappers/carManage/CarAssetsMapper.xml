<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.carManage.mapper.CarAssetsMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.carManage.entity.CarAssets">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="small_type" jdbcType="VARCHAR" property="smallType" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="personMis" jdbcType="VARCHAR" property="personmis" />
    <result column="owner_department" jdbcType="VARCHAR" property="ownerDepartment" />
    <result column="scrap" jdbcType="BIT" property="scrap" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="car_size_type" jdbcType="INTEGER" property="carSizeType" />
    <result column="car_belong_type" jdbcType="INTEGER" property="carBelongType" />
    <result column="power_type" jdbcType="INTEGER" property="powerType" />
    <result column="refited" jdbcType="INTEGER" property="refited" />
    <result column="driving_permit_in_car" jdbcType="VARCHAR" property="drivingPermitInCar" />
    <result column="car_owner" jdbcType="VARCHAR" property="carOwner" />
    <result column="indicate_owner" jdbcType="VARCHAR" property="indicateOwner" />
    <result column="exec_word" jdbcType="CHAR" property="execWord" />
    <result column="scrap_confirm" jdbcType="BIT" property="scrapConfirm" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, sn, `label`, small_type, brand, personMis, owner_department, scrap, `comment`, 
    add_time, update_time, car_size_type, car_belong_type, power_type, refited, driving_permit_in_car, 
    car_owner, indicate_owner, exec_word, scrap_confirm
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.carManage.example.CarAssetsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_assets
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_assets
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.carManage.example.CarAssetsExample">
    delete from car_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.carManage.entity.CarAssets">
    insert into car_assets (id, vin, sn, `label`, 
      small_type, brand, personMis, 
      owner_department, scrap, `comment`, 
      add_time, update_time, car_size_type, 
      car_belong_type, power_type, refited, 
      driving_permit_in_car, car_owner, indicate_owner, 
      exec_word, scrap_confirm)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR}, 
      #{smallType,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{personmis,jdbcType=VARCHAR}, 
      #{ownerDepartment,jdbcType=VARCHAR}, #{scrap,jdbcType=BIT}, #{comment,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{carSizeType,jdbcType=INTEGER}, 
      #{carBelongType,jdbcType=INTEGER}, #{powerType,jdbcType=INTEGER}, #{refited,jdbcType=INTEGER}, 
      #{drivingPermitInCar,jdbcType=VARCHAR}, #{carOwner,jdbcType=VARCHAR}, #{indicateOwner,jdbcType=VARCHAR}, 
      #{execWord,jdbcType=CHAR}, #{scrapConfirm,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.carManage.entity.CarAssets">
    insert into car_assets
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="smallType != null">
        small_type,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="personmis != null">
        personMis,
      </if>
      <if test="ownerDepartment != null">
        owner_department,
      </if>
      <if test="scrap != null">
        scrap,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="carSizeType != null">
        car_size_type,
      </if>
      <if test="carBelongType != null">
        car_belong_type,
      </if>
      <if test="powerType != null">
        power_type,
      </if>
      <if test="refited != null">
        refited,
      </if>
      <if test="drivingPermitInCar != null">
        driving_permit_in_car,
      </if>
      <if test="carOwner != null">
        car_owner,
      </if>
      <if test="indicateOwner != null">
        indicate_owner,
      </if>
      <if test="execWord != null">
        exec_word,
      </if>
      <if test="scrapConfirm != null">
        scrap_confirm,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="smallType != null">
        #{smallType,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="personmis != null">
        #{personmis,jdbcType=VARCHAR},
      </if>
      <if test="ownerDepartment != null">
        #{ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="scrap != null">
        #{scrap,jdbcType=BIT},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carSizeType != null">
        #{carSizeType,jdbcType=INTEGER},
      </if>
      <if test="carBelongType != null">
        #{carBelongType,jdbcType=INTEGER},
      </if>
      <if test="powerType != null">
        #{powerType,jdbcType=INTEGER},
      </if>
      <if test="refited != null">
        #{refited,jdbcType=INTEGER},
      </if>
      <if test="drivingPermitInCar != null">
        #{drivingPermitInCar,jdbcType=VARCHAR},
      </if>
      <if test="carOwner != null">
        #{carOwner,jdbcType=VARCHAR},
      </if>
      <if test="indicateOwner != null">
        #{indicateOwner,jdbcType=VARCHAR},
      </if>
      <if test="execWord != null">
        #{execWord,jdbcType=CHAR},
      </if>
      <if test="scrapConfirm != null">
        #{scrapConfirm,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.carManage.example.CarAssetsExample" resultType="java.lang.Long">
    select count(*) from car_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_assets
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null">
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.smallType != null">
        small_type = #{record.smallType,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.personmis != null">
        personMis = #{record.personmis,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerDepartment != null">
        owner_department = #{record.ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.scrap != null">
        scrap = #{record.scrap,jdbcType=BIT},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.carSizeType != null">
        car_size_type = #{record.carSizeType,jdbcType=INTEGER},
      </if>
      <if test="record.carBelongType != null">
        car_belong_type = #{record.carBelongType,jdbcType=INTEGER},
      </if>
      <if test="record.powerType != null">
        power_type = #{record.powerType,jdbcType=INTEGER},
      </if>
      <if test="record.refited != null">
        refited = #{record.refited,jdbcType=INTEGER},
      </if>
      <if test="record.drivingPermitInCar != null">
        driving_permit_in_car = #{record.drivingPermitInCar,jdbcType=VARCHAR},
      </if>
      <if test="record.carOwner != null">
        car_owner = #{record.carOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.indicateOwner != null">
        indicate_owner = #{record.indicateOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.execWord != null">
        exec_word = #{record.execWord,jdbcType=CHAR},
      </if>
      <if test="record.scrapConfirm != null">
        scrap_confirm = #{record.scrapConfirm,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_assets
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR},
      `label` = #{record.label,jdbcType=VARCHAR},
      small_type = #{record.smallType,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      personMis = #{record.personmis,jdbcType=VARCHAR},
      owner_department = #{record.ownerDepartment,jdbcType=VARCHAR},
      scrap = #{record.scrap,jdbcType=BIT},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      car_size_type = #{record.carSizeType,jdbcType=INTEGER},
      car_belong_type = #{record.carBelongType,jdbcType=INTEGER},
      power_type = #{record.powerType,jdbcType=INTEGER},
      refited = #{record.refited,jdbcType=INTEGER},
      driving_permit_in_car = #{record.drivingPermitInCar,jdbcType=VARCHAR},
      car_owner = #{record.carOwner,jdbcType=VARCHAR},
      indicate_owner = #{record.indicateOwner,jdbcType=VARCHAR},
      exec_word = #{record.execWord,jdbcType=CHAR},
      scrap_confirm = #{record.scrapConfirm,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.carManage.entity.CarAssets">
    update car_assets
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="smallType != null">
        small_type = #{smallType,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="personmis != null">
        personMis = #{personmis,jdbcType=VARCHAR},
      </if>
      <if test="ownerDepartment != null">
        owner_department = #{ownerDepartment,jdbcType=VARCHAR},
      </if>
      <if test="scrap != null">
        scrap = #{scrap,jdbcType=BIT},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carSizeType != null">
        car_size_type = #{carSizeType,jdbcType=INTEGER},
      </if>
      <if test="carBelongType != null">
        car_belong_type = #{carBelongType,jdbcType=INTEGER},
      </if>
      <if test="powerType != null">
        power_type = #{powerType,jdbcType=INTEGER},
      </if>
      <if test="refited != null">
        refited = #{refited,jdbcType=INTEGER},
      </if>
      <if test="drivingPermitInCar != null">
        driving_permit_in_car = #{drivingPermitInCar,jdbcType=VARCHAR},
      </if>
      <if test="carOwner != null">
        car_owner = #{carOwner,jdbcType=VARCHAR},
      </if>
      <if test="indicateOwner != null">
        indicate_owner = #{indicateOwner,jdbcType=VARCHAR},
      </if>
      <if test="execWord != null">
        exec_word = #{execWord,jdbcType=CHAR},
      </if>
      <if test="scrapConfirm != null">
        scrap_confirm = #{scrapConfirm,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.carManage.entity.CarAssets">
    update car_assets
    set vin = #{vin,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      `label` = #{label,jdbcType=VARCHAR},
      small_type = #{smallType,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      personMis = #{personmis,jdbcType=VARCHAR},
      owner_department = #{ownerDepartment,jdbcType=VARCHAR},
      scrap = #{scrap,jdbcType=BIT},
      `comment` = #{comment,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      car_size_type = #{carSizeType,jdbcType=INTEGER},
      car_belong_type = #{carBelongType,jdbcType=INTEGER},
      power_type = #{powerType,jdbcType=INTEGER},
      refited = #{refited,jdbcType=INTEGER},
      driving_permit_in_car = #{drivingPermitInCar,jdbcType=VARCHAR},
      car_owner = #{carOwner,jdbcType=VARCHAR},
      indicate_owner = #{indicateOwner,jdbcType=VARCHAR},
      exec_word = #{execWord,jdbcType=CHAR},
      scrap_confirm = #{scrapConfirm,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_assets
    (id, vin, sn, `label`, small_type, brand, personMis, owner_department, scrap, `comment`, 
      add_time, update_time, car_size_type, car_belong_type, power_type, refited, driving_permit_in_car, 
      car_owner, indicate_owner, exec_word, scrap_confirm)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR}, 
        #{item.label,jdbcType=VARCHAR}, #{item.smallType,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, 
        #{item.personmis,jdbcType=VARCHAR}, #{item.ownerDepartment,jdbcType=VARCHAR}, #{item.scrap,jdbcType=BIT}, 
        #{item.comment,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.carSizeType,jdbcType=INTEGER}, #{item.carBelongType,jdbcType=INTEGER}, #{item.powerType,jdbcType=INTEGER}, 
        #{item.refited,jdbcType=INTEGER}, #{item.drivingPermitInCar,jdbcType=VARCHAR}, 
        #{item.carOwner,jdbcType=VARCHAR}, #{item.indicateOwner,jdbcType=VARCHAR}, #{item.execWord,jdbcType=CHAR}, 
        #{item.scrapConfirm,jdbcType=BIT})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.carManage.example.CarAssetsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>