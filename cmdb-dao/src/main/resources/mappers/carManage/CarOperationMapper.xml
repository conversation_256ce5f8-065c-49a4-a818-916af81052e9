<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.carManage.mapper.CarOperationMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.carManage.entity.CarOperation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="car_used_target" jdbcType="VARCHAR" property="carUsedTarget" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="exec_word" jdbcType="CHAR" property="execWord" />
    <result column="area_str" jdbcType="VARCHAR" property="areaStr" />
    <result column="car_used_target_str" jdbcType="VARCHAR" property="carUsedTargetStr" />
    <result column="used_target" jdbcType="VARCHAR" property="usedTarget" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, city, district, area, car_used_target, `comment`, add_time, update_time, 
    exec_word, area_str, car_used_target_str, used_target
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.carManage.example.CarOperationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_operation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_operation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.carManage.example.CarOperationExample">
    delete from car_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.carManage.entity.CarOperation">
    insert into car_operation (id, vin, city, 
      district, area, car_used_target, 
      `comment`, add_time, update_time, 
      exec_word, area_str, car_used_target_str, 
      used_target)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{district,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{carUsedTarget,jdbcType=VARCHAR}, 
      #{comment,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{execWord,jdbcType=CHAR}, #{areaStr,jdbcType=VARCHAR}, #{carUsedTargetStr,jdbcType=VARCHAR}, 
      #{usedTarget,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.carManage.entity.CarOperation">
    insert into car_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="district != null">
        district,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="carUsedTarget != null">
        car_used_target,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="execWord != null">
        exec_word,
      </if>
      <if test="areaStr != null">
        area_str,
      </if>
      <if test="carUsedTargetStr != null">
        car_used_target_str,
      </if>
      <if test="usedTarget != null">
        used_target,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="carUsedTarget != null">
        #{carUsedTarget,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execWord != null">
        #{execWord,jdbcType=CHAR},
      </if>
      <if test="areaStr != null">
        #{areaStr,jdbcType=VARCHAR},
      </if>
      <if test="carUsedTargetStr != null">
        #{carUsedTargetStr,jdbcType=VARCHAR},
      </if>
      <if test="usedTarget != null">
        #{usedTarget,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.carManage.example.CarOperationExample" resultType="java.lang.Long">
    select count(*) from car_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_operation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.district != null">
        district = #{record.district,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.carUsedTarget != null">
        car_used_target = #{record.carUsedTarget,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.execWord != null">
        exec_word = #{record.execWord,jdbcType=CHAR},
      </if>
      <if test="record.areaStr != null">
        area_str = #{record.areaStr,jdbcType=VARCHAR},
      </if>
      <if test="record.carUsedTargetStr != null">
        car_used_target_str = #{record.carUsedTargetStr,jdbcType=VARCHAR},
      </if>
      <if test="record.usedTarget != null">
        used_target = #{record.usedTarget,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_operation
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      district = #{record.district,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      car_used_target = #{record.carUsedTarget,jdbcType=VARCHAR},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      exec_word = #{record.execWord,jdbcType=CHAR},
      area_str = #{record.areaStr,jdbcType=VARCHAR},
      car_used_target_str = #{record.carUsedTargetStr,jdbcType=VARCHAR},
      used_target = #{record.usedTarget,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.carManage.entity.CarOperation">
    update car_operation
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        district = #{district,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="carUsedTarget != null">
        car_used_target = #{carUsedTarget,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execWord != null">
        exec_word = #{execWord,jdbcType=CHAR},
      </if>
      <if test="areaStr != null">
        area_str = #{areaStr,jdbcType=VARCHAR},
      </if>
      <if test="carUsedTargetStr != null">
        car_used_target_str = #{carUsedTargetStr,jdbcType=VARCHAR},
      </if>
      <if test="usedTarget != null">
        used_target = #{usedTarget,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.carManage.entity.CarOperation">
    update car_operation
    set vin = #{vin,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      district = #{district,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      car_used_target = #{carUsedTarget,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      exec_word = #{execWord,jdbcType=CHAR},
      area_str = #{areaStr,jdbcType=VARCHAR},
      car_used_target_str = #{carUsedTargetStr,jdbcType=VARCHAR},
      used_target = #{usedTarget,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_operation
    (id, vin, city, district, area, car_used_target, `comment`, add_time, update_time, 
      exec_word, area_str, car_used_target_str, used_target)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, 
        #{item.district,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR}, #{item.carUsedTarget,jdbcType=VARCHAR}, 
        #{item.comment,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.execWord,jdbcType=CHAR}, #{item.areaStr,jdbcType=VARCHAR}, #{item.carUsedTargetStr,jdbcType=VARCHAR}, 
        #{item.usedTarget,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.carManage.example.CarOperationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>