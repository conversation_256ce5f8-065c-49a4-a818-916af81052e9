<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walle.carManage.mapper.CarObjectsMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walle.carManage.entity.CarObjects">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="licenseNo" jdbcType="VARCHAR" property="licenseno" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tag" jdbcType="CHAR" property="tag" />
    <result column="assembly_parts" jdbcType="CHAR" property="assemblyParts" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vin, `name`, car_type, licenseNo, `comment`, add_time, update_time, tag, assembly_parts
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.walle.carManage.example.CarObjectsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_objects
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from car_objects
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walle.carManage.example.CarObjectsExample">
    delete from car_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walle.carManage.entity.CarObjects">
    insert into car_objects (id, vin, `name`, 
      car_type, licenseNo, `comment`, 
      add_time, update_time, tag, 
      assembly_parts)
    values (#{id,jdbcType=BIGINT}, #{vin,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{carType,jdbcType=VARCHAR}, #{licenseno,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{tag,jdbcType=CHAR}, 
      #{assemblyParts,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walle.carManage.entity.CarObjects">
    insert into car_objects
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="carType != null">
        car_type,
      </if>
      <if test="licenseno != null">
        licenseNo,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="assemblyParts != null">
        assembly_parts,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        #{carType,jdbcType=VARCHAR},
      </if>
      <if test="licenseno != null">
        #{licenseno,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=CHAR},
      </if>
      <if test="assemblyParts != null">
        #{assemblyParts,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walle.carManage.example.CarObjectsExample" resultType="java.lang.Long">
    select count(*) from car_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update car_objects
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vin != null">
        vin = #{record.vin,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.carType != null">
        car_type = #{record.carType,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseno != null">
        licenseNo = #{record.licenseno,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tag != null">
        tag = #{record.tag,jdbcType=CHAR},
      </if>
      <if test="record.assemblyParts != null">
        assembly_parts = #{record.assemblyParts,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update car_objects
    set id = #{record.id,jdbcType=BIGINT},
      vin = #{record.vin,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      car_type = #{record.carType,jdbcType=VARCHAR},
      licenseNo = #{record.licenseno,jdbcType=VARCHAR},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tag = #{record.tag,jdbcType=CHAR},
      assembly_parts = #{record.assemblyParts,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walle.carManage.entity.CarObjects">
    update car_objects
    <set>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        car_type = #{carType,jdbcType=VARCHAR},
      </if>
      <if test="licenseno != null">
        licenseNo = #{licenseno,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=CHAR},
      </if>
      <if test="assemblyParts != null">
        assembly_parts = #{assemblyParts,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walle.carManage.entity.CarObjects">
    update car_objects
    set vin = #{vin,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=VARCHAR},
      licenseNo = #{licenseno,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tag = #{tag,jdbcType=CHAR},
      assembly_parts = #{assemblyParts,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into car_objects
    (id, vin, `name`, car_type, licenseNo, `comment`, add_time, update_time, tag, assembly_parts
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.vin,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.carType,jdbcType=VARCHAR}, #{item.licenseno,jdbcType=VARCHAR}, #{item.comment,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.tag,jdbcType=CHAR}, 
        #{item.assemblyParts,jdbcType=CHAR})
    </foreach>
  </insert>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.walle.carManage.example.CarObjectsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from car_objects
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>