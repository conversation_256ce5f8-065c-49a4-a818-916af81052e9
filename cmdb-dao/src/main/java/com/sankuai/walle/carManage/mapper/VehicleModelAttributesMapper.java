package com.sankuai.walle.carManage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.carManage.entity.VehicleModelAttributes;
import com.sankuai.walle.carManage.example.VehicleModelAttributesExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface VehicleModelAttributesMapper extends MybatisBaseMapper<VehicleModelAttributes, VehicleModelAttributesExample, Long> {
    int batchInsert(@Param("list") List<VehicleModelAttributes> list);
}