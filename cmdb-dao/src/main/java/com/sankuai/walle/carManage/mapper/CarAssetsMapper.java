package com.sankuai.walle.carManage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.example.CarAssetsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarAssetsMapper extends MybatisBaseMapper<CarAssets, CarAssetsExample, Long> {
    int batchInsert(@Param("list") List<CarAssets> list);
}