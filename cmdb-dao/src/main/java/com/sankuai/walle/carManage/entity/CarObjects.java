package com.sankuai.walle.carManage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_objects
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarObjects {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: vin
     *   说明: 车辆vin
     */
    private String vin;

    /**
     *   字段: name
     *   说明: 车辆编码
     */
    private String name;

    /**
     *   字段: car_type
     *   说明: 车辆型号
     */
    private String carType;

    /**
     *   字段: licenseNo
     *   说明: 车牌号
     */
    private String licenseno;

    /**
     *   字段: comment
     *   说明: 备注
     */
    private String comment;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     *   字段: tag
     *   说明: 标签
     */
    private String tag;

    /**
     *   字段: assembly_parts
     */
    private String assemblyParts;
}