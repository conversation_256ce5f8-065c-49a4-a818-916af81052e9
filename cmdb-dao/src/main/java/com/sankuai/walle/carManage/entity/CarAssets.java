package com.sankuai.walle.carManage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_assets
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarAssets {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: vin
     *   说明: vin号，对应资产SN
     */
    private String vin;

    /**
     *   字段: sn
     *   说明: 海鸥sn号
     */
    private String sn;

    /**
     *   字段: label
     *   说明: 资产标签号
     */
    private String label;

    /**
     *   字段: small_type
     *   说明: 实物小类，包含"硬盘","乘用车","房车","汽车","S项目整车"
     */
    private String smallType;

    /**
     *   字段: brand
     *   说明: 品牌
     */
    private String brand;

    /**
     *   字段: personMis
     *   说明: 资产责任人
     */
    private String personmis;

    /**
     *   字段: owner_department
     */
    private String ownerDepartment;

    /**
     *   字段: scrap
     *   说明: 是否报废
     */
    private Boolean scrap;

    /**
     *   字段: comment
     *   说明: 备注
     */
    private String comment;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     *   字段: car_size_type
     *   说明: 车辆类型
     */
    private Integer carSizeType;

    /**
     *   字段: car_belong_type
     *   说明: 车辆性质
     */
    private Integer carBelongType;

    /**
     *   字段: power_type
     *   说明: 车辆燃油种类
     */
    private Integer powerType;

    /**
     *   字段: refited
     *   说明: 是否改装
     */
    private Integer refited;

    /**
     *   字段: driving_permit_in_car
     *   说明: 行驶证是否随车
     */
    private String drivingPermitInCar;

    /**
     *   字段: car_owner
     *   说明: 车辆归属主体
     */
    private String carOwner;

    /**
     *   字段: indicate_owner
     *   说明: 指标归属主体
     */
    private String indicateOwner;

    /**
     *   字段: exec_word
     *   说明: 扩展字段
     */
    private String execWord;

    /**
     *   字段: scrap_confirm
     *   说明: 车管确认是否报废，如果车管确认未报废，则表示该车未报废
     */
    private Boolean scrapConfirm;
}