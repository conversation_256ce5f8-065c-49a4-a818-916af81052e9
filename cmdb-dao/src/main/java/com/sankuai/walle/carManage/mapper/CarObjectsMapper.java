package com.sankuai.walle.carManage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarObjectsMapper extends MybatisBaseMapper<CarObjects, CarObjectsExample, Long> {
    int batchInsert(@Param("list") List<CarObjects> list);
}