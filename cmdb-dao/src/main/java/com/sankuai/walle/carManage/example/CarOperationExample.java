package com.sankuai.walle.carManage.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CarOperationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CarOperationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CarOperationExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CarOperationExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CarOperationExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVinIsNull() {
            addCriterion("vin is null");
            return (Criteria) this;
        }

        public Criteria andVinIsNotNull() {
            addCriterion("vin is not null");
            return (Criteria) this;
        }

        public Criteria andVinEqualTo(String value) {
            addCriterion("vin =", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotEqualTo(String value) {
            addCriterion("vin <>", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThan(String value) {
            addCriterion("vin >", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThanOrEqualTo(String value) {
            addCriterion("vin >=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThan(String value) {
            addCriterion("vin <", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThanOrEqualTo(String value) {
            addCriterion("vin <=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLike(String value) {
            addCriterion("vin like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotLike(String value) {
            addCriterion("vin not like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinIn(List<String> values) {
            addCriterion("vin in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotIn(List<String> values) {
            addCriterion("vin not in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinBetween(String value1, String value2) {
            addCriterion("vin between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotBetween(String value1, String value2) {
            addCriterion("vin not between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andDistrictIsNull() {
            addCriterion("district is null");
            return (Criteria) this;
        }

        public Criteria andDistrictIsNotNull() {
            addCriterion("district is not null");
            return (Criteria) this;
        }

        public Criteria andDistrictEqualTo(String value) {
            addCriterion("district =", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotEqualTo(String value) {
            addCriterion("district <>", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictGreaterThan(String value) {
            addCriterion("district >", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictGreaterThanOrEqualTo(String value) {
            addCriterion("district >=", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictLessThan(String value) {
            addCriterion("district <", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictLessThanOrEqualTo(String value) {
            addCriterion("district <=", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictLike(String value) {
            addCriterion("district like", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotLike(String value) {
            addCriterion("district not like", value, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictIn(List<String> values) {
            addCriterion("district in", values, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotIn(List<String> values) {
            addCriterion("district not in", values, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictBetween(String value1, String value2) {
            addCriterion("district between", value1, value2, "district");
            return (Criteria) this;
        }

        public Criteria andDistrictNotBetween(String value1, String value2) {
            addCriterion("district not between", value1, value2, "district");
            return (Criteria) this;
        }

        public Criteria andAreaIsNull() {
            addCriterion("area is null");
            return (Criteria) this;
        }

        public Criteria andAreaIsNotNull() {
            addCriterion("area is not null");
            return (Criteria) this;
        }

        public Criteria andAreaEqualTo(String value) {
            addCriterion("area =", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotEqualTo(String value) {
            addCriterion("area <>", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThan(String value) {
            addCriterion("area >", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThanOrEqualTo(String value) {
            addCriterion("area >=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThan(String value) {
            addCriterion("area <", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThanOrEqualTo(String value) {
            addCriterion("area <=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLike(String value) {
            addCriterion("area like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotLike(String value) {
            addCriterion("area not like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaIn(List<String> values) {
            addCriterion("area in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotIn(List<String> values) {
            addCriterion("area not in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaBetween(String value1, String value2) {
            addCriterion("area between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotBetween(String value1, String value2) {
            addCriterion("area not between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetIsNull() {
            addCriterion("car_used_target is null");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetIsNotNull() {
            addCriterion("car_used_target is not null");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetEqualTo(String value) {
            addCriterion("car_used_target =", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotEqualTo(String value) {
            addCriterion("car_used_target <>", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetGreaterThan(String value) {
            addCriterion("car_used_target >", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetGreaterThanOrEqualTo(String value) {
            addCriterion("car_used_target >=", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetLessThan(String value) {
            addCriterion("car_used_target <", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetLessThanOrEqualTo(String value) {
            addCriterion("car_used_target <=", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetLike(String value) {
            addCriterion("car_used_target like", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotLike(String value) {
            addCriterion("car_used_target not like", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetIn(List<String> values) {
            addCriterion("car_used_target in", values, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotIn(List<String> values) {
            addCriterion("car_used_target not in", values, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetBetween(String value1, String value2) {
            addCriterion("car_used_target between", value1, value2, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotBetween(String value1, String value2) {
            addCriterion("car_used_target not between", value1, value2, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCommentIsNull() {
            addCriterion("`comment` is null");
            return (Criteria) this;
        }

        public Criteria andCommentIsNotNull() {
            addCriterion("`comment` is not null");
            return (Criteria) this;
        }

        public Criteria andCommentEqualTo(String value) {
            addCriterion("`comment` =", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotEqualTo(String value) {
            addCriterion("`comment` <>", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThan(String value) {
            addCriterion("`comment` >", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThanOrEqualTo(String value) {
            addCriterion("`comment` >=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThan(String value) {
            addCriterion("`comment` <", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThanOrEqualTo(String value) {
            addCriterion("`comment` <=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLike(String value) {
            addCriterion("`comment` like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotLike(String value) {
            addCriterion("`comment` not like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentIn(List<String> values) {
            addCriterion("`comment` in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotIn(List<String> values) {
            addCriterion("`comment` not in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentBetween(String value1, String value2) {
            addCriterion("`comment` between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotBetween(String value1, String value2) {
            addCriterion("`comment` not between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNull() {
            addCriterion("exec_word is null");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNotNull() {
            addCriterion("exec_word is not null");
            return (Criteria) this;
        }

        public Criteria andExecWordEqualTo(String value) {
            addCriterion("exec_word =", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotEqualTo(String value) {
            addCriterion("exec_word <>", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThan(String value) {
            addCriterion("exec_word >", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThanOrEqualTo(String value) {
            addCriterion("exec_word >=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThan(String value) {
            addCriterion("exec_word <", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThanOrEqualTo(String value) {
            addCriterion("exec_word <=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLike(String value) {
            addCriterion("exec_word like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotLike(String value) {
            addCriterion("exec_word not like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordIn(List<String> values) {
            addCriterion("exec_word in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotIn(List<String> values) {
            addCriterion("exec_word not in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordBetween(String value1, String value2) {
            addCriterion("exec_word between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotBetween(String value1, String value2) {
            addCriterion("exec_word not between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andAreaStrIsNull() {
            addCriterion("area_str is null");
            return (Criteria) this;
        }

        public Criteria andAreaStrIsNotNull() {
            addCriterion("area_str is not null");
            return (Criteria) this;
        }

        public Criteria andAreaStrEqualTo(String value) {
            addCriterion("area_str =", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrNotEqualTo(String value) {
            addCriterion("area_str <>", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrGreaterThan(String value) {
            addCriterion("area_str >", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrGreaterThanOrEqualTo(String value) {
            addCriterion("area_str >=", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrLessThan(String value) {
            addCriterion("area_str <", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrLessThanOrEqualTo(String value) {
            addCriterion("area_str <=", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrLike(String value) {
            addCriterion("area_str like", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrNotLike(String value) {
            addCriterion("area_str not like", value, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrIn(List<String> values) {
            addCriterion("area_str in", values, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrNotIn(List<String> values) {
            addCriterion("area_str not in", values, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrBetween(String value1, String value2) {
            addCriterion("area_str between", value1, value2, "areaStr");
            return (Criteria) this;
        }

        public Criteria andAreaStrNotBetween(String value1, String value2) {
            addCriterion("area_str not between", value1, value2, "areaStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrIsNull() {
            addCriterion("car_used_target_str is null");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrIsNotNull() {
            addCriterion("car_used_target_str is not null");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrEqualTo(String value) {
            addCriterion("car_used_target_str =", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrNotEqualTo(String value) {
            addCriterion("car_used_target_str <>", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrGreaterThan(String value) {
            addCriterion("car_used_target_str >", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrGreaterThanOrEqualTo(String value) {
            addCriterion("car_used_target_str >=", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrLessThan(String value) {
            addCriterion("car_used_target_str <", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrLessThanOrEqualTo(String value) {
            addCriterion("car_used_target_str <=", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrLike(String value) {
            addCriterion("car_used_target_str like", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrNotLike(String value) {
            addCriterion("car_used_target_str not like", value, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrIn(List<String> values) {
            addCriterion("car_used_target_str in", values, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrNotIn(List<String> values) {
            addCriterion("car_used_target_str not in", values, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrBetween(String value1, String value2) {
            addCriterion("car_used_target_str between", value1, value2, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetStrNotBetween(String value1, String value2) {
            addCriterion("car_used_target_str not between", value1, value2, "carUsedTargetStr");
            return (Criteria) this;
        }

        public Criteria andUsedTargetIsNull() {
            addCriterion("used_target is null");
            return (Criteria) this;
        }

        public Criteria andUsedTargetIsNotNull() {
            addCriterion("used_target is not null");
            return (Criteria) this;
        }

        public Criteria andUsedTargetEqualTo(String value) {
            addCriterion("used_target =", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetNotEqualTo(String value) {
            addCriterion("used_target <>", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetGreaterThan(String value) {
            addCriterion("used_target >", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetGreaterThanOrEqualTo(String value) {
            addCriterion("used_target >=", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetLessThan(String value) {
            addCriterion("used_target <", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetLessThanOrEqualTo(String value) {
            addCriterion("used_target <=", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetLike(String value) {
            addCriterion("used_target like", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetNotLike(String value) {
            addCriterion("used_target not like", value, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetIn(List<String> values) {
            addCriterion("used_target in", values, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetNotIn(List<String> values) {
            addCriterion("used_target not in", values, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetBetween(String value1, String value2) {
            addCriterion("used_target between", value1, value2, "usedTarget");
            return (Criteria) this;
        }

        public Criteria andUsedTargetNotBetween(String value1, String value2) {
            addCriterion("used_target not between", value1, value2, "usedTarget");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}