package com.sankuai.walle.carManage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.carManage.example.CarOperationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarOperationMapper extends MybatisBaseMapper<CarOperation, CarOperationExample, Long> {
    int batchInsert(@Param("list") List<CarOperation> list);
}