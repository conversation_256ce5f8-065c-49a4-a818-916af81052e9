package com.sankuai.walle.carManage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: vehicle_model_attributes
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VehicleModelAttributes {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Long id;

    /**
     *   字段: attribute_name
     *   说明: 属性名称
     */
    private String attributeName;

    /**
     *   字段: attribute_value
     *   说明: 属性值
     */
    private String attributeValue;

    /**
     *   字段: editor
     *   说明: 编辑人
     */
    private String editor;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: is_deleted
     *   说明: 是否删除[0-未删除|1-已删除]
     */
    private Boolean isDeleted;

    /**
     *   字段: car_type
     *   说明: 车辆类型
     */
    private String carType;
}