package com.sankuai.walle.carManage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_operation
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarOperation {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: vin
     *   说明: vin号，对应资产SN
     */
    private String vin;

    /**
     *   字段: city
     *   说明: 城市
     */
    private String city;

    /**
     *   字段: district
     *   说明: 行政区
     */
    private String district;

    /**
     *   字段: area
     *   说明: 运营区域
     */
    private String area;

    /**
     *   字段: car_used_target
     *   说明: 车辆用途
     */
    private String carUsedTarget;

    /**
     *   字段: comment
     *   说明: 备注
     */
    private String comment;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     *   字段: exec_word
     *   说明: 扩展字段
     */
    private String execWord;

    /**
     *   字段: area_str
     *   说明: 字符串用车场地
     */
    private String areaStr;

    /**
     *   字段: car_used_target_str
     *   说明: 字符串用车目的
     */
    private String carUsedTargetStr;

    /**
     *   字段: used_target
     *   说明: 车辆用途，通过位置计算获得
     */
    private String usedTarget;
}