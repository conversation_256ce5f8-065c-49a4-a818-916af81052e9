package com.sankuai.walle.carManage.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CarAssetsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CarAssetsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CarAssetsExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CarAssetsExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CarAssetsExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVinIsNull() {
            addCriterion("vin is null");
            return (Criteria) this;
        }

        public Criteria andVinIsNotNull() {
            addCriterion("vin is not null");
            return (Criteria) this;
        }

        public Criteria andVinEqualTo(String value) {
            addCriterion("vin =", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotEqualTo(String value) {
            addCriterion("vin <>", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThan(String value) {
            addCriterion("vin >", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThanOrEqualTo(String value) {
            addCriterion("vin >=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThan(String value) {
            addCriterion("vin <", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThanOrEqualTo(String value) {
            addCriterion("vin <=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLike(String value) {
            addCriterion("vin like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotLike(String value) {
            addCriterion("vin not like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinIn(List<String> values) {
            addCriterion("vin in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotIn(List<String> values) {
            addCriterion("vin not in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinBetween(String value1, String value2) {
            addCriterion("vin between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotBetween(String value1, String value2) {
            addCriterion("vin not between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("`label` is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("`label` is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("`label` =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("`label` <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("`label` >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("`label` >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("`label` <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("`label` <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("`label` like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("`label` not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("`label` in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("`label` not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("`label` between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("`label` not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andSmallTypeIsNull() {
            addCriterion("small_type is null");
            return (Criteria) this;
        }

        public Criteria andSmallTypeIsNotNull() {
            addCriterion("small_type is not null");
            return (Criteria) this;
        }

        public Criteria andSmallTypeEqualTo(String value) {
            addCriterion("small_type =", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotEqualTo(String value) {
            addCriterion("small_type <>", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeGreaterThan(String value) {
            addCriterion("small_type >", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeGreaterThanOrEqualTo(String value) {
            addCriterion("small_type >=", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeLessThan(String value) {
            addCriterion("small_type <", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeLessThanOrEqualTo(String value) {
            addCriterion("small_type <=", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeLike(String value) {
            addCriterion("small_type like", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotLike(String value) {
            addCriterion("small_type not like", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeIn(List<String> values) {
            addCriterion("small_type in", values, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotIn(List<String> values) {
            addCriterion("small_type not in", values, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeBetween(String value1, String value2) {
            addCriterion("small_type between", value1, value2, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotBetween(String value1, String value2) {
            addCriterion("small_type not between", value1, value2, "smallType");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andPersonmisIsNull() {
            addCriterion("personMis is null");
            return (Criteria) this;
        }

        public Criteria andPersonmisIsNotNull() {
            addCriterion("personMis is not null");
            return (Criteria) this;
        }

        public Criteria andPersonmisEqualTo(String value) {
            addCriterion("personMis =", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisNotEqualTo(String value) {
            addCriterion("personMis <>", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisGreaterThan(String value) {
            addCriterion("personMis >", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisGreaterThanOrEqualTo(String value) {
            addCriterion("personMis >=", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisLessThan(String value) {
            addCriterion("personMis <", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisLessThanOrEqualTo(String value) {
            addCriterion("personMis <=", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisLike(String value) {
            addCriterion("personMis like", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisNotLike(String value) {
            addCriterion("personMis not like", value, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisIn(List<String> values) {
            addCriterion("personMis in", values, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisNotIn(List<String> values) {
            addCriterion("personMis not in", values, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisBetween(String value1, String value2) {
            addCriterion("personMis between", value1, value2, "personmis");
            return (Criteria) this;
        }

        public Criteria andPersonmisNotBetween(String value1, String value2) {
            addCriterion("personMis not between", value1, value2, "personmis");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIsNull() {
            addCriterion("owner_department is null");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIsNotNull() {
            addCriterion("owner_department is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentEqualTo(String value) {
            addCriterion("owner_department =", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotEqualTo(String value) {
            addCriterion("owner_department <>", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThan(String value) {
            addCriterion("owner_department >", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("owner_department >=", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThan(String value) {
            addCriterion("owner_department <", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThanOrEqualTo(String value) {
            addCriterion("owner_department <=", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLike(String value) {
            addCriterion("owner_department like", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotLike(String value) {
            addCriterion("owner_department not like", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIn(List<String> values) {
            addCriterion("owner_department in", values, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotIn(List<String> values) {
            addCriterion("owner_department not in", values, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentBetween(String value1, String value2) {
            addCriterion("owner_department between", value1, value2, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotBetween(String value1, String value2) {
            addCriterion("owner_department not between", value1, value2, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andScrapIsNull() {
            addCriterion("scrap is null");
            return (Criteria) this;
        }

        public Criteria andScrapIsNotNull() {
            addCriterion("scrap is not null");
            return (Criteria) this;
        }

        public Criteria andScrapEqualTo(Boolean value) {
            addCriterion("scrap =", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotEqualTo(Boolean value) {
            addCriterion("scrap <>", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapGreaterThan(Boolean value) {
            addCriterion("scrap >", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapGreaterThanOrEqualTo(Boolean value) {
            addCriterion("scrap >=", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapLessThan(Boolean value) {
            addCriterion("scrap <", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapLessThanOrEqualTo(Boolean value) {
            addCriterion("scrap <=", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapIn(List<Boolean> values) {
            addCriterion("scrap in", values, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotIn(List<Boolean> values) {
            addCriterion("scrap not in", values, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap between", value1, value2, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap not between", value1, value2, "scrap");
            return (Criteria) this;
        }

        public Criteria andCommentIsNull() {
            addCriterion("`comment` is null");
            return (Criteria) this;
        }

        public Criteria andCommentIsNotNull() {
            addCriterion("`comment` is not null");
            return (Criteria) this;
        }

        public Criteria andCommentEqualTo(String value) {
            addCriterion("`comment` =", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotEqualTo(String value) {
            addCriterion("`comment` <>", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThan(String value) {
            addCriterion("`comment` >", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThanOrEqualTo(String value) {
            addCriterion("`comment` >=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThan(String value) {
            addCriterion("`comment` <", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThanOrEqualTo(String value) {
            addCriterion("`comment` <=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLike(String value) {
            addCriterion("`comment` like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotLike(String value) {
            addCriterion("`comment` not like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentIn(List<String> values) {
            addCriterion("`comment` in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotIn(List<String> values) {
            addCriterion("`comment` not in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentBetween(String value1, String value2) {
            addCriterion("`comment` between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotBetween(String value1, String value2) {
            addCriterion("`comment` not between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeIsNull() {
            addCriterion("car_size_type is null");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeIsNotNull() {
            addCriterion("car_size_type is not null");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeEqualTo(Integer value) {
            addCriterion("car_size_type =", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeNotEqualTo(Integer value) {
            addCriterion("car_size_type <>", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeGreaterThan(Integer value) {
            addCriterion("car_size_type >", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("car_size_type >=", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeLessThan(Integer value) {
            addCriterion("car_size_type <", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("car_size_type <=", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeIn(List<Integer> values) {
            addCriterion("car_size_type in", values, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeNotIn(List<Integer> values) {
            addCriterion("car_size_type not in", values, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeBetween(Integer value1, Integer value2) {
            addCriterion("car_size_type between", value1, value2, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("car_size_type not between", value1, value2, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeIsNull() {
            addCriterion("car_belong_type is null");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeIsNotNull() {
            addCriterion("car_belong_type is not null");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeEqualTo(Integer value) {
            addCriterion("car_belong_type =", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeNotEqualTo(Integer value) {
            addCriterion("car_belong_type <>", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeGreaterThan(Integer value) {
            addCriterion("car_belong_type >", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("car_belong_type >=", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeLessThan(Integer value) {
            addCriterion("car_belong_type <", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeLessThanOrEqualTo(Integer value) {
            addCriterion("car_belong_type <=", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeIn(List<Integer> values) {
            addCriterion("car_belong_type in", values, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeNotIn(List<Integer> values) {
            addCriterion("car_belong_type not in", values, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeBetween(Integer value1, Integer value2) {
            addCriterion("car_belong_type between", value1, value2, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("car_belong_type not between", value1, value2, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIsNull() {
            addCriterion("power_type is null");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIsNotNull() {
            addCriterion("power_type is not null");
            return (Criteria) this;
        }

        public Criteria andPowerTypeEqualTo(Integer value) {
            addCriterion("power_type =", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotEqualTo(Integer value) {
            addCriterion("power_type <>", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeGreaterThan(Integer value) {
            addCriterion("power_type >", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("power_type >=", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeLessThan(Integer value) {
            addCriterion("power_type <", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("power_type <=", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIn(List<Integer> values) {
            addCriterion("power_type in", values, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotIn(List<Integer> values) {
            addCriterion("power_type not in", values, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeBetween(Integer value1, Integer value2) {
            addCriterion("power_type between", value1, value2, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("power_type not between", value1, value2, "powerType");
            return (Criteria) this;
        }

        public Criteria andRefitedIsNull() {
            addCriterion("refited is null");
            return (Criteria) this;
        }

        public Criteria andRefitedIsNotNull() {
            addCriterion("refited is not null");
            return (Criteria) this;
        }

        public Criteria andRefitedEqualTo(Integer value) {
            addCriterion("refited =", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedNotEqualTo(Integer value) {
            addCriterion("refited <>", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedGreaterThan(Integer value) {
            addCriterion("refited >", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedGreaterThanOrEqualTo(Integer value) {
            addCriterion("refited >=", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedLessThan(Integer value) {
            addCriterion("refited <", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedLessThanOrEqualTo(Integer value) {
            addCriterion("refited <=", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedIn(List<Integer> values) {
            addCriterion("refited in", values, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedNotIn(List<Integer> values) {
            addCriterion("refited not in", values, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedBetween(Integer value1, Integer value2) {
            addCriterion("refited between", value1, value2, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedNotBetween(Integer value1, Integer value2) {
            addCriterion("refited not between", value1, value2, "refited");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarIsNull() {
            addCriterion("driving_permit_in_car is null");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarIsNotNull() {
            addCriterion("driving_permit_in_car is not null");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarEqualTo(String value) {
            addCriterion("driving_permit_in_car =", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotEqualTo(String value) {
            addCriterion("driving_permit_in_car <>", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarGreaterThan(String value) {
            addCriterion("driving_permit_in_car >", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarGreaterThanOrEqualTo(String value) {
            addCriterion("driving_permit_in_car >=", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarLessThan(String value) {
            addCriterion("driving_permit_in_car <", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarLessThanOrEqualTo(String value) {
            addCriterion("driving_permit_in_car <=", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarLike(String value) {
            addCriterion("driving_permit_in_car like", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotLike(String value) {
            addCriterion("driving_permit_in_car not like", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarIn(List<String> values) {
            addCriterion("driving_permit_in_car in", values, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotIn(List<String> values) {
            addCriterion("driving_permit_in_car not in", values, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarBetween(String value1, String value2) {
            addCriterion("driving_permit_in_car between", value1, value2, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotBetween(String value1, String value2) {
            addCriterion("driving_permit_in_car not between", value1, value2, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andCarOwnerIsNull() {
            addCriterion("car_owner is null");
            return (Criteria) this;
        }

        public Criteria andCarOwnerIsNotNull() {
            addCriterion("car_owner is not null");
            return (Criteria) this;
        }

        public Criteria andCarOwnerEqualTo(String value) {
            addCriterion("car_owner =", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotEqualTo(String value) {
            addCriterion("car_owner <>", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerGreaterThan(String value) {
            addCriterion("car_owner >", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("car_owner >=", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerLessThan(String value) {
            addCriterion("car_owner <", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerLessThanOrEqualTo(String value) {
            addCriterion("car_owner <=", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerLike(String value) {
            addCriterion("car_owner like", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotLike(String value) {
            addCriterion("car_owner not like", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerIn(List<String> values) {
            addCriterion("car_owner in", values, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotIn(List<String> values) {
            addCriterion("car_owner not in", values, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerBetween(String value1, String value2) {
            addCriterion("car_owner between", value1, value2, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotBetween(String value1, String value2) {
            addCriterion("car_owner not between", value1, value2, "carOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerIsNull() {
            addCriterion("indicate_owner is null");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerIsNotNull() {
            addCriterion("indicate_owner is not null");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerEqualTo(String value) {
            addCriterion("indicate_owner =", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotEqualTo(String value) {
            addCriterion("indicate_owner <>", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerGreaterThan(String value) {
            addCriterion("indicate_owner >", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("indicate_owner >=", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerLessThan(String value) {
            addCriterion("indicate_owner <", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerLessThanOrEqualTo(String value) {
            addCriterion("indicate_owner <=", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerLike(String value) {
            addCriterion("indicate_owner like", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotLike(String value) {
            addCriterion("indicate_owner not like", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerIn(List<String> values) {
            addCriterion("indicate_owner in", values, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotIn(List<String> values) {
            addCriterion("indicate_owner not in", values, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerBetween(String value1, String value2) {
            addCriterion("indicate_owner between", value1, value2, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotBetween(String value1, String value2) {
            addCriterion("indicate_owner not between", value1, value2, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNull() {
            addCriterion("exec_word is null");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNotNull() {
            addCriterion("exec_word is not null");
            return (Criteria) this;
        }

        public Criteria andExecWordEqualTo(String value) {
            addCriterion("exec_word =", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotEqualTo(String value) {
            addCriterion("exec_word <>", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThan(String value) {
            addCriterion("exec_word >", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThanOrEqualTo(String value) {
            addCriterion("exec_word >=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThan(String value) {
            addCriterion("exec_word <", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThanOrEqualTo(String value) {
            addCriterion("exec_word <=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLike(String value) {
            addCriterion("exec_word like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotLike(String value) {
            addCriterion("exec_word not like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordIn(List<String> values) {
            addCriterion("exec_word in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotIn(List<String> values) {
            addCriterion("exec_word not in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordBetween(String value1, String value2) {
            addCriterion("exec_word between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotBetween(String value1, String value2) {
            addCriterion("exec_word not between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmIsNull() {
            addCriterion("scrap_confirm is null");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmIsNotNull() {
            addCriterion("scrap_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmEqualTo(Boolean value) {
            addCriterion("scrap_confirm =", value, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmNotEqualTo(Boolean value) {
            addCriterion("scrap_confirm <>", value, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmGreaterThan(Boolean value) {
            addCriterion("scrap_confirm >", value, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmGreaterThanOrEqualTo(Boolean value) {
            addCriterion("scrap_confirm >=", value, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmLessThan(Boolean value) {
            addCriterion("scrap_confirm <", value, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmLessThanOrEqualTo(Boolean value) {
            addCriterion("scrap_confirm <=", value, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmIn(List<Boolean> values) {
            addCriterion("scrap_confirm in", values, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmNotIn(List<Boolean> values) {
            addCriterion("scrap_confirm not in", values, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap_confirm between", value1, value2, "scrapConfirm");
            return (Criteria) this;
        }

        public Criteria andScrapConfirmNotBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap_confirm not between", value1, value2, "scrapConfirm");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}