package com.sankuai.walle.carManage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_selects
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarSelects {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 选项名称
     */
    private String name;

    /**
     *   字段: type
     *   说明: 选项唯一标识
     */
    private String type;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: belong
     *   说明: 选项的键
     */
    private String belong;

    /**
     *   字段: father_id
     *   说明: 父节点id,0代表当前节点没有父节点
     */
    private Long fatherId;

    /**
     *   字段: editor
     *   说明: 编辑人
     */
    private String editor;

    /**
     *   字段: is_deleted
     *   说明: 是否逻辑删除,0表示否,1表示是
     */
    private Boolean isDeleted;
}