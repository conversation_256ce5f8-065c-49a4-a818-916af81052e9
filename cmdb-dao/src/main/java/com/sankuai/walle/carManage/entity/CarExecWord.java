package com.sankuai.walle.carManage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_exec_word
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarExecWord {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: vin
     *   说明: vin号，对应资产SN
     */
    private String vin;

    /**
     *   字段: car_assets_exec_words
     *   说明: 资产扩展字段
     */
    private String carAssetsExecWords;

    /**
     *   字段: car_operation_exec_words
     *   说明: 运营扩展字段
     */
    private String carOperationExecWords;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}