package com.sankuai.walle.carManage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.example.CarExecWordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarExecWordMapper extends MybatisBaseMapper<CarExecWord, CarExecWordExample, Long> {
    int batchInsert(@Param("list") List<CarExecWord> list);
}