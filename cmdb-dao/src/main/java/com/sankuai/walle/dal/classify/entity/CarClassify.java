package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_classify
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Deprecated
public class CarClassify {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 分类名称
     */
    private String name;

    /**
     *   字段: classify_desc
     *   说明: 简介
     */
    private String classifyDesc;

    /**
     *   字段: first_car_model
     *   说明: 一级车型
     */
    private Long firstCarModel;

    /**
     *   字段: second_car_model
     *   说明: 二级车型
     */
    private Long secondCarModel;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}