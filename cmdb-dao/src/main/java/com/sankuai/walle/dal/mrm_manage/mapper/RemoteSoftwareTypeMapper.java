package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteSoftwareType;
import com.sankuai.walle.dal.mrm_manage.example.RemoteSoftwareTypeExample;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RemoteSoftwareTypeMapper extends MybatisBLOBsMapper<RemoteSoftwareType, RemoteSoftwareTypeExample, Long> {
    int batchInsert(@Param("list") List<RemoteSoftwareType> list);
}