package com.sankuai.walle.dal.walle_data_center.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: tmp_bds_vehicle_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TmpBdsVehicleInfo {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Long id;

    /**
     *   字段: vehicle_id
     *   说明: 车辆ID
     */
    private String vehicleId;

    /**
     *   字段: vehicle_device_id
     *   说明: 车辆设备ID
     */
    private String vehicleDeviceId;

    /**
     *   字段: name
     *   说明: 车辆名称
     */
    private String name;

    /**
     *   字段: brand
     *   说明: 品牌
     */
    private String brand;

    /**
     *   字段: type
     *   说明: 车辆型号
     */
    private String type;

    /**
     *   字段: engine_number
     *   说明: 发动机号
     */
    private String engineNumber;

    /**
     *   字段: license_number
     *   说明: 车牌号
     */
    private String licenseNumber;

    /**
     *   字段: year
     *   说明: 生产年份
     */
    private Integer year;

    /**
     *   字段: power_type
     *   说明: 能源类型
     */
    private Byte powerType;

    /**
     *   字段: operation_state
     *   说明: 车辆运营状态[0:不在线|1:在线|2:故障]
     */
    private Byte operationState;

    /**
     *   字段: status
     *   说明: 状态[0:不可用|1:可用|2:已删除]
     */
    private Byte status;

    /**
     *   字段: first_add_time
     *   说明: 首次添加时间
     */
    private Date firstAddTime;

    /**
     *   字段: last_update_time
     *   说明: 最近更新时间
     */
    private Date lastUpdateTime;

    /**
     *   字段: vid
     *   说明: VID
     */
    private String vid;

    /**
     *   字段: owner
     *   说明: 负责人
     */
    private String owner;

    /**
     *   字段: product
     *   说明: 产品线
     */
    private Integer product;
}