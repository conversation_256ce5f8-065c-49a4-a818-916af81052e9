package com.sankuai.walle.dal.mrm_manage.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BizHandlePolicyInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public BizHandlePolicyInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public BizHandlePolicyInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public BizHandlePolicyInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public BizHandlePolicyInfoExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBizTableNameIsNull() {
            addCriterion("biz_table_name is null");
            return (Criteria) this;
        }

        public Criteria andBizTableNameIsNotNull() {
            addCriterion("biz_table_name is not null");
            return (Criteria) this;
        }

        public Criteria andBizTableNameEqualTo(String value) {
            addCriterion("biz_table_name =", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameNotEqualTo(String value) {
            addCriterion("biz_table_name <>", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameGreaterThan(String value) {
            addCriterion("biz_table_name >", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameGreaterThanOrEqualTo(String value) {
            addCriterion("biz_table_name >=", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameLessThan(String value) {
            addCriterion("biz_table_name <", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameLessThanOrEqualTo(String value) {
            addCriterion("biz_table_name <=", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameLike(String value) {
            addCriterion("biz_table_name like", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameNotLike(String value) {
            addCriterion("biz_table_name not like", value, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameIn(List<String> values) {
            addCriterion("biz_table_name in", values, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameNotIn(List<String> values) {
            addCriterion("biz_table_name not in", values, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameBetween(String value1, String value2) {
            addCriterion("biz_table_name between", value1, value2, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableNameNotBetween(String value1, String value2) {
            addCriterion("biz_table_name not between", value1, value2, "bizTableName");
            return (Criteria) this;
        }

        public Criteria andBizTableDescIsNull() {
            addCriterion("biz_table_desc is null");
            return (Criteria) this;
        }

        public Criteria andBizTableDescIsNotNull() {
            addCriterion("biz_table_desc is not null");
            return (Criteria) this;
        }

        public Criteria andBizTableDescEqualTo(String value) {
            addCriterion("biz_table_desc =", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescNotEqualTo(String value) {
            addCriterion("biz_table_desc <>", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescGreaterThan(String value) {
            addCriterion("biz_table_desc >", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescGreaterThanOrEqualTo(String value) {
            addCriterion("biz_table_desc >=", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescLessThan(String value) {
            addCriterion("biz_table_desc <", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescLessThanOrEqualTo(String value) {
            addCriterion("biz_table_desc <=", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescLike(String value) {
            addCriterion("biz_table_desc like", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescNotLike(String value) {
            addCriterion("biz_table_desc not like", value, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescIn(List<String> values) {
            addCriterion("biz_table_desc in", values, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescNotIn(List<String> values) {
            addCriterion("biz_table_desc not in", values, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescBetween(String value1, String value2) {
            addCriterion("biz_table_desc between", value1, value2, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andBizTableDescNotBetween(String value1, String value2) {
            addCriterion("biz_table_desc not between", value1, value2, "bizTableDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyNameIsNull() {
            addCriterion("policy_name is null");
            return (Criteria) this;
        }

        public Criteria andPolicyNameIsNotNull() {
            addCriterion("policy_name is not null");
            return (Criteria) this;
        }

        public Criteria andPolicyNameEqualTo(String value) {
            addCriterion("policy_name =", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameNotEqualTo(String value) {
            addCriterion("policy_name <>", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameGreaterThan(String value) {
            addCriterion("policy_name >", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameGreaterThanOrEqualTo(String value) {
            addCriterion("policy_name >=", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameLessThan(String value) {
            addCriterion("policy_name <", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameLessThanOrEqualTo(String value) {
            addCriterion("policy_name <=", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameLike(String value) {
            addCriterion("policy_name like", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameNotLike(String value) {
            addCriterion("policy_name not like", value, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameIn(List<String> values) {
            addCriterion("policy_name in", values, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameNotIn(List<String> values) {
            addCriterion("policy_name not in", values, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameBetween(String value1, String value2) {
            addCriterion("policy_name between", value1, value2, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyNameNotBetween(String value1, String value2) {
            addCriterion("policy_name not between", value1, value2, "policyName");
            return (Criteria) this;
        }

        public Criteria andPolicyDescIsNull() {
            addCriterion("policy_desc is null");
            return (Criteria) this;
        }

        public Criteria andPolicyDescIsNotNull() {
            addCriterion("policy_desc is not null");
            return (Criteria) this;
        }

        public Criteria andPolicyDescEqualTo(String value) {
            addCriterion("policy_desc =", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescNotEqualTo(String value) {
            addCriterion("policy_desc <>", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescGreaterThan(String value) {
            addCriterion("policy_desc >", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescGreaterThanOrEqualTo(String value) {
            addCriterion("policy_desc >=", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescLessThan(String value) {
            addCriterion("policy_desc <", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescLessThanOrEqualTo(String value) {
            addCriterion("policy_desc <=", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescLike(String value) {
            addCriterion("policy_desc like", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescNotLike(String value) {
            addCriterion("policy_desc not like", value, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescIn(List<String> values) {
            addCriterion("policy_desc in", values, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescNotIn(List<String> values) {
            addCriterion("policy_desc not in", values, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescBetween(String value1, String value2) {
            addCriterion("policy_desc between", value1, value2, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyDescNotBetween(String value1, String value2) {
            addCriterion("policy_desc not between", value1, value2, "policyDesc");
            return (Criteria) this;
        }

        public Criteria andPolicyValueIsNull() {
            addCriterion("policy_value is null");
            return (Criteria) this;
        }

        public Criteria andPolicyValueIsNotNull() {
            addCriterion("policy_value is not null");
            return (Criteria) this;
        }

        public Criteria andPolicyValueEqualTo(String value) {
            addCriterion("policy_value =", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueNotEqualTo(String value) {
            addCriterion("policy_value <>", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueGreaterThan(String value) {
            addCriterion("policy_value >", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueGreaterThanOrEqualTo(String value) {
            addCriterion("policy_value >=", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueLessThan(String value) {
            addCriterion("policy_value <", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueLessThanOrEqualTo(String value) {
            addCriterion("policy_value <=", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueLike(String value) {
            addCriterion("policy_value like", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueNotLike(String value) {
            addCriterion("policy_value not like", value, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueIn(List<String> values) {
            addCriterion("policy_value in", values, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueNotIn(List<String> values) {
            addCriterion("policy_value not in", values, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueBetween(String value1, String value2) {
            addCriterion("policy_value between", value1, value2, "policyValue");
            return (Criteria) this;
        }

        public Criteria andPolicyValueNotBetween(String value1, String value2) {
            addCriterion("policy_value not between", value1, value2, "policyValue");
            return (Criteria) this;
        }

        public Criteria andHandleMethodIsNull() {
            addCriterion("handle_method is null");
            return (Criteria) this;
        }

        public Criteria andHandleMethodIsNotNull() {
            addCriterion("handle_method is not null");
            return (Criteria) this;
        }

        public Criteria andHandleMethodEqualTo(String value) {
            addCriterion("handle_method =", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodNotEqualTo(String value) {
            addCriterion("handle_method <>", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodGreaterThan(String value) {
            addCriterion("handle_method >", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodGreaterThanOrEqualTo(String value) {
            addCriterion("handle_method >=", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodLessThan(String value) {
            addCriterion("handle_method <", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodLessThanOrEqualTo(String value) {
            addCriterion("handle_method <=", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodLike(String value) {
            addCriterion("handle_method like", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodNotLike(String value) {
            addCriterion("handle_method not like", value, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodIn(List<String> values) {
            addCriterion("handle_method in", values, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodNotIn(List<String> values) {
            addCriterion("handle_method not in", values, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodBetween(String value1, String value2) {
            addCriterion("handle_method between", value1, value2, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andHandleMethodNotBetween(String value1, String value2) {
            addCriterion("handle_method not between", value1, value2, "handleMethod");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andIsEnableIsNull() {
            addCriterion("is_enable is null");
            return (Criteria) this;
        }

        public Criteria andIsEnableIsNotNull() {
            addCriterion("is_enable is not null");
            return (Criteria) this;
        }

        public Criteria andIsEnableEqualTo(Boolean value) {
            addCriterion("is_enable =", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableNotEqualTo(Boolean value) {
            addCriterion("is_enable <>", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableGreaterThan(Boolean value) {
            addCriterion("is_enable >", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_enable >=", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableLessThan(Boolean value) {
            addCriterion("is_enable <", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("is_enable <=", value, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableIn(List<Boolean> values) {
            addCriterion("is_enable in", values, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableNotIn(List<Boolean> values) {
            addCriterion("is_enable not in", values, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("is_enable between", value1, value2, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_enable not between", value1, value2, "isEnable");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}