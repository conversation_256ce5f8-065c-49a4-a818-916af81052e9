package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisSimpleBaseMapper;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import org.apache.ibatis.annotations.Param;

public interface CarConfigMyMapper  extends MybatisSimpleBaseMapper<CarConfig,Long>{
//    int batchInsert(@Param("list") List<CarConfig> list);
    Long getBigVersion(@Param("master_id") Long master_id);
    Integer getTaskTotal();
}