package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.dal.mrm_manage.example.TagsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TagsMapper extends MybatisBaseMapper<Tags, TagsExample, Long> {
    int batchInsert(@Param("list") List<Tags> list);
}