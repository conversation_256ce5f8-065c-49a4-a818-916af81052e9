package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.CarEquipment;
import com.sankuai.walle.dal.mrm_manage.example.CarEquipmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarEquipmentMapper extends MybatisBaseMapper<CarEquipment, CarEquipmentExample, Long> {
    int batchInsert(@Param("list") List<CarEquipment> list);
}