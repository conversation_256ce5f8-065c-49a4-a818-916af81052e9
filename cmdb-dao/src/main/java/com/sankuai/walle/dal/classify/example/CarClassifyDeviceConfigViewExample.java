package com.sankuai.walle.dal.classify.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CarClassifyDeviceConfigViewExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CarClassifyDeviceConfigViewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CarClassifyDeviceConfigViewExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CarClassifyDeviceConfigViewExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CarClassifyDeviceConfigViewExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdIsNull() {
            addCriterion("first_device_type_id is null");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdIsNotNull() {
            addCriterion("first_device_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdEqualTo(Long value) {
            addCriterion("first_device_type_id =", value, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdNotEqualTo(Long value) {
            addCriterion("first_device_type_id <>", value, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdGreaterThan(Long value) {
            addCriterion("first_device_type_id >", value, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("first_device_type_id >=", value, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdLessThan(Long value) {
            addCriterion("first_device_type_id <", value, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("first_device_type_id <=", value, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdIn(List<Long> values) {
            addCriterion("first_device_type_id in", values, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdNotIn(List<Long> values) {
            addCriterion("first_device_type_id not in", values, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdBetween(Long value1, Long value2) {
            addCriterion("first_device_type_id between", value1, value2, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andFirstDeviceTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("first_device_type_id not between", value1, value2, "firstDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdIsNull() {
            addCriterion("second_device_type_id is null");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdIsNotNull() {
            addCriterion("second_device_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdEqualTo(Long value) {
            addCriterion("second_device_type_id =", value, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdNotEqualTo(Long value) {
            addCriterion("second_device_type_id <>", value, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdGreaterThan(Long value) {
            addCriterion("second_device_type_id >", value, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("second_device_type_id >=", value, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdLessThan(Long value) {
            addCriterion("second_device_type_id <", value, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("second_device_type_id <=", value, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdIn(List<Long> values) {
            addCriterion("second_device_type_id in", values, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdNotIn(List<Long> values) {
            addCriterion("second_device_type_id not in", values, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdBetween(Long value1, Long value2) {
            addCriterion("second_device_type_id between", value1, value2, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andSecondDeviceTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("second_device_type_id not between", value1, value2, "secondDeviceTypeId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNull() {
            addCriterion("config_id is null");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNotNull() {
            addCriterion("config_id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigIdEqualTo(Long value) {
            addCriterion("config_id =", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotEqualTo(Long value) {
            addCriterion("config_id <>", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThan(Long value) {
            addCriterion("config_id >", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThanOrEqualTo(Long value) {
            addCriterion("config_id >=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThan(Long value) {
            addCriterion("config_id <", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThanOrEqualTo(Long value) {
            addCriterion("config_id <=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIn(List<Long> values) {
            addCriterion("config_id in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotIn(List<Long> values) {
            addCriterion("config_id not in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdBetween(Long value1, Long value2) {
            addCriterion("config_id between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotBetween(Long value1, Long value2) {
            addCriterion("config_id not between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdIsNull() {
            addCriterion("car_classify_id is null");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdIsNotNull() {
            addCriterion("car_classify_id is not null");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdEqualTo(Long value) {
            addCriterion("car_classify_id =", value, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdNotEqualTo(Long value) {
            addCriterion("car_classify_id <>", value, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdGreaterThan(Long value) {
            addCriterion("car_classify_id >", value, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("car_classify_id >=", value, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdLessThan(Long value) {
            addCriterion("car_classify_id <", value, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdLessThanOrEqualTo(Long value) {
            addCriterion("car_classify_id <=", value, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdIn(List<Long> values) {
            addCriterion("car_classify_id in", values, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdNotIn(List<Long> values) {
            addCriterion("car_classify_id not in", values, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdBetween(Long value1, Long value2) {
            addCriterion("car_classify_id between", value1, value2, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyIdNotBetween(Long value1, Long value2) {
            addCriterion("car_classify_id not between", value1, value2, "carClassifyId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdIsNull() {
            addCriterion("car_classify_third_model_id is null");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdIsNotNull() {
            addCriterion("car_classify_third_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdEqualTo(Long value) {
            addCriterion("car_classify_third_model_id =", value, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdNotEqualTo(Long value) {
            addCriterion("car_classify_third_model_id <>", value, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdGreaterThan(Long value) {
            addCriterion("car_classify_third_model_id >", value, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("car_classify_third_model_id >=", value, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdLessThan(Long value) {
            addCriterion("car_classify_third_model_id <", value, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdLessThanOrEqualTo(Long value) {
            addCriterion("car_classify_third_model_id <=", value, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdIn(List<Long> values) {
            addCriterion("car_classify_third_model_id in", values, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdNotIn(List<Long> values) {
            addCriterion("car_classify_third_model_id not in", values, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdBetween(Long value1, Long value2) {
            addCriterion("car_classify_third_model_id between", value1, value2, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andCarClassifyThirdModelIdNotBetween(Long value1, Long value2) {
            addCriterion("car_classify_third_model_id not between", value1, value2, "carClassifyThirdModelId");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}