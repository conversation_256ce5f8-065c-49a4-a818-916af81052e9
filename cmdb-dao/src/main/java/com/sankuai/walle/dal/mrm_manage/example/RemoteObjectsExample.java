package com.sankuai.walle.dal.mrm_manage.example;

import java.util.ArrayList;
import java.util.List;

public class RemoteObjectsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public RemoteObjectsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public RemoteObjectsExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public RemoteObjectsExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public RemoteObjectsExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andVinIsNull() {
            addCriterion("vin is null");
            return (Criteria) this;
        }

        public Criteria andVinIsNotNull() {
            addCriterion("vin is not null");
            return (Criteria) this;
        }

        public Criteria andVinEqualTo(String value) {
            addCriterion("vin =", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotEqualTo(String value) {
            addCriterion("vin <>", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThan(String value) {
            addCriterion("vin >", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThanOrEqualTo(String value) {
            addCriterion("vin >=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThan(String value) {
            addCriterion("vin <", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThanOrEqualTo(String value) {
            addCriterion("vin <=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLike(String value) {
            addCriterion("vin like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotLike(String value) {
            addCriterion("vin not like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinIn(List<String> values) {
            addCriterion("vin in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotIn(List<String> values) {
            addCriterion("vin not in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinBetween(String value1, String value2) {
            addCriterion("vin between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotBetween(String value1, String value2) {
            addCriterion("vin not between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIsNull() {
            addCriterion("license_no is null");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIsNotNull() {
            addCriterion("license_no is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseNoEqualTo(String value) {
            addCriterion("license_no =", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotEqualTo(String value) {
            addCriterion("license_no <>", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoGreaterThan(String value) {
            addCriterion("license_no >", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoGreaterThanOrEqualTo(String value) {
            addCriterion("license_no >=", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLessThan(String value) {
            addCriterion("license_no <", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLessThanOrEqualTo(String value) {
            addCriterion("license_no <=", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLike(String value) {
            addCriterion("license_no like", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotLike(String value) {
            addCriterion("license_no not like", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIn(List<String> values) {
            addCriterion("license_no in", values, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotIn(List<String> values) {
            addCriterion("license_no not in", values, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoBetween(String value1, String value2) {
            addCriterion("license_no between", value1, value2, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotBetween(String value1, String value2) {
            addCriterion("license_no not between", value1, value2, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLoginUserIsNull() {
            addCriterion("login_user is null");
            return (Criteria) this;
        }

        public Criteria andLoginUserIsNotNull() {
            addCriterion("login_user is not null");
            return (Criteria) this;
        }

        public Criteria andLoginUserEqualTo(String value) {
            addCriterion("login_user =", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserNotEqualTo(String value) {
            addCriterion("login_user <>", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserGreaterThan(String value) {
            addCriterion("login_user >", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserGreaterThanOrEqualTo(String value) {
            addCriterion("login_user >=", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserLessThan(String value) {
            addCriterion("login_user <", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserLessThanOrEqualTo(String value) {
            addCriterion("login_user <=", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserLike(String value) {
            addCriterion("login_user like", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserNotLike(String value) {
            addCriterion("login_user not like", value, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserIn(List<String> values) {
            addCriterion("login_user in", values, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserNotIn(List<String> values) {
            addCriterion("login_user not in", values, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserBetween(String value1, String value2) {
            addCriterion("login_user between", value1, value2, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginUserNotBetween(String value1, String value2) {
            addCriterion("login_user not between", value1, value2, "loginUser");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordIsNull() {
            addCriterion("login_password is null");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordIsNotNull() {
            addCriterion("login_password is not null");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordEqualTo(String value) {
            addCriterion("login_password =", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordNotEqualTo(String value) {
            addCriterion("login_password <>", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordGreaterThan(String value) {
            addCriterion("login_password >", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("login_password >=", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordLessThan(String value) {
            addCriterion("login_password <", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordLessThanOrEqualTo(String value) {
            addCriterion("login_password <=", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordLike(String value) {
            addCriterion("login_password like", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordNotLike(String value) {
            addCriterion("login_password not like", value, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordIn(List<String> values) {
            addCriterion("login_password in", values, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordNotIn(List<String> values) {
            addCriterion("login_password not in", values, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordBetween(String value1, String value2) {
            addCriterion("login_password between", value1, value2, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andLoginPasswordNotBetween(String value1, String value2) {
            addCriterion("login_password not between", value1, value2, "loginPassword");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdIsNull() {
            addCriterion("remote_object_type_id is null");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdIsNotNull() {
            addCriterion("remote_object_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdEqualTo(Long value) {
            addCriterion("remote_object_type_id =", value, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdNotEqualTo(Long value) {
            addCriterion("remote_object_type_id <>", value, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdGreaterThan(Long value) {
            addCriterion("remote_object_type_id >", value, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("remote_object_type_id >=", value, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdLessThan(Long value) {
            addCriterion("remote_object_type_id <", value, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("remote_object_type_id <=", value, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdIn(List<Long> values) {
            addCriterion("remote_object_type_id in", values, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdNotIn(List<Long> values) {
            addCriterion("remote_object_type_id not in", values, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdBetween(Long value1, Long value2) {
            addCriterion("remote_object_type_id between", value1, value2, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("remote_object_type_id not between", value1, value2, "remoteObjectTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdIsNull() {
            addCriterion("remote_car_type_id is null");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdIsNotNull() {
            addCriterion("remote_car_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdEqualTo(Long value) {
            addCriterion("remote_car_type_id =", value, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdNotEqualTo(Long value) {
            addCriterion("remote_car_type_id <>", value, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdGreaterThan(Long value) {
            addCriterion("remote_car_type_id >", value, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("remote_car_type_id >=", value, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdLessThan(Long value) {
            addCriterion("remote_car_type_id <", value, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("remote_car_type_id <=", value, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdIn(List<Long> values) {
            addCriterion("remote_car_type_id in", values, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdNotIn(List<Long> values) {
            addCriterion("remote_car_type_id not in", values, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdBetween(Long value1, Long value2) {
            addCriterion("remote_car_type_id between", value1, value2, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteCarTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("remote_car_type_id not between", value1, value2, "remoteCarTypeId");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableIsNull() {
            addCriterion("remote_control_enable is null");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableIsNotNull() {
            addCriterion("remote_control_enable is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableEqualTo(Boolean value) {
            addCriterion("remote_control_enable =", value, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableNotEqualTo(Boolean value) {
            addCriterion("remote_control_enable <>", value, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableGreaterThan(Boolean value) {
            addCriterion("remote_control_enable >", value, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("remote_control_enable >=", value, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableLessThan(Boolean value) {
            addCriterion("remote_control_enable <", value, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("remote_control_enable <=", value, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableIn(List<Boolean> values) {
            addCriterion("remote_control_enable in", values, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableNotIn(List<Boolean> values) {
            addCriterion("remote_control_enable not in", values, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("remote_control_enable between", value1, value2, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteControlEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("remote_control_enable not between", value1, value2, "remoteControlEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableIsNull() {
            addCriterion("remote_ssh_enable is null");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableIsNotNull() {
            addCriterion("remote_ssh_enable is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableEqualTo(Boolean value) {
            addCriterion("remote_ssh_enable =", value, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableNotEqualTo(Boolean value) {
            addCriterion("remote_ssh_enable <>", value, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableGreaterThan(Boolean value) {
            addCriterion("remote_ssh_enable >", value, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("remote_ssh_enable >=", value, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableLessThan(Boolean value) {
            addCriterion("remote_ssh_enable <", value, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("remote_ssh_enable <=", value, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableIn(List<Boolean> values) {
            addCriterion("remote_ssh_enable in", values, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableNotIn(List<Boolean> values) {
            addCriterion("remote_ssh_enable not in", values, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("remote_ssh_enable between", value1, value2, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteSshEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("remote_ssh_enable not between", value1, value2, "remoteSshEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableIsNull() {
            addCriterion("remote_debug_enable is null");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableIsNotNull() {
            addCriterion("remote_debug_enable is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableEqualTo(Boolean value) {
            addCriterion("remote_debug_enable =", value, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableNotEqualTo(Boolean value) {
            addCriterion("remote_debug_enable <>", value, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableGreaterThan(Boolean value) {
            addCriterion("remote_debug_enable >", value, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("remote_debug_enable >=", value, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableLessThan(Boolean value) {
            addCriterion("remote_debug_enable <", value, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("remote_debug_enable <=", value, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableIn(List<Boolean> values) {
            addCriterion("remote_debug_enable in", values, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableNotIn(List<Boolean> values) {
            addCriterion("remote_debug_enable not in", values, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("remote_debug_enable between", value1, value2, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andRemoteDebugEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("remote_debug_enable not between", value1, value2, "remoteDebugEnable");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortIsNull() {
            addCriterion("stream_base_port is null");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortIsNotNull() {
            addCriterion("stream_base_port is not null");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortEqualTo(Integer value) {
            addCriterion("stream_base_port =", value, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortNotEqualTo(Integer value) {
            addCriterion("stream_base_port <>", value, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortGreaterThan(Integer value) {
            addCriterion("stream_base_port >", value, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortGreaterThanOrEqualTo(Integer value) {
            addCriterion("stream_base_port >=", value, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortLessThan(Integer value) {
            addCriterion("stream_base_port <", value, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortLessThanOrEqualTo(Integer value) {
            addCriterion("stream_base_port <=", value, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortIn(List<Integer> values) {
            addCriterion("stream_base_port in", values, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortNotIn(List<Integer> values) {
            addCriterion("stream_base_port not in", values, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortBetween(Integer value1, Integer value2) {
            addCriterion("stream_base_port between", value1, value2, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andStreamBasePortNotBetween(Integer value1, Integer value2) {
            addCriterion("stream_base_port not between", value1, value2, "streamBasePort");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureIsNull() {
            addCriterion("remote_object_configure is null");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureIsNotNull() {
            addCriterion("remote_object_configure is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureEqualTo(String value) {
            addCriterion("remote_object_configure =", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureNotEqualTo(String value) {
            addCriterion("remote_object_configure <>", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureGreaterThan(String value) {
            addCriterion("remote_object_configure >", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureGreaterThanOrEqualTo(String value) {
            addCriterion("remote_object_configure >=", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureLessThan(String value) {
            addCriterion("remote_object_configure <", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureLessThanOrEqualTo(String value) {
            addCriterion("remote_object_configure <=", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureLike(String value) {
            addCriterion("remote_object_configure like", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureNotLike(String value) {
            addCriterion("remote_object_configure not like", value, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureIn(List<String> values) {
            addCriterion("remote_object_configure in", values, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureNotIn(List<String> values) {
            addCriterion("remote_object_configure not in", values, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureBetween(String value1, String value2) {
            addCriterion("remote_object_configure between", value1, value2, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteObjectConfigureNotBetween(String value1, String value2) {
            addCriterion("remote_object_configure not between", value1, value2, "remoteObjectConfigure");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseIsNull() {
            addCriterion("remote_release is null");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseIsNotNull() {
            addCriterion("remote_release is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseEqualTo(String value) {
            addCriterion("remote_release =", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseNotEqualTo(String value) {
            addCriterion("remote_release <>", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseGreaterThan(String value) {
            addCriterion("remote_release >", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseGreaterThanOrEqualTo(String value) {
            addCriterion("remote_release >=", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseLessThan(String value) {
            addCriterion("remote_release <", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseLessThanOrEqualTo(String value) {
            addCriterion("remote_release <=", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseLike(String value) {
            addCriterion("remote_release like", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseNotLike(String value) {
            addCriterion("remote_release not like", value, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseIn(List<String> values) {
            addCriterion("remote_release in", values, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseNotIn(List<String> values) {
            addCriterion("remote_release not in", values, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseBetween(String value1, String value2) {
            addCriterion("remote_release between", value1, value2, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andRemoteReleaseNotBetween(String value1, String value2) {
            addCriterion("remote_release not between", value1, value2, "remoteRelease");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}