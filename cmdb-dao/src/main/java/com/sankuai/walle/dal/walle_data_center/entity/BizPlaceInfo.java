package com.sankuai.walle.dal.walle_data_center.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: biz_place_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BizPlaceInfo {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Long id;

    /**
     *   字段: code
     *   说明: 场地编号
     */
    private String code;

    /**
     *   字段: name
     *   说明: 中文名称
     */
    private String name;

    /**
     *   字段: status
     *   说明: 状态，0:未使用，1:运营中，2:已废弃
     */
    private Byte status;

    /**
     *   字段: city
     *   说明: 所属城市
     */
    private String city;

    /**
     *   字段: city_code
     *   说明: 城市编码
     */
    private String cityCode;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: type
     *   说明: 类型:1场地，2区域
     */
    private Short type;

    /**
     *   字段: parent_id
     *   说明: 上级区域code
     */
    private String parentId;

    /**
     *   字段: task_type
     *   说明: 业务类型取值为：maicai,dongao,maishop等 可以填写多个逗号分隔
     */
    private String taskType;

    /**
     *   字段: area_code
     *   说明: 区域code
     */
    private String areaCode;

    /**
     *   字段: alias
     *   说明: 场地别名
     */
    private String alias;

    /**
     *   字段: affiliation
     *   说明: 场地归属[校园|公开道路|其他]
     */
    private String affiliation;

    /**
     *   字段: is_self_place
     *   说明: 收货地是否使用数据库内的已有站点【1:是 0:否】
     */
    private Boolean isSelfPlace;

    /**
     *   字段: hd_map
     *   说明: 高精地图名称
     */
    private String hdMap;
}