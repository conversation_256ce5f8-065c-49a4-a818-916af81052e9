package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: mis_group
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MisGroup {
    /**
     *   字段: id
     *   说明: 主键ID
     */
    private Long id;

    /**
     *   字段: group_name
     *   说明: 群组名
     */
    private String groupName;

    /**
     *   字段: group_desc
     *   说明: 群组描述
     */
    private String groupDesc;

    /**
     *   字段: group_members
     *   说明: 群组成员
     */
    private String groupMembers;

    /**
     *   字段: create_user
     *   说明: 创建人
     */
    private String createUser;

    /**
     *   字段: update_user
     *   说明: 更新人
     */
    private String updateUser;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 修改时间
     */
    private Date updateTime;

    /**
     *   字段: is_deleted
     *   说明: 是否删除
     */
    private Boolean isDeleted;
}