package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RemoteObjectsMapper extends MybatisBLOBsMapper<RemoteObjects, RemoteObjectsExample, Long> {
    int batchInsert(@Param("list") List<RemoteObjects> list);
}