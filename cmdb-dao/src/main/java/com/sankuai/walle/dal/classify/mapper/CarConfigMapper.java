package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.example.CarConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarConfigMapper extends MybatisBLOBsMapper<CarConfig, CarConfigExample, Long> {
    int batchInsert(@Param("list") List<CarConfig> list);
}