package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfigExtend;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExtendExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarDeviceConfigExtendMapper extends MybatisBaseMapper<CarDeviceConfigExtend, CarDeviceConfigExtendExample, Long> {
    int batchInsert(@Param("list") List<CarDeviceConfigExtend> list);
}