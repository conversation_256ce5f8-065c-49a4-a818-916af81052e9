package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.classify.entity.CarConfigQuery;
import com.sankuai.walle.dal.classify.example.CarConfigQueryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarConfigQueryMapper extends MybatisBLOBsMapper<CarConfigQuery, CarConfigQueryExample, Long> {
    int batchInsert(@Param("list") List<CarConfigQuery> list);
}