package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: biz_handle_policy_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BizHandlePolicyInfo {
    /**
     *   字段: id
     *   说明: 主键ID
     */
    private Long id;

    /**
     *   字段: biz_table_name
     *   说明: 业务表名
     */
    private String bizTableName;

    /**
     *   字段: biz_table_desc
     *   说明: 业务描述
     */
    private String bizTableDesc;

    /**
     *   字段: policy_name
     *   说明: 策略名
     */
    private String policyName;

    /**
     *   字段: policy_desc
     *   说明: 策略描述
     */
    private String policyDesc;

    /**
     *   字段: policy_value
     *   说明: 策略值
     */
    private String policyValue;

    /**
     *   字段: handle_method
     *   说明: 处理方法
     */
    private String handleMethod;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: create_user
     *   说明: 创建用户
     */
    private String createUser;

    /**
     *   字段: update_user
     *   说明: 更新用户
     */
    private String updateUser;

    /**
     *   字段: is_enable
     *   说明: 是否上线, 否0/是1
     */
    private Boolean isEnable;

    /**
     *   字段: is_deleted
     *   说明: 是否删除
     */
    private Boolean isDeleted;
}