package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType;
import com.sankuai.walle.dal.mrm_manage.example.RemoteDeviceTypeExample;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RemoteDeviceTypeMapper extends MybatisBaseMapper<RemoteDeviceType, RemoteDeviceTypeExample, Long> {
    int batchInsert(@Param("list") List<RemoteDeviceType> list);
}