package com.sankuai.walle.dal.mrm_manage.entity;

import lombok.*;

/**
 *
 *   表名: remote_object_tags
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RemoteObjectTags {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: remote_object_id
     *   说明: 远程对象ID
     */
    private Long remoteObjectId;

    /**
     *   字段: tag_id
     *   说明: 标签ID
     */
    private Long tagId;
}