package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.MisGroup;
import com.sankuai.walle.dal.mrm_manage.example.MisGroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface MisGroupMapper extends MybatisBaseMapper<MisGroup, MisGroupExample, Long> {
    int batchInsert(@Param("list") List<MisGroup> list);
}