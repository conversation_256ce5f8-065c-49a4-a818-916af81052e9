package com.sankuai.walle.dal.walle_data_center.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VehicleInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public VehicleInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public VehicleInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public VehicleInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public VehicleInfoExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNull() {
            addCriterion("vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNotNull() {
            addCriterion("vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdEqualTo(String value) {
            addCriterion("vehicle_id =", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotEqualTo(String value) {
            addCriterion("vehicle_id <>", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThan(String value) {
            addCriterion("vehicle_id >", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_id >=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThan(String value) {
            addCriterion("vehicle_id <", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThanOrEqualTo(String value) {
            addCriterion("vehicle_id <=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLike(String value) {
            addCriterion("vehicle_id like", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotLike(String value) {
            addCriterion("vehicle_id not like", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIn(List<String> values) {
            addCriterion("vehicle_id in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotIn(List<String> values) {
            addCriterion("vehicle_id not in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdBetween(String value1, String value2) {
            addCriterion("vehicle_id between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotBetween(String value1, String value2) {
            addCriterion("vehicle_id not between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVinIsNull() {
            addCriterion("vin is null");
            return (Criteria) this;
        }

        public Criteria andVinIsNotNull() {
            addCriterion("vin is not null");
            return (Criteria) this;
        }

        public Criteria andVinEqualTo(String value) {
            addCriterion("vin =", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotEqualTo(String value) {
            addCriterion("vin <>", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThan(String value) {
            addCriterion("vin >", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThanOrEqualTo(String value) {
            addCriterion("vin >=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThan(String value) {
            addCriterion("vin <", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThanOrEqualTo(String value) {
            addCriterion("vin <=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLike(String value) {
            addCriterion("vin like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotLike(String value) {
            addCriterion("vin not like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinIn(List<String> values) {
            addCriterion("vin in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotIn(List<String> values) {
            addCriterion("vin not in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinBetween(String value1, String value2) {
            addCriterion("vin between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotBetween(String value1, String value2) {
            addCriterion("vin not between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andParkIsNull() {
            addCriterion("park is null");
            return (Criteria) this;
        }

        public Criteria andParkIsNotNull() {
            addCriterion("park is not null");
            return (Criteria) this;
        }

        public Criteria andParkEqualTo(String value) {
            addCriterion("park =", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotEqualTo(String value) {
            addCriterion("park <>", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkGreaterThan(String value) {
            addCriterion("park >", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkGreaterThanOrEqualTo(String value) {
            addCriterion("park >=", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkLessThan(String value) {
            addCriterion("park <", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkLessThanOrEqualTo(String value) {
            addCriterion("park <=", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkLike(String value) {
            addCriterion("park like", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotLike(String value) {
            addCriterion("park not like", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkIn(List<String> values) {
            addCriterion("park in", values, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotIn(List<String> values) {
            addCriterion("park not in", values, "park");
            return (Criteria) this;
        }

        public Criteria andParkBetween(String value1, String value2) {
            addCriterion("park between", value1, value2, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotBetween(String value1, String value2) {
            addCriterion("park not between", value1, value2, "park");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(Byte value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(Byte value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(Byte value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(Byte value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<Byte> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<Byte> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryIsNull() {
            addCriterion("vehicle_category is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryIsNotNull() {
            addCriterion("vehicle_category is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryEqualTo(String value) {
            addCriterion("vehicle_category =", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotEqualTo(String value) {
            addCriterion("vehicle_category <>", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryGreaterThan(String value) {
            addCriterion("vehicle_category >", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_category >=", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryLessThan(String value) {
            addCriterion("vehicle_category <", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryLessThanOrEqualTo(String value) {
            addCriterion("vehicle_category <=", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryLike(String value) {
            addCriterion("vehicle_category like", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotLike(String value) {
            addCriterion("vehicle_category not like", value, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryIn(List<String> values) {
            addCriterion("vehicle_category in", values, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotIn(List<String> values) {
            addCriterion("vehicle_category not in", values, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryBetween(String value1, String value2) {
            addCriterion("vehicle_category between", value1, value2, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andVehicleCategoryNotBetween(String value1, String value2) {
            addCriterion("vehicle_category not between", value1, value2, "vehicleCategory");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberIsNull() {
            addCriterion("license_number is null");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberIsNotNull() {
            addCriterion("license_number is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberEqualTo(String value) {
            addCriterion("license_number =", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotEqualTo(String value) {
            addCriterion("license_number <>", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberGreaterThan(String value) {
            addCriterion("license_number >", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberGreaterThanOrEqualTo(String value) {
            addCriterion("license_number >=", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberLessThan(String value) {
            addCriterion("license_number <", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberLessThanOrEqualTo(String value) {
            addCriterion("license_number <=", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberLike(String value) {
            addCriterion("license_number like", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotLike(String value) {
            addCriterion("license_number not like", value, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberIn(List<String> values) {
            addCriterion("license_number in", values, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotIn(List<String> values) {
            addCriterion("license_number not in", values, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberBetween(String value1, String value2) {
            addCriterion("license_number between", value1, value2, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenseNumberNotBetween(String value1, String value2) {
            addCriterion("license_number not between", value1, value2, "licenseNumber");
            return (Criteria) this;
        }

        public Criteria andLicenceIsNull() {
            addCriterion("licence is null");
            return (Criteria) this;
        }

        public Criteria andLicenceIsNotNull() {
            addCriterion("licence is not null");
            return (Criteria) this;
        }

        public Criteria andLicenceEqualTo(String value) {
            addCriterion("licence =", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceNotEqualTo(String value) {
            addCriterion("licence <>", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceGreaterThan(String value) {
            addCriterion("licence >", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceGreaterThanOrEqualTo(String value) {
            addCriterion("licence >=", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceLessThan(String value) {
            addCriterion("licence <", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceLessThanOrEqualTo(String value) {
            addCriterion("licence <=", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceLike(String value) {
            addCriterion("licence like", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceNotLike(String value) {
            addCriterion("licence not like", value, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceIn(List<String> values) {
            addCriterion("licence in", values, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceNotIn(List<String> values) {
            addCriterion("licence not in", values, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceBetween(String value1, String value2) {
            addCriterion("licence between", value1, value2, "licence");
            return (Criteria) this;
        }

        public Criteria andLicenceNotBetween(String value1, String value2) {
            addCriterion("licence not between", value1, value2, "licence");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("year is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("year is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(Integer value) {
            addCriterion("year =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(Integer value) {
            addCriterion("year <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(Integer value) {
            addCriterion("year >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("year >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThan(Integer value) {
            addCriterion("year <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(Integer value) {
            addCriterion("year <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearIn(List<Integer> values) {
            addCriterion("year in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<Integer> values) {
            addCriterion("year not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(Integer value1, Integer value2) {
            addCriterion("year between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(Integer value1, Integer value2) {
            addCriterion("year not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIsNull() {
            addCriterion("power_type is null");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIsNotNull() {
            addCriterion("power_type is not null");
            return (Criteria) this;
        }

        public Criteria andPowerTypeEqualTo(Byte value) {
            addCriterion("power_type =", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotEqualTo(Byte value) {
            addCriterion("power_type <>", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeGreaterThan(Byte value) {
            addCriterion("power_type >", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("power_type >=", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeLessThan(Byte value) {
            addCriterion("power_type <", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeLessThanOrEqualTo(Byte value) {
            addCriterion("power_type <=", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIn(List<Byte> values) {
            addCriterion("power_type in", values, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotIn(List<Byte> values) {
            addCriterion("power_type not in", values, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeBetween(Byte value1, Byte value2) {
            addCriterion("power_type between", value1, value2, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("power_type not between", value1, value2, "powerType");
            return (Criteria) this;
        }

        public Criteria andOperationStateIsNull() {
            addCriterion("operation_state is null");
            return (Criteria) this;
        }

        public Criteria andOperationStateIsNotNull() {
            addCriterion("operation_state is not null");
            return (Criteria) this;
        }

        public Criteria andOperationStateEqualTo(Byte value) {
            addCriterion("operation_state =", value, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateNotEqualTo(Byte value) {
            addCriterion("operation_state <>", value, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateGreaterThan(Byte value) {
            addCriterion("operation_state >", value, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateGreaterThanOrEqualTo(Byte value) {
            addCriterion("operation_state >=", value, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateLessThan(Byte value) {
            addCriterion("operation_state <", value, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateLessThanOrEqualTo(Byte value) {
            addCriterion("operation_state <=", value, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateIn(List<Byte> values) {
            addCriterion("operation_state in", values, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateNotIn(List<Byte> values) {
            addCriterion("operation_state not in", values, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateBetween(Byte value1, Byte value2) {
            addCriterion("operation_state between", value1, value2, "operationState");
            return (Criteria) this;
        }

        public Criteria andOperationStateNotBetween(Byte value1, Byte value2) {
            addCriterion("operation_state not between", value1, value2, "operationState");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andProductIsNull() {
            addCriterion("product is null");
            return (Criteria) this;
        }

        public Criteria andProductIsNotNull() {
            addCriterion("product is not null");
            return (Criteria) this;
        }

        public Criteria andProductEqualTo(Integer value) {
            addCriterion("product =", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotEqualTo(Integer value) {
            addCriterion("product <>", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductGreaterThan(Integer value) {
            addCriterion("product >", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductGreaterThanOrEqualTo(Integer value) {
            addCriterion("product >=", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductLessThan(Integer value) {
            addCriterion("product <", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductLessThanOrEqualTo(Integer value) {
            addCriterion("product <=", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductIn(List<Integer> values) {
            addCriterion("product in", values, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotIn(List<Integer> values) {
            addCriterion("product not in", values, "product");
            return (Criteria) this;
        }

        public Criteria andProductBetween(Integer value1, Integer value2) {
            addCriterion("product between", value1, value2, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotBetween(Integer value1, Integer value2) {
            addCriterion("product not between", value1, value2, "product");
            return (Criteria) this;
        }

        public Criteria andPurposeIdIsNull() {
            addCriterion("purpose_id is null");
            return (Criteria) this;
        }

        public Criteria andPurposeIdIsNotNull() {
            addCriterion("purpose_id is not null");
            return (Criteria) this;
        }

        public Criteria andPurposeIdEqualTo(Byte value) {
            addCriterion("purpose_id =", value, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdNotEqualTo(Byte value) {
            addCriterion("purpose_id <>", value, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdGreaterThan(Byte value) {
            addCriterion("purpose_id >", value, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdGreaterThanOrEqualTo(Byte value) {
            addCriterion("purpose_id >=", value, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdLessThan(Byte value) {
            addCriterion("purpose_id <", value, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdLessThanOrEqualTo(Byte value) {
            addCriterion("purpose_id <=", value, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdIn(List<Byte> values) {
            addCriterion("purpose_id in", values, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdNotIn(List<Byte> values) {
            addCriterion("purpose_id not in", values, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdBetween(Byte value1, Byte value2) {
            addCriterion("purpose_id between", value1, value2, "purposeId");
            return (Criteria) this;
        }

        public Criteria andPurposeIdNotBetween(Byte value1, Byte value2) {
            addCriterion("purpose_id not between", value1, value2, "purposeId");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedIsNull() {
            addCriterion("is_dedicated is null");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedIsNotNull() {
            addCriterion("is_dedicated is not null");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedEqualTo(Boolean value) {
            addCriterion("is_dedicated =", value, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedNotEqualTo(Boolean value) {
            addCriterion("is_dedicated <>", value, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedGreaterThan(Boolean value) {
            addCriterion("is_dedicated >", value, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_dedicated >=", value, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedLessThan(Boolean value) {
            addCriterion("is_dedicated <", value, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_dedicated <=", value, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedIn(List<Boolean> values) {
            addCriterion("is_dedicated in", values, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedNotIn(List<Boolean> values) {
            addCriterion("is_dedicated not in", values, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_dedicated between", value1, value2, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_dedicated not between", value1, value2, "isDedicated");
            return (Criteria) this;
        }

        public Criteria andSubmitterIsNull() {
            addCriterion("submitter is null");
            return (Criteria) this;
        }

        public Criteria andSubmitterIsNotNull() {
            addCriterion("submitter is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitterEqualTo(String value) {
            addCriterion("submitter =", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterNotEqualTo(String value) {
            addCriterion("submitter <>", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterGreaterThan(String value) {
            addCriterion("submitter >", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterGreaterThanOrEqualTo(String value) {
            addCriterion("submitter >=", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterLessThan(String value) {
            addCriterion("submitter <", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterLessThanOrEqualTo(String value) {
            addCriterion("submitter <=", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterLike(String value) {
            addCriterion("submitter like", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterNotLike(String value) {
            addCriterion("submitter not like", value, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterIn(List<String> values) {
            addCriterion("submitter in", values, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterNotIn(List<String> values) {
            addCriterion("submitter not in", values, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterBetween(String value1, String value2) {
            addCriterion("submitter between", value1, value2, "submitter");
            return (Criteria) this;
        }

        public Criteria andSubmitterNotBetween(String value1, String value2) {
            addCriterion("submitter not between", value1, value2, "submitter");
            return (Criteria) this;
        }

        public Criteria andActivateCodeIsNull() {
            addCriterion("activate_code is null");
            return (Criteria) this;
        }

        public Criteria andActivateCodeIsNotNull() {
            addCriterion("activate_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivateCodeEqualTo(String value) {
            addCriterion("activate_code =", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeNotEqualTo(String value) {
            addCriterion("activate_code <>", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeGreaterThan(String value) {
            addCriterion("activate_code >", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activate_code >=", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeLessThan(String value) {
            addCriterion("activate_code <", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeLessThanOrEqualTo(String value) {
            addCriterion("activate_code <=", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeLike(String value) {
            addCriterion("activate_code like", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeNotLike(String value) {
            addCriterion("activate_code not like", value, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeIn(List<String> values) {
            addCriterion("activate_code in", values, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeNotIn(List<String> values) {
            addCriterion("activate_code not in", values, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeBetween(String value1, String value2) {
            addCriterion("activate_code between", value1, value2, "activateCode");
            return (Criteria) this;
        }

        public Criteria andActivateCodeNotBetween(String value1, String value2) {
            addCriterion("activate_code not between", value1, value2, "activateCode");
            return (Criteria) this;
        }

        public Criteria andAccessSecretIsNull() {
            addCriterion("access_secret is null");
            return (Criteria) this;
        }

        public Criteria andAccessSecretIsNotNull() {
            addCriterion("access_secret is not null");
            return (Criteria) this;
        }

        public Criteria andAccessSecretEqualTo(String value) {
            addCriterion("access_secret =", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretNotEqualTo(String value) {
            addCriterion("access_secret <>", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretGreaterThan(String value) {
            addCriterion("access_secret >", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretGreaterThanOrEqualTo(String value) {
            addCriterion("access_secret >=", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretLessThan(String value) {
            addCriterion("access_secret <", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretLessThanOrEqualTo(String value) {
            addCriterion("access_secret <=", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretLike(String value) {
            addCriterion("access_secret like", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretNotLike(String value) {
            addCriterion("access_secret not like", value, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretIn(List<String> values) {
            addCriterion("access_secret in", values, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretNotIn(List<String> values) {
            addCriterion("access_secret not in", values, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretBetween(String value1, String value2) {
            addCriterion("access_secret between", value1, value2, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andAccessSecretNotBetween(String value1, String value2) {
            addCriterion("access_secret not between", value1, value2, "accessSecret");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeIsNull() {
            addCriterion("box_qrcode is null");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeIsNotNull() {
            addCriterion("box_qrcode is not null");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeEqualTo(String value) {
            addCriterion("box_qrcode =", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeNotEqualTo(String value) {
            addCriterion("box_qrcode <>", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeGreaterThan(String value) {
            addCriterion("box_qrcode >", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeGreaterThanOrEqualTo(String value) {
            addCriterion("box_qrcode >=", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeLessThan(String value) {
            addCriterion("box_qrcode <", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeLessThanOrEqualTo(String value) {
            addCriterion("box_qrcode <=", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeLike(String value) {
            addCriterion("box_qrcode like", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeNotLike(String value) {
            addCriterion("box_qrcode not like", value, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeIn(List<String> values) {
            addCriterion("box_qrcode in", values, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeNotIn(List<String> values) {
            addCriterion("box_qrcode not in", values, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeBetween(String value1, String value2) {
            addCriterion("box_qrcode between", value1, value2, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andBoxQrcodeNotBetween(String value1, String value2) {
            addCriterion("box_qrcode not between", value1, value2, "boxQrcode");
            return (Criteria) this;
        }

        public Criteria andStartPostionIsNull() {
            addCriterion("start_postion is null");
            return (Criteria) this;
        }

        public Criteria andStartPostionIsNotNull() {
            addCriterion("start_postion is not null");
            return (Criteria) this;
        }

        public Criteria andStartPostionEqualTo(String value) {
            addCriterion("start_postion =", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionNotEqualTo(String value) {
            addCriterion("start_postion <>", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionGreaterThan(String value) {
            addCriterion("start_postion >", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionGreaterThanOrEqualTo(String value) {
            addCriterion("start_postion >=", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionLessThan(String value) {
            addCriterion("start_postion <", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionLessThanOrEqualTo(String value) {
            addCriterion("start_postion <=", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionLike(String value) {
            addCriterion("start_postion like", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionNotLike(String value) {
            addCriterion("start_postion not like", value, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionIn(List<String> values) {
            addCriterion("start_postion in", values, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionNotIn(List<String> values) {
            addCriterion("start_postion not in", values, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionBetween(String value1, String value2) {
            addCriterion("start_postion between", value1, value2, "startPostion");
            return (Criteria) this;
        }

        public Criteria andStartPostionNotBetween(String value1, String value2) {
            addCriterion("start_postion not between", value1, value2, "startPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionIsNull() {
            addCriterion("end_postion is null");
            return (Criteria) this;
        }

        public Criteria andEndPostionIsNotNull() {
            addCriterion("end_postion is not null");
            return (Criteria) this;
        }

        public Criteria andEndPostionEqualTo(String value) {
            addCriterion("end_postion =", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionNotEqualTo(String value) {
            addCriterion("end_postion <>", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionGreaterThan(String value) {
            addCriterion("end_postion >", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionGreaterThanOrEqualTo(String value) {
            addCriterion("end_postion >=", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionLessThan(String value) {
            addCriterion("end_postion <", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionLessThanOrEqualTo(String value) {
            addCriterion("end_postion <=", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionLike(String value) {
            addCriterion("end_postion like", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionNotLike(String value) {
            addCriterion("end_postion not like", value, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionIn(List<String> values) {
            addCriterion("end_postion in", values, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionNotIn(List<String> values) {
            addCriterion("end_postion not in", values, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionBetween(String value1, String value2) {
            addCriterion("end_postion between", value1, value2, "endPostion");
            return (Criteria) this;
        }

        public Criteria andEndPostionNotBetween(String value1, String value2) {
            addCriterion("end_postion not between", value1, value2, "endPostion");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordIsNull() {
            addCriterion("super_password is null");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordIsNotNull() {
            addCriterion("super_password is not null");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordEqualTo(String value) {
            addCriterion("super_password =", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordNotEqualTo(String value) {
            addCriterion("super_password <>", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordGreaterThan(String value) {
            addCriterion("super_password >", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("super_password >=", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordLessThan(String value) {
            addCriterion("super_password <", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordLessThanOrEqualTo(String value) {
            addCriterion("super_password <=", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordLike(String value) {
            addCriterion("super_password like", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordNotLike(String value) {
            addCriterion("super_password not like", value, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordIn(List<String> values) {
            addCriterion("super_password in", values, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordNotIn(List<String> values) {
            addCriterion("super_password not in", values, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordBetween(String value1, String value2) {
            addCriterion("super_password between", value1, value2, "superPassword");
            return (Criteria) this;
        }

        public Criteria andSuperPasswordNotBetween(String value1, String value2) {
            addCriterion("super_password not between", value1, value2, "superPassword");
            return (Criteria) this;
        }

        public Criteria andUsageRightsIsNull() {
            addCriterion("usage_rights is null");
            return (Criteria) this;
        }

        public Criteria andUsageRightsIsNotNull() {
            addCriterion("usage_rights is not null");
            return (Criteria) this;
        }

        public Criteria andUsageRightsEqualTo(String value) {
            addCriterion("usage_rights =", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsNotEqualTo(String value) {
            addCriterion("usage_rights <>", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsGreaterThan(String value) {
            addCriterion("usage_rights >", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsGreaterThanOrEqualTo(String value) {
            addCriterion("usage_rights >=", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsLessThan(String value) {
            addCriterion("usage_rights <", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsLessThanOrEqualTo(String value) {
            addCriterion("usage_rights <=", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsLike(String value) {
            addCriterion("usage_rights like", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsNotLike(String value) {
            addCriterion("usage_rights not like", value, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsIn(List<String> values) {
            addCriterion("usage_rights in", values, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsNotIn(List<String> values) {
            addCriterion("usage_rights not in", values, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsBetween(String value1, String value2) {
            addCriterion("usage_rights between", value1, value2, "usageRights");
            return (Criteria) this;
        }

        public Criteria andUsageRightsNotBetween(String value1, String value2) {
            addCriterion("usage_rights not between", value1, value2, "usageRights");
            return (Criteria) this;
        }

        public Criteria andIsBusinessIsNull() {
            addCriterion("is_business is null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessIsNotNull() {
            addCriterion("is_business is not null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessEqualTo(Boolean value) {
            addCriterion("is_business =", value, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessNotEqualTo(Boolean value) {
            addCriterion("is_business <>", value, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessGreaterThan(Boolean value) {
            addCriterion("is_business >", value, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_business >=", value, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLessThan(Boolean value) {
            addCriterion("is_business <", value, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLessThanOrEqualTo(Boolean value) {
            addCriterion("is_business <=", value, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessIn(List<Boolean> values) {
            addCriterion("is_business in", values, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessNotIn(List<Boolean> values) {
            addCriterion("is_business not in", values, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessBetween(Boolean value1, Boolean value2) {
            addCriterion("is_business between", value1, value2, "isBusiness");
            return (Criteria) this;
        }

        public Criteria andIsBusinessNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_business not between", value1, value2, "isBusiness");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}