package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware;
import com.sankuai.walle.dal.mrm_manage.example.DeviceSoftwareExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeviceSoftwareMapper extends MybatisBaseMapper<DeviceSoftware, DeviceSoftwareExample, Long> {
    int batchInsert(@Param("list") List<DeviceSoftware> list);
}