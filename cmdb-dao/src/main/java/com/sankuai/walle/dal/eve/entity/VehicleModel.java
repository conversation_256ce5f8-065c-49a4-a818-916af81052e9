package com.sankuai.walle.dal.eve.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: vehicle_model
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VehicleModel {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: vehicle_id
     *   说明: 车型Id,对应 car_selects 表
     */
    private Long vehicleId;

    /**
     *   字段: device_id
     *   说明: 设备Id, 对应 device_types 表
     */
    private Long deviceId;

    /**
     *   字段: level
     *   说明: 1 代表 1级车型。2代表2级车型
     */
    private Integer level;

    /**
     *   字段: editor
     *   说明: 编辑人
     */
    private String editor;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: is_deleted
     *   说明: 是否删除[0-未删除|1-已删除]
     */
    private Boolean isDeleted;
}