package com.sankuai.walle.dal.walle_data_center.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs;
import com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample;
import com.sankuai.walle.dal.walle_data_center.entity.VehicleInfo;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface VehicleInfoMapper extends MybatisBLOBsMapper<VehicleInfo, VehicleInfoExample, Long> {
    int batchInsert(@Param("list") List<VehicleInfoWithBLOBs> list);
}