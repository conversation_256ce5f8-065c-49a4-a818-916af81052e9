package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.classify.entity.CarClassifyThirdModel;
import com.sankuai.walle.dal.classify.example.CarClassifyThirdModelExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarClassifyThirdModelMapper extends MybatisBaseMapper<CarClassifyThirdModel, CarClassifyThirdModelExample, Long> {
    int batchInsert(@Param("list") List<CarClassifyThirdModel> list);
}