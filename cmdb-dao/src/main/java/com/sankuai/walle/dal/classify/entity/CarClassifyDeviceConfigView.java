package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_classify_device_config_view
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarClassifyDeviceConfigView {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: first_device_type_id
     *   说明: 一级设备类型id
     */
    private Long firstDeviceTypeId;

    /**
     *   字段: second_device_type_id
     *   说明: 二级设备类型id
     */
    private Long secondDeviceTypeId;

    /**
     *   字段: config_id
     *   说明: car_config关联id
     */
    private Long configId;

    /**
     *   字段: car_classify_id
     */
    private Long carClassifyId;

    /**
     *   字段: car_classify_third_model_id
     *   说明: 关联三级分类+四级分类表id
     */
    private Long carClassifyThirdModelId;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}