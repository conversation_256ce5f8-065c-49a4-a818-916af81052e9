package com.sankuai.walle.dal.eve.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: vehicle_base_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VehicleBaseInfo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: vin
     *   说明: 车架号
     */
    private String vin;

    /**
     *   字段: first_class_model
     *   说明: 车辆一级型号
     */
    private String firstClassModel;

    /**
     *   字段: second_class_model
     *   说明: 车辆二级型号
     */
    private String secondClassModel;

    /**
     *   字段: brand
     *   说明: 品牌
     */
    private String brand;

    /**
     *   字段: exec_word
     *   说明: 保留字段
     */
    private String execWord;

    /**
     *   字段: car_size_type
     *   说明: 车辆类型[0:unknown, 1:big, 2:middle，3:guarantee]
     */
    private Byte carSizeType;

    /**
     *   字段: power_type
     *   说明: 燃油种类[null: 未知，1: 电 2: 油]
     */
    private Integer powerType;

    /**
     *   字段: refited
     *   说明: 是否改装[null: 未知，0: 否 1: 是]
     */
    private Byte refited;

    /**
     *   字段: assembly_parts
     *   说明: 加装件
     */
    private String assemblyParts;

    /**
     *   字段: license_no
     *   说明: 车牌号
     */
    private String licenseNo;

    /**
     *   字段: registration_date
     *   说明: 大车注册时间
     */
    private Date registrationDate;

    /**
     *   字段: annual_inspection_date
     *   说明: 年检到期日期
     */
    private Date annualInspectionDate;

    /**
     *   字段: car_preduce_owner
     *   说明: 车辆手续保管人
     */
    private String carPreduceOwner;

    /**
     *   字段: indicate_owner
     *   说明: 车牌指标归属主体
     */
    private String indicateOwner;

    /**
     *   字段: indicate_rental_duration
     *   说明: 指标租用时长(/天)
     */
    private Long indicateRentalDuration;

    /**
     *   字段: rent_expense
     *   说明: 指标租用费用(元/月)
     */
    private Long rentExpense;

    /**
     *   字段: leasing_expense
     *   说明: 车辆融租费用(元/期)
     */
    private Long leasingExpense;

    /**
     *   字段: leasing_duration
     *   说明: 融租时长(/天)
     */
    private Long leasingDuration;

    /**
     *   字段: heavy_traffic_insurance
     *   说明: 交强险到期日期
     */
    private Date heavyTrafficInsurance;

    /**
     *   字段: business_insurance_date
     *   说明: 商业险到期日期
     */
    private Date businessInsuranceDate;

    /**
     *   字段: equipment_insurance_date
     *   说明: 设备险到期日期
     */
    private Date equipmentInsuranceDate;

    /**
     *   字段: priority_insurance_date
     *   说明: 财产险到期日期
     */
    private Date priorityInsuranceDate;

    /**
     *   字段: third_liability_insurance_date
     *   说明: 三方责任险到期日期
     */
    private Date thirdLiabilityInsuranceDate;

    /**
     *   字段: key_num
     *   说明: 钥匙数量
     */
    private Long keyNum;

    /**
     *   字段: back_key_owner
     *   说明: 备用钥匙保管人
     */
    private String backKeyOwner;

    /**
     *   字段: driving_permit_in_car
     *   说明: 行驶证是否随车
     */
    private String drivingPermitInCar;

    /**
     *   字段: car_used_target
     *   说明: 车辆用途
     */
    private String carUsedTarget;

    /**
     *   字段: vehicle_name
     *   说明: 车辆名称
     */
    private String vehicleName;

    /**
     *   字段: city
     *   说明: 城市
     */
    private String city;

    /**
     *   字段: place
     *   说明: 场地
     */
    private String place;

    /**
     *   字段: business_name
     *   说明: 站点
     */
    private String businessName;

    /**
     *   字段: ability
     *   说明: 岗位
     */
    private String ability;

    /**
     *   字段: status
     *   说明: 可用状态[业务站状态 0: 停用，1:启用]
     */
    private Byte status;

    /**
     *   字段: person_mis
     *   说明: 资产责任人
     */
    private String personMis;

    /**
     *   字段: owner_department
     *   说明: 资产责任部门
     */
    private String ownerDepartment;

    /**
     *   字段: label
     *   说明: 资产标签号
     */
    private String label;

    /**
     *   字段: sn
     *   说明: SN号
     */
    private String sn;

    /**
     *   字段: car_belong_type
     *   说明: 车辆性质[1: 自有 2: 租赁  3: 融租]
     */
    private Long carBelongType;

    /**
     *   字段: car_owner
     *   说明: 车辆归属主体
     */
    private String carOwner;

    /**
     *   字段: scrap
     *   说明: 是否报废[null: 未知 true: 是 false: 否]
     */
    private Boolean scrap;

    /**
     *   字段: scrap_reason
     *   说明: 报废原因
     */
    private String scrapReason;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}