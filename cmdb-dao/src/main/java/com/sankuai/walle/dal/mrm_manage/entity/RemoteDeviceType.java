package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: remote_device_type
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RemoteDeviceType {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: type_name
     *   说明: 类型名
     */
    private String typeName;

    /**
     *   字段: friend_name
     *   说明: 类型别名
     */
    private String friendName;

    /**
     *   字段: conf_content
     *   说明: 配置信息
     */
    private String confContent;

    /**
     *   字段: latest_version_id
     *   说明: 当前设备最新版本ID
     */
    private Long latestVersionId;

    /**
     *   字段: add_time
     *   说明: 建立时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}