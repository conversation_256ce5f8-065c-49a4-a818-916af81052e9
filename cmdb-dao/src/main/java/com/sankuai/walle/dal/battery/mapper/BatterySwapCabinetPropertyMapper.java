package com.sankuai.walle.dal.battery.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty;
import com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatterySwapCabinetPropertyMapper extends MybatisBLOBsMapper<BatterySwapCabinetProperty, BatterySwapCabinetPropertyExample, Long> {
    int batchInsert(@Param("list") List<BatterySwapCabinetProperty> list);
}