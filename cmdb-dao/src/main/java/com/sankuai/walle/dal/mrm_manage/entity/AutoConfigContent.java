package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;

import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import lombok.*;

/**
 *
 *   表名: auto_config_content
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AutoConfigContent {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: vin
     *   说明: VIN号，通常为17位字符
     */
    private String vin;

    /**
     *   字段: property_content
     *   说明: 属性项
     */
    private String propertyContent;

    /**
     *   字段: property_value
     *   说明: 属性值
     */
    private String propertyValue;

    /**
     *   字段: file_type
     *   说明: 文件类型
     */
    private String fileType;

    /**
     *   字段: user
     *   说明: misId
     */
    private String user;

    /**
     *   字段: add_time
     *   说明: 添加时间，默认当前时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间，记录最后修改时间
     */
    private Date updateTime;

    /**
     *   字段: is_delete
     *   说明: 1-已删除|0-未删除
     */
    private Boolean isDelete;

    /**
     *   字段: device_name
     *   说明: 设备名称
     */
    private String deviceName;

    public static AutoConfigContent fromExcelReqBody(SendExcelDeviceConfigReq sendExcelDeviceConfigReq) {
        return new AutoConfigContent() {{
            setVin(sendExcelDeviceConfigReq.getVin());
            setPropertyContent(sendExcelDeviceConfigReq.getConfigName());
            setPropertyValue(sendExcelDeviceConfigReq.getContent());
            setFileType(sendExcelDeviceConfigReq.getFileType());
            setUser(sendExcelDeviceConfigReq.getUser());
            setDeviceName(sendExcelDeviceConfigReq.getDeviceName());
            setIsDelete(false);
        }};
    }
}