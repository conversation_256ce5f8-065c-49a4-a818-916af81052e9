package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectTagsExample;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RemoteObjectTagsMapper extends MybatisBaseMapper<RemoteObjectTags, RemoteObjectTagsExample, Long> {
    int batchInsert(@Param("list") List<RemoteObjectTags> list);
}