package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_equipment
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarEquipment {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: sn
     *   说明: 海鸥sn号
     */
    private String sn;

    /**
     *   字段: uid
     *   说明: 当前分类中的唯一标识：电池id
     */
    private String uid;

    /**
     *   字段: vin
     *   说明: 关联车辆，没有为空
     */
    private String vin;

    /**
     *   字段: label
     *   说明: 资产数据库：海鸥中的唯一标识号
     */
    private String label;

    /**
     *   字段: big_type
     *   说明: 实物大类
     */
    private String bigType;

    /**
     *   字段: small_type
     *   说明: 实物小类
     */
    private String smallType;

    /**
     *   字段: mis
     *   说明: 资产责任人mis
     */
    private String mis;

    /**
     *   字段: status
     *   说明: 资产状态
     */
    private String status;

    /**
     *   字段: remote_type_name
     *   说明: 资产类别，关联remote_device_type表
     */
    private String remoteTypeName;

    /**
     *   字段: scrap
     *   说明: 是否报废
     */
    private Boolean scrap;

    /**
     *   字段: park
     *   说明: 场地
     */
    private String park;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: exec_word
     *   说明: 扩展字段
     */
    private String execWord;
}