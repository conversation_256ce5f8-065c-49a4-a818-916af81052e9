package com.sankuai.walle.dal.battery.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: battery_swap_cabinet_property
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BatterySwapCabinetProperty {
    /**
     *   字段: battery_property_id
     *   说明: 自增ID
     */
    private Long batteryPropertyId;

    /**
     *   字段: time
     *   说明: 时间戳
     */
    private Long time;

    /**
     *   字段: method
     *   说明: 方法
     */
    private String method;

    /**
     *   字段: topic
     *   说明: 事件主题
     */
    private String topic;

    /**
     *   字段: id
     *   说明: id
     */
    private String id;

    /**
     *   字段: version
     *   说明: 版本号
     */
    private String version;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: device_key
     *   说明: 换电柜设备key
     */
    private String deviceKey;

    /**
     *   字段: message_id
     *   说明: 消息id
     */
    private String messageId;

    /**
     *   字段: product_key
     *   说明: 换电柜海雀产品key
     */
    private String productKey;

    /**
     *   字段: arg
     *   说明: 变更事件参数
     */
    String arg;
}