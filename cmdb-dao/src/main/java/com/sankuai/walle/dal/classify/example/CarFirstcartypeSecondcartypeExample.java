package com.sankuai.walle.dal.classify.example;

import java.util.ArrayList;
import java.util.List;

public class CarFirstcartypeSecondcartypeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CarFirstcartypeSecondcartypeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CarFirstcartypeSecondcartypeExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CarFirstcartypeSecondcartypeExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CarFirstcartypeSecondcartypeExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdIsNull() {
            addCriterion("first_car_id is null");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdIsNotNull() {
            addCriterion("first_car_id is not null");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdEqualTo(Long value) {
            addCriterion("first_car_id =", value, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdNotEqualTo(Long value) {
            addCriterion("first_car_id <>", value, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdGreaterThan(Long value) {
            addCriterion("first_car_id >", value, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdGreaterThanOrEqualTo(Long value) {
            addCriterion("first_car_id >=", value, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdLessThan(Long value) {
            addCriterion("first_car_id <", value, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdLessThanOrEqualTo(Long value) {
            addCriterion("first_car_id <=", value, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdIn(List<Long> values) {
            addCriterion("first_car_id in", values, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdNotIn(List<Long> values) {
            addCriterion("first_car_id not in", values, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdBetween(Long value1, Long value2) {
            addCriterion("first_car_id between", value1, value2, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andFirstCarIdNotBetween(Long value1, Long value2) {
            addCriterion("first_car_id not between", value1, value2, "firstCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdIsNull() {
            addCriterion("second_car_id is null");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdIsNotNull() {
            addCriterion("second_car_id is not null");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdEqualTo(Long value) {
            addCriterion("second_car_id =", value, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdNotEqualTo(Long value) {
            addCriterion("second_car_id <>", value, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdGreaterThan(Long value) {
            addCriterion("second_car_id >", value, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdGreaterThanOrEqualTo(Long value) {
            addCriterion("second_car_id >=", value, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdLessThan(Long value) {
            addCriterion("second_car_id <", value, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdLessThanOrEqualTo(Long value) {
            addCriterion("second_car_id <=", value, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdIn(List<Long> values) {
            addCriterion("second_car_id in", values, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdNotIn(List<Long> values) {
            addCriterion("second_car_id not in", values, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdBetween(Long value1, Long value2) {
            addCriterion("second_car_id between", value1, value2, "secondCarId");
            return (Criteria) this;
        }

        public Criteria andSecondCarIdNotBetween(Long value1, Long value2) {
            addCriterion("second_car_id not between", value1, value2, "secondCarId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}