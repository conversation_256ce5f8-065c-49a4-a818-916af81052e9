package com.sankuai.walle.dal.walle_data_center.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfo;
import com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfoWithBLOBs;
import com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BizPlaceInfoMapper extends MybatisBLOBsMapper<BizPlaceInfo, BizPlaceInfoExample, Long> {
    int batchInsert(@Param("list") List<BizPlaceInfoWithBLOBs> list);
}