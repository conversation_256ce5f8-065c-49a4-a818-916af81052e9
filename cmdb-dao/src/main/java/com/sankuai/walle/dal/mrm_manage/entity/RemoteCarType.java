package com.sankuai.walle.dal.mrm_manage.entity;

import lombok.*;

/**
 *
 *   表名: remote_car_type
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RemoteCarType {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: type_name
     *   说明: 类型名
     */
    private String typeName;

    /**
     *   字段: friend_name
     *   说明: 类型别名
     */
    private String friendName;

    /**
     *   字段: conf_content
     *   说明: 配置信息
     */
    private String confContent;

    /**
     *   字段: father_type
     *   说明: 父级类型
     */
    private String fatherType;
}