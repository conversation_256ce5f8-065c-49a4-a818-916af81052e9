package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_classify_third_model
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Deprecated
public class CarClassifyThirdModel {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: remote_car_classify_id
     *   说明: 关联car_classify的 id
     */
    private Long remoteCarClassifyId;

    /**
     *   字段: third_model
     *   说明: 三级车型：选择范围。1 城市 2 运营区域 3 运营组别 4 车辆编号
     */
    private Short thirdModel;

    /**
     *   字段: third_model_next
     *   说明: 三级的下一级
     */
    private String thirdModelNext;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}