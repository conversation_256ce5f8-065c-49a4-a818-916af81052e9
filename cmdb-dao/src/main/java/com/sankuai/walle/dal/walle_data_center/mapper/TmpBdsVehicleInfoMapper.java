package com.sankuai.walle.dal.walle_data_center.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfo;
import com.sankuai.walle.dal.walle_data_center.entity.TmpBdsVehicleInfoWithBLOBs;
import com.sankuai.walle.dal.walle_data_center.example.TmpBdsVehicleInfoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TmpBdsVehicleInfoMapper extends MybatisBLOBsMapper<TmpBdsVehicleInfo, TmpBdsVehicleInfoExample, Long> {
    int batchInsert(@Param("list") List<TmpBdsVehicleInfoWithBLOBs> list);
}