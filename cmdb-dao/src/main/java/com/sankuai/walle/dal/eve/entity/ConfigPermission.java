package com.sankuai.walle.dal.eve.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: config_permission
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ConfigPermission {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: file
     *   说明: 配置
     */
    private String file;

    /**
     *   字段: mis
     *   说明: 用户mis
     */
    private String mis;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}