package com.sankuai.walle.dal.classify.entity;

/**
 *
 *   表名: car_device_config
 */
public class CarDeviceConfigWithBLOBs extends CarDeviceConfig {
    /**
     *   字段: config
     *   说明: 配置内容
     */
    private String config;

    /**
     *   字段: task_history_id
     *   说明: 所有下发过的历史任务id，不同id之间用逗号隔开
     */
    private String taskHistoryId;

    /**
     *   字段: config_id_history
     *   说明: 所有配置的历史id
     */
    private String configIdHistory;
}