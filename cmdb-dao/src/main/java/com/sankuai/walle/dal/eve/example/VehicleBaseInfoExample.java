package com.sankuai.walle.dal.eve.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VehicleBaseInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public VehicleBaseInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public VehicleBaseInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public VehicleBaseInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public VehicleBaseInfoExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVinIsNull() {
            addCriterion("vin is null");
            return (Criteria) this;
        }

        public Criteria andVinIsNotNull() {
            addCriterion("vin is not null");
            return (Criteria) this;
        }

        public Criteria andVinEqualTo(String value) {
            addCriterion("vin =", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotEqualTo(String value) {
            addCriterion("vin <>", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThan(String value) {
            addCriterion("vin >", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThanOrEqualTo(String value) {
            addCriterion("vin >=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThan(String value) {
            addCriterion("vin <", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThanOrEqualTo(String value) {
            addCriterion("vin <=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLike(String value) {
            addCriterion("vin like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotLike(String value) {
            addCriterion("vin not like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinIn(List<String> values) {
            addCriterion("vin in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotIn(List<String> values) {
            addCriterion("vin not in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinBetween(String value1, String value2) {
            addCriterion("vin between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotBetween(String value1, String value2) {
            addCriterion("vin not between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelIsNull() {
            addCriterion("first_class_model is null");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelIsNotNull() {
            addCriterion("first_class_model is not null");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelEqualTo(String value) {
            addCriterion("first_class_model =", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelNotEqualTo(String value) {
            addCriterion("first_class_model <>", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelGreaterThan(String value) {
            addCriterion("first_class_model >", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelGreaterThanOrEqualTo(String value) {
            addCriterion("first_class_model >=", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelLessThan(String value) {
            addCriterion("first_class_model <", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelLessThanOrEqualTo(String value) {
            addCriterion("first_class_model <=", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelLike(String value) {
            addCriterion("first_class_model like", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelNotLike(String value) {
            addCriterion("first_class_model not like", value, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelIn(List<String> values) {
            addCriterion("first_class_model in", values, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelNotIn(List<String> values) {
            addCriterion("first_class_model not in", values, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelBetween(String value1, String value2) {
            addCriterion("first_class_model between", value1, value2, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andFirstClassModelNotBetween(String value1, String value2) {
            addCriterion("first_class_model not between", value1, value2, "firstClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelIsNull() {
            addCriterion("second_class_model is null");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelIsNotNull() {
            addCriterion("second_class_model is not null");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelEqualTo(String value) {
            addCriterion("second_class_model =", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelNotEqualTo(String value) {
            addCriterion("second_class_model <>", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelGreaterThan(String value) {
            addCriterion("second_class_model >", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelGreaterThanOrEqualTo(String value) {
            addCriterion("second_class_model >=", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelLessThan(String value) {
            addCriterion("second_class_model <", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelLessThanOrEqualTo(String value) {
            addCriterion("second_class_model <=", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelLike(String value) {
            addCriterion("second_class_model like", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelNotLike(String value) {
            addCriterion("second_class_model not like", value, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelIn(List<String> values) {
            addCriterion("second_class_model in", values, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelNotIn(List<String> values) {
            addCriterion("second_class_model not in", values, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelBetween(String value1, String value2) {
            addCriterion("second_class_model between", value1, value2, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andSecondClassModelNotBetween(String value1, String value2) {
            addCriterion("second_class_model not between", value1, value2, "secondClassModel");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNull() {
            addCriterion("exec_word is null");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNotNull() {
            addCriterion("exec_word is not null");
            return (Criteria) this;
        }

        public Criteria andExecWordEqualTo(String value) {
            addCriterion("exec_word =", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotEqualTo(String value) {
            addCriterion("exec_word <>", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThan(String value) {
            addCriterion("exec_word >", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThanOrEqualTo(String value) {
            addCriterion("exec_word >=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThan(String value) {
            addCriterion("exec_word <", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThanOrEqualTo(String value) {
            addCriterion("exec_word <=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLike(String value) {
            addCriterion("exec_word like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotLike(String value) {
            addCriterion("exec_word not like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordIn(List<String> values) {
            addCriterion("exec_word in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotIn(List<String> values) {
            addCriterion("exec_word not in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordBetween(String value1, String value2) {
            addCriterion("exec_word between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotBetween(String value1, String value2) {
            addCriterion("exec_word not between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeIsNull() {
            addCriterion("car_size_type is null");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeIsNotNull() {
            addCriterion("car_size_type is not null");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeEqualTo(Byte value) {
            addCriterion("car_size_type =", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeNotEqualTo(Byte value) {
            addCriterion("car_size_type <>", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeGreaterThan(Byte value) {
            addCriterion("car_size_type >", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("car_size_type >=", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeLessThan(Byte value) {
            addCriterion("car_size_type <", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeLessThanOrEqualTo(Byte value) {
            addCriterion("car_size_type <=", value, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeIn(List<Byte> values) {
            addCriterion("car_size_type in", values, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeNotIn(List<Byte> values) {
            addCriterion("car_size_type not in", values, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeBetween(Byte value1, Byte value2) {
            addCriterion("car_size_type between", value1, value2, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andCarSizeTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("car_size_type not between", value1, value2, "carSizeType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIsNull() {
            addCriterion("power_type is null");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIsNotNull() {
            addCriterion("power_type is not null");
            return (Criteria) this;
        }

        public Criteria andPowerTypeEqualTo(Integer value) {
            addCriterion("power_type =", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotEqualTo(Integer value) {
            addCriterion("power_type <>", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeGreaterThan(Integer value) {
            addCriterion("power_type >", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("power_type >=", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeLessThan(Integer value) {
            addCriterion("power_type <", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("power_type <=", value, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeIn(List<Integer> values) {
            addCriterion("power_type in", values, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotIn(List<Integer> values) {
            addCriterion("power_type not in", values, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeBetween(Integer value1, Integer value2) {
            addCriterion("power_type between", value1, value2, "powerType");
            return (Criteria) this;
        }

        public Criteria andPowerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("power_type not between", value1, value2, "powerType");
            return (Criteria) this;
        }

        public Criteria andRefitedIsNull() {
            addCriterion("refited is null");
            return (Criteria) this;
        }

        public Criteria andRefitedIsNotNull() {
            addCriterion("refited is not null");
            return (Criteria) this;
        }

        public Criteria andRefitedEqualTo(Byte value) {
            addCriterion("refited =", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedNotEqualTo(Byte value) {
            addCriterion("refited <>", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedGreaterThan(Byte value) {
            addCriterion("refited >", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedGreaterThanOrEqualTo(Byte value) {
            addCriterion("refited >=", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedLessThan(Byte value) {
            addCriterion("refited <", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedLessThanOrEqualTo(Byte value) {
            addCriterion("refited <=", value, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedIn(List<Byte> values) {
            addCriterion("refited in", values, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedNotIn(List<Byte> values) {
            addCriterion("refited not in", values, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedBetween(Byte value1, Byte value2) {
            addCriterion("refited between", value1, value2, "refited");
            return (Criteria) this;
        }

        public Criteria andRefitedNotBetween(Byte value1, Byte value2) {
            addCriterion("refited not between", value1, value2, "refited");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsIsNull() {
            addCriterion("assembly_parts is null");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsIsNotNull() {
            addCriterion("assembly_parts is not null");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsEqualTo(String value) {
            addCriterion("assembly_parts =", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsNotEqualTo(String value) {
            addCriterion("assembly_parts <>", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsGreaterThan(String value) {
            addCriterion("assembly_parts >", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsGreaterThanOrEqualTo(String value) {
            addCriterion("assembly_parts >=", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsLessThan(String value) {
            addCriterion("assembly_parts <", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsLessThanOrEqualTo(String value) {
            addCriterion("assembly_parts <=", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsLike(String value) {
            addCriterion("assembly_parts like", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsNotLike(String value) {
            addCriterion("assembly_parts not like", value, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsIn(List<String> values) {
            addCriterion("assembly_parts in", values, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsNotIn(List<String> values) {
            addCriterion("assembly_parts not in", values, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsBetween(String value1, String value2) {
            addCriterion("assembly_parts between", value1, value2, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andAssemblyPartsNotBetween(String value1, String value2) {
            addCriterion("assembly_parts not between", value1, value2, "assemblyParts");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIsNull() {
            addCriterion("license_no is null");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIsNotNull() {
            addCriterion("license_no is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseNoEqualTo(String value) {
            addCriterion("license_no =", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotEqualTo(String value) {
            addCriterion("license_no <>", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoGreaterThan(String value) {
            addCriterion("license_no >", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoGreaterThanOrEqualTo(String value) {
            addCriterion("license_no >=", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLessThan(String value) {
            addCriterion("license_no <", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLessThanOrEqualTo(String value) {
            addCriterion("license_no <=", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLike(String value) {
            addCriterion("license_no like", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotLike(String value) {
            addCriterion("license_no not like", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIn(List<String> values) {
            addCriterion("license_no in", values, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotIn(List<String> values) {
            addCriterion("license_no not in", values, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoBetween(String value1, String value2) {
            addCriterion("license_no between", value1, value2, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotBetween(String value1, String value2) {
            addCriterion("license_no not between", value1, value2, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateIsNull() {
            addCriterion("registration_date is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateIsNotNull() {
            addCriterion("registration_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateEqualTo(Date value) {
            addCriterion("registration_date =", value, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateNotEqualTo(Date value) {
            addCriterion("registration_date <>", value, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateGreaterThan(Date value) {
            addCriterion("registration_date >", value, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("registration_date >=", value, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateLessThan(Date value) {
            addCriterion("registration_date <", value, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateLessThanOrEqualTo(Date value) {
            addCriterion("registration_date <=", value, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateIn(List<Date> values) {
            addCriterion("registration_date in", values, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateNotIn(List<Date> values) {
            addCriterion("registration_date not in", values, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateBetween(Date value1, Date value2) {
            addCriterion("registration_date between", value1, value2, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andRegistrationDateNotBetween(Date value1, Date value2) {
            addCriterion("registration_date not between", value1, value2, "registrationDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateIsNull() {
            addCriterion("annual_inspection_date is null");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateIsNotNull() {
            addCriterion("annual_inspection_date is not null");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateEqualTo(Date value) {
            addCriterion("annual_inspection_date =", value, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateNotEqualTo(Date value) {
            addCriterion("annual_inspection_date <>", value, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateGreaterThan(Date value) {
            addCriterion("annual_inspection_date >", value, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("annual_inspection_date >=", value, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateLessThan(Date value) {
            addCriterion("annual_inspection_date <", value, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateLessThanOrEqualTo(Date value) {
            addCriterion("annual_inspection_date <=", value, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateIn(List<Date> values) {
            addCriterion("annual_inspection_date in", values, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateNotIn(List<Date> values) {
            addCriterion("annual_inspection_date not in", values, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateBetween(Date value1, Date value2) {
            addCriterion("annual_inspection_date between", value1, value2, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andAnnualInspectionDateNotBetween(Date value1, Date value2) {
            addCriterion("annual_inspection_date not between", value1, value2, "annualInspectionDate");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerIsNull() {
            addCriterion("car_preduce_owner is null");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerIsNotNull() {
            addCriterion("car_preduce_owner is not null");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerEqualTo(String value) {
            addCriterion("car_preduce_owner =", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerNotEqualTo(String value) {
            addCriterion("car_preduce_owner <>", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerGreaterThan(String value) {
            addCriterion("car_preduce_owner >", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("car_preduce_owner >=", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerLessThan(String value) {
            addCriterion("car_preduce_owner <", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerLessThanOrEqualTo(String value) {
            addCriterion("car_preduce_owner <=", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerLike(String value) {
            addCriterion("car_preduce_owner like", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerNotLike(String value) {
            addCriterion("car_preduce_owner not like", value, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerIn(List<String> values) {
            addCriterion("car_preduce_owner in", values, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerNotIn(List<String> values) {
            addCriterion("car_preduce_owner not in", values, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerBetween(String value1, String value2) {
            addCriterion("car_preduce_owner between", value1, value2, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andCarPreduceOwnerNotBetween(String value1, String value2) {
            addCriterion("car_preduce_owner not between", value1, value2, "carPreduceOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerIsNull() {
            addCriterion("indicate_owner is null");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerIsNotNull() {
            addCriterion("indicate_owner is not null");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerEqualTo(String value) {
            addCriterion("indicate_owner =", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotEqualTo(String value) {
            addCriterion("indicate_owner <>", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerGreaterThan(String value) {
            addCriterion("indicate_owner >", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("indicate_owner >=", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerLessThan(String value) {
            addCriterion("indicate_owner <", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerLessThanOrEqualTo(String value) {
            addCriterion("indicate_owner <=", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerLike(String value) {
            addCriterion("indicate_owner like", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotLike(String value) {
            addCriterion("indicate_owner not like", value, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerIn(List<String> values) {
            addCriterion("indicate_owner in", values, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotIn(List<String> values) {
            addCriterion("indicate_owner not in", values, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerBetween(String value1, String value2) {
            addCriterion("indicate_owner between", value1, value2, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateOwnerNotBetween(String value1, String value2) {
            addCriterion("indicate_owner not between", value1, value2, "indicateOwner");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationIsNull() {
            addCriterion("indicate_rental_duration is null");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationIsNotNull() {
            addCriterion("indicate_rental_duration is not null");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationEqualTo(Long value) {
            addCriterion("indicate_rental_duration =", value, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationNotEqualTo(Long value) {
            addCriterion("indicate_rental_duration <>", value, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationGreaterThan(Long value) {
            addCriterion("indicate_rental_duration >", value, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationGreaterThanOrEqualTo(Long value) {
            addCriterion("indicate_rental_duration >=", value, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationLessThan(Long value) {
            addCriterion("indicate_rental_duration <", value, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationLessThanOrEqualTo(Long value) {
            addCriterion("indicate_rental_duration <=", value, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationIn(List<Long> values) {
            addCriterion("indicate_rental_duration in", values, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationNotIn(List<Long> values) {
            addCriterion("indicate_rental_duration not in", values, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationBetween(Long value1, Long value2) {
            addCriterion("indicate_rental_duration between", value1, value2, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andIndicateRentalDurationNotBetween(Long value1, Long value2) {
            addCriterion("indicate_rental_duration not between", value1, value2, "indicateRentalDuration");
            return (Criteria) this;
        }

        public Criteria andRentExpenseIsNull() {
            addCriterion("rent_expense is null");
            return (Criteria) this;
        }

        public Criteria andRentExpenseIsNotNull() {
            addCriterion("rent_expense is not null");
            return (Criteria) this;
        }

        public Criteria andRentExpenseEqualTo(Long value) {
            addCriterion("rent_expense =", value, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseNotEqualTo(Long value) {
            addCriterion("rent_expense <>", value, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseGreaterThan(Long value) {
            addCriterion("rent_expense >", value, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseGreaterThanOrEqualTo(Long value) {
            addCriterion("rent_expense >=", value, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseLessThan(Long value) {
            addCriterion("rent_expense <", value, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseLessThanOrEqualTo(Long value) {
            addCriterion("rent_expense <=", value, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseIn(List<Long> values) {
            addCriterion("rent_expense in", values, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseNotIn(List<Long> values) {
            addCriterion("rent_expense not in", values, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseBetween(Long value1, Long value2) {
            addCriterion("rent_expense between", value1, value2, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andRentExpenseNotBetween(Long value1, Long value2) {
            addCriterion("rent_expense not between", value1, value2, "rentExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseIsNull() {
            addCriterion("leasing_expense is null");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseIsNotNull() {
            addCriterion("leasing_expense is not null");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseEqualTo(Long value) {
            addCriterion("leasing_expense =", value, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseNotEqualTo(Long value) {
            addCriterion("leasing_expense <>", value, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseGreaterThan(Long value) {
            addCriterion("leasing_expense >", value, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseGreaterThanOrEqualTo(Long value) {
            addCriterion("leasing_expense >=", value, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseLessThan(Long value) {
            addCriterion("leasing_expense <", value, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseLessThanOrEqualTo(Long value) {
            addCriterion("leasing_expense <=", value, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseIn(List<Long> values) {
            addCriterion("leasing_expense in", values, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseNotIn(List<Long> values) {
            addCriterion("leasing_expense not in", values, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseBetween(Long value1, Long value2) {
            addCriterion("leasing_expense between", value1, value2, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingExpenseNotBetween(Long value1, Long value2) {
            addCriterion("leasing_expense not between", value1, value2, "leasingExpense");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationIsNull() {
            addCriterion("leasing_duration is null");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationIsNotNull() {
            addCriterion("leasing_duration is not null");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationEqualTo(Long value) {
            addCriterion("leasing_duration =", value, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationNotEqualTo(Long value) {
            addCriterion("leasing_duration <>", value, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationGreaterThan(Long value) {
            addCriterion("leasing_duration >", value, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationGreaterThanOrEqualTo(Long value) {
            addCriterion("leasing_duration >=", value, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationLessThan(Long value) {
            addCriterion("leasing_duration <", value, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationLessThanOrEqualTo(Long value) {
            addCriterion("leasing_duration <=", value, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationIn(List<Long> values) {
            addCriterion("leasing_duration in", values, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationNotIn(List<Long> values) {
            addCriterion("leasing_duration not in", values, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationBetween(Long value1, Long value2) {
            addCriterion("leasing_duration between", value1, value2, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andLeasingDurationNotBetween(Long value1, Long value2) {
            addCriterion("leasing_duration not between", value1, value2, "leasingDuration");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceIsNull() {
            addCriterion("heavy_traffic_insurance is null");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceIsNotNull() {
            addCriterion("heavy_traffic_insurance is not null");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceEqualTo(Date value) {
            addCriterion("heavy_traffic_insurance =", value, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceNotEqualTo(Date value) {
            addCriterion("heavy_traffic_insurance <>", value, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceGreaterThan(Date value) {
            addCriterion("heavy_traffic_insurance >", value, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceGreaterThanOrEqualTo(Date value) {
            addCriterion("heavy_traffic_insurance >=", value, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceLessThan(Date value) {
            addCriterion("heavy_traffic_insurance <", value, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceLessThanOrEqualTo(Date value) {
            addCriterion("heavy_traffic_insurance <=", value, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceIn(List<Date> values) {
            addCriterion("heavy_traffic_insurance in", values, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceNotIn(List<Date> values) {
            addCriterion("heavy_traffic_insurance not in", values, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceBetween(Date value1, Date value2) {
            addCriterion("heavy_traffic_insurance between", value1, value2, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andHeavyTrafficInsuranceNotBetween(Date value1, Date value2) {
            addCriterion("heavy_traffic_insurance not between", value1, value2, "heavyTrafficInsurance");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateIsNull() {
            addCriterion("business_insurance_date is null");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateIsNotNull() {
            addCriterion("business_insurance_date is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateEqualTo(Date value) {
            addCriterion("business_insurance_date =", value, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateNotEqualTo(Date value) {
            addCriterion("business_insurance_date <>", value, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateGreaterThan(Date value) {
            addCriterion("business_insurance_date >", value, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("business_insurance_date >=", value, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateLessThan(Date value) {
            addCriterion("business_insurance_date <", value, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateLessThanOrEqualTo(Date value) {
            addCriterion("business_insurance_date <=", value, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateIn(List<Date> values) {
            addCriterion("business_insurance_date in", values, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateNotIn(List<Date> values) {
            addCriterion("business_insurance_date not in", values, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateBetween(Date value1, Date value2) {
            addCriterion("business_insurance_date between", value1, value2, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andBusinessInsuranceDateNotBetween(Date value1, Date value2) {
            addCriterion("business_insurance_date not between", value1, value2, "businessInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateIsNull() {
            addCriterion("equipment_insurance_date is null");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateIsNotNull() {
            addCriterion("equipment_insurance_date is not null");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateEqualTo(Date value) {
            addCriterion("equipment_insurance_date =", value, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateNotEqualTo(Date value) {
            addCriterion("equipment_insurance_date <>", value, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateGreaterThan(Date value) {
            addCriterion("equipment_insurance_date >", value, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("equipment_insurance_date >=", value, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateLessThan(Date value) {
            addCriterion("equipment_insurance_date <", value, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateLessThanOrEqualTo(Date value) {
            addCriterion("equipment_insurance_date <=", value, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateIn(List<Date> values) {
            addCriterion("equipment_insurance_date in", values, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateNotIn(List<Date> values) {
            addCriterion("equipment_insurance_date not in", values, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateBetween(Date value1, Date value2) {
            addCriterion("equipment_insurance_date between", value1, value2, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andEquipmentInsuranceDateNotBetween(Date value1, Date value2) {
            addCriterion("equipment_insurance_date not between", value1, value2, "equipmentInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateIsNull() {
            addCriterion("priority_insurance_date is null");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateIsNotNull() {
            addCriterion("priority_insurance_date is not null");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateEqualTo(Date value) {
            addCriterion("priority_insurance_date =", value, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateNotEqualTo(Date value) {
            addCriterion("priority_insurance_date <>", value, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateGreaterThan(Date value) {
            addCriterion("priority_insurance_date >", value, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("priority_insurance_date >=", value, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateLessThan(Date value) {
            addCriterion("priority_insurance_date <", value, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateLessThanOrEqualTo(Date value) {
            addCriterion("priority_insurance_date <=", value, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateIn(List<Date> values) {
            addCriterion("priority_insurance_date in", values, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateNotIn(List<Date> values) {
            addCriterion("priority_insurance_date not in", values, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateBetween(Date value1, Date value2) {
            addCriterion("priority_insurance_date between", value1, value2, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andPriorityInsuranceDateNotBetween(Date value1, Date value2) {
            addCriterion("priority_insurance_date not between", value1, value2, "priorityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateIsNull() {
            addCriterion("third_liability_insurance_date is null");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateIsNotNull() {
            addCriterion("third_liability_insurance_date is not null");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateEqualTo(Date value) {
            addCriterion("third_liability_insurance_date =", value, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateNotEqualTo(Date value) {
            addCriterion("third_liability_insurance_date <>", value, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateGreaterThan(Date value) {
            addCriterion("third_liability_insurance_date >", value, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("third_liability_insurance_date >=", value, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateLessThan(Date value) {
            addCriterion("third_liability_insurance_date <", value, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateLessThanOrEqualTo(Date value) {
            addCriterion("third_liability_insurance_date <=", value, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateIn(List<Date> values) {
            addCriterion("third_liability_insurance_date in", values, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateNotIn(List<Date> values) {
            addCriterion("third_liability_insurance_date not in", values, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateBetween(Date value1, Date value2) {
            addCriterion("third_liability_insurance_date between", value1, value2, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andThirdLiabilityInsuranceDateNotBetween(Date value1, Date value2) {
            addCriterion("third_liability_insurance_date not between", value1, value2, "thirdLiabilityInsuranceDate");
            return (Criteria) this;
        }

        public Criteria andKeyNumIsNull() {
            addCriterion("key_num is null");
            return (Criteria) this;
        }

        public Criteria andKeyNumIsNotNull() {
            addCriterion("key_num is not null");
            return (Criteria) this;
        }

        public Criteria andKeyNumEqualTo(Long value) {
            addCriterion("key_num =", value, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumNotEqualTo(Long value) {
            addCriterion("key_num <>", value, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumGreaterThan(Long value) {
            addCriterion("key_num >", value, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumGreaterThanOrEqualTo(Long value) {
            addCriterion("key_num >=", value, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumLessThan(Long value) {
            addCriterion("key_num <", value, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumLessThanOrEqualTo(Long value) {
            addCriterion("key_num <=", value, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumIn(List<Long> values) {
            addCriterion("key_num in", values, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumNotIn(List<Long> values) {
            addCriterion("key_num not in", values, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumBetween(Long value1, Long value2) {
            addCriterion("key_num between", value1, value2, "keyNum");
            return (Criteria) this;
        }

        public Criteria andKeyNumNotBetween(Long value1, Long value2) {
            addCriterion("key_num not between", value1, value2, "keyNum");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerIsNull() {
            addCriterion("back_key_owner is null");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerIsNotNull() {
            addCriterion("back_key_owner is not null");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerEqualTo(String value) {
            addCriterion("back_key_owner =", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerNotEqualTo(String value) {
            addCriterion("back_key_owner <>", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerGreaterThan(String value) {
            addCriterion("back_key_owner >", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("back_key_owner >=", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerLessThan(String value) {
            addCriterion("back_key_owner <", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerLessThanOrEqualTo(String value) {
            addCriterion("back_key_owner <=", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerLike(String value) {
            addCriterion("back_key_owner like", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerNotLike(String value) {
            addCriterion("back_key_owner not like", value, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerIn(List<String> values) {
            addCriterion("back_key_owner in", values, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerNotIn(List<String> values) {
            addCriterion("back_key_owner not in", values, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerBetween(String value1, String value2) {
            addCriterion("back_key_owner between", value1, value2, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andBackKeyOwnerNotBetween(String value1, String value2) {
            addCriterion("back_key_owner not between", value1, value2, "backKeyOwner");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarIsNull() {
            addCriterion("driving_permit_in_car is null");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarIsNotNull() {
            addCriterion("driving_permit_in_car is not null");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarEqualTo(String value) {
            addCriterion("driving_permit_in_car =", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotEqualTo(String value) {
            addCriterion("driving_permit_in_car <>", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarGreaterThan(String value) {
            addCriterion("driving_permit_in_car >", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarGreaterThanOrEqualTo(String value) {
            addCriterion("driving_permit_in_car >=", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarLessThan(String value) {
            addCriterion("driving_permit_in_car <", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarLessThanOrEqualTo(String value) {
            addCriterion("driving_permit_in_car <=", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarLike(String value) {
            addCriterion("driving_permit_in_car like", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotLike(String value) {
            addCriterion("driving_permit_in_car not like", value, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarIn(List<String> values) {
            addCriterion("driving_permit_in_car in", values, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotIn(List<String> values) {
            addCriterion("driving_permit_in_car not in", values, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarBetween(String value1, String value2) {
            addCriterion("driving_permit_in_car between", value1, value2, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andDrivingPermitInCarNotBetween(String value1, String value2) {
            addCriterion("driving_permit_in_car not between", value1, value2, "drivingPermitInCar");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetIsNull() {
            addCriterion("car_used_target is null");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetIsNotNull() {
            addCriterion("car_used_target is not null");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetEqualTo(String value) {
            addCriterion("car_used_target =", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotEqualTo(String value) {
            addCriterion("car_used_target <>", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetGreaterThan(String value) {
            addCriterion("car_used_target >", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetGreaterThanOrEqualTo(String value) {
            addCriterion("car_used_target >=", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetLessThan(String value) {
            addCriterion("car_used_target <", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetLessThanOrEqualTo(String value) {
            addCriterion("car_used_target <=", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetLike(String value) {
            addCriterion("car_used_target like", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotLike(String value) {
            addCriterion("car_used_target not like", value, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetIn(List<String> values) {
            addCriterion("car_used_target in", values, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotIn(List<String> values) {
            addCriterion("car_used_target not in", values, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetBetween(String value1, String value2) {
            addCriterion("car_used_target between", value1, value2, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andCarUsedTargetNotBetween(String value1, String value2) {
            addCriterion("car_used_target not between", value1, value2, "carUsedTarget");
            return (Criteria) this;
        }

        public Criteria andVehicleNameIsNull() {
            addCriterion("vehicle_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleNameIsNotNull() {
            addCriterion("vehicle_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleNameEqualTo(String value) {
            addCriterion("vehicle_name =", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameNotEqualTo(String value) {
            addCriterion("vehicle_name <>", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameGreaterThan(String value) {
            addCriterion("vehicle_name >", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_name >=", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameLessThan(String value) {
            addCriterion("vehicle_name <", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_name <=", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameLike(String value) {
            addCriterion("vehicle_name like", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameNotLike(String value) {
            addCriterion("vehicle_name not like", value, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameIn(List<String> values) {
            addCriterion("vehicle_name in", values, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameNotIn(List<String> values) {
            addCriterion("vehicle_name not in", values, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameBetween(String value1, String value2) {
            addCriterion("vehicle_name between", value1, value2, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andVehicleNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_name not between", value1, value2, "vehicleName");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andPlaceIsNull() {
            addCriterion("place is null");
            return (Criteria) this;
        }

        public Criteria andPlaceIsNotNull() {
            addCriterion("place is not null");
            return (Criteria) this;
        }

        public Criteria andPlaceEqualTo(String value) {
            addCriterion("place =", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceNotEqualTo(String value) {
            addCriterion("place <>", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceGreaterThan(String value) {
            addCriterion("place >", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("place >=", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceLessThan(String value) {
            addCriterion("place <", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceLessThanOrEqualTo(String value) {
            addCriterion("place <=", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceLike(String value) {
            addCriterion("place like", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceNotLike(String value) {
            addCriterion("place not like", value, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceIn(List<String> values) {
            addCriterion("place in", values, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceNotIn(List<String> values) {
            addCriterion("place not in", values, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceBetween(String value1, String value2) {
            addCriterion("place between", value1, value2, "place");
            return (Criteria) this;
        }

        public Criteria andPlaceNotBetween(String value1, String value2) {
            addCriterion("place not between", value1, value2, "place");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIsNull() {
            addCriterion("business_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIsNotNull() {
            addCriterion("business_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNameEqualTo(String value) {
            addCriterion("business_name =", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotEqualTo(String value) {
            addCriterion("business_name <>", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameGreaterThan(String value) {
            addCriterion("business_name >", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_name >=", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLessThan(String value) {
            addCriterion("business_name <", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLessThanOrEqualTo(String value) {
            addCriterion("business_name <=", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLike(String value) {
            addCriterion("business_name like", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotLike(String value) {
            addCriterion("business_name not like", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIn(List<String> values) {
            addCriterion("business_name in", values, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotIn(List<String> values) {
            addCriterion("business_name not in", values, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameBetween(String value1, String value2) {
            addCriterion("business_name between", value1, value2, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotBetween(String value1, String value2) {
            addCriterion("business_name not between", value1, value2, "businessName");
            return (Criteria) this;
        }

        public Criteria andAbilityIsNull() {
            addCriterion("ability is null");
            return (Criteria) this;
        }

        public Criteria andAbilityIsNotNull() {
            addCriterion("ability is not null");
            return (Criteria) this;
        }

        public Criteria andAbilityEqualTo(String value) {
            addCriterion("ability =", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityNotEqualTo(String value) {
            addCriterion("ability <>", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityGreaterThan(String value) {
            addCriterion("ability >", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityGreaterThanOrEqualTo(String value) {
            addCriterion("ability >=", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityLessThan(String value) {
            addCriterion("ability <", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityLessThanOrEqualTo(String value) {
            addCriterion("ability <=", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityLike(String value) {
            addCriterion("ability like", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityNotLike(String value) {
            addCriterion("ability not like", value, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityIn(List<String> values) {
            addCriterion("ability in", values, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityNotIn(List<String> values) {
            addCriterion("ability not in", values, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityBetween(String value1, String value2) {
            addCriterion("ability between", value1, value2, "ability");
            return (Criteria) this;
        }

        public Criteria andAbilityNotBetween(String value1, String value2) {
            addCriterion("ability not between", value1, value2, "ability");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andPersonMisIsNull() {
            addCriterion("person_mis is null");
            return (Criteria) this;
        }

        public Criteria andPersonMisIsNotNull() {
            addCriterion("person_mis is not null");
            return (Criteria) this;
        }

        public Criteria andPersonMisEqualTo(String value) {
            addCriterion("person_mis =", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisNotEqualTo(String value) {
            addCriterion("person_mis <>", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisGreaterThan(String value) {
            addCriterion("person_mis >", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisGreaterThanOrEqualTo(String value) {
            addCriterion("person_mis >=", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisLessThan(String value) {
            addCriterion("person_mis <", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisLessThanOrEqualTo(String value) {
            addCriterion("person_mis <=", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisLike(String value) {
            addCriterion("person_mis like", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisNotLike(String value) {
            addCriterion("person_mis not like", value, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisIn(List<String> values) {
            addCriterion("person_mis in", values, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisNotIn(List<String> values) {
            addCriterion("person_mis not in", values, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisBetween(String value1, String value2) {
            addCriterion("person_mis between", value1, value2, "personMis");
            return (Criteria) this;
        }

        public Criteria andPersonMisNotBetween(String value1, String value2) {
            addCriterion("person_mis not between", value1, value2, "personMis");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIsNull() {
            addCriterion("owner_department is null");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIsNotNull() {
            addCriterion("owner_department is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentEqualTo(String value) {
            addCriterion("owner_department =", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotEqualTo(String value) {
            addCriterion("owner_department <>", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThan(String value) {
            addCriterion("owner_department >", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("owner_department >=", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThan(String value) {
            addCriterion("owner_department <", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLessThanOrEqualTo(String value) {
            addCriterion("owner_department <=", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentLike(String value) {
            addCriterion("owner_department like", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotLike(String value) {
            addCriterion("owner_department not like", value, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentIn(List<String> values) {
            addCriterion("owner_department in", values, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotIn(List<String> values) {
            addCriterion("owner_department not in", values, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentBetween(String value1, String value2) {
            addCriterion("owner_department between", value1, value2, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andOwnerDepartmentNotBetween(String value1, String value2) {
            addCriterion("owner_department not between", value1, value2, "ownerDepartment");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("`label` is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("`label` is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("`label` =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("`label` <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("`label` >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("`label` >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("`label` <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("`label` <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("`label` like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("`label` not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("`label` in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("`label` not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("`label` between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("`label` not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeIsNull() {
            addCriterion("car_belong_type is null");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeIsNotNull() {
            addCriterion("car_belong_type is not null");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeEqualTo(Long value) {
            addCriterion("car_belong_type =", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeNotEqualTo(Long value) {
            addCriterion("car_belong_type <>", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeGreaterThan(Long value) {
            addCriterion("car_belong_type >", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("car_belong_type >=", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeLessThan(Long value) {
            addCriterion("car_belong_type <", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeLessThanOrEqualTo(Long value) {
            addCriterion("car_belong_type <=", value, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeIn(List<Long> values) {
            addCriterion("car_belong_type in", values, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeNotIn(List<Long> values) {
            addCriterion("car_belong_type not in", values, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeBetween(Long value1, Long value2) {
            addCriterion("car_belong_type between", value1, value2, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarBelongTypeNotBetween(Long value1, Long value2) {
            addCriterion("car_belong_type not between", value1, value2, "carBelongType");
            return (Criteria) this;
        }

        public Criteria andCarOwnerIsNull() {
            addCriterion("car_owner is null");
            return (Criteria) this;
        }

        public Criteria andCarOwnerIsNotNull() {
            addCriterion("car_owner is not null");
            return (Criteria) this;
        }

        public Criteria andCarOwnerEqualTo(String value) {
            addCriterion("car_owner =", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotEqualTo(String value) {
            addCriterion("car_owner <>", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerGreaterThan(String value) {
            addCriterion("car_owner >", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("car_owner >=", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerLessThan(String value) {
            addCriterion("car_owner <", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerLessThanOrEqualTo(String value) {
            addCriterion("car_owner <=", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerLike(String value) {
            addCriterion("car_owner like", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotLike(String value) {
            addCriterion("car_owner not like", value, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerIn(List<String> values) {
            addCriterion("car_owner in", values, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotIn(List<String> values) {
            addCriterion("car_owner not in", values, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerBetween(String value1, String value2) {
            addCriterion("car_owner between", value1, value2, "carOwner");
            return (Criteria) this;
        }

        public Criteria andCarOwnerNotBetween(String value1, String value2) {
            addCriterion("car_owner not between", value1, value2, "carOwner");
            return (Criteria) this;
        }

        public Criteria andScrapIsNull() {
            addCriterion("scrap is null");
            return (Criteria) this;
        }

        public Criteria andScrapIsNotNull() {
            addCriterion("scrap is not null");
            return (Criteria) this;
        }

        public Criteria andScrapEqualTo(Boolean value) {
            addCriterion("scrap =", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotEqualTo(Boolean value) {
            addCriterion("scrap <>", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapGreaterThan(Boolean value) {
            addCriterion("scrap >", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapGreaterThanOrEqualTo(Boolean value) {
            addCriterion("scrap >=", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapLessThan(Boolean value) {
            addCriterion("scrap <", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapLessThanOrEqualTo(Boolean value) {
            addCriterion("scrap <=", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapIn(List<Boolean> values) {
            addCriterion("scrap in", values, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotIn(List<Boolean> values) {
            addCriterion("scrap not in", values, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap between", value1, value2, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap not between", value1, value2, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapReasonIsNull() {
            addCriterion("scrap_reason is null");
            return (Criteria) this;
        }

        public Criteria andScrapReasonIsNotNull() {
            addCriterion("scrap_reason is not null");
            return (Criteria) this;
        }

        public Criteria andScrapReasonEqualTo(String value) {
            addCriterion("scrap_reason =", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotEqualTo(String value) {
            addCriterion("scrap_reason <>", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonGreaterThan(String value) {
            addCriterion("scrap_reason >", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonGreaterThanOrEqualTo(String value) {
            addCriterion("scrap_reason >=", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonLessThan(String value) {
            addCriterion("scrap_reason <", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonLessThanOrEqualTo(String value) {
            addCriterion("scrap_reason <=", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonLike(String value) {
            addCriterion("scrap_reason like", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotLike(String value) {
            addCriterion("scrap_reason not like", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonIn(List<String> values) {
            addCriterion("scrap_reason in", values, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotIn(List<String> values) {
            addCriterion("scrap_reason not in", values, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonBetween(String value1, String value2) {
            addCriterion("scrap_reason between", value1, value2, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotBetween(String value1, String value2) {
            addCriterion("scrap_reason not between", value1, value2, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}