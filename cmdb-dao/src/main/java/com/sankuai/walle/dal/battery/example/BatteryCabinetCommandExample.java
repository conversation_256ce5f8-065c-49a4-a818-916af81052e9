package com.sankuai.walle.dal.battery.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BatteryCabinetCommandExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public BatteryCabinetCommandExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public BatteryCabinetCommandExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public BatteryCabinetCommandExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public BatteryCabinetCommandExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierIsNull() {
            addCriterion("module_identifier is null");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierIsNotNull() {
            addCriterion("module_identifier is not null");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierEqualTo(String value) {
            addCriterion("module_identifier =", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierNotEqualTo(String value) {
            addCriterion("module_identifier <>", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierGreaterThan(String value) {
            addCriterion("module_identifier >", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierGreaterThanOrEqualTo(String value) {
            addCriterion("module_identifier >=", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierLessThan(String value) {
            addCriterion("module_identifier <", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierLessThanOrEqualTo(String value) {
            addCriterion("module_identifier <=", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierLike(String value) {
            addCriterion("module_identifier like", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierNotLike(String value) {
            addCriterion("module_identifier not like", value, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierIn(List<String> values) {
            addCriterion("module_identifier in", values, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierNotIn(List<String> values) {
            addCriterion("module_identifier not in", values, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierBetween(String value1, String value2) {
            addCriterion("module_identifier between", value1, value2, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andModuleIdentifierNotBetween(String value1, String value2) {
            addCriterion("module_identifier not between", value1, value2, "moduleIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierIsNull() {
            addCriterion("command_identifier is null");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierIsNotNull() {
            addCriterion("command_identifier is not null");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierEqualTo(String value) {
            addCriterion("command_identifier =", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierNotEqualTo(String value) {
            addCriterion("command_identifier <>", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierGreaterThan(String value) {
            addCriterion("command_identifier >", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierGreaterThanOrEqualTo(String value) {
            addCriterion("command_identifier >=", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierLessThan(String value) {
            addCriterion("command_identifier <", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierLessThanOrEqualTo(String value) {
            addCriterion("command_identifier <=", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierLike(String value) {
            addCriterion("command_identifier like", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierNotLike(String value) {
            addCriterion("command_identifier not like", value, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierIn(List<String> values) {
            addCriterion("command_identifier in", values, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierNotIn(List<String> values) {
            addCriterion("command_identifier not in", values, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierBetween(String value1, String value2) {
            addCriterion("command_identifier between", value1, value2, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandIdentifierNotBetween(String value1, String value2) {
            addCriterion("command_identifier not between", value1, value2, "commandIdentifier");
            return (Criteria) this;
        }

        public Criteria andCommandContentIsNull() {
            addCriterion("command_content is null");
            return (Criteria) this;
        }

        public Criteria andCommandContentIsNotNull() {
            addCriterion("command_content is not null");
            return (Criteria) this;
        }

        public Criteria andCommandContentEqualTo(String value) {
            addCriterion("command_content =", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentNotEqualTo(String value) {
            addCriterion("command_content <>", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentGreaterThan(String value) {
            addCriterion("command_content >", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentGreaterThanOrEqualTo(String value) {
            addCriterion("command_content >=", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentLessThan(String value) {
            addCriterion("command_content <", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentLessThanOrEqualTo(String value) {
            addCriterion("command_content <=", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentLike(String value) {
            addCriterion("command_content like", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentNotLike(String value) {
            addCriterion("command_content not like", value, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentIn(List<String> values) {
            addCriterion("command_content in", values, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentNotIn(List<String> values) {
            addCriterion("command_content not in", values, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentBetween(String value1, String value2) {
            addCriterion("command_content between", value1, value2, "commandContent");
            return (Criteria) this;
        }

        public Criteria andCommandContentNotBetween(String value1, String value2) {
            addCriterion("command_content not between", value1, value2, "commandContent");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusMsgIsNull() {
            addCriterion("status_msg is null");
            return (Criteria) this;
        }

        public Criteria andStatusMsgIsNotNull() {
            addCriterion("status_msg is not null");
            return (Criteria) this;
        }

        public Criteria andStatusMsgEqualTo(String value) {
            addCriterion("status_msg =", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgNotEqualTo(String value) {
            addCriterion("status_msg <>", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgGreaterThan(String value) {
            addCriterion("status_msg >", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgGreaterThanOrEqualTo(String value) {
            addCriterion("status_msg >=", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgLessThan(String value) {
            addCriterion("status_msg <", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgLessThanOrEqualTo(String value) {
            addCriterion("status_msg <=", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgLike(String value) {
            addCriterion("status_msg like", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgNotLike(String value) {
            addCriterion("status_msg not like", value, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgIn(List<String> values) {
            addCriterion("status_msg in", values, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgNotIn(List<String> values) {
            addCriterion("status_msg not in", values, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgBetween(String value1, String value2) {
            addCriterion("status_msg between", value1, value2, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andStatusMsgNotBetween(String value1, String value2) {
            addCriterion("status_msg not between", value1, value2, "statusMsg");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNull() {
            addCriterion("request_id is null");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNotNull() {
            addCriterion("request_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequestIdEqualTo(String value) {
            addCriterion("request_id =", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotEqualTo(String value) {
            addCriterion("request_id <>", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThan(String value) {
            addCriterion("request_id >", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("request_id >=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThan(String value) {
            addCriterion("request_id <", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThanOrEqualTo(String value) {
            addCriterion("request_id <=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLike(String value) {
            addCriterion("request_id like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotLike(String value) {
            addCriterion("request_id not like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdIn(List<String> values) {
            addCriterion("request_id in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotIn(List<String> values) {
            addCriterion("request_id not in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdBetween(String value1, String value2) {
            addCriterion("request_id between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotBetween(String value1, String value2) {
            addCriterion("request_id not between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andMisIsNull() {
            addCriterion("mis is null");
            return (Criteria) this;
        }

        public Criteria andMisIsNotNull() {
            addCriterion("mis is not null");
            return (Criteria) this;
        }

        public Criteria andMisEqualTo(String value) {
            addCriterion("mis =", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotEqualTo(String value) {
            addCriterion("mis <>", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisGreaterThan(String value) {
            addCriterion("mis >", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisGreaterThanOrEqualTo(String value) {
            addCriterion("mis >=", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisLessThan(String value) {
            addCriterion("mis <", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisLessThanOrEqualTo(String value) {
            addCriterion("mis <=", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisLike(String value) {
            addCriterion("mis like", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotLike(String value) {
            addCriterion("mis not like", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisIn(List<String> values) {
            addCriterion("mis in", values, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotIn(List<String> values) {
            addCriterion("mis not in", values, "mis");
            return (Criteria) this;
        }

        public Criteria andMisBetween(String value1, String value2) {
            addCriterion("mis between", value1, value2, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotBetween(String value1, String value2) {
            addCriterion("mis not between", value1, value2, "mis");
            return (Criteria) this;
        }

        public Criteria andRepIsNull() {
            addCriterion("rep is null");
            return (Criteria) this;
        }

        public Criteria andRepIsNotNull() {
            addCriterion("rep is not null");
            return (Criteria) this;
        }

        public Criteria andRepEqualTo(String value) {
            addCriterion("rep =", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepNotEqualTo(String value) {
            addCriterion("rep <>", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepGreaterThan(String value) {
            addCriterion("rep >", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepGreaterThanOrEqualTo(String value) {
            addCriterion("rep >=", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepLessThan(String value) {
            addCriterion("rep <", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepLessThanOrEqualTo(String value) {
            addCriterion("rep <=", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepLike(String value) {
            addCriterion("rep like", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepNotLike(String value) {
            addCriterion("rep not like", value, "rep");
            return (Criteria) this;
        }

        public Criteria andRepIn(List<String> values) {
            addCriterion("rep in", values, "rep");
            return (Criteria) this;
        }

        public Criteria andRepNotIn(List<String> values) {
            addCriterion("rep not in", values, "rep");
            return (Criteria) this;
        }

        public Criteria andRepBetween(String value1, String value2) {
            addCriterion("rep between", value1, value2, "rep");
            return (Criteria) this;
        }

        public Criteria andRepNotBetween(String value1, String value2) {
            addCriterion("rep not between", value1, value2, "rep");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyIsNull() {
            addCriterion("device_key is null");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyIsNotNull() {
            addCriterion("device_key is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyEqualTo(String value) {
            addCriterion("device_key =", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyNotEqualTo(String value) {
            addCriterion("device_key <>", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyGreaterThan(String value) {
            addCriterion("device_key >", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyGreaterThanOrEqualTo(String value) {
            addCriterion("device_key >=", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyLessThan(String value) {
            addCriterion("device_key <", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyLessThanOrEqualTo(String value) {
            addCriterion("device_key <=", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyLike(String value) {
            addCriterion("device_key like", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyNotLike(String value) {
            addCriterion("device_key not like", value, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyIn(List<String> values) {
            addCriterion("device_key in", values, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyNotIn(List<String> values) {
            addCriterion("device_key not in", values, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyBetween(String value1, String value2) {
            addCriterion("device_key between", value1, value2, "deviceKey");
            return (Criteria) this;
        }

        public Criteria andDeviceKeyNotBetween(String value1, String value2) {
            addCriterion("device_key not between", value1, value2, "deviceKey");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}