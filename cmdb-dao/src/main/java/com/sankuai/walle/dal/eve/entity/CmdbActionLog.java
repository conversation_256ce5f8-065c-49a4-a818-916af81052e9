package com.sankuai.walle.dal.eve.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: cmdb_action_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CmdbActionLog {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: mis
     *   说明: 操作人id
     */
    private String mis;

    /**
     *   字段: vin
     *   说明: 操作的车辆
     */
    private String vin;

    /**
     *   字段: action_content
     *   说明: 操作的内容描述
     */
    private String actionContent;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: type
     *   说明: 类型
     */
    private String type;
}