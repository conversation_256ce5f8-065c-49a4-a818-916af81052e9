package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_device_config_extend
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarDeviceConfigExtend {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: config_id
     *   说明: car_device_config表的id
     */
    private Long configId;

    /**
     *   字段: error_status
     *   说明: 如果statuss是2,代表车端业务执行错误，会透传错误消息到此处
     */
    private String errorStatus;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}