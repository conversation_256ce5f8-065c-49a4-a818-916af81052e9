package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.classify.example.CarConfigTaskExample;
import com.sankuai.walle.dal.classify.entity.CarConfigTask;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarConfigTaskMapper extends MybatisBLOBsMapper<CarConfigTask, CarConfigTaskExample, Long> {
    int batchInsert(@Param("list") List<CarConfigTask> list);
}