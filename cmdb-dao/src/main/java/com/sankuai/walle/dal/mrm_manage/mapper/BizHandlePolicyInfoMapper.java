package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo;
import com.sankuai.walle.dal.mrm_manage.example.BizHandlePolicyInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface BizHandlePolicyInfoMapper extends MybatisBaseMapper<BizHandlePolicyInfo, BizHandlePolicyInfoExample, Long> {
    int batchInsert(@Param("list") List<BizHandlePolicyInfo> list);

}