package com.sankuai.walle.dal.mrm_manage.entity;

import lombok.*;

/**
 *
 *   表名: device_software
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DeviceSoftware {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: related_remote_device_type_id
     *   说明: 关联的设备类型ID
     */
    private Long relatedRemoteDeviceTypeId;

    /**
     *   字段: related_remote_software_type_id
     *   说明: 关联的软件类型ID
     */
    private Long relatedRemoteSoftwareTypeId;
}