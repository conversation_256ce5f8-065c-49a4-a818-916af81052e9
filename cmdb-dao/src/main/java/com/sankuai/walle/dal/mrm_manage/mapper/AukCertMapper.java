package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AukCert;
import com.sankuai.walle.dal.mrm_manage.example.AukCertExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AukCertMapper extends MybatisBLOBsMapper<AukCert, AukCertExample, Long> {
    int batchInsert(@Param("list") List<AukCert> list);
}