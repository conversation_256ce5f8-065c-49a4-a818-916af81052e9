package com.sankuai.walle.dal.mrm_manage.example;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class MyTagsVinExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<MyTagsVinExample.Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public MyTagsVinExample() {
        oredCriteria = new ArrayList<MyTagsVinExample.Criteria>();
    }

    public MyTagsVinExample.Criteria createCriteria() {
        MyTagsVinExample.Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected MyTagsVinExample.Criteria createCriteriaInternal() {
        MyTagsVinExample.Criteria criteria = new MyTagsVinExample.Criteria();
        return criteria;
    }

    protected abstract static class GeneratedCriteria {
        protected List<MyTagsVinExample.Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<MyTagsVinExample.Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<MyTagsVinExample.Criterion> getAllCriteria() {
            return criteria;
        }

        public List<MyTagsVinExample.Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new MyTagsVinExample.Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new MyTagsVinExample.Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new MyTagsVinExample.Criterion(condition, value1, value2));
        }
        //addCriterion("id in", values, "id");
        public MyTagsVinExample.Criteria andVinIn(List<String> values) {
            addCriterion("vin in",values,"vin");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdIsNull() {
            addCriterion("id is null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameIsNull() {
            addCriterion("name is null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorIsNull() {
            addCriterion("color is null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeIsNull() {
            addCriterion("tag_type is null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeIsNotNull() {
            addCriterion("tag_type is not null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeEqualTo(Long value) {
            addCriterion("tag_type =", value, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeNotEqualTo(Long value) {
            addCriterion("tag_type <>", value, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeGreaterThan(Long value) {
            addCriterion("tag_type >", value, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("tag_type >=", value, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeLessThan(Long value) {
            addCriterion("tag_type <", value, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeLessThanOrEqualTo(Long value) {
            addCriterion("tag_type <=", value, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeIn(List<Long> values) {
            addCriterion("tag_type in", values, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeNotIn(List<Long> values) {
            addCriterion("tag_type not in", values, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeBetween(Long value1, Long value2) {
            addCriterion("tag_type between", value1, value2, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andTagTypeNotBetween(Long value1, Long value2) {
            addCriterion("tag_type not between", value1, value2, "tagType");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }

        public MyTagsVinExample.Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (MyTagsVinExample.Criteria) this;
        }
    }

    public static class Criteria extends MyTagsVinExample.GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
