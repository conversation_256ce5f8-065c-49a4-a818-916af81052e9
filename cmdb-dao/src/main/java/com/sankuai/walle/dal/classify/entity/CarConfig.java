package com.sankuai.walle.dal.classify.entity;

import lombok.*;

/**
 *
 *   表名: car_config
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarConfig {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 配置项名称
     */
    private String name;

    /**
     *   字段: config_version
     *   说明: 版本号，0是dev版本
     */
    private Long configVersion;

    /**
     *   字段: config_name
     *   说明: 配置的名称，类似简介
     */
    private String configName;

    /**
     *   字段: master_id
     *   说明: 主配置项的id，同一个系列配置，不同的版本之间，共同拥有的唯一标识为dev配置的id。主配置的此项为0
     */
    private Long masterId;

    /**
     *   字段: file_type
     */
    private String fileType;

    /**
     *   字段: config
     *   说明: 配置内容
     */
    private String config;
}