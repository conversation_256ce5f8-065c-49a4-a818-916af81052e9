package com.sankuai.walle.dal.wallevresv.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: vresv_city_park
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VresvCityPark {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Integer id;

    /**
     *   字段: city
     *   说明: 城市
     */
    private String city;

    /**
     *   字段: park
     *   说明: 园区
     */
    private String park;

    /**
     *   字段: address
     *   说明: 地址
     */
    private String address;

    /**
     *   字段: deleted
     *   说明: 是否删除[0:未删除|1:已删除]
     */
    private Boolean deleted;

    /**
     *   字段: first_add_time
     *   说明: 首次添加时间
     */
    private Date firstAddTime;

    /**
     *   字段: last_update_time
     *   说明: 最近更新时间
     */
    private Date lastUpdateTime;

    /**
     *   字段: code
     *   说明: 城市
     */
    private String code;
}