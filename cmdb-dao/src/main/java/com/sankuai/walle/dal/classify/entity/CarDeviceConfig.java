package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_device_config
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarDeviceConfig {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: vin
     */
    private String vin;

    /**
     *   字段: device_type_id
     *   说明: 关联remote_device_type的id
     */
    private Long deviceTypeId;

    /**
     *   字段: name
     *   说明: 配置项的名称，例如“白名单、ipmi…”
     */
    private String name;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;

    /**
     *   字段: create_user
     */
    private String createUser;

    /**
     *   字段: update_user
     */
    private String updateUser;

    /**
     *   字段: task_id
     *   说明: 配置任务表car_config_taks的id
     */
    private Long taskId;

    /**
     *   字段: config_id
     *   说明: 当前配置的id
     */
    private Long configId;

    /**
     *   字段: config_id_last
     *   说明: 上一次配置的id
     */
    private Long configIdLast;

    /**
     *   字段: status
     *   说明: 0 下发中 1 已完成
     */
    private Short status;

    /**
     *   字段: file_type
     *   说明: 文件类型
     */
    private String fileType;

    /**
     *   字段: config
     *   说明: 配置内容
     */
    private String config;

    /**
     *   字段: task_history_id
     *   说明: 所有下发过的历史任务id，不同id之间用逗号隔开
     */
    private String taskHistoryId;

    /**
     *   字段: config_id_history
     *   说明: 所有配置的历史id
     */
    private String configIdHistory;
}