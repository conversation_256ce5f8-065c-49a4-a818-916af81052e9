package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.classify.example.CarClassifyExample;
import com.sankuai.walle.dal.classify.entity.CarClassify;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarClassifyMapper extends MybatisBaseMapper<CarClassify, CarClassifyExample, Long> {
    int batchInsert(@Param("list") List<CarClassify> list);
}