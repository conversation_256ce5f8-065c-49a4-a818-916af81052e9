package com.sankuai.walle.dal.mrm_manage.entity;

import lombok.*;

/**
 *
 *   表名: remote_objects
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RemoteObjects {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 远程对象别名
     */
    private String name;

    /**
     *   字段: vin
     *   说明: 远程对象唯一编号
     */
    private String vin;

    /**
     *   字段: license_no
     *   说明: 对象的牌照
     */
    private String licenseNo;

    /**
     *   字段: login_user
     *   说明: 登录用户
     */
    private String loginUser;

    /**
     *   字段: login_password
     *   说明: 登录密码
     */
    private String loginPassword;

    /**
     *   字段: remote_object_type_id
     *   说明: 类型ID
     */
    private Long remoteObjectTypeId;

    /**
     *   字段: remote_car_type_id
     *   说明: 车型ID
     */
    private Long remoteCarTypeId;

    /**
     *   字段: remote_control_enable
     *   说明: 是否远程控制
     */
    private Boolean remoteControlEnable;

    /**
     *   字段: remote_ssh_enable
     *   说明: 是否远程登录
     */
    private Boolean remoteSshEnable;

    /**
     *   字段: remote_debug_enable
     *   说明: 是否远程Debug
     */
    private Boolean remoteDebugEnable;

    /**
     *   字段: stream_base_port
     *   说明: 流端口
     */
    private Integer streamBasePort;

    /**
     *   字段: remote_object_configure
     *   说明: 待下发的配置信息
     */
    private String remoteObjectConfigure;

    /**
     *   字段: remote_release
     *   说明: 车辆所有设备,软件稳定版本信息
     */
    private String remoteRelease;

    /**
     *   字段: status
     *   说明: 对象的状态
     */
    private Integer status;

    /**
     *   字段: desc
     *   说明: 相关描述
     */
    private String desc;
}