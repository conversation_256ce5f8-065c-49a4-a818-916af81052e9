package com.sankuai.walle.dal.battery.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty;
import com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BatterySwapCabinetPropertyMapperMy {
    @Select("SELECT * FROM battery_swap_cabinet_property WHERE device_key = #{deviceKey} ORDER BY id DESC LIMIT 1")
    BatterySwapCabinetProperty selectLatestByDeviceKey(@Param("deviceKey") String deviceKey);
}