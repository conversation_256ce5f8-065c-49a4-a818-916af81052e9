package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_config_query
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarConfigQuery {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: vin
     */
    private String vin;

    /**
     *   字段: name
     *   说明: 配置项名称
     */
    private String name;

    /**
     *   字段: file_name
     */
    private String fileName;

    /**
     *   字段: config_version
     *   说明: 版本号，0是dev版本
     */
    private String configVersion;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: config
     *   说明: 配置内容
     */
    private String config;
}