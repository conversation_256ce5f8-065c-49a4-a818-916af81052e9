package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView;
import com.sankuai.walle.dal.classify.example.CarClassifyDeviceConfigViewExample;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarClassifyDeviceConfigViewMapper extends MybatisBaseMapper<CarClassifyDeviceConfigView, CarClassifyDeviceConfigViewExample, Long> {
    int batchInsert(@Param("list") List<CarClassifyDeviceConfigView> list);
}