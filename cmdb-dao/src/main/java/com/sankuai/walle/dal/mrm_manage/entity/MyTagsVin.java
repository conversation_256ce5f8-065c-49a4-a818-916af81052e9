package com.sankuai.walle.dal.mrm_manage.entity;

import lombok.*;

import java.util.Date;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MyTagsVin {
    String vin;
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 展示TAG
     */
    private String name;

    /**
     *   字段: color
     *   说明: 使用的颜色
     */
    private String color;

    /**
     *   字段: tag_type
     *   说明: 标签类型
     */
    private Long tagType;

    /**
     *   字段: add_time
     *   说明: 建立时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}
