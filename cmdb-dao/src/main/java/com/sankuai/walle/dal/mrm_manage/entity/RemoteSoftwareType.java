package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: remote_software_type
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RemoteSoftwareType {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: type_name
     *   说明: 自定义主键
     */
    private String typeName;

    /**
     *   字段: friend_name
     *   说明: 软件别名
     */
    private String friendName;

    /**
     *   字段: latest_version_id
     *   说明: 当前软件最新版本ID
     */
    private Long latestVersionId;

    /**
     *   字段: add_time
     *   说明: 建立时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: desc
     *   说明: 相关描述
     */
    private String desc;
}