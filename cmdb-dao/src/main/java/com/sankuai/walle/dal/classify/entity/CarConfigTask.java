package com.sankuai.walle.dal.classify.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_config_task
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarConfigTask {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: task_name
     *   说明: 任务名称
     */
    private String taskName;

    /**
     *   字段: device_id
     *   说明: 设备号
     */
    private Long deviceId;

    /**
     *   字段: config_id
     *   说明: 配置id。0代表此任务为回滚任务
     */
    private Long configId;

    /**
     *   字段: create_user
     *   说明: 任务的创建人
     */
    private String createUser;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: vins
     *   说明: 此次任务包含的vin号，两个vin之间用逗号分开
     */
    private String vins;
}