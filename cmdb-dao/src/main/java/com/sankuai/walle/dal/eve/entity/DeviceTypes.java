package com.sankuai.walle.dal.eve.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: device_types
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DeviceTypes {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: type_name
     *   说明: 类型名
     */
    private String typeName;

    /**
     *   字段: friend_name
     *   说明: 类型别名
     */
    private String friendName;

    /**
     *   字段: add_time
     *   说明: 建立时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: category
     *   说明: 设备分类
     */
    private String category;

    /**
     *   字段: editor
     *   说明: 编辑人
     */
    private String editor;

    /**
     *   字段: is_deleted
     *   说明: 是否逻辑删除,0表示否,1表示是
     */
    private Boolean isDeleted;
}