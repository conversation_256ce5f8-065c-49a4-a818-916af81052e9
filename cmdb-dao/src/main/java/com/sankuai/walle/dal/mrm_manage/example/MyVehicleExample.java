package com.sankuai.walle.dal.mrm_manage.example;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class MyVehicleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<MyVehicleExample.Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public MyVehicleExample() {
        oredCriteria = new ArrayList<MyVehicleExample.Criteria>();
    }

    public MyVehicleExample.Criteria createCriteria() {
        MyVehicleExample.Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected MyVehicleExample.Criteria createCriteriaInternal() {
        MyVehicleExample.Criteria criteria = new MyVehicleExample.Criteria();
        return criteria;
    }

    protected abstract static class GeneratedCriteria {
        protected List<MyVehicleExample.Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<MyVehicleExample.Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<MyVehicleExample.Criterion> getAllCriteria() {
            return criteria;
        }

        public List<MyVehicleExample.Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new MyVehicleExample.Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new MyVehicleExample.Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new MyVehicleExample.Criterion(condition, value1, value2));
        }

        protected void addCriterion(String condition, Object value, String property, boolean isCompleteCondition) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new MyVehicleExample.Criterion(condition, value, property, isCompleteCondition));
        }

        // 自定义的8个搜索字段中的：车辆名、分类名
        public MyVehicleExample.Criteria andCarNameLike(String value) {

            addCriterion("a.name like", value, "a.name");
            return (MyVehicleExample.Criteria) this;
        }
        public MyVehicleExample.Criteria andTypeName(String value) {
            addCriterion("b.name =", value, "b.name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andVinIn(List<String> values) {
            addCriterion("vin in",values,"vin");
            return (MyVehicleExample.Criteria) this;
        }
        
        // 添加 assembly_parts JSON 字段查询方法
        public MyVehicleExample.Criteria andAssemblyPartsContains(String value) {
            addCriterion("JSON_CONTAINS(a.assembly_parts, JSON_ARRAY(#{criterion.value}))", value, "assemblyParts",true);
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdIsNull() {
            addCriterion("id is null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameIsNull() {
            addCriterion("name is null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorIsNull() {
            addCriterion("color is null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeIsNull() {
            addCriterion("tag_type is null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeIsNotNull() {
            addCriterion("tag_type is not null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeEqualTo(Long value) {
            addCriterion("tag_type =", value, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeNotEqualTo(Long value) {
            addCriterion("tag_type <>", value, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeGreaterThan(Long value) {
            addCriterion("tag_type >", value, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeGreaterThanOrEqualTo(Long value) {
            addCriterion("tag_type >=", value, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeLessThan(Long value) {
            addCriterion("tag_type <", value, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeLessThanOrEqualTo(Long value) {
            addCriterion("tag_type <=", value, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeIn(List<Long> values) {
            addCriterion("tag_type in", values, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeNotIn(List<Long> values) {
            addCriterion("tag_type not in", values, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeBetween(Long value1, Long value2) {
            addCriterion("tag_type between", value1, value2, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andTagTypeNotBetween(Long value1, Long value2) {
            addCriterion("tag_type not between", value1, value2, "tagType");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }

        public MyVehicleExample.Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (MyVehicleExample.Criteria) this;
        }
    }

    public static class Criteria extends MyVehicleExample.GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }
        // 搜索用的8个字段：vin/name/label/tagName/city/area/ownerMis/carUsedTarget
        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler, boolean isCompleteCondition) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            this.noValue = isCompleteCondition; // 查询语句中，占位符需要替换，但不需要拼接value值
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
