package com.sankuai.walle.dal.classify.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CarClassifyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CarClassifyExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CarClassifyExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CarClassifyExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CarClassifyExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andClassifyDescIsNull() {
            addCriterion("classify_desc is null");
            return (Criteria) this;
        }

        public Criteria andClassifyDescIsNotNull() {
            addCriterion("classify_desc is not null");
            return (Criteria) this;
        }

        public Criteria andClassifyDescEqualTo(String value) {
            addCriterion("classify_desc =", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescNotEqualTo(String value) {
            addCriterion("classify_desc <>", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescGreaterThan(String value) {
            addCriterion("classify_desc >", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescGreaterThanOrEqualTo(String value) {
            addCriterion("classify_desc >=", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescLessThan(String value) {
            addCriterion("classify_desc <", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescLessThanOrEqualTo(String value) {
            addCriterion("classify_desc <=", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescLike(String value) {
            addCriterion("classify_desc like", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescNotLike(String value) {
            addCriterion("classify_desc not like", value, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescIn(List<String> values) {
            addCriterion("classify_desc in", values, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescNotIn(List<String> values) {
            addCriterion("classify_desc not in", values, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescBetween(String value1, String value2) {
            addCriterion("classify_desc between", value1, value2, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andClassifyDescNotBetween(String value1, String value2) {
            addCriterion("classify_desc not between", value1, value2, "classifyDesc");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelIsNull() {
            addCriterion("first_car_model is null");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelIsNotNull() {
            addCriterion("first_car_model is not null");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelEqualTo(Long value) {
            addCriterion("first_car_model =", value, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelNotEqualTo(Long value) {
            addCriterion("first_car_model <>", value, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelGreaterThan(Long value) {
            addCriterion("first_car_model >", value, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelGreaterThanOrEqualTo(Long value) {
            addCriterion("first_car_model >=", value, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelLessThan(Long value) {
            addCriterion("first_car_model <", value, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelLessThanOrEqualTo(Long value) {
            addCriterion("first_car_model <=", value, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelIn(List<Long> values) {
            addCriterion("first_car_model in", values, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelNotIn(List<Long> values) {
            addCriterion("first_car_model not in", values, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelBetween(Long value1, Long value2) {
            addCriterion("first_car_model between", value1, value2, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andFirstCarModelNotBetween(Long value1, Long value2) {
            addCriterion("first_car_model not between", value1, value2, "firstCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelIsNull() {
            addCriterion("second_car_model is null");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelIsNotNull() {
            addCriterion("second_car_model is not null");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelEqualTo(Long value) {
            addCriterion("second_car_model =", value, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelNotEqualTo(Long value) {
            addCriterion("second_car_model <>", value, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelGreaterThan(Long value) {
            addCriterion("second_car_model >", value, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelGreaterThanOrEqualTo(Long value) {
            addCriterion("second_car_model >=", value, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelLessThan(Long value) {
            addCriterion("second_car_model <", value, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelLessThanOrEqualTo(Long value) {
            addCriterion("second_car_model <=", value, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelIn(List<Long> values) {
            addCriterion("second_car_model in", values, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelNotIn(List<Long> values) {
            addCriterion("second_car_model not in", values, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelBetween(Long value1, Long value2) {
            addCriterion("second_car_model between", value1, value2, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andSecondCarModelNotBetween(Long value1, Long value2) {
            addCriterion("second_car_model not between", value1, value2, "secondCarModel");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}