package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarDeviceConfigMapper extends MybatisBLOBsMapper<CarDeviceConfig, CarDeviceConfigExample, Long> {
    int batchInsert(@Param("list") List<CarDeviceConfig> list);
}