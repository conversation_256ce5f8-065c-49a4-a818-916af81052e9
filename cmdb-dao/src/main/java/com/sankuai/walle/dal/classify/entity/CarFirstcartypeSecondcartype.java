package com.sankuai.walle.dal.classify.entity;

import lombok.*;

/**
 *
 *   表名: car_firstCarType_secondCarType
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarFirstcartypeSecondcartype {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: first_car_id
     *   说明: 一级车型的id
     */
    private Long firstCarId;

    /**
     *   字段: second_car_id
     *   说明: 二级车型的id
     */
    private Long secondCarId;
}