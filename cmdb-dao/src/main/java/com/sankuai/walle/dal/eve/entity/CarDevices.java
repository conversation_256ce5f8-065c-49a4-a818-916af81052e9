package com.sankuai.walle.dal.eve.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: car_devices
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CarDevices {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: type
     *   说明: 设备类型
     */
    private String type;

    /**
     *   字段: vin
     */
    private String vin;

    /**
     *   字段: sn
     *   说明: sn address
     */
    private String sn;

    /**
     *   字段: operator_id
     *   说明: mis
     */
    private String operatorId;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: mac
     *   说明: mac地址
     */
    private String mac;
}