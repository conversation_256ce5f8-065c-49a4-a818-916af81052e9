package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.MyTagsVin;
import com.sankuai.walle.dal.mrm_manage.example.MyTagsVinExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MyTagsMyMapper extends MybatisBaseMapper<MyTagsVin, MyTagsVinExample, Long> {

    List<MyTagsVin> batchGet(@Param("list") List<String> list);
}