package com.sankuai.walle.dal.mrm_manage.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CarEquipmentExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CarEquipmentExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CarEquipmentExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CarEquipmentExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CarEquipmentExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("`uid` is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("`uid` is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(String value) {
            addCriterion("`uid` =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(String value) {
            addCriterion("`uid` <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(String value) {
            addCriterion("`uid` >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(String value) {
            addCriterion("`uid` >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(String value) {
            addCriterion("`uid` <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(String value) {
            addCriterion("`uid` <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLike(String value) {
            addCriterion("`uid` like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotLike(String value) {
            addCriterion("`uid` not like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<String> values) {
            addCriterion("`uid` in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<String> values) {
            addCriterion("`uid` not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(String value1, String value2) {
            addCriterion("`uid` between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(String value1, String value2) {
            addCriterion("`uid` not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andVinIsNull() {
            addCriterion("vin is null");
            return (Criteria) this;
        }

        public Criteria andVinIsNotNull() {
            addCriterion("vin is not null");
            return (Criteria) this;
        }

        public Criteria andVinEqualTo(String value) {
            addCriterion("vin =", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotEqualTo(String value) {
            addCriterion("vin <>", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThan(String value) {
            addCriterion("vin >", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinGreaterThanOrEqualTo(String value) {
            addCriterion("vin >=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThan(String value) {
            addCriterion("vin <", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLessThanOrEqualTo(String value) {
            addCriterion("vin <=", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinLike(String value) {
            addCriterion("vin like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotLike(String value) {
            addCriterion("vin not like", value, "vin");
            return (Criteria) this;
        }

        public Criteria andVinIn(List<String> values) {
            addCriterion("vin in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotIn(List<String> values) {
            addCriterion("vin not in", values, "vin");
            return (Criteria) this;
        }

        public Criteria andVinBetween(String value1, String value2) {
            addCriterion("vin between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andVinNotBetween(String value1, String value2) {
            addCriterion("vin not between", value1, value2, "vin");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("`label` is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("`label` is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("`label` =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("`label` <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("`label` >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("`label` >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("`label` <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("`label` <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("`label` like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("`label` not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("`label` in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("`label` not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("`label` between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("`label` not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andBigTypeIsNull() {
            addCriterion("big_type is null");
            return (Criteria) this;
        }

        public Criteria andBigTypeIsNotNull() {
            addCriterion("big_type is not null");
            return (Criteria) this;
        }

        public Criteria andBigTypeEqualTo(String value) {
            addCriterion("big_type =", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeNotEqualTo(String value) {
            addCriterion("big_type <>", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeGreaterThan(String value) {
            addCriterion("big_type >", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeGreaterThanOrEqualTo(String value) {
            addCriterion("big_type >=", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeLessThan(String value) {
            addCriterion("big_type <", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeLessThanOrEqualTo(String value) {
            addCriterion("big_type <=", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeLike(String value) {
            addCriterion("big_type like", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeNotLike(String value) {
            addCriterion("big_type not like", value, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeIn(List<String> values) {
            addCriterion("big_type in", values, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeNotIn(List<String> values) {
            addCriterion("big_type not in", values, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeBetween(String value1, String value2) {
            addCriterion("big_type between", value1, value2, "bigType");
            return (Criteria) this;
        }

        public Criteria andBigTypeNotBetween(String value1, String value2) {
            addCriterion("big_type not between", value1, value2, "bigType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeIsNull() {
            addCriterion("small_type is null");
            return (Criteria) this;
        }

        public Criteria andSmallTypeIsNotNull() {
            addCriterion("small_type is not null");
            return (Criteria) this;
        }

        public Criteria andSmallTypeEqualTo(String value) {
            addCriterion("small_type =", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotEqualTo(String value) {
            addCriterion("small_type <>", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeGreaterThan(String value) {
            addCriterion("small_type >", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeGreaterThanOrEqualTo(String value) {
            addCriterion("small_type >=", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeLessThan(String value) {
            addCriterion("small_type <", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeLessThanOrEqualTo(String value) {
            addCriterion("small_type <=", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeLike(String value) {
            addCriterion("small_type like", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotLike(String value) {
            addCriterion("small_type not like", value, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeIn(List<String> values) {
            addCriterion("small_type in", values, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotIn(List<String> values) {
            addCriterion("small_type not in", values, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeBetween(String value1, String value2) {
            addCriterion("small_type between", value1, value2, "smallType");
            return (Criteria) this;
        }

        public Criteria andSmallTypeNotBetween(String value1, String value2) {
            addCriterion("small_type not between", value1, value2, "smallType");
            return (Criteria) this;
        }

        public Criteria andMisIsNull() {
            addCriterion("mis is null");
            return (Criteria) this;
        }

        public Criteria andMisIsNotNull() {
            addCriterion("mis is not null");
            return (Criteria) this;
        }

        public Criteria andMisEqualTo(String value) {
            addCriterion("mis =", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotEqualTo(String value) {
            addCriterion("mis <>", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisGreaterThan(String value) {
            addCriterion("mis >", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisGreaterThanOrEqualTo(String value) {
            addCriterion("mis >=", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisLessThan(String value) {
            addCriterion("mis <", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisLessThanOrEqualTo(String value) {
            addCriterion("mis <=", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisLike(String value) {
            addCriterion("mis like", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotLike(String value) {
            addCriterion("mis not like", value, "mis");
            return (Criteria) this;
        }

        public Criteria andMisIn(List<String> values) {
            addCriterion("mis in", values, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotIn(List<String> values) {
            addCriterion("mis not in", values, "mis");
            return (Criteria) this;
        }

        public Criteria andMisBetween(String value1, String value2) {
            addCriterion("mis between", value1, value2, "mis");
            return (Criteria) this;
        }

        public Criteria andMisNotBetween(String value1, String value2) {
            addCriterion("mis not between", value1, value2, "mis");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameIsNull() {
            addCriterion("remote_type_name is null");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameIsNotNull() {
            addCriterion("remote_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameEqualTo(String value) {
            addCriterion("remote_type_name =", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameNotEqualTo(String value) {
            addCriterion("remote_type_name <>", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameGreaterThan(String value) {
            addCriterion("remote_type_name >", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("remote_type_name >=", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameLessThan(String value) {
            addCriterion("remote_type_name <", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameLessThanOrEqualTo(String value) {
            addCriterion("remote_type_name <=", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameLike(String value) {
            addCriterion("remote_type_name like", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameNotLike(String value) {
            addCriterion("remote_type_name not like", value, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameIn(List<String> values) {
            addCriterion("remote_type_name in", values, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameNotIn(List<String> values) {
            addCriterion("remote_type_name not in", values, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameBetween(String value1, String value2) {
            addCriterion("remote_type_name between", value1, value2, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andRemoteTypeNameNotBetween(String value1, String value2) {
            addCriterion("remote_type_name not between", value1, value2, "remoteTypeName");
            return (Criteria) this;
        }

        public Criteria andScrapIsNull() {
            addCriterion("scrap is null");
            return (Criteria) this;
        }

        public Criteria andScrapIsNotNull() {
            addCriterion("scrap is not null");
            return (Criteria) this;
        }

        public Criteria andScrapEqualTo(Boolean value) {
            addCriterion("scrap =", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotEqualTo(Boolean value) {
            addCriterion("scrap <>", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapGreaterThan(Boolean value) {
            addCriterion("scrap >", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapGreaterThanOrEqualTo(Boolean value) {
            addCriterion("scrap >=", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapLessThan(Boolean value) {
            addCriterion("scrap <", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapLessThanOrEqualTo(Boolean value) {
            addCriterion("scrap <=", value, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapIn(List<Boolean> values) {
            addCriterion("scrap in", values, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotIn(List<Boolean> values) {
            addCriterion("scrap not in", values, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap between", value1, value2, "scrap");
            return (Criteria) this;
        }

        public Criteria andScrapNotBetween(Boolean value1, Boolean value2) {
            addCriterion("scrap not between", value1, value2, "scrap");
            return (Criteria) this;
        }

        public Criteria andParkIsNull() {
            addCriterion("park is null");
            return (Criteria) this;
        }

        public Criteria andParkIsNotNull() {
            addCriterion("park is not null");
            return (Criteria) this;
        }

        public Criteria andParkEqualTo(String value) {
            addCriterion("park =", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotEqualTo(String value) {
            addCriterion("park <>", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkGreaterThan(String value) {
            addCriterion("park >", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkGreaterThanOrEqualTo(String value) {
            addCriterion("park >=", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkLessThan(String value) {
            addCriterion("park <", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkLessThanOrEqualTo(String value) {
            addCriterion("park <=", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkLike(String value) {
            addCriterion("park like", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotLike(String value) {
            addCriterion("park not like", value, "park");
            return (Criteria) this;
        }

        public Criteria andParkIn(List<String> values) {
            addCriterion("park in", values, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotIn(List<String> values) {
            addCriterion("park not in", values, "park");
            return (Criteria) this;
        }

        public Criteria andParkBetween(String value1, String value2) {
            addCriterion("park between", value1, value2, "park");
            return (Criteria) this;
        }

        public Criteria andParkNotBetween(String value1, String value2) {
            addCriterion("park not between", value1, value2, "park");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNull() {
            addCriterion("exec_word is null");
            return (Criteria) this;
        }

        public Criteria andExecWordIsNotNull() {
            addCriterion("exec_word is not null");
            return (Criteria) this;
        }

        public Criteria andExecWordEqualTo(String value) {
            addCriterion("exec_word =", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotEqualTo(String value) {
            addCriterion("exec_word <>", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThan(String value) {
            addCriterion("exec_word >", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordGreaterThanOrEqualTo(String value) {
            addCriterion("exec_word >=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThan(String value) {
            addCriterion("exec_word <", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLessThanOrEqualTo(String value) {
            addCriterion("exec_word <=", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordLike(String value) {
            addCriterion("exec_word like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotLike(String value) {
            addCriterion("exec_word not like", value, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordIn(List<String> values) {
            addCriterion("exec_word in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotIn(List<String> values) {
            addCriterion("exec_word not in", values, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordBetween(String value1, String value2) {
            addCriterion("exec_word between", value1, value2, "execWord");
            return (Criteria) this;
        }

        public Criteria andExecWordNotBetween(String value1, String value2) {
            addCriterion("exec_word not between", value1, value2, "execWord");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}