package com.sankuai.walle.dal.walle_data_center.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: vehicle_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class VehicleInfo {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Long id;

    /**
     *   字段: vehicle_id
     *   说明: 车辆ID
     */
    private String vehicleId;

    /**
     *   字段: vin
     *   说明: 车辆设备ID
     */
    private String vin;

    /**
     *   字段: name
     *   说明: 车辆名称
     */
    private String name;

    /**
     *   字段: park
     *   说明: 园区
     */
    private String park;

    /**
     *   字段: brand
     *   说明: 品牌
     */
    private String brand;

    /**
     *   字段: type
     *   说明: 车辆型号
     */
    private String type;

    /**
     *   字段: vehicle_type
     *   说明: 车辆类型[0:unknown, 1:big, 2:middle，3:guarantee]
     */
    private Byte vehicleType;

    /**
     *   字段: vehicle_category
     *   说明: 用于区分是不是远程遥控车
     */
    private String vehicleCategory;

    /**
     *   字段: license_number
     *   说明: 车牌号
     */
    private String licenseNumber;

    /**
     *   字段: licence
     *   说明: 车牌号
     */
    private String licence;

    /**
     *   字段: year
     *   说明: 生产年份
     */
    private Integer year;

    /**
     *   字段: power_type
     *   说明: 能源类型
     */
    private Byte powerType;

    /**
     *   字段: operation_state
     *   说明: 车辆运营状态[0:不在线|1:在线|2:故障]
     */
    private Byte operationState;

    /**
     *   字段: status
     *   说明: 状态[0:不可用|1:可用|2:已删除]
     */
    private Byte status;

    /**
     *   字段: create_time
     *   说明: 首次添加时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 最近更新时间
     */
    private Date updateTime;

    /**
     *   字段: owner
     *   说明: 负责人
     */
    private String owner;

    /**
     *   字段: product
     *   说明: 产品线
     */
    private Integer product;

    /**
     *   字段: purpose_id
     *   说明: 用途 [1:路测B组,2:运营,3:保障车,4:路测A组，5:路测C组
     */
    private Byte purposeId;

    /**
     *   字段: is_dedicated
     *   说明: 专用车[1-是 0-不是]
     */
    private Boolean isDedicated;

    /**
     *   字段: submitter
     *   说明: 录入者
     */
    private String submitter;

    /**
     *   字段: activate_code
     *   说明: 激活码
     */
    private String activateCode;

    /**
     *   字段: access_secret
     *   说明: 密钥
     */
    private String accessSecret;

    /**
     *   字段: box_qrcode
     *   说明: 开箱二维码
     */
    private String boxQrcode;

    /**
     *   字段: start_postion
     *   说明: 发车点
     */
    private String startPostion;

    /**
     *   字段: end_postion
     *   说明: 停靠点
     */
    private String endPostion;

    /**
     *   字段: super_password
     *   说明: 超级密码
     */
    private String superPassword;

    /**
     *   字段: usage_rights
     *   说明: 业务使用权, biz_autocar（自动车配送部），biz_campus（校园业务）biz_public_road（公开道路业务）
     */
    private String usageRights;

    /**
     *   字段: is_business
     *   说明: 是否运营用途，0否，1是
     */
    private Boolean isBusiness;
}