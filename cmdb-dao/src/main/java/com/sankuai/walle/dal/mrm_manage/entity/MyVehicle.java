package com.sankuai.walle.dal.mrm_manage.entity;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.*;

import java.util.ArrayList;
import java.util.Date;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MyVehicle {
    String vin;
    @FieldDoc(description = "资产标签")
    String label;
    @FieldDoc(description = "车辆名称")
    String name;
    @FieldDoc(description = "车辆标签")
    String tagName;
    @FieldDoc(description = "城市")
    String city;
    @FieldDoc(description = "场地")
    String area;
    @FieldDoc(description = "资产责任人")
    String ownerMis;
    @FieldDoc(description = "用车目的")
    String carUsedTarget;
}
