package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: auto_config_vehicle_task_status
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AutoConfigVehicleTaskStatus {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: vin
     */
    private String vin;

    /**
     *   字段: task_type
     *   说明: 任务类型(10-获取sn)
     */
    private Integer taskType;

    /**
     *   字段: task_status
     */
    private Integer taskStatus;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}