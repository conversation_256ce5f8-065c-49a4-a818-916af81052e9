package com.sankuai.walle.dal.mrm_manage.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: auk_cert
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AukCert {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: device_key
     *   说明: 设备key
     */
    private String deviceKey;

    /**
     *   字段: validity_period
     *   说明: 有效天数
     */
    private Integer validityPeriod;

    /**
     *   字段: expire_time
     *   说明: 过期时间
     */
    private Date expireTime;

    /**
     *   字段: certId
     *   说明: certid,查询证书使用
     */
    private String certid;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: privateKey
     *   说明: 私钥
     */
    private String privatekey;

    /**
     *   字段: pem
     *   说明: 证书
     */
    private String pem;
}