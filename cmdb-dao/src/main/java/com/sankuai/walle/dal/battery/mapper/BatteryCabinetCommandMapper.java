package com.sankuai.walle.dal.battery.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand;
import com.sankuai.walle.dal.battery.example.BatteryCabinetCommandExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatteryCabinetCommandMapper extends MybatisBaseMapper<BatteryCabinetCommand, BatteryCabinetCommandExample, Long> {
    int batchInsert(@Param("list") List<BatteryCabinetCommand> list);
}