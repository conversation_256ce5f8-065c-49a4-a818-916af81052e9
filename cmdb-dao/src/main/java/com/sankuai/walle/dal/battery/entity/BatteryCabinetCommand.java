package com.sankuai.walle.dal.battery.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: battery_cabinet_command
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BatteryCabinetCommand {
    /**
     *   字段: id
     *   说明: 主键ID
     */
    private Long id;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: module_identifier
     *   说明: 模块标识符
     */
    private String moduleIdentifier;

    /**
     *   字段: command_identifier
     *   说明: 命令标识符
     */
    private String commandIdentifier;

    /**
     *   字段: command_content
     *   说明: 指令内容，JSON格式
     */
    private String commandContent;

    /**
     *   字段: status
     *   说明: 指令状态 1下发中 2成功 3失败
     */
    private Integer status;

    /**
     *   字段: status_msg
     *   说明: 状态数据
     */
    private String statusMsg;

    /**
     *   字段: request_id
     *   说明: 当前指令的下行request_id
     */
    private String requestId;

    /**
     *   字段: mis
     *   说明: 操作人mis
     */
    private String mis;

    /**
     *   字段: rep
     *   说明: 异步响应数据，JSON格式
     */
    private String rep;

    /**
     *   字段: device_key
     *   说明: 设备key
     */
    private String deviceKey;
}