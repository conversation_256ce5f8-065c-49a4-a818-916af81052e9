package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent;
import com.sankuai.walle.dal.mrm_manage.example.AutoConfigContentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AutoConfigContentMapper extends MybatisBaseMapper<AutoConfigContent, AutoConfigContentExample, Long> {
    int batchInsert(@Param("list") List<AutoConfigContent> list);
}