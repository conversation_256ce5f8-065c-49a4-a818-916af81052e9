package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigVehicleTaskStatus;
import com.sankuai.walle.dal.mrm_manage.example.AutoConfigVehicleTaskStatusExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AutoConfigVehicleTaskStatusMapper extends MybatisBaseMapper<AutoConfigVehicleTaskStatus, AutoConfigVehicleTaskStatusExample, Long> {
    int batchInsert(@Param("list") List<AutoConfigVehicleTaskStatus> list);
}