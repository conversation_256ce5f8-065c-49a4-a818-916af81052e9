package com.sankuai.walle.dal.classify.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype;
import com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarFirstcartypeSecondcartypeMapper extends MybatisBaseMapper<CarFirstcartypeSecondcartype, CarFirstcartypeSecondcartypeExample, Long> {
    int batchInsert(@Param("list") List<CarFirstcartypeSecondcartype> list);
}