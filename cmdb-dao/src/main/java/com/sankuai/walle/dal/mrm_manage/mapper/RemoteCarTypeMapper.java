package com.sankuai.walle.dal.mrm_manage.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType;
import com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RemoteCarTypeMapper extends MybatisBaseMapper<RemoteCarType, RemoteCarTypeExample, Long> {
    int batchInsert(@Param("list") List<RemoteCarType> list);
}