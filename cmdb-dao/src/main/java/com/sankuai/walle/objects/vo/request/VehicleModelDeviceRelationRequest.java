package com.sankuai.walle.objects.vo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleModelDeviceRelationRequest {
    /**
     * 车型id
     */
    private Long vehicleModelId; // 车型id

    /**
     * 车型等级
     */
    private Integer vehicleModelLevel; // 1： 1级车型 2： 2级车型

    /**
     * 设备id列表
     */
    private List<Long> deviceIds; // 设备id

}
