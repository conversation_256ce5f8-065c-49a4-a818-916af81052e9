package com.sankuai.walle.objects.execDo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.mdp.doc.generator.entity.doc.MethodDoc;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.Date;

// 用于处理 资产表的扩展字段
@Data
public class AssetExecWord {
    String vin;
    // 车辆手续保管人
//    String Car_preduce_owner;
    String carPreduceOwner;
//    钥匙数量
//    int Key_num;
    int keyNum;
    //    备用钥匙保管人
//    String Back_key_owner;
    String backKeyOwner;
    //    设备险到期日期
//    Date Equipment_insurance_date;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date equipmentInsuranceDate;
//    商业险到期日期
//    Date Business_insurance_date;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date businessInsuranceDate;
//    交强险到期日期
//    Date Heavy_traffic_insurance;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date heavyTrafficInsurance;
//    年检到期日期
//    Date Annual_inspection;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date annualInspection;
//    注册时间
//    Date Registration_date;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date registrationDate;
//    三方责任险到期日期
//    Date Third_liability_insurance;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date thirdLiabilityInsurance;
//    财产险到期日期
//    Date Priority_insurance;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date priorityInsurance;
    /* 租用扩展字段
     */
    @FieldDoc(description = "指标租用时长")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date indecatorStart;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date indecatorEnd;
    @FieldDoc(description = "指标租用费用,单位元")
    Long rentExpense;
    @FieldDoc(description = "车辆融租费用,单位元")
    Long leasingExpense;
    @FieldDoc(description = "融租时长")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date leasingStart;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date leasingEnd;
    @FieldDoc(description = "手续备注")
    String processDesc;
    @FieldDoc(description = "融租备注")
    String leasingDesc;
    /* 校园扩展
     */
    String school;
    @FieldDoc(description = "校园合伙人")
    String schoolPartner;
    @FieldDoc(description = "校园督导")
    String schoolSupvisor;
    @FieldDoc(description = "到校日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date comeSchoolDate;
}

