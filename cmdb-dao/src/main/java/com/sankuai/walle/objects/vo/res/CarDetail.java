package com.sankuai.walle.objects.vo.res;

import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.objects.vo.CarDeviceVO;
import com.sankuai.walle.objects.vo.request.CarEditReq;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CarDetail {
    CarObjects carObjects;
    List<CarSelects> carType;
    List<Tags> tags;
    Map<String,Object> carAssets;
    List<CarOperation> carOperation;
    List<CarEditReq.CarExecWordConvertor> carExecWords;
    CarDeviceVO carDevice;
}
