package com.sankuai.walle.objects.bo;

import lombok.Data;

import java.util.List;

@Data
public class HandleMethodObj {
    /**
     * 方法英文
     * */
    private String id;
    /**
     * 方法中文
     * */
    private String title;
    /**
     * 组件类型
     * */
    private String componentType;
    /**
     * 默认值
     * */
    private String settingValue;
    /**
     * 选项
     * */
    private List<String> optionValue;
    /**
     * 占位字符
     * */
    private String placeHolder;
}