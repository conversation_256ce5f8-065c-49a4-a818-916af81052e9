package com.sankuai.walle.objects.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class MisGroupReq {
    @JsonProperty("id")
    private Long id;
    @JsonProperty("group_name")
    private String groupName;
    @JsonProperty("group_desc")
    private String groupDesc;
    @JsonProperty("group_members")
    private String groupMembers;
    @JsonProperty("create_user")
    private String createUser;
    @JsonProperty("update_user")
    private String updateUser;
    @JsonProperty("is_deleted")
    private Boolean isDeleted;
}