package com.sankuai.walle.objects.vo.request;

import com.google.gson.Gson;
import com.sankuai.walle.carManage.entity.*;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import com.sankuai.walle.objects.execDo.OperationExecWord;
import com.sankuai.walle.objects.vo.CarDeviceVO;
import lombok.Data;
import org.springframework.cglib.beans.BeanCopier;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class CarEditReq {
    CarObjects carObjects;
    CarSelects carType;
    List<Tags> tags;
    CarAssets carAssets; // 要转换为 CarAssets 类型
    CarOperation carOperation;
    CarExecWordConvertor carExecWord;
    CarDeviceVO carDevice;

    @Data
    public static class CarExecWordConvertor extends CarExecWord {
        AssetExecWord assetexecWord;
        OperationExecWord operationExecWord;

        public static List<CarExecWordConvertor> jsonToObjExec(List<CarExecWord> carExecWordList){
            return carExecWordList.stream().map(
                    carExecWord -> {
                        Gson gson = new Gson();
                        CarExecWordConvertor target = new CarExecWordConvertor();
                        BeanCopier b = BeanCopier.create(CarExecWord.class, CarExecWordConvertor.class, false);
                        b.copy(carExecWord, target,null);
                        // 转换
                        target.setAssetexecWord(gson.fromJson(carExecWord.getCarAssetsExecWords(), AssetExecWord.class));
                        target.setOperationExecWord(gson.fromJson(carExecWord.getCarOperationExecWords(), OperationExecWord.class));
                        return target;
                    }
            ).collect(Collectors.toList());
        }

        public static CarExecWord objExecToJson(CarExecWordConvertor carExecWord){
            Gson gson = new Gson();
            CarExecWord target = new CarExecWord();
            BeanCopier b = BeanCopier.create(CarExecWordConvertor.class, CarExecWord.class, false);
            b.copy(carExecWord, target,null);
            // 转换
            target.setCarAssetsExecWords(gson.toJson(carExecWord.getAssetexecWord()));
            target.setCarOperationExecWords(gson.toJson(carExecWord.getOperationExecWord()));
            return target;
        }
    }

}
