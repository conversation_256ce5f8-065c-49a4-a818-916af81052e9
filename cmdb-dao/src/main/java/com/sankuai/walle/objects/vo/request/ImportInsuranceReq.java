package com.sankuai.walle.objects.vo.request;

import com.sankuai.walle.objects.execDo.AssetExecWord;
import lombok.Data;

import java.util.Date;

/**
 * 导入保险日期请求类
 * 参考CarEditReq.CarExecWordConvertor的结构设计
 */
@Data
public class ImportInsuranceReq {
    /**
     * 车辆VIN号
     */
    private String vin;
    /**
     *   字段: licenseNo
     *   说明: 车牌号
     */
    private String licenseno;
    
    /**
     * 保险信息，对应AssetExecWord中的保险字段
     */
    private AssetExecWord assetExecWord;
} 