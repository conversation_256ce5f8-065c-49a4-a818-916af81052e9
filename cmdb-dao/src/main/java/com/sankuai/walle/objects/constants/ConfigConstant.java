package com.sankuai.walle.objects.constants;

import java.util.Arrays;
import java.util.List;

// 配置下发的状态。0已下发 1已完成 2业务处理失败
public interface ConfigConstant {
    Short SEND = 0;
    String SEND_STATUS = "下发中";
    Short FINISH = 1;
    String FINISH_STATUS = "已下发";
    Short ERROR = 2; // 车端上传错误状态
    // 版本号为0，标识dev版本
    Long DEV_VERSION = 0L;
    Long ROLL_BACK_TASK = 0L; // 当任务中的config_id==0时，代表此task为回滚任务

    Long EXCEL_VERSION = -1L; // 通过 excel,或接口，没有版本的配置。 下发的版本号
    Long EXCEL_CONFIG_ID = -1L; // 通过 excel,或接口，没有版本的配置。 下发的版本号
    String EXCEL_CONFIG_FILE_TYPE = "yaml"; //通过excel下发的配置文件格式，标签下发的文件格式
    String JSON_CONFIG_FILE_TYPE = "json";

    // 接口对外开发的白名单
    List<String> ConfigApiWhiteList = Arrays.asList("SwiningConfig","CAR_INFO");

}
