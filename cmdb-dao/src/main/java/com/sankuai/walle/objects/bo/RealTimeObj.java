package com.sankuai.walle.objects.bo;

import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.objects.constants.CarConstant;
import lombok.Data;

import java.util.Date;

@Data
public class RealTimeObj {
    String vin;
    String name;
    String carType;
    String licenseno;
    Date update_time;
    String use_status;
    String use_reason;
    String key;
    String sn;
    public static RealTimeObj carObjectsToRealTimeObj(CarObjects carObjects,String sCarType) {
        return new RealTimeObj() {{
            setCarType(sCarType);
            setVin(carObjects.getVin());
            setName(carObjects.getName());
            setLicenseno(carObjects.getLicenseno());
            setUpdate_time(carObjects.getUpdateTime());
            setKey(CarConstant.CarRealtimeKey);
        }};
    }

    public static RealTimeObj carObjectsToRealTimeObj(CarObjects carObjects,String sCarType, String  sn) {
        RealTimeObj realTimeObj = new RealTimeObj();
        realTimeObj.setCarType(sCarType);
        realTimeObj.setVin(carObjects.getVin());
        realTimeObj.setName(carObjects.getName());
        realTimeObj.setLicenseno(carObjects.getLicenseno());
        realTimeObj.setUpdate_time(carObjects.getUpdateTime());
        realTimeObj.setKey(CarConstant.CarRealtimeKey);
        realTimeObj.setSn(sn);
        return realTimeObj;
    }
}
