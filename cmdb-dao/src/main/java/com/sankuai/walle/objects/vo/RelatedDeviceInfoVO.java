package com.sankuai.walle.objects.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RelatedDeviceInfoVO {
    /**
     * 关联ID
     */
    private Long relatedId;
    /**
     *  设备id
     */
    private Long id;

    /**
     *  类型名
     */
    private String typeName;

    /**
     *  类型别名
     */
    private String friendName;

    /**
     *  设备分类
     */
    private String category;

    /**
     *  建立时间
     */
    private String createTime;

    /**
     *  更新时间
     */
    private String updateTime;

    /**
     *  编辑人
     */
    private String editor;
}
