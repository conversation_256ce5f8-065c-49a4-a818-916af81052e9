package com.sankuai.walle.objects.vo.res;

import com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;

@Data
public class CarDeviceConfigRes {
    Long config_id;
//    String carClassify;
//    String firstCarModel;
//    String secondCarModel;
//    Integer thirdCarModel;
    String carName;
    String vin;
    RemoteDeviceType device;
    String creater;
    Date addTime;
    String updater;
    Date updateTime;
    String config_name;
    String config;
    HashMap status;
    Long now_config_id; // 当前的配置id
    Long last_config_id; // 上一次的配置id
}
