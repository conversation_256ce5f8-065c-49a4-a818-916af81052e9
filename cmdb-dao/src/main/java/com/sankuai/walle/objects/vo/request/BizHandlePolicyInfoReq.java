package com.sankuai.walle.objects.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BizHandlePolicyInfoReq {
    /**
     * 主键id
     * */
    @JsonProperty("id")
    private Long id;
    /**
     * 业务表名
     * */
    @JsonProperty("biz_table_name")
    private String bizTableName;
    /**
     * 业务表描述
     * */
    @JsonProperty("biz_table_desc")
    private String bizTableDesc;
    /**
     * 策略名
     * */
    @JsonProperty("policy_name")
    private String policyName;
    /**
     * 策略描述
     * */
    @JsonProperty("policy_desc")
    private String policyDesc;
    /**
     * 策略参数值
     * */
    @JsonProperty("policy_value")
    private String policyValue;
    /**
     * 创建人
     * */
    @JsonProperty("create_user")
    private String createUser;
    /**
     * 更新人
     * */
    @JsonProperty("update_user")
    private String updateUser;
    /**
     * 处理方法：包含方法名和参数
     * */
    @JsonProperty("handle_method")
    private String handleMethod;
    /**
     * 是否上线: 0-下线，1-上线
     * */
    @JsonProperty("is_enable")
    private Boolean isEnable;
    /**
     * 是否删除: 0-未删除，1-已删除
     * */
    @JsonProperty("is_deleted")
    private Boolean isDeleted = false;
}