package com.sankuai.walle.objects.execDo;

import lombok.Data;

import java.util.Date;

@Data
public class OperationExecWord {
    String vin;
    // 用车起始时间
    Date useStartTime;
    // 用车结束时间
    Date useEndTime;
//    近场安全员
    String securityPerson;
//    是否vhr>1
    Boolean vhrOverOne;
//    是否寄盘
    Boolean MailDisk;
//    运营日期
    Date operationDate;
//    发运日期
    Date goforOperationDate;
//    指标管理责任人
    String indecatorOwner;
    // 使用状态
    String operationStatus;
    // 报废类型
    String scrappedType;
    // 报废原因
    String scrappedReason;
}
