package com.sankuai.walle.objects.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleModelInfoVO {

    /**
     * 车型Id
     */
    private Long id;

    /**
     * 车型名称
     */
    private String carTypeName;

    /**
     * 车型code
     */
    private String carType;

    /**
     * 车型品牌
     */
    private String carBrand;

    /**
     * 车辆尺寸
     */
    private String carMedium;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 编辑人
     */
    private String editor;

    /**
     * 子车型列表
     */
    private List<VehicleModelInfoVO> children;
}
