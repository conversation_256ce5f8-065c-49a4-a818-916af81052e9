package com.sankuai.walle.objects.constants;

import org.apache.commons.lang3.StringUtils;

public enum CarDeviceTypeEnum {
    DMS,
    SCREEN_IN,
    SCREEN_OUT;

    public static boolean belongEnum(String type){
        // 判断type是否此枚举值其中之一
        if (StringUtils.isBlank(type)){
            return false;
        }
        for (CarDeviceTypeEnum value : CarDeviceTypeEnum.values()) {
            if (StringUtils.equals(type, value.name())){
                return true;
            }
        }
        return false;
    }
}
