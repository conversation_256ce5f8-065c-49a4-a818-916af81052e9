package com.sankuai.walle.objects.bo;

import lombok.Data;

@Data
public class AukChargingCarbinetUpContentBO {
    // 充电柜物模型返回消息结构
    private Integer processDataCategory;
    private String productKey;
    private String deviceKey;
    private String moduleIdentifier;
    private String funcIdentifier;
    private String funcName;
    private String msgId;
    private String msgTime;
    private String outValue;           // 建议可进一步定义为对象
    private Integer outValueValidate;
    private String outValueErrMsg;
    private String inputValue;         // 建议可进一步定义为对象
    private Integer inputValueValidate;
    private String inputValueErrMsg;
}
