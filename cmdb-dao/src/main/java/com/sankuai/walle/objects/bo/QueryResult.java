package com.sankuai.walle.objects.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Created on 2022/2/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class QueryResult<T> {
    private Integer total;

    private List<T> items;

    public List<T> getItems() {
        if (items == null) {
            items = new ArrayList<>();
        }
        return items;
    }
}
