package com.sankuai.walle.objects.constants;

public interface CommonConstants {

    String cmdbAppKey = "com.sankuai.caros.wallecmdb";
    String bizHandlePolicyRelationshipKey = "bizHandlePolicyRelationship";
    String bizAccidentInfoTableName = "biz_accident_info";
    String handleMethodAliasKey = "handleMethodAlias";

    String cmdbOrgAppKeyConfig = "orgAppKey";

    int ERROR_CODE = 500;
    int SUCCEED_CODE = 0;
    int DUPLICATE_KEY_ERROR_CODE = 505;

    int firstCarType = 1;
    int secondCarType = 2;

    // 1 城市 2 运营区域 3 运营组别 4 车辆编号
    public static enum thirdModels {
        NONE,CITY,PARK,PURPOSE,VNAME
    }

    //  [1:路测B组,2:运营,3:保障车,4:路测A组，5:路测C组]
    public static enum purposes {
        NONE, ROAD_B, OPERATION, ASSURE, ROAD_A, ROAD_C
    }

    String succeedMsg = "success";

    public static final String UNKNOWN = "unknown";

    public static final Integer RESPONSE_SUCCESS_CODE = 200;

    /**
     * 与数据总线约定的业务唯一键，用于上报车管基础数据
     */
    public static final String CAR_MANAGE_BUSINESS_KEY = "vehicle_manage";

    /**
     * 数据总线上报接口的数据限制大小
     */
    public static final int DATA_BUS_BATCH_SIZE = 100;

    /**
     * 未知
     */
    public static final String UNKNOWN_CHINESE = "未知";

    /**
     * 一级车型的默认父级Id
     */
    public static final long DEFAULT_FATHER_ID = 0L;

    /**
     * car_selects 表中 belong 字段的取值
     */
    public static final String CAR_TYPE = "car_type";

}
