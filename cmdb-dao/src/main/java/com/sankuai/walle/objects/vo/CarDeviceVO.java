package com.sankuai.walle.objects.vo;

import com.sankuai.walle.dal.eve.entity.CarDevices;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Getter
@Setter
@Builder
public class CarDeviceVO {
    String dmsDeviceNumber;

    public static CarDeviceVO carDevice2CarDeviceVO(CarDevices carDevice){
        return CarDeviceVO.builder()
                .dmsDeviceNumber(carDevice.getSn())
                .build();
    }
}
