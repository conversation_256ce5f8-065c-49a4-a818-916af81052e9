package com.sankuai.walle.objects.vo.res;

import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.common.ErrorCode;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResData {
    public int code;
    public String msg;
    public Object data;
    public Long rows;

    public ResData success(){
        this.code = 0;
        return this;
    }

    public ResData failed(){
        this.code = -1;
        return this;
    }

    /**
     *  响应带有错误信息的失败结果
     * @param msg 错误信息
     * @return ResData
     */
    public static ResData failedWithMsg(String msg){
        ResData resData = new ResData();
        resData.code = -1;
        resData.msg = msg;
        return resData;
    }

    /**
     * 响应结果
     * @param errorCode 错误Code
     * @param msg 错误信息
     * @return  ResData
     */
    public static ResData result(ErrorCode errorCode, String msg){
        ResData resData = new ResData();
        resData.code = errorCode.getCode();
        resData.msg = msg;
        return resData;
    }

    /**
     * 响应结果
     * msg 消息
     * @return  ResData
     */
    public static ResData successWithMsg(String msg){
        ResData resData = new ResData();
        resData.code = 0;
        resData.msg = msg;
        return resData;
    }
    public static ResData successWithMsg(){
        ResData resData = new ResData();
        resData.code = 0;
        resData.msg = CommonConstants.succeedMsg;
        return resData;
    }

    /**
     * 响应结果
     * msg 消息
     * @return  ResData
     */
    public static ResData successWithData(Object obj){
        ResData resData = new ResData();
        resData.code = 0;
        resData.msg = "ok";
        resData.data = obj;
        return resData;
    }


}
