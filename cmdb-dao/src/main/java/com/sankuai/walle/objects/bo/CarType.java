package com.sankuai.walle.objects.bo;

import lombok.ToString;

/**
 * <AUTHOR> Created on 2022/1/17
 */
@ToString
public class CarType {
    private static final String SPLITTER = "::";

    /**
     * 车辆类型名称，例如 mkz, s20等
     */
    private String typeName; // required
    /**
     * 车辆子类型名称，例如s20车型下有EP1-1, EP1-2等
     */
    private String subTypeName; // required
    /**
     * 车辆类型全名, 形如 {$typeName}::{$subTypeName}。例如 mkz, s20::EP1-1, s20::EP1-2
     * )
     */
    private String fullName; // required

    public CarType(String typeName) {
        this.typeName = typeName;
        this.fullName = typeName;
    }

    public CarType(String typeName, String subTypeName) {
        this.typeName = typeName;
        this.subTypeName = subTypeName;
        this.fullName = typeName + SPLITTER + subTypeName;
    }

    public String getTypeName() {
        return typeName;
    }

    public String getSubTypeName() {
        return subTypeName;
    }

    public String getFullName() {
        return fullName;
    }
}
