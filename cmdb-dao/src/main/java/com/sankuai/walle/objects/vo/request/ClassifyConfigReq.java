package com.sankuai.walle.objects.vo.request;

import lombok.*;

import java.util.ArrayList;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClassifyConfigReq {
    String name;
    String desc;
    Long first_car_model_id;// 一级车型
    Long second_car_model_id; // 二级车型
    ArrayList<ThirdCarModelVo> third_car_model; // 三级车的选择
    ArrayList<DeviceConfigVo> deviceConfig; // 配置列表
    String config;
    @Getter
    @Setter
    @Builder
    public static class ThirdCarModelVo {
        Short id; // 1 城市 2 运营区域 3 运营组别 4 车辆编号
        ArrayList<String> content; // 城市接口的city字段 , 园区接口的code字段 , 前端自定义 ，车辆接口的id
    }
    @Getter
    @Setter
    @Builder
    public static class DeviceConfigVo {
        Long device_type_id; // 设备id
        String name;
        String config;
        String config_name;
    }
}
