package com.sankuai.walle.objects.vo.request;

import com.google.gson.Gson;
import com.sankuai.walle.carManage.entity.*;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import com.sankuai.walle.objects.execDo.OperationExecWord;
import lombok.Data;
import org.springframework.cglib.beans.BeanCopier;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class CarAllocateReq {
    String label;
    String vin;
    String inpark; // 在车管中叫 CarOperation.area
    String inparkStr; // 车辆用途的字符串
    String incity; // 城市名称 CarOperation.city
    String inUseType; // 车辆用途，对应车管中写的 CarOperation.carUsedTarget
    String inUseTypeStr; // 车辆用途的字符串
}
