package com.sankuai.walle.objects.vo.res;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.walle.dal.mrm_manage.entity.MyTagsVin;
import lombok.Data;

import java.util.ArrayList;

@Data
public class CarListRes {
    Long remoteObjectId;
    String vin;
    @FieldDoc(description = "资产标签")
    String label;
    @FieldDoc(description = "车辆名称")
    String name;
    @FieldDoc(description = "车辆型号")
    String carType;
    @FieldDoc(description = "车辆标签")
    ArrayList<MyTagsVin> tags;
    @FieldDoc(description = "城市")
    Long cityId;
    String city;
    @FieldDoc(description = "场地")
    Long areaId;
    String area;
    @FieldDoc(description = "资产责任人")
    String ownerMis;
    @FieldDoc(description = "资产归属部门")
    String ownerDepartment;
    @FieldDoc(description = "用车目的")
    String carUsedTarget;
    @FieldDoc(description = "配件")
    String assemblyParts;
}
