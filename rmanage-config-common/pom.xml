<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.walle</groupId>
        <artifactId>rmanage-cmdb</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rmanage-config-common</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>rmanage-config-common</name>
    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.sankuai.walle</groupId>-->
<!--            <artifactId>cmdb-dao</artifactId>-->
<!--            <version>0.0.1-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.sankuai.walle</groupId>-->
<!--            <artifactId>cmdb-client</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.sankuai.it.erp.eam</groupId>
            <artifactId>erp-eam-api</artifactId>
            <version>1.1.11</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
