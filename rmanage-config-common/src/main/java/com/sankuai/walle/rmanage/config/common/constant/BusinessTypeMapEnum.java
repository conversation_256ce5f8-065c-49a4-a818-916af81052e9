package com.sankuai.walle.rmanage.config.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型映射枚举
 */
@AllArgsConstructor
@Getter
public enum BusinessTypeMapEnum {
    BUSINESS_OPERATION("业务运营", "运"),
    ROAD_TEST("路测", "测"),
    MAINTENANCE("维保", ""),
    BUSINESS_EXPLORE("业务探索", "运"),
    UNKNOWN("未知", "");

    // 业务类型
    private final String businessType;

    // 映射值
    private final String value;

    /**
     * 根据业务类型获取映射值。
     *
     * @param businessType 业务类型的字符串表示
     * @return 对应业务类型的值。如果业务类型不存在，则返回空字符串。
     */
    public static String getValueByBusinessType(String businessType) {
        for (BusinessTypeMapEnum businessTypeMapEnum : BusinessTypeMapEnum.values()) {
            if (businessTypeMapEnum.getBusinessType().equals(businessType)) {
                return businessTypeMapEnum.getValue();
            }
        }
        return "";
    }

    /**
     * 根据业务类型字符串查找对应的枚举实例。
     *
     * @param businessType 业务类型的字符串表示
     * @return 对应的枚举实例。如果没有找到匹配的业务类型，则返回null。
     */
    public static BusinessTypeMapEnum findByBusinessType(String businessType) {
        for (BusinessTypeMapEnum businessTypeMapEnum : BusinessTypeMapEnum.values()) {
            if (businessTypeMapEnum.getBusinessType().equals(businessType)) {
                return businessTypeMapEnum;
            }
        }
        return UNKNOWN;
    }
}