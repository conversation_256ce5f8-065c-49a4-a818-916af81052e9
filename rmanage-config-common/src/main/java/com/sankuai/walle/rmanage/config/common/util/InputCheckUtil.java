package com.sankuai.walle.rmanage.config.common.util;

import com.sankuai.walle.rmanage.config.common.exception.ParamException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

/**
 * 上游入参校验，仅校验当前是否合法，是否满足入参条件
 *
 */
@Slf4j
public class InputCheckUtil {

    /**
     * 判断空
     *
     * @param obj
     * @param msg
     */
    public static void isNull(Object obj, String msg) throws ParamException {
        if (obj != null) {
            throwBizException(msg);
        }
    }

    public static void isNull(Object obj, String msg, String... replaces) throws ParamException {
        if (obj != null) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 不为null，否则抛出异常
     *
     * @param obj
     * @param msg
     */
    public static void isNotNull(Object obj, String msg) throws ParamException {
        if (obj == null) {
            throwBizException(msg);
        }
    }

    public static void isNotNull(Object obj, String msg, String... replaces) throws ParamException {
        if (obj == null) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 字符串不为空，否则抛出异常
     *
     * @param str
     * @param msg
     */
    public static void isNotBlank(String str, String msg) throws ParamException {
        if (StringUtils.isBlank(str)) {
            throwBizException(msg);
        }
    }

    public static void isNotBlank(String str, String msg, String... replaces) throws ParamException {
        if (StringUtils.isBlank(str)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    /**
     * 集合不为空，否则抛出异常
     *
     * @param coll
     * @param msg
     */
    public static void isNotEmpty(Collection coll, String msg) {
        if (CollectionUtils.isEmpty(coll)) {
            throwBizException(msg);
        }
    }

    public static void isNotEmpty(Collection coll, String msg, String... replaces) {
        if (CollectionUtils.isEmpty(coll)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    private static void throwBizException(String msg) throws ParamException {
        throw new ParamException(msg);
    }

    private static String replaceMsg(String msg, Object... replace) {
        String str = msg;
        if (StringUtils.isNotBlank(str) && replace != null && replace.length > 0) {
            for (Object temp : replace) {
                str = str.replaceFirst("\\{}", "" + temp);
            }
        }
        return str;
    }
}
