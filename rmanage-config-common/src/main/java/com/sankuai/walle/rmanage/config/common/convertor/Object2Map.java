package com.sankuai.walle.rmanage.config.common.convertor;

import java.lang.reflect.Field;
import java.util.HashMap;

public class Object2Map {
    public static HashMap<String, Object> obj2map(Object obj) {
        HashMap<String, Object> map = new HashMap<>();
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                map.put(field.getName(), field.get(obj));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return map;
    }
}
