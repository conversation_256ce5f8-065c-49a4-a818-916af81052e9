package com.sankuai.walle.rmanage.config.common.exception;

import com.sankuai.walle.rmanage.config.common.ErrorCode;
import lombok.Getter;

/**
 * <AUTHOR> Created on 2021/11/29
 */
@Getter
public class BasicException extends RuntimeException {
    private Integer code;

    private String msg;

    public BasicException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BasicException(ErrorCode errorCode) {
        super(errorCode.getDefaultMsg());
        this.code = errorCode.getCode();
        this.msg = errorCode.getDefaultMsg();
    }

    public BasicException(ErrorCode code, String msg) {
        super(msg);
        this.code = code.getCode();
        this.msg = msg;
    }
}
