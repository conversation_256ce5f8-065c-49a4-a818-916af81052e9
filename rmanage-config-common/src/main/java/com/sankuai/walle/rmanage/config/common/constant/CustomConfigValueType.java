package com.sankuai.walle.rmanage.config.common.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义配置数据类型
 *
 * <AUTHOR> Created on 2022/2/22
 */
public enum CustomConfigValueType {
    STRING(1, "String"),
    PROTOBUF(2, "protobuf"),
    ;

    private static final Map<Integer, CustomConfigValueType> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<>();
        for (CustomConfigValueType value : CustomConfigValueType.values()) {
            VALUE_MAP.put(value.getCode(), value);
        }
    }

    private int code;
    private String desc;

    CustomConfigValueType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code查询配置类型，默认为String
     *
     * @param code
     * @return
     */
    public static CustomConfigValueType getByCode(Integer code) {
        if (code == null) {
            return STRING;
        }
        return VALUE_MAP.get(code) == null ? STRING : VALUE_MAP.get(code);
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
