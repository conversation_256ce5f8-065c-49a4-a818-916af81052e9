package com.sankuai.walle.rmanage.config.common.convertor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Created on 2022/2/24
 */
@Slf4j
public class BinaryConvertor {
    public static List<Byte> convertByteArrayToList(byte[] byteArray) {
        if (byteArray == null || byteArray.length == 0) {
            return Collections.emptyList();
        }

        return Arrays.asList(ArrayUtils.toObject(byteArray));
    }

    public static byte[] convertByteListToArray(List<Byte> byteValue) {
        byte[] byteArray = null;
        if (CollectionUtils.isNotEmpty(byteValue)) {
            byteArray = ArrayUtils.toPrimitive(byteValue.toArray(new Byte[0]));
        }
        log.info("CustomConfigService#convertByteListToArray byteValue = {}, byteArray = {}", byteValue, byteArray);
        return byteArray;
    }
}
