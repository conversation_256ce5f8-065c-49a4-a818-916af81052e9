package com.sankuai.walle.rmanage.config.common.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用转换工具
 *
 * <AUTHOR> Created on 2021/12/3
 */
public class CommonConvertUtils {

    public static <E, K> List<K> toList(Collection<E> collection, Function<E, K> convertFunction) {
        if (CollectionUtils.isEmpty(collection)) {
            return new ArrayList<>();
        }

        return collection.stream()
                .map(convertFunction)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
