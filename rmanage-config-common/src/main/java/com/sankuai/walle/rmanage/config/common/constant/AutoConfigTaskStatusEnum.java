package com.sankuai.walle.rmanage.config.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AutoConfigTaskStatusEnum {
    CREATED(0, "已创建"),
    IN_PROCESS(100, "进行中"),
    COMPLETED(200, "已完成"),
    ABORTED(300, "已中止"),
    CANCEL(400, "已取消"),
    DEFAULT_ABORTED(500, "异常终止");

    private final int code;
    private final String msg;


    public static boolean isTaskFinished(Integer value) {
        return value == COMPLETED.getCode();
    }
}
