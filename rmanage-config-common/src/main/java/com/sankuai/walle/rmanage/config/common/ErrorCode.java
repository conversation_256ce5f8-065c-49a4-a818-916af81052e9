package com.sankuai.walle.rmanage.config.common;

import lombok.Getter;

/**
 * <AUTHOR> Created on 2021/11/29
 */
@Getter
public enum ErrorCode {

    /**
     * 业务异常
     */
    SERVICE_INTERNAL_ERROR(500, "服务器内部异常"),
    PARAM_ERROR(501, "参数错误"),
    REMOTE_ERROR(502, "远程服务异常"),

    // 设备相关异常
    DEVICE_NOT_EXIST(50001, "设备不存在"),
    DEVICE_SN_EMPTY(50002, "设备SN不能为空"),

    // 配置相关异常
    CONFIG_SUBJECT_PARAM_ERROR(60001, "配置主体参数错误"),
    CONFIG_PARAM_ERROR(60002, "配置参数错误"),
    CONFIG_SUBJECT_NOT_EXIST(60003, "配置主体不存在"),
    CONFIG_SUBJECT_TYPE_ID_ERROR(60004, "配置主体类型错误"),
    CONFIG_NOT_EXIST(60005, "配置不存在"),
    CONFIG_KEY_PARAM_NULL(60006, "配置key参数为空"),
    CONFIG_TYPE_NOT_STRING(60007, "配置类型不是string"),

    // 车辆配置相关异常 60101-60199
    CAR_CONFIG_CAR_OBJECT_TYPE_NOT_EXIST(60101, "车辆远程对象类型不存在"),
    CAR_OBJECT_NOT_EXIST(60102, "车辆远程对象不存在"),
    CAR_TYPE_NOT_EXIST(60103, "车辆类型不存"),
    CAR_TYPE_QUERY_BY_VIN_ERROR(60104, "根据vin查询车辆类型错误"),
    CAR_VIN_PARAM_NULL(60105, "车辆vin参数为空"),

    // 车辆设备树相关异常 60201-60299
    DEVICE_TREE_TEMPLATE_PARSE_ERR(60201, "解析设备树模版错误"),
    DEVICE_TREE_PARSE_ERR(60202, "解析设备树错误"),


    // 设备/车型相关异常 60301 - 60399
    DEVICE_TYPE_NAME_EXIST(60301, "设备类型名已存在"),
    DEVICE_FRIEND_NAME_EXIST(60302, "设备类型别名已存在"),
    FATHER_CAR_TYPE_NAME_EXIST(60303, "一级车型已存在"),
    SON_CAR_TYPE_NAME_EXIST(60304, "二级车型已存在"),
    ;

    private int code;
    private String defaultMsg;

    ErrorCode(int code, String defaultMsg) {
        this.code = code;
        this.defaultMsg = defaultMsg;
    }

}
