namespace java com.sankuai.walle.common

/**
 * @TypeDoc(
 *     description = "请求上下文"
 * )
 */
struct ReqContext {
        /**
         * @FieldDoc(
         *     description = "操作人名称"
         * )
         */
        1: optional string operatorName;
}

// 请求响应状态
struct Status {
        // 状态码
        1: required i32 code;
        // 状态信息
        2: optional string msg;
}

/**
 * 布尔类型返回值
 **/
struct BoolResp {
        // 状态
        1: required Status status;
        // 结果
        2: optional bool data;
}

/**
 * 字符串类型返回值
 **/
struct StringResp {
        // 状态
        1: required Status status;
        // 结果
        2: optional string data;
}


struct StringStringMapResp {
        // 状态
        1: required Status status;
        // string >> string map结果
        2: optional map<string, string> data;
}

struct LongResp {
        // 状态
        1: required Status status;
        // long型结果
        2: optional i64 data;
}

/**
 * @TypeDoc(
 *     description = "分页信息"
 * )
 */
struct Page {
        /**
         * @FieldDoc(
         *     description = "页码"
         * )
         */
        1: required i32 pageNo = 1;
        /**
         * @FieldDoc(
         *     description = "分页大小，[1, 200]"
         * )
         */
        2: required i32 pageSize = 20;
        /**
         * @FieldDoc(
         *     description = "查询命中总记录数, 查询请求时无需设置"
         * )
         */
        3: optional i32 totalCount;
}