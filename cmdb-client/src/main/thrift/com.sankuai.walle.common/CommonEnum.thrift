namespace java com.sankuai.walle.common

/**
 * 配置主体类型
 **/
enum ConfigSubjectType {
        // 远程对象类型
        REMOTE_OBJECT_TYPE = 1,
        // 车辆类型
        CAR_TYPE = 11,
        // 车辆实例
        CAR_INSTANCE = 12,
        // 车辆子类型 EP1-1, EP1-2等
        CAR_SUB_TYPE = 13,
        // 设备类型
        DEVICE_TYPE = 21,
        // 设备实例
        DEVICE_INSTANCE = 22,
        // 软件类型
        SOFTWARE_TYPE = 31,
        // 软件实例
        SOFTWARE_INSTANCE = 32,
        // 坐席实例
        COCKPIT_INSTANCE = 42,
        // 系统管理
        SYSTEM_MANAGE = 91,
}