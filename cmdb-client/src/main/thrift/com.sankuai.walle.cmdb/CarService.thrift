include 'com.sankuai.walle.cmdb/CarConfigModel.thrift'
include 'com.sankuai.walle.cmdb/CarModel.thrift'

namespace java com.sankuai.walle.cmdb.thrift.service


/**
 * @InterfaceDoc(
 *     displayName = "远程管理-设备管理接口",
 *     type = "octo.thrift",
 *     description = "配置管理相关能力",
 *     scenarios = "车端获取云端的配置信息"
 * )
 */
service CarThriftService {

        /**
         * @MethodDoc(
         *     displayName = "根据vin，deviceId 查询设备配置信息",
         *     description = "根据vin，deviceId 查询设备配置信息",
         *     parameters = {
         *         @ParamDoc(
         *             sn = "vin号",
         *             deviceId = "设备唯一编号"
         *         )
         *     },
         *     returnValueDescription = "查询结果"
         * )
         */
        CarConfigModel.CarConfigResp getConfigByVin(1: string sn,2: string name);
        /**
         * @MethodDoc(
         *     displayName = "根据vin 查询设备配置信息",
         *     description = "根据vin 查询设备配置信息",
         *     parameters = {
         *         @ParamDoc(
         *             sn = "vin号",
         *         )
         *     },
         *     returnValueDescription = "查询结果"
         * )
         */
        CarConfigModel.CarConfigAllResp getConfigsByVin(1: string vin);


        // 获取所有车型
        CarModel.CarModelResp getCarType();
        /*
        *     description = "根据车型id，获取对应的硬件",
        *     parameters = {
        *         @ParamDoc(
        *             type_id = "二级车型的id，来自 getCarType 接口返回的数据",
        *             level = "1 代表一级车型，2：代表2级车型"
        *         )
        *     },
        * */
        CarModel.CarDeviceResp getCarDevices(1: i64 type_id,2: i32 level);
        /*
        *     description = "根据车型id，车型级别，获取其vin列表",
        *     parameters = {
        *         @ParamDoc(
        *             type_id = "二级车型的id，来自 getCarType 接口返回的数据",
        *             level = "1 代表一级车型，2：代表2级车型"
        *         )
        *     },
        * */
//        CarModel.VinsResp getTypeCars(1: i64 type_id,2: i32 level);
}