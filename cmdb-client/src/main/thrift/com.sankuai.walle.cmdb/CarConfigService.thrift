include 'com.sankuai.walle.cmdb/CarConfigModel.thrift'
include 'com.sankuai.walle.cmdb/CarModel.thrift'
include 'com.sankuai.walle.cmdb/NormalResModel.thrift'

namespace java com.sankuai.walle.cmdb.thrift.service

service CarConfigThriftService {
    void sendConfigToMqtt();
    //通过vin获取车辆auk对应的的secret
    NormalResModel.NormalResp getVechildSecret(1:string vin);
    NormalResModel.NormalResp getVechildCert(1:string vin);

    // 发起配置下发
    NormalResModel.NormalResp sendConfigApi(1:CarConfigModel.ApiConfigReq req);
    // 查询车辆配置
    NormalResModel.NormalResp getCarConfigApi(1:CarConfigModel.ApiCarConfigReq req);
    // 查询配置项的信息
    NormalResModel.NormalResp getNameConfigApi(1:CarConfigModel.ApiNameConfigReq req);
    // 查询车辆当前配置内容
    CarConfigModel.VinConfigResp getCarConfigContentApi(1:CarConfigModel.ApiCarConfigReq req);
    // 查询所有车辆配置内容
    CarConfigModel.VinConfigResp getAllCarConfigContentApi(1:CarConfigModel.ApiCarConfigReq req);
}