include "com.sankuai.walle.common/CommonModel.thrift"

namespace java com.sankuai.walle.cmdb.thrift.model
// 一级车型
struct FirstCarType {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    1: i64 id;

    /**
     *   字段: type_name
     *   说明: 类型名
     */
    2: string typeName;

    /**
     *   字段: friend_name
     *   说明: 类型别名
     */
    3: string friendName;

    /**
     *   字段: conf_content
     *   说明: 配置信息
     */
    4: string confContent;
}

// 二级车型
struct SecondCarType {
        /**
         *   字段: id
         *   说明: 自增主键
         */
        1: i64 id;

        /**
         *   字段: name
         *   说明: 展示TAG
         */
        2: string name;

        /**
         *   字段: color
         *   说明: 使用的颜色
         */
        3: string color;

        /**
         *   字段: tag_type
         *   说明: 标签类型
         */
        4: i64 tagType;

        /**
         *   字段: add_time
         *   说明: 建立时间
         */
        5: string addTime;

        /**
         *   字段: update_time
         *   说明: 更新时间
         */
        6: string updateTime;
}

// 一级、二级关联的resp
struct CarModel {
        /**
         * @FieldDoc(
         *     description = "一级车型"
         * )
         */
        2: required FirstCarType firstCarType;
        /**
         * @FieldDoc(
         *     description = "二级车型列表"
         * )
         */
        3: required list<SecondCarType> secondCarTypeList;
}

// 车型结构树
struct CarModelResp {
        /**
         * @FieldDoc(
         *     description = "结果状态，status.code为0时表示成功；否则status表示错误状态"
         * )
         */
        1: required CommonModel.Status status;
        /**
         * @FieldDoc(
         *     description = "所有二级车型列表, string 为序列化的string"
         * )
         */
        2: required list<CarModel> data;
}

// 设备
struct Device{ //对应remote_device_type
        /**
         *   字段: id
         *   说明: 自增主键
         */
        1: i64 id;

        /**
         *   字段: type_name
         *   说明: 类型名
         */
        2: string typeName;

        /**
         *   字段: friend_name
         *   说明: 类型别名
         */
        3: string friendName;

        /**
         *   字段: conf_content
         *   说明: 配置信息
         */
        4: string confContent;

        /**
         *   字段: latest_version_id
         *   说明: 当前设备最新版本ID
         */
        5: i64 latestVersionId;

        /**
         *   字段: add_time
         *   说明: 建立时间
         */
        6: string addTime;

        /**
         *   字段: update_time
         *   说明: 更新时间
         */
        7: string updateTime;
}

struct VehicleDevice{
        1: Device device;
        2: i64 id; // 表的id
        3: i64 vehicle_id; //车型id
}

struct CarDeviceResp {
        /**
         * @FieldDoc(
         *     description = "结果状态，status.code为0时表示成功；否则status表示错误状态"
         * )
         */
        1: required CommonModel.Status status;
        /**
         * @FieldDoc(
         *     description = "设备"
         * )
         */
        2: required list<VehicleDevice> devices;
}

struct VinsResp {
        /**
         * @FieldDoc(
         *     description = "结果状态，status.code为0时表示成功；否则status表示错误状态"
         * )
         */
        1: required CommonModel.Status status;
        /**
         * @FieldDoc(
         *     description = "设备"
         * )
         */
        2: required list<string> data;
}