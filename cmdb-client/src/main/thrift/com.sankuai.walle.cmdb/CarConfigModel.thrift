include "com.sankuai.walle.common/CommonModel.thrift"

namespace java com.sankuai.walle.cmdb.thrift.model

/**
 * @TypeDoc(
 *     description = "根据车辆vin查询车辆类型响应结果"
 * )
 */
struct CarConfigResp {
        /**
         * @FieldDoc(
         *     description = "结果状态，status.code为0时表示成功；否则status表示错误状态"
         * )
         */
        1: required CommonModel.Status status;
        /**
         * @FieldDoc(
         *     description = "配置名称"
         * )
         */
        2: required string name;
        /**
         * @FieldDoc(
         *     description = "内容"
         * )
         */
        3: required string config;
}

struct CarConfig {
        /**
         * @FieldDoc(
         *     description = "配置名称"
         * )
         */
        1: required string name;
        /**
         * @FieldDoc(
         *     description = "内容"
         * )
         */
        2: required string config;
}

struct CarConfigAllResp {
        /**
         * @FieldDoc(
         *     description = "结果状态，status.code为0时表示成功；否则status表示错误状态"
         * )
         */
        1: required CommonModel.Status status;
        2: required list<CarConfig> data;
}

struct VinConfigModel {
    1: required string vin;
    2: required CarConfig config;
}

struct VinConfigResp {
    // 用于查询vin，对应的name的配置内容
    1: required CommonModel.Status status;
    2: required list<VinConfigModel> data;
}

// 对外提供配置下发接口
struct ApiConfigReq{
    1: required list<string> vins;
    2: required string taskName; // 任务名称
    3: required i64 deviceId;
    4: required i64 configId;
    5: string userMis;
    6: string name; // 配置项名称
}

struct ApiCarConfigReq{
    1: required list<string> vins;
    2: required string name; // 配置项名称
}

struct ApiNameConfigReq{
    1: required string name;
}