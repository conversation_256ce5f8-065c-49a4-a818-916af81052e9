package com.sankuai.walle.rmanage.config.thrift.model;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Builder;
import lombok.Data;
import org.apache.thrift.TBase;

@Data
@Builder
@ThriftStruct
public class CarDeviceDTO {

        /**
         *   字段: id
         *   说明: 自增主键
         */
        @ThriftField(value = 1, requiredness = ThriftField.Requiredness.REQUIRED)
        private Long id;

        /**
         *   字段: type
         *   说明: 设备类型
         */
        @ThriftField(value = 2, requiredness = ThriftField.Requiredness.REQUIRED)
        private String type;

        /**
         *   字段: vin
         */
        @ThriftField(value = 3, requiredness = ThriftField.Requiredness.REQUIRED)
        private String vin;

        /**
         *   字段: sn
         *   说明: sn address
         */
        @ThriftField(value = 4, requiredness = ThriftField.Requiredness.REQUIRED)
        private String sn;
}
