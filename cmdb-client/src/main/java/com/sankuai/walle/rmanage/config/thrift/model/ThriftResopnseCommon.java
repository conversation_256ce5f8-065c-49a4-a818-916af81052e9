package com.sankuai.walle.rmanage.config.thrift.model;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@ThriftStruct
@Data
public class ThriftResopnseCommon<T> {
    @ThriftField(1)
    private int code;
    @ThriftField(2)
    private String message;
    @ThriftField(3)
    private transient T data;
}