#!/bin/bash
# 项目版本号自动升级脚本
# 在本地master分支执行该脚本 `./shell/release.sh`
#
# 脚本效果
# 一. 如果当前pom版本是release版本：`a.b.c`，
#    1. 打tag为`a.b.c`
#    2. 更新版本号为`a.b.(c+1)-SNAPSHOT`, commit.
# 二. 如果当前pom版本是snapshot版本：`a.b.c-SNAPSHOT`
#    1. 更新版本号为`a.b.c`, commit
#    2. 打tag为`a.b.c`
#    3. 更新版本号为`a.b.(c+1)-SNAPSHOT`, commit.
#
# 可选参数 -a 是否自动push
#    `./shell/release.sh` 不会自动push到远程，需手动push commit及tag;
#    `./shell/release.sh -a` 或 `./shell/release.sh -a=true` 会自动push commits及tag到远程。


########## 退出脚本 ##########
export TOP_PID=$$
trap 'exit 1' TERM

exit_script(){
    kill -s TERM $TOP_PID
}

########## git utils ##########

# 判断当前工作区是否有未提交代码
function is_work_directory_clear() {
    local change_for_commit=$(git status | grep 'Changes not staged for commit')
    local change_added_to_commit=$(git status | grep 'Changes to be committed')
    if [ -n "$change_for_commit" ] || [ -n "$change_added_to_commit" ];
    then
        true;
    else
        false;
    fi
}

# 判断是否落后于远程或分叉
function is_behind_or_diverged() {
    local behind_match=$(git status | grep 'Your branch is behind ')
    local diverge_match=$(git status | grep 'have diverged')
    if [ -n "$behind_match" ] || [ -n "$diverge_match" ];
    then
        true;
    else
        false;
    fi
}

# 判断当前工作区
function is_up_to_date_and_clean() {
    local match=$(git status | grep -E 'nothing to commit,.*clean')
    # 字符串判断非空
    if [ -n "$match" ];
    then
        true;
    else
        false;
    fi
}

# 获取当前分支名称
function get_current_branch_name() {
    local br=$(git branch | grep "*")
    echo ${br/* /}
}

# add and commit
# param1 commit信息
function git_add_and_commit() {
    local commit_message=$1

    add_cmd="git add ."
    commit_cmd=$(printf "git commit -m %s" $commit_message)
    echo $add_cmd
    echo $commit_cmd

    $add_cmd &&
    $commit_cmd
}

# add tag 添加tag
# param1 release版本号
function git_tag() {
    local tag_name=$1

    add_tag_cmd=$(printf "git tag -a %s -m 'release%s'" $tag_name $tag_name)
    echo $add_tag_cmd

    $add_tag_cmd
}

# git push 分支
# param1 分支名称
function git_push_branch() {
    local branch_name=$1

    push_cmd=$(printf "git push origin %s:%s" $branch_name $branch_name)
    echo $push_cmd

    $push_cmd
}

# push push tag 到远程
# param1 tag名称
function git_push_tag() {
    local tag=$1
    push_tag_cmd=$(printf "git push origin %s" $tag)
    echo $push_tag_cmd

    $push_tag_cmd
}

########## String utils ##########

# 判断是否正式版本号 bash正则不支持\d
function is_release() {
    if
        [[ $1 =~ ^([0-9]*.){1,3}([0-9]*)$ ]];
    then
        return 0;
    else
        return 1;
    fi
}

# 判断是否是SNAPSHOT版本号
function is_snapshot() {
    if
        [[ $1 =~ ^([0-9]*.){1,3}([0-9]*)\-SNAPSHOT$ ]];
    then
        return 0;
    else
        return 1;
    fi
}

# 获取最后一位版本号
function get_patch_version() {
    local full_version=$1
    echo ${full_version##[0-9]*.}
}

# 获取主版本号(不含最后一位)
function get_major_version() {
    local full_version=$1
    echo ${full_version%.[0-9]*}
}

# 获取下一个SNAPSHOT版本号
function build_next_full_snapshot_version() {
    local current_version=$1
    patch_version=$(get_patch_version $current_version)
    ((patch_version++))
    major_version=$(get_major_version $current_version)

    printf "%s.%s-SNAPSHOT" $major_version $patch_version
}

# 根据当前snapshot版本号获取release版本号
function get_next_release_version_by_snapshot() {
    local current_version=$1
    if
        is_snapshot $current_version
    then
        echo ${current_version%-SNAPSHOT*}
    else
        echo "不是SNAPSHOT版本"
        exit_script
    fi
}

########## mvn utils ##########

# 获取当前项目版本号
function get_current_version() {
    local project_version=$(mvn -q \
                     -Dexec.executable="echo" \
                     -Dexec.args='${project.version}' \
                     --non-recursive \
                     org.codehaus.mojo:exec-maven-plugin:1.6.0:exec)
    echo $project_version
}

# 组装设置版本号命令
function build_reset_version_cmd() {
    local new_version=$1
    reset_cmd=$(printf "mvn versions:set -DnewVersion=%s -DgenerateBackupPoms=false" $new_version)
    echo $reset_cmd
}

# mvn set version
function reset_project_version() {
    local new_version=$1
    cmd=$(build_reset_version_cmd $new_version)
    $cmd
}

########## combined methods ##########

# 校验当前分支名称是否匹配
# param1 期望分支名称
function check_branch_name() {
    local branch_name=$(get_current_branch_name)
    local checking_branch_name=$1

    echo "当前分支为:"$branch_name

    if [[ "$checking_branch_name" != "$branch_name" ]]
    then
        echo "请切换到master分支执行";
        exit;
    fi
}

# 拉取远程分支
# param1 分支名称
function fetching_remote_branch() {
    local branch_name=$1

    echo "fetching origin "$branch_name
    fetch_cmd="git fetch origin "$branch_name
    $fetch_cmd
}

# 校验git工作区 与远程仓库
# 如果有未提交代码或落后于远程 退出
function check_working_directory() {
    # 判断工作区是否有未提交代码
    if
        is_work_directory_clear;
    then
        echo "工作区有未提交或暂存的变更, 请'git status'确认后重试"
        exit;
    fi

    # 判断当前分支是否落后于远程
    if
        is_behind_or_diverged;
    then
        echo "当前分支落后于远程分支或分叉, 请确认不落后于远程分支";
        exit;
    fi

    if
        is_up_to_date_and_clean;
    then
        echo "up to date and clean."
    else
        echo "请确认与远程相应分支一致";
        exit;
    fi
}

# 处理SNAPSHOT版本
# param1 项目版本号
# param2 是否自动push参数
function handle_snapshot_version() {
    local project_version=$1
    local auto_push_param=$2

    # 1 获取当前版本对应的正式版本号
    echo "当前是SNAPSHOT版本"
    next_release_version=$(get_next_release_version_by_snapshot $project_version)
    echo "下一个release版本为: "$next_release_version

    # 2 更新release版本号
    reset_project_version $next_release_version

    # 3 git add, git commit
    commit_message=$(printf "release:%s" $next_release_version)
    git_add_and_commit $commit_message

    # 4 处理release版本
    handle_release_version $next_release_version $auto_push_param
}

# 处理release版本
# param1 项目版本号
# param2 是否自动push参数
function handle_release_version() {
    local release_version=$1
    local auto_push_param=$2

    # 1 git tag
    git_tag $release_version

    # 2 获取下一个SNAPSHOT版本
    next_snapshot_version=$(build_next_full_snapshot_version $release_version)

    # 3 更新项目版本为下一个SNAPSHOT
    reset_project_version $next_snapshot_version

    # 4 git add, git commit
    commit_message=$(printf "update:%s" $next_snapshot_version)
    git_add_and_commit $commit_message

    # 5 是否自动push
    if
        is_auto_push $auto_push_param
    then
        git_push_branch master
        git_push_tag $release_version
    else
        echo "有新的commit及tag未push"
        echo "使用 'git push' 推送commits, 或 'git reset {commitId} --hard' 取消 commit 到指定commitId"
        echo "使用 'git push origin "$release_version"' 推送tag, 或 'git tag -d "$release_version"' 删除本地tag"
    fi
}

# 判断是否自动push
# param1 是否自动push参数
function is_auto_push() {
    local auto_push_param=$1
    if [[ "true" == "$auto_push_param" ]]
    then
        true
    else
        false
    fi
}

################################################################################
## main
################################################################################
# 0 获取输入参数，是否自动push参数
auto_push_param=false
while getopts ":a:" opt; do
    case $opt in
        a)
            if [[ "$OPTARG" == "=true" ]] || [[ "$OPTARG" == "=True" ]];
            then
                auto_push_param=true
            fi
            ;;
        \?)
            echo "Invalid option: -$OPTARG"
            ;;
        \:)
            # 不传值默认 true
            auto_push_param=true
            ;;
    esac
done
echo "auto_push "$auto_push_param

# 1 校验分支是否是master
check_branch_name master

# 2 拉取远程代码
fetching_remote_branch master

# 3 校验工作区是否与远程一致
check_working_directory

# 4 获取当前项目版本号
project_version=$(get_current_version)
echo "当前项目版本号: " $project_version

# 4.1 如果是snapshot版本,
if
    is_snapshot $project_version;
then
    handle_snapshot_version $project_version $auto_push_param
    exit
fi

# 4.2 如果是release版本， 修改版本号并提交
if
    is_release $project_version
then
    handle_release_version $project_version $auto_push_param
    exit;
else
    echo "版本号不符合规范, exit."
    exit;
fi
