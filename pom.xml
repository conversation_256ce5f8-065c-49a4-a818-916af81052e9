<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.walle</groupId>
    <artifactId>rmanage-cmdb</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>rmanage-config</name>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>1.7.17.8</version>
    </parent>

    <properties>
        <grpc.version>1.6.1</grpc.version>
        <protobuf.version>3.9.0</protobuf.version>
        <mapstruct-jdk8.version>1.5.2.Final</mapstruct-jdk8.version>
    </properties>

    <modules>
        <module>rmanage-config-common</module>
        <module>cmdb-client</module>
        <module>rmanage-config-server</module>
        <module>schedule</module>
        <module>cmdb-dao</module>
        <!--        <module>rmanage-config-device</module>-->
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>3.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>3.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.6</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.walle</groupId>
                <artifactId>rmanage-config-common</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.walle</groupId>
                <artifactId>cmdb-client</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.walle</groupId>
                <artifactId>rmanage-config-server</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>uac-common-sdk</artifactId>
                <version>1.1.37</version>
                <exclusions>
                    <exclusion>
                        <groupId>asm</groupId>
                        <artifactId>asm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sankuai.meituan.bsi</groupId>
                        <artifactId>mt-config-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>asm</groupId>
                        <artifactId>asm-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sankuai.meituan</groupId>
                        <artifactId>mt-config-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mars.framework</groupId>
                        <artifactId>mars-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.walleeve</groupId>
                <artifactId>walle-eve-utils</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct-jdk8.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct-jdk8.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct-jdk8.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.22.0</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.wallecmdb.monitor</groupId>
                <artifactId>eve_monitor_online-client</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version> <!-- 请使用最新版本 -->
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>meituan-nexus-releases</id>
            <name>Meituan Nexus Repository</name>
            <url>http://pixel.sankuai.com/repository/releases</url>
        </repository>
        <snapshotRepository>
            <id>meituan-nexus-snapshots</id>
            <name>Meituan Nexus Repository</name>
            <url>http://pixel.sankuai.com/repository/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>
