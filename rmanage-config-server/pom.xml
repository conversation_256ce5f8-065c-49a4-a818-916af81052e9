<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.walle</groupId>
        <artifactId>rmanage-cmdb</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rmanage-config-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>rmanage-config-server</name>


    <properties>
        <mt-config-api.version>1.4.1</mt-config-api.version>
        <gson.version>2.8.6</gson.version>
        <spring-retry.version>1.2.2.RELEASE</spring-retry.version>
        <grpc.version>1.42.1</grpc.version>
        <protobuf.version>3.19.1</protobuf.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>xframe-threadpool-sdk</artifactId>
            <version>1.0.12</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.walleops.cloud.triage</groupId>
            <artifactId>cloud-triage</artifactId>
            <version>0.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.walle</groupId>
            <artifactId>cmdb-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.walle</groupId>
            <artifactId>cmdb-dao</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.walle</groupId>
            <artifactId>schedule</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-s3plus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.service.mobile</groupId>
                    <artifactId>mtthrift</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-config-customized</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>uac-common-sdk</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.sankuai.kv</groupId>-->
        <!--            <artifactId>kv-client</artifactId>-->
        <!--            <version>0.0.5</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <!--aop-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!--leaf-->
        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>
        <!--crane-->
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>
        <!-- 快搭 -->
        <dependency>
            <groupId>com.meituan.it</groupId>
            <artifactId>bpm-open-sdk</artifactId>
            <version>1.6.1</version>
        </dependency>
        <!--MAFKA-->
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <!--depends on scala version, offer 2.9 and 2.10-->
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>
        <!--MQTT依赖-->
        <dependency>
            <groupId>com.sankuai.auk</groupId>
            <artifactId>banma_auk_device_server_sdk</artifactId>
            <version>2.3.6</version>
        </dependency>
        <!--        基地资产-->
        <dependency>
            <groupId>com.sankuai.it.erp.eam</groupId>
            <artifactId>erp-eam-api</artifactId>
            <version>1.1.11</version>
        </dependency>
        <!--        ota-->
        <dependency>
            <groupId>com.sankuai.walle</groupId>
            <artifactId>rmanage-ota-client</artifactId>
            <version>1.11.0</version>
        </dependency>
        <!--        ORG-->
        <dependency>
            <groupId>com.sankuai.meituan.org</groupId>
            <artifactId>open-sdk</artifactId>
        </dependency>
        <!--third-party-->
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.5.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.5.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.2</version>
        </dependency>
        <!--proto-->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>${protobuf.version}</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.1.4</version>
        </dependency>
<!--        mapstruct-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.databus</groupId>
            <artifactId>dbusUtils_thrift0.8</artifactId>
            <version>0.0.19</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.walle</groupId>
            <artifactId>cms-client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.8.4</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.walleeve</groupId>
            <artifactId>walle-eve-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wallecmdb.monitor</groupId>
            <artifactId>eve_monitor_online-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.carosscan</groupId>
            <artifactId>eve_common_client</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.walledelivery</groupId>
            <artifactId>walle-delivery-basic-client</artifactId>
            <version>1.0.19</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf.kms</groupId>
            <artifactId>kms_java_client_autoconfig_starter</artifactId>
            <version>0.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.21.12</version> <!-- 请根据项目需求选择合适的版本 -->
        </dependency>
        <!-- 在测试依赖部分添加 ByteBuddy 依赖 -->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.12.10</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.12.10</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <!--            <resource>-->
            <!--                <directory>src/main/java</directory>-->
            <!--                <includes>-->
            <!--                    <include>**/*.xml</include>-->
            <!--                    <include>**/*.properties</include>-->
            <!--                </includes>-->
            <!--            </resource>-->
            <!--            <resource>-->
            <!--                <directory>src/main/resources</directory>-->
            <!--                <includes>-->
            <!--                    <include>**/*.xml</include>-->
            <!--                    <include>**/*.properties</include>-->
            <!--                </includes>-->
            <!--            </resource>-->
            <!--            <resource>-->
            <!--                <directory>src/main/profiles</directory>-->
            <!--                <includes>-->
            <!--                    <include>**/*.xml</include>-->
            <!--                    <include>**/*.properties</include>-->
            <!--                </includes>-->
            <!--            </resource>-->
            <!--            <resource>-->
            <!--                <directory>src/main/resources/profiles</directory>-->
            <!--                <filtering>true</filtering>-->
            <!--                <includes>-->
            <!--                    <include>**/*.properties</include>-->
            <!--                </includes>-->
            <!--                <excludes>-->
            <!--                    <exclude>**/*</exclude>-->
            <!--                </excludes>-->
            <!--            </resource>-->
        </resources>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.0</version>
            </extension>
        </extensions>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <configuration>
                    <protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}</protocArtifact>
                    <pluginId>grpc-java</pluginId>
                    <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}</pluginArtifact>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compile-custom</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>