package com.sankuai.walle.rmanage.config.controller;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.banma.auk.server.sdk.request.thing.ThingInvokeServiceRequest;
import com.sankuai.banma.auk.server.sdk.response.thing.ThingInvokeServiceResponse;
import com.sankuai.banma.auk.server.sdk.util.JsonUtil;
import com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.convertor.ChargingCabinetConvert;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.repository.ChargingCabinetRepsitory;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ChargingCabinetControllerTest {

    @Mock
    private ChargingCabinetRepsitory chargingCabinetRepsitory;

    @InjectMocks
    private ChargingCabinetController chargingCabinetController;

    @Mock
    private MyAukService myAukService;

    @Mock
    private ChargingCabinetConvert chargingCabinetConvert;

    private ThingInvokeServiceRequest mockRequest;

    private ThingInvokeServiceResponse mockResponse;

    @Before
    public void setUp() {
        Mockito.reset(chargingCabinetRepsitory);
        mockRequest = new ThingInvokeServiceRequest();
        mockResponse = new ThingInvokeServiceResponse();
    }

    /**
     * 测试场景：数据库查询结果为 null
     */
    @Test
    public void testGetImportConfigDatabaseResultNull() throws Throwable {
        // arrange
        String deviceId = "testDeviceId";
        when(chargingCabinetRepsitory.queryDaoByDeviceId(deviceId)).thenReturn(null);
        // act
        ResData result = chargingCabinetController.getImportConfig(deviceId);
        // assert
        assertEquals(null, result.getData());
        assertEquals(0, result.getCode());
        verify(chargingCabinetRepsitory, times(1)).queryDaoByDeviceId(deviceId);
    }

    /**
     * 测试场景：数据库查询结果不为 null，但 arg 字段为空
     */
    @Test
    public void testGetImportConfigArgFieldEmpty() throws Throwable {
        // arrange
        String deviceId = "testDeviceId";
        BatterySwapCabinetProperty property = new BatterySwapCabinetProperty();
        property.setArg("");
        when(chargingCabinetRepsitory.queryDaoByDeviceId(deviceId)).thenReturn(property);
        // act
        ResData result = chargingCabinetController.getImportConfig(deviceId);
        // assert
        assertEquals(null, result.getData());
        assertEquals(0, result.getCode());
        verify(chargingCabinetRepsitory, times(1)).queryDaoByDeviceId(deviceId);
    }

    /**
     * 测试场景：arg 字段为合法 JSON，且包含 Remote:Remote_ELock_Control
     */
    @Test
    public void testGetImportConfigArgFieldContainsRemoteELockControl() throws Throwable {
        // arrange
        String deviceId = "testDeviceId";
        BatterySwapCabinetProperty property = new BatterySwapCabinetProperty();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("Remote:Remote_ELock_Control", "LOCKED");
        String arg = JsonUtil.toJsonString(jsonMap);
        property.setArg(arg);
        when(chargingCabinetRepsitory.queryDaoByDeviceId(deviceId)).thenReturn(property);
        // act
        ResData result = chargingCabinetController.getImportConfig(deviceId);
        // assert
        assertEquals("LOCKED", result.getData());
        assertEquals(0, result.getCode());
        verify(chargingCabinetRepsitory, times(1)).queryDaoByDeviceId(deviceId);
    }

    /**
     * 测试场景：arg 字段为合法 JSON，但不包含 Remote:Remote_ELock_Control
     */
    @Test
    public void testGetImportConfigArgFieldDoesNotContainRemoteELockControl() throws Throwable {
        // arrange
        String deviceId = "testDeviceId";
        BatterySwapCabinetProperty property = new BatterySwapCabinetProperty();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("OtherKey", "Value");
        String arg = JsonUtil.toJsonString(jsonMap);
        property.setArg(arg);
        when(chargingCabinetRepsitory.queryDaoByDeviceId(deviceId)).thenReturn(property);
        // act
        ResData result = chargingCabinetController.getImportConfig(deviceId);
        // assert
        assertEquals("null", result.getData());
        assertEquals(0, result.getCode());
        verify(chargingCabinetRepsitory, times(1)).queryDaoByDeviceId(deviceId);
    }

    /**
     * 测试场景：arg 字段不是合法 JSON
     */
    @Test(expected = RuntimeException.class)
    public void testGetImportConfigArgFieldInvalidJson() throws Throwable {
        // arrange
        String deviceId = "testDeviceId";
        BatterySwapCabinetProperty property = new BatterySwapCabinetProperty();
        property.setArg("InvalidJsonString");
        when(chargingCabinetRepsitory.queryDaoByDeviceId(deviceId)).thenReturn(property);
        // act
        chargingCabinetController.getImportConfig(deviceId);
        // assert
        // 异常场景无需额外断言
    }




}
