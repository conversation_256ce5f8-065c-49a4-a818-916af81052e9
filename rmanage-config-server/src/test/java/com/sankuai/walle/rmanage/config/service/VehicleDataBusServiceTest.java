package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.rmanage.config.BaseTest;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.ReserveVehicle;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.VresvItem;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;

@Slf4j
public class VehicleDataBusServiceTest extends BaseTest {

    @Resource
    private VehicleDataBusService vehicleDataBusService;

    @Test
    public void testGetVehicleInfoString() {
        VehicleDataBusVTO vehicleInfo = vehicleDataBusService.getVehicleInfoString("LMTZSV024MC062730");
        log.info("vehicleInfo: {}", JacksonUtils.to(vehicleInfo));

        // 获得 reserve_vehicle.vresv_list 列表中所有的 substitute 近场安全员信息
//        List<VresvItem> substituteList = Optional.ofNullable(vehicleInfo.getReserveVehicle())
//                .map(ReserveVehicle::getVresvList)
//                .orElse(null);
//
//        // 如果没有安全员则为"-"
//        String substituteListString = "-";
//        if (CollectionUtils.isNotEmpty(substituteList)) {
//            // 转换为需要显示的字符串
//            substituteListString = substituteList.stream()
//                    .map(VresvItem::getSubstitute)
//                    .collect(Collectors.joining(","));
//        }
//        log.info("substituteListString={}", substituteListString);
    }
}
