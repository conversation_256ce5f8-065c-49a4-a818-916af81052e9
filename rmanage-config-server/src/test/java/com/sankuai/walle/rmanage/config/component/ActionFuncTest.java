package com.sankuai.walle.rmanage.config.component;

import com.sankuai.walle.rmanage.config.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Ignore
public class ActionFuncTest extends BaseTest {
    @Resource
    ActionFunc actionFunc;

    @Test
    public void createGroup() throws Exception {
        Map<String,Object> accidentDetail = new HashMap<>();
        accidentDetail.put("id",2115);
        accidentDetail.put("reporter","zhaoduanc<PERSON>");
        accidentDetail.put("vin","LMTZSV024MC048701");
        accidentDetail.put("accident_time","2024-01-23 16:38:08");
        actionFunc.create_group("", accidentDetail);
    }

    @Test
    public void createGroupToFilter() throws Exception {
        Map<String,Object> accidentDetail = new HashMap<>();
        accidentDetail.put("id",2115);
        accidentDetail.put("reporter","zhaoduancai");
        accidentDetail.put("vin","LMTZSV021MC067304");
        accidentDetail.put("accident_time","2024-01-23 16:38:08");
        actionFunc.create_group("", accidentDetail);
    }

}