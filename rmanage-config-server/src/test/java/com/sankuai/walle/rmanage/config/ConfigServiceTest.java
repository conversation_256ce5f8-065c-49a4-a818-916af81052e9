package com.sankuai.walle.rmanage.config;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.ByteString;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfigExtend;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigExtendMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.rmanage.config.Listener.strategy.MessageHandlerFactory;
import com.sankuai.walle.rmanage.config.Listener.strategy.MessageHandlerStrategy;
import com.sankuai.walle.rmanage.config.service.impl.CarDeviceConfigServiceImpl;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ConfigServiceTest {

    @Mock
    private CarDeviceConfigMapper carDeviceConfigMapper;

    @Mock
    private CarDeviceConfigExtendMapper carDeviceConfigExtendMapper;

    @Mock
    private CarConfigMapper carConfigMapper;

    @InjectMocks
    private CarDeviceConfigServiceImpl carDeviceConfigService;

    @Resource
    private MessageHandlerFactory messageHandlerFactory;

    private CarDeviceConfig mockCarDeviceConfig;
    private CarDeviceConfigExtend mockCarDeviceConfigExtend;
    private CarConfig mockCarConfig;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        
        // Mock CarDeviceConfig data
        mockCarDeviceConfig = CarDeviceConfig.builder()
                .id(1L)
                .vin("LMTZSV02XNC023061")
                .deviceTypeId(100L)
                .name("test_config")
                .addTime(new Date())
                .updateTime(new Date())
                .createUser("test_user")
                .updateUser("test_user")
                .taskId(1L)
                .configId(1L)
                .configIdLast(0L)
                .status((short) 0)
                .fileType("json")
                .config("{\"key\":\"value\"}")
                .taskHistoryId("1")
                .configIdHistory("1")
                .build();

        // Mock CarDeviceConfigExtend data
        mockCarDeviceConfigExtend = CarDeviceConfigExtend.builder()
                .id(1L)
                .configId(1L)
                .errorStatus("success")
                .addTime(new Date())
                .updateTime(new Date())
                .build();

        // Mock CarConfig data
        mockCarConfig = new CarConfig();
        mockCarConfig.setId(1L);
        mockCarConfig.setConfigVersion(1L);
        mockCarConfig.setConfig("{\"key\":\"value\"}");

        // Mock database operations
        when(carDeviceConfigMapper.selectByExample(any(CarDeviceConfigExample.class)))
                .thenReturn(Collections.singletonList(mockCarDeviceConfig));
        when(carConfigMapper.selectByPrimaryKey(anyLong()))
                .thenReturn(mockCarConfig);
        when(carDeviceConfigMapper.updateByPrimaryKeySelective(any(CarDeviceConfig.class)))
                .thenReturn(1);
        when(carDeviceConfigExtendMapper.insert(any(CarDeviceConfigExtend.class)))
                .thenReturn(1);
    }

    @Test
    public void testHandleMessage() throws Exception {
        // Given
        String messageBody = "{\"messageId\":\"AAJNChncQQYftBwWZIAE\",\"productKey\":\"walle\",\"deviceKey\":\"LMTZSV02XNC023061\",\"topic\":\"vehicle-config-manage-release-rsp\",\"message\":\"EhFMTVRaU1YwMlhOQzAyMzA2MSIHVURTX1ZDVSoBMjIjCLf+/////////wESFnZlaGljbGVfc3RhdHVzX25vbWF0Y2g=\"}";
        AukUpCountent message = JSONObject.parseObject(messageBody, AukUpCountent.class);
        
        // When
        MessageHandlerStrategy strategy = messageHandlerFactory.getStrategy(message.getTopic());
        strategy.handle(message);
        
        // Then
        // Verify that the message was processed correctly
//        verify(carDeviceConfigMapper, times(1)).selectByExample(any(CarDeviceConfigExample.class)); // 下发成功的才会查，失败就不查。
        verify(carDeviceConfigMapper, times(1)).selectByExample(any(CarDeviceConfigExample.class)); //
//        verify(carDeviceConfigMapper, times(1)).updateByPrimaryKeySelective(any(CarDeviceConfig.class));
    }


} 