package com.sankuai.walle.rmanage.config.service;

import com.google.common.collect.Lists;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.rmanage.config.BaseTest;
import com.sankuai.walle.rmanage.config.dto.config.AutoBatchPreConfigResponseDto;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class AutoConfigServiceTest extends BaseTest {

    @Autowired
    CarObjectsMapper carObjectsMapper;

    @Resource
    AutoConfigService autoConfigService;

    @Test
    public void testQueryVin() {
        CarObjectsExample carObjectsExample = new CarObjectsExample();
        carObjectsExample.createCriteria().andVinIn(Lists.newArrayList("LMTZSV016MC000107", "LMTZSV014MC000106"));
        List<CarObjects> carObjects = carObjectsMapper.selectByExample(carObjectsExample);
        System.out.println(carObjects);
    }

    @Test
    public void handleExcelConfigTest() {
        List<SendExcelDeviceConfigReq> reqList = new ArrayList<>();
        reqList.add(new SendExcelDeviceConfigReq() {{
            setConfigName("ldsk1");
            setVin("LMTZSV019MC000105");
            setUser("zhuojialin");
            setDeviceName("device");
            setFileType("yaml");
            setContent("222");
        }});

        reqList.add(new SendExcelDeviceConfigReq() {{
            setConfigName("ldsk2");
            setVin("LA22");
            setUser("zhuojialin02");
            setDeviceName("device");
            setFileType("yaml");
            setContent("cnouswcbuocbosubaocbocnac");
        }});

        AutoBatchPreConfigResponseDto autoBatchPreConfigResponseDto = autoConfigService.handleExcelConfig(reqList);
        System.out.println(autoBatchPreConfigResponseDto);


    }

    @Test
    public void queryEveConfigTest() {
        ArrayList<String> vins = Lists.newArrayList("LMTZSV023NC092853");
        List<CarDeviceConfigRes> ipmi = autoConfigService.queryEveConfig(vins, "IPMI");
        System.out.println(ipmi);
    }

    @Test
    public void sendEveToCarTest() {
        autoConfigService.sendEveConfig(Lists.newArrayList("vin-test"), "CAR_INFO", "zhaojianfeng05");
        autoConfigService.sendEveConfig(Lists.newArrayList("vin-test", "LMTZSV027MC008046"), "CAR_INFO", "zhaojianfeng05");
    }

    @Test
    public void uploadS3Test() throws IOException {
        autoConfigService.uploadS3(Lists.newArrayList("LMTZSV014MC000106"));
    }



}
