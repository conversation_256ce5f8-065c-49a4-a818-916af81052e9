package com.sankuai.walle.rmanage.config.script;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.gson.Gson;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.example.CarExecWordExample;
import com.sankuai.walle.carManage.mapper.CarExecWordMapper;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import com.sankuai.walle.objects.execDo.OperationExecWord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@SpringBootTest
@RunWith(SpringRunner.class)
public class InsertDataFromExcel {

    @Resource
    CarExecWordMapper carExecWordMapper;

    @Test
    public void excel() {
        //欲导出excel的数据结果集
        List<AssetExecWord> excel = new ArrayList<>();
        //省略 向结果集里插入数据的操作

        //UUID生成唯一name
        String name = UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
        //实现excel写的操作

        //1 设置写入文件夹地址和excel文件名称
        String filename = "路径" + name;

        try {
            // 2 调用easyexcel里面的方法实现写操作
            // write方法两个参数：第一个参数文件路径名称，第二个参数实体类class
            EasyExcel.write(filename, AssetExecWord.class).sheet("1").doWrite(excel);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            new File(filename).delete();
        }
    }

//AssetExecWord
    @Test
    public void assetData() {
        //要读取的文件名字
//        String fileName = "AssetExecWord.xlsx";
        String fileName = "大车4-0315.xlsx";

        //使用EasyExcel表格方法读取数据。
        //read方法里三个参数 文件名 模板类 一个解析监听
        //注意：监听里面的方法是需要我们重写的。
        EasyExcel.read(fileName, AssetExecWord.class, new AnalysisEventListener<AssetExecWord>() {
            int num=0;

            //解析一行运行一次此方法。
            @Override
            public void invoke(AssetExecWord assetExecWord, AnalysisContext analysisContext) {
//                num++;
//                Gson gson=new Gson();
//                System.out.println(assetExecWord);
//                String vin = assetExecWord.getVin();
//                CarExecWordExample example = new CarExecWordExample();
//                example.createCriteria().andVinEqualTo(vin);
//                CarExecWord carExecWord = new CarExecWord();
//                carExecWord.setVin(assetExecWord.getVin());
//                carExecWord.setCarAssetsExecWords(gson.toJson(assetExecWord));
//                if(carExecWordMapper.selectByExample(example).size()>0) {
//                    carExecWordMapper.updateByExampleSelective(carExecWord, example);
//                } else {
//                    carExecWordMapper.insert(carExecWord);
//                }
            }

            //解析所有数据完成，运行此方法。
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println("读取完成"+"共 "+num+" 条数据");


            }
        }).sheet().doRead();
    }
//    OperationExecWord
    @Test
    public void operationData() {
        //要读取的文件名字
        String fileName = "OperationExecWord2.xlsx";
        fileName = "大车6operationExec.xlsx";

        //使用EasyExcel表格方法读取数据。
        //read方法里三个参数 文件名 模板类 一个解析监听
        //注意：监听里面的方法是需要我们重写的。
        EasyExcel.read(fileName, OperationExecWord.class, new AnalysisEventListener<OperationExecWord>() {
            int num=0;

            //解析一行运行一次此方法。
            @Override
            public void invoke(OperationExecWord operationExecWord, AnalysisContext analysisContext) {
//                num++;
//                String vin = operationExecWord.getVin();
//                System.out.println(operationExecWord);
//                Gson gson=new Gson();
//                System.out.println(gson.toJson(operationExecWord));
//                CarExecWord carExecWord = new CarExecWord();
//                carExecWord.setVin(vin);
//                carExecWord.setCarAssetsExecWords(gson.toJson(operationExecWord));
//                CarExecWordExample example = new CarExecWordExample();
//                example.createCriteria().andVinEqualTo(vin);
//
//                List<CarExecWord> data = carExecWordMapper.selectByExample(example);
//                if(data.size()>0){
//                    carExecWordMapper.updateByExampleSelective(carExecWord, example);
//                } else {
//                    carExecWordMapper.insert(carExecWord);
//                }
            }

            //解析所有数据完成，运行此方法。
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println("读取完成"+"共 "+num+" 条数据");
            }
        }).sheet().doRead();
    }
}
