package com.sankuai.walle.rmanage.config.mafka;

import com.sankuai.walle.rmanage.config.service.impl.SecretKeyServiceImpl;
import com.sankuai.walle.rmanage.config.util.StringUtil;
import org.junit.Test;

import java.util.Base64;

public class WithOutSpringTest {

    @Test
    public void decodeMafka() {
        Base64.Decoder decoder = Base64.getDecoder();
        String msg = "eyJrZXkiOiJ2ZWhpY2xlX2NtZGIiLCJ1cGRhdGVfdGltZSI6MTczMzkwMDU4MTI1MiwidXNlX3N0YXR1cyI6IuWcqOW6kyIsInZpbiI6IkxBNzFBVUIxM1IwNTE1MjgxIn0=";
        System.out.println(decoder.decode(msg));
    }

    @Test
    public void generate64() {
        String s = StringUtil.generateRandomString(12);
        System.out.println(s.length());
        System.out.println(s);
    }


}
