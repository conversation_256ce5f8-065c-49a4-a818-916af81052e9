package com.sankuai.walle.rmanage.config.Listener;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.rmanage.config.constant.ConfigEnumConstant;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MafkaDeliveryTagCustomerTest {

    @InjectMocks
    private MafkaDeliveryTagCustomer mafkaDeliveryTagCustomer;

    @Mock
    private ConfigService configService;

    @Mock
    private ConfigEnumConstant configEnumConstant;

    private static final String DELIVERY_TAG_NAME = "DELIVERY_TAG";

    @Before
    public void setUp() {
        Mockito.when(configEnumConstant.contains(DELIVERY_TAG_NAME)).thenReturn(true);
    }

    /**
     * 场景1：configEnumConstant.contains(DELIVERY_TAG_NAME) 为 false，抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testReceive_InvalidConfigName_ThrowsException() {
        // arrange
        Mockito.when(configEnumConstant.contains(DELIVERY_TAG_NAME)).thenReturn(false);
        String msgBody = "{\"ConfigName\":\"INVALID_CONFIG\"}";

        // act
        mafkaDeliveryTagCustomer.receive(msgBody);

        // assert
        // 异常抛出即为测试通过
    }

    /**
     * 场景2：configName 不为空，按配置项下发
     */
    @Test
    public void testReceive_ConfigNameNotBlank_SendConfig() {
        // arrange
        String msgBody = "{\"ConfigName\":\"VRESV_RECORD_HISTORY\",\"data\":\"data\",\"vin\":\"LMTZSV020MC036691\"}";

        // act
        ConsumeStatus status = mafkaDeliveryTagCustomer.receive(msgBody);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
        verify(configService, times(1)).sendConfigToCar(anyList());
    }

    /**
     * 场景3：configName 为空，vins 或 tags 为空，记录错误日志并返回 CONSUME_SUCCESS
     */
    @Test
    public void testReceive_ConfigNameBlank_VinsOrTagsNull_ReturnSuccess() {
        // arrange
        String msgBody = "{\"vins\":[],\"tags\":\"\"}";

        // act
        ConsumeStatus status = mafkaDeliveryTagCustomer.receive(msgBody);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
        verify(configService, never()).sendConfigToCar(anyList());
    }

    /**
     * 场景4：configName 为空，vins 和 tags 有效，按标签下发
     */
    @Test
    public void testReceive_ConfigNameBlank_VinsAndTagsValid_SendConfig() {
        // arrange
        String msgBody = "{\"vins\":[\"VIN1\",\"VIN2\"],\"tags\":\"validTags\"}";

        // act
        ConsumeStatus status = mafkaDeliveryTagCustomer.receive(msgBody);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
        verify(configService, times(1)).sendConfigToCar(anyList());
    }

    /**
     * 场景5：调用 configService.sendConfigToCar(config) 成功
     */
    @Test
    public void testReceive_SendConfigToCar_Success() {
        // arrange
        String msgBody = "{\"ConfigName\":\"VRESV_RECORD_HISTORY\",\"data\":\"data\",\"vin\":\"LMTZSV020MC036691\"}";

        // act
        ConsumeStatus status = mafkaDeliveryTagCustomer.receive(msgBody);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
        verify(configService, times(1)).sendConfigToCar(anyList());
    }
}