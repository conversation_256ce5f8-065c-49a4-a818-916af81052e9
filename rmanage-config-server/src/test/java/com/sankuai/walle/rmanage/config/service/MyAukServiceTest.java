package com.sankuai.walle.rmanage.config.service;

import com.google.common.collect.Lists;
import com.sankuai.auk.open.api.contracts.service.DeviceEnrichment;
import com.sankuai.banma.auk.server.sdk.response.DeviceCreateResponse;
import com.sankuai.walle.rmanage.config.BaseTest;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class MyAukServiceTest extends BaseTest {
    @Resource
    MyAukService aukService ;

    @Test
    public void createAukNumTest() {
        DeviceCreateResponse aukDevice = aukService.createAukDevice("000");
        System.out.println(aukDevice);
    }

    @Test
    public void queryDeviceListWithSliceTest() {
        ArrayList<String> vins = Lists.newArrayList(
                "LA22",
                "LMTZSV014MC000106",
                "LMTZSV010MC000104",
                "LMTZSV019MC000103",
                "LMTZSV017MC000102",
                "LMTZSV015MC000101",
                "LMTZSV013MC000100",
                "LMTZSV010MC000099",
                "LMTZSV019MC000098",
                "LMTZSV017MC000097",
                "LMTZSV015MC000096",
                "LMTZSV013MC000095",
                "LMTZSV021MC044122",
                "LMTZSV024MC088826",
                "LMTZSV022MC023327",
                "LMTZSV02XMC045785",
                "LMTZSV023MC044638",
                "LMTZSV025MC032958",
                "LMTZSV025MC027307",
                "LMTZSV021MC066010",
                "LMTZSV024MC062730",
                "LMTZSV021MC099623",
                "LMTZSV021MC001593",
                "LMTZSV025MC075003",
                "LMTZSV022MC041682",
                "LMTZSV024MC041134",
                "LMTZSV025MC087216",
                "LMTZSV022MC077680",
                "LMTZSV020MC029353",
                "LMTZSV029NC070498",
                "LMTZSV021NC068079",
                "LMTZSV025NC039586",
                "LMTZSV021NC097985",
                "LMTZSV022NC044714",
                "LMTZSV021NC060435",
                "LMTZSV023NC044687",
                "LMTZSV024NC023444",
                "LMTZSV025NC046585",
                "LMTZSV024NC098449",
                "LMTZSV027NC048922",
                "LMTZSV027NC004743",
                "LMTZSV027NC068538",
                "LMTZSV027NC076736",
                "LMTZSV025NC019838",
                "LMTZSV024NC050255",
                "LMTZSV021NC098070",
                "LMTZSV029NC072669",
                "LMTZSV020NC049460",
                "LMTZSV028NC085767",
                "LMTZSV023NC023760",
                "LMTZSV021NC096089",
                "LMTZSV023NC065524",
                "LMTZSV028NC050243",
                "LMTZSV022NC039643",
                "LMTZSV027NC099966",
                "LMTZSV024NC092764",
                "LMTZSV022NC088924",
                "LMTZSV021NC078210",
                "LMTZSV020NC097198",
                "LMTZSV02XNC022489",
                "LMTZSV025NC010556",
                "LMTZSV026NC051200",
                "LMTZSV020NC041486",
                "LMTZSV027NC048984",
                "LMTZSV028NC008087",
                "LMTZSV023NC039019",
                "LMTZSV020NC037292",
                "LMTZSV021NC040671",
                "LMTZSV025NC051270",
                "LMTZSV023NC071260",
                "LMTZSV020NC073791",
                "LMTZSV027NC078356",
                "LMTZSV020NC036384",
                "LMTZSV021NC056238",
                "LMTZSV021NC071001",
                "LMTZSV028NC081511",
                "LMTZSV02XNC018782",
                "LMTZSV023NC017229",
                "LMTZSV021NC091894",
                "LMTZSV024NC025047",
                "LMTZSV029NC013606",
                "LMTZSV020NC032917",
                "LMTZSV022NC032322",
                "LMTZSV020NC075248",
                "LMTZSV023NC070951",
                "LMTZSV025NC022254",
                "LMTZSV028NC059511",
                "LMTZSV029NC083042",
                "LMTZSV028NC042644",
                "LMTZSV025NC050877",
                "LMTZSV021NC040735",
                "LMTZSV025NC000660",
                "LMTZSV020NC094088",
                "LMTZSV025NC014266",
                "LMTZSV020NC001408",
                "LMTZSV022NC062940",
                "LMTZSV029NC061994",
                "LMTZSV024NC099455",
                "LMTZSV024NC073390",
                "LMTZSV020NC072284");
        List<DeviceEnrichment> deviceEnrichments = aukService.queryDeviceListWithSlice(vins, 20);
        System.out.println(deviceEnrichments);
        System.out.println(deviceEnrichments.size());
    }
}
