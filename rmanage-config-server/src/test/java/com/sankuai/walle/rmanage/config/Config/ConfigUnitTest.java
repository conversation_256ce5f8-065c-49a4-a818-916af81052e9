package com.sankuai.walle.rmanage.config.Config;

import com.sankuai.walle.rmanage.config.FieldTest;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.service.infrastructureService.LexicalAnalyzerService;
import com.sankuai.walle.rmanage.config.util.ObjectUtil;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ConfigUnitTest {

    @Test
    public void TestObjectFun() throws Exception {
        FieldTest obj = new FieldTest();
        obj.setConfigIdHistory("123");
        System.out.println(obj.getConfigIdHistory());
        Long value = 111L;
        String name = "configIdHistory";
        ObjectUtil.ObjectFun<FieldTest> objfun = new ObjectUtil.ObjectFun<FieldTest>();
        objfun.safeSetValueFun(obj, name, value);
        System.out.println(obj.getConfigIdHistory());
    }

    @Test
    public void TestObjectNullFun() throws Exception {
        FieldTest obj = new FieldTest();
        System.out.println(obj.getConfigIdHistory());
        Long value = 111L;
        String name = "configIdHistory";
        ObjectUtil.ObjectFun<FieldTest> objfun = new ObjectUtil.ObjectFun<FieldTest>();
        objfun.safeSetValueFun(obj, name, value);
        System.out.println(obj.getConfigIdHistory());
    }

    // 测试主动获取配置数据
    @Test
    public void TestGetConfigsByVin() throws Exception {
//        BeanFactory beanFactory = new ClassPathXmlApplicationContext("client.xml");
//        CarThriftService.Iface client = (CarThriftService.Iface) beanFactory.getBean("clientProxy");
//
//        Thread.sleep(3000);
//        CarConfigAllResp result = null;
//        try {
////            CarModelRespesp result = client.getCarType();
//            result = client.getConfigsByVin("LMTZSV024NC005171");
//            System.out.println("\n" + result + "\n");
////            System.out.println("\n" + client.getTypeCars(0,1) + "\n");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
////        System.exit(0);
//        assert (result!=null);
//
//        if (result.getData().size()<1 || result.getData().get(0).getConfig()==null || Objects.equals(result.getData().get(0).getConfig(), "{}")) {
//            throw new Exception("config下发的配置数据为空"+result);
//        }
//
//        assert (result.data.size()>=1);
//        assert (result.data.get(0)!=null);
//        assert (!Objects.equals(result.data.get(0).getConfig(), "{}"));
    }

    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;

    // 测试 CarDeviceConfigService 配置类下发的数据
    @Test
    public void TestCarDeviceConfigService(){
        String vin = "LMTZSV027NC089504";
        CarDeviceConfigExample query = new CarDeviceConfigExample();
        query.createCriteria().andVinEqualTo(vin);
        List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
        assert configs.size()>0;
        CarDeviceConfig config = configs.get(0);

        CloudCgf.CloudToVehicleReleaseConfig.Builder target = carDeviceConfigService.handleTargetData(config);
        System.out.println(target);
        assert target!=null;
    }

    @Autowired
    LexicalAnalyzerService lexicalAnalyzerService;
    @Test
    public void TestRestSend() {
//        String str = "Hello}}}``${{${##name}` `${{{#name}`,you name is`${#name}`, your vin is `${#vin}`";
//        lexicalAnalyzerService.getConfigContent(str, "LK6CACE16NG600042");
//        lexicalAnalyzerService.getConfigContent(str, "3LN6L5SU9KR631259");
        String str = "{\n" +
                "\t\"vinfo\": {\n" +
                "\t  \"model\": \"`${#type}`\",\n" +
                "    \"phase\": \"`${#phase}`\",\n" +
                "    \"compute_name\": \"`${#compute_name}`\",\n" +
                "\t\t\"switch_name\": \"`${#switch_name}`\",\n" +
                "    \"car_name\": \"`${#name}`\",\n" +
                "    \"vin\":\"`${#vin}`\",\n" +
                "    \"type\":\"${size}\",\n" +
                "    \"type-model\": \"`${#vehicleType}`\", // 父子车型组成的列表[D01,D23,EP1]\n" +
                "\t\t\"equipments\": [\n" +
                "    \t{\n" +
                "      \t\"compute_name\": \"`${#compute_name}`\",\n" +
                "\t\t\t\t\"switch_name\": \"`${#switch_name}`\",\n" +
                "      }\n" +
                "    ],\n" +
                "\t}\n" +
                "}";
        str = "vin: \"`${#vin}`\" \n" +
                "name: \"`${#name}`\" \n" +
                "type: \"`${#size}`\" \n" +
                "vendor: \"`${#vendor}`\" \n" +
                "batch: \"`${#type}`|`${#phase}`\"\n";
        String data = lexicalAnalyzerService.getConfigContent(str, "h24-001");
        System.out.println(data);
    }
}
