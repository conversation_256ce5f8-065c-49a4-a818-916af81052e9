package com.sankuai.walle.rmanage.config.Config;

import com.sankuai.walle.rmanage.config.helper.ConfigTokenAnalyze;
import org.junit.Test;

import java.util.List;

public class TokenAnalyzeTest {

    @Test
    public void testAnalyze(){
        String str = "Hello}}}``${{${##name}` `${{{#name}``${#age}`, your age is `${#age}`";
        ConfigTokenAnalyze tokenAnalyze = new ConfigTokenAnalyze(str);
        List<ConfigTokenAnalyze.Token> tokens = tokenAnalyze.getTokens();
        System.out.println(tokens);
    }
}
