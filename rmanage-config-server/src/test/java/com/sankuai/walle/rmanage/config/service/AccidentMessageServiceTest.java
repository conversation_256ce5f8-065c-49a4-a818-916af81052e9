package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.rmanage.config.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

public class AccidentMessageServiceTest extends BaseTest {

    @Resource
    AccidentMessageService accidentMessageService;

    @Test
    public void getRoadTypeTest() throws Exception {
        Map<String,Object> params = new HashMap<>();
        params.put("location_gps", "116.54158597846497,40.09979333603567");
        params.put("record_name",  "20240130_100632_s20-313");
        Object result = accidentMessageService.getRoadType(params);
        System.out.println("result: "+ result);
    }

    @Test
    public void getLocationNameByGpsTest() throws Exception {
        String locationGps = "116.54158597846497,40.09979333603567";
        String locationName = accidentMessageService.getLocationNameByGps(locationGps);
        System.out.println("locationName: "+ locationName);
    }
}
