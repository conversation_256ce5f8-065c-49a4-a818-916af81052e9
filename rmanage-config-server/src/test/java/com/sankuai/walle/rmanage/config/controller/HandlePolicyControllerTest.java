package com.sankuai.walle.rmanage.config.controller;

/**
 * Created by IntelliJ IDEA
 * User: lijun131
 * Date: 2023-11-13
 */

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.sankuai.walle.objects.vo.request.BizHandlePolicyInfoReq;
import com.sankuai.walle.objects.vo.request.MisGroupReq;
import com.sankuai.walle.rmanage.config.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@AutoConfigureMockMvc
@Ignore
public class HandlePolicyControllerTest extends BaseTest {

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected HandlePolicyController handlePolicyController;

    @Before
    public void before() {
        mockMvc = MockMvcBuilders.standaloneSetup(handlePolicyController).build();
    }

    @Test
    public void addHandlePolicyTest() throws Exception {
        BizHandlePolicyInfoReq handlePolicyInfoReq = new BizHandlePolicyInfoReq();
        handlePolicyInfoReq.setBizTableName("biz_accident_info");
        handlePolicyInfoReq.setBizTableDesc("事故信息表");
        handlePolicyInfoReq.setPolicyName("事故处置默认策略3");
        handlePolicyInfoReq.setPolicyDesc("事故处置默认策略3");
        JSONObject policyInfo = new JSONObject();
        policyInfo.put("现场是否有人拍照", "1");
        policyInfo.put("人伤", "0");
        handlePolicyInfoReq.setPolicyValue(policyInfo.toJSONString());
        handlePolicyInfoReq.setCreateUser("lijun131");
        handlePolicyInfoReq.setIsEnable(true);
        JSONObject handleMethod = new JSONObject();
        List<String> callList = new ArrayList<>();
        callList.add("zhouwen");
        callList.add("huxiaoxiao");
        handleMethod.put("call", "测试1");
        handleMethod.put("create_group", "测试1");
        handlePolicyInfoReq.setHandleMethod(handleMethod.toJSONString());

        ObjectMapper mapper = new ObjectMapper();
        String policyInfoStr = mapper.writeValueAsString(handlePolicyInfoReq);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post("/api/cmdb/addHandlePolicy")
                .content(policyInfoStr)
                .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void updateHandlePolicyTest() throws Exception {
        BizHandlePolicyInfoReq handlePolicyInfoReq = new BizHandlePolicyInfoReq();
        handlePolicyInfoReq.setId(57L);
        handlePolicyInfoReq.setIsEnable(true);
        handlePolicyInfoReq.setUpdateUser("zhaoduancai");

        ObjectMapper mapper = new ObjectMapper();
        String policyInfoStr = mapper.writeValueAsString(handlePolicyInfoReq);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.put("/api/cmdb/updateHandlePolicy")
                .content(policyInfoStr)
                .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void queryAccidentAttributesTest() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/api/cmdb/queryAccidentAttributes")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void queryHandleMethodAliasTest() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/api/cmdb/queryHandleMethodAlias")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void queryHandlePolicyTest() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/api/cmdb/queryHandlePolicy")
                .param("page", "1")
                .param("pageSize", "10")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public  void queryEmpInfosTest() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/api/cmdb/queryEmpInfos")
                .param("misId", "lijun")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public  void addMisGroupTest() throws Exception {
        MisGroupReq misGroupReq = new MisGroupReq();
        misGroupReq.setGroupName("默认事故处置群");
        misGroupReq.setGroupDesc("默认事故处置群");
        misGroupReq.setCreateUser("lijun131");
        misGroupReq.setUpdateUser("lijun131");
        List<String> members = Arrays.asList("lijun131", "zhouwen", "huxiaoxiao", "zhaoduancai");
        JsonArray jsonArray = new JsonArray();
        jsonArray.add("lijun131");
        jsonArray.add("zhaoduancai");
        misGroupReq.setGroupMembers(jsonArray.toString());
        ObjectMapper mapper = new ObjectMapper();
        String misGroupReqStr = mapper.writeValueAsString(misGroupReq);
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post("/api/cmdb/addMisGroup")
                .content(misGroupReqStr).contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void updateMisGroupTest() throws Exception {
        MisGroupReq misGroupReq = new MisGroupReq();
        misGroupReq.setId(4L);
        misGroupReq.setIsDeleted(true);
        ObjectMapper mapper = new ObjectMapper();
        String misGroupReqStr = mapper.writeValueAsString(misGroupReq);
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.put("/api/cmdb/updateMisGroup")
                .content(misGroupReqStr).contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void updateMisGroup2Test() throws Exception {
        MisGroupReq misGroupReq = new MisGroupReq();
        misGroupReq.setId(32L);
        misGroupReq.setGroupMembers("[\"pengruoyu/彭若愚\"]");
        ObjectMapper mapper = new ObjectMapper();
        String misGroupReqStr = mapper.writeValueAsString(misGroupReq);
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.put("/api/cmdb/updateMisGroup")
                        .content(misGroupReqStr).contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    public void queryMisGroupTest() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/api/cmdb/queryMisGroup")
                .param("page", "1")
                .param("pageSize", "10")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }
}
