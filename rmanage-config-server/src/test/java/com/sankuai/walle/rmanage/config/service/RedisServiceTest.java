package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.rmanage.config.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Ignore
public class RedisServiceTest extends BaseTest {

    @Resource
    RedisService redisService;
    @Test
    public void pushGroupIdInRedisTest() {
        Map<String,Object> param = new HashMap<>();
        param.put("id","2115");
        param.put("groupId", 64011821176L);
        try{
            redisService.pushGroupIdInRedis("accident_id_group_id_reflect", param);
        }
        catch (Exception e){
            log.error("writeGroupIdReflectToRedis is failed, param is {}",param,e);
        }
    }

    @Test
    public void getGroupIdInRedisTest() {
        try{
            redisService.getGroupIdInRedis("accident_id_group_id_reflect", 2115);
        }
        catch (Exception e){
            log.error("writeGroupIdReflectToRedis is failed",e);
        }
    }

}
