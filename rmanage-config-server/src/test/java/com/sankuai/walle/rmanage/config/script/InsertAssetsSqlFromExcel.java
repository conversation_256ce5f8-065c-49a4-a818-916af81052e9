package com.sankuai.walle.rmanage.config.script;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.carManage.example.CarAssetsExample;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarOperationExample;
import com.sankuai.walle.carManage.mapper.CarAssetsMapper;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.carManage.mapper.CarOperationMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class InsertAssetsSqlFromExcel {

    @Resource
    CarAssetsMapper carAssetsMapper;

    @Test
    public void insertAsset(){
        String fileName = "大车2asset.xlsx";
        EasyExcel.read(fileName, CarAssets.class, new AnalysisEventListener<CarAssets>() {
            int num=0;
            @Override
            public void invoke(CarAssets o, AnalysisContext analysisContext) {
//                CarAssetsExample example = new CarAssetsExample();
//                example.createCriteria().andVinEqualTo(o.getVin());
//                List<CarAssets> assets = carAssetsMapper.selectByExample(example);
//                if (assets.size()==0) {
//                    carAssetsMapper.insert(o);
//                    System.out.println(o);
//                    num++;
//                }

            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println("读取完成"+"共 "+num+" 条数据");
            }
        }).sheet().doRead();
    }

    @Resource
    CarOperationMapper carOperationMapper;

    @Test
    public void insertOperation(){
        String fileName = "operation2.xlsx";
        fileName = "大车1operation.xlsx";
        EasyExcel.read(fileName, CarOperation.class, new AnalysisEventListener<CarOperation>() {
            int num=0;
            @Override
            public void invoke(CarOperation o, AnalysisContext analysisContext) {
//                CarOperationExample example = new CarOperationExample();
//                example.createCriteria().andVinEqualTo(o.getVin());
//                List<CarOperation> objs = carOperationMapper.selectByExample(example);
//                if (objs.size()==0) {
//                    carOperationMapper.insert(o);
//                    System.out.println(o);
//                    num++;
//                }

            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println("读取完成"+"共 "+num+" 条数据");
            }
        }).sheet().doRead();
    }

    @Resource
    CarObjectsMapper carObjectsMapper;

    @Test
    public void insertObjects(){
        String fileName = "objects.xlsx";
        fileName = "大车3车辆型号object.xlsx";
        EasyExcel.read(fileName, CarObjects.class, new AnalysisEventListener<CarObjects>() {
            int num=0;
            int up_num=0;
            @Override
            public void invoke(CarObjects o, AnalysisContext analysisContext) {
//                System.out.println(o);
//                CarObjectsExample example = new CarObjectsExample();
//                example.createCriteria().andVinEqualTo(o.getVin());
//                List<CarObjects> objs = carObjectsMapper.selectByExample(example);
//                if (objs.size()==0) {
//                    carObjectsMapper.insert(o);
//                    num++;
//                }else{
//                    carObjectsMapper.updateByExampleSelective(o, example);
//                    up_num++;
//                }

            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println("读取完成"+"共 "+num+" 条数据");
                System.out.println("update完成"+"共 "+up_num+" 条数据");
            }
        }).sheet().doRead();
    }
}
