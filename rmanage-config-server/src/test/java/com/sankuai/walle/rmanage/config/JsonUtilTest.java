package com.sankuai.walle.rmanage.config;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.sankuai.walle.rmanage.config.util.JsonUtils;
import org.hamcrest.CoreMatchers;
import org.junit.Assert;
import org.junit.Test;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.transform.Result;
import java.util.HashMap;

/**
 * <AUTHOR> Created on 2022/3/10
 */
public class JsonUtilTest {

    @Test
    public void testJSONBytes() throws JAXBException {
        HashMap<String,Object> map = new HashMap<>();
        map.put("1","222");
        String res = JSONObject.toJSONString(map);
        System.out.println(res);
        byte[] resb = JSONObject.toJSONBytes(res);
        System.out.println(resb);
    }

    @Test
    public void testIsJsonObject() {
        boolean bool = JsonUtils.isJsonObject(null);
        Assert.assertThat(bool, CoreMatchers.equalTo(false));

        bool = JsonUtils.isJsonObject("");
        Assert.assertThat(bool, CoreMatchers.equalTo(false));

        bool = JsonUtils.isJsonObject("{}");
        Assert.assertThat(bool, CoreMatchers.equalTo(true));

        bool = JsonUtils.isJsonObject("{\"l1\":[{\"a\":2}]}");
        Assert.assertThat(bool, CoreMatchers.equalTo(true));

        bool = JsonUtils.isJsonObject("[]");
        Assert.assertThat(bool, CoreMatchers.equalTo(false));
    }

    @Test
    public void testMergeJsonObjectWhenMergeLevel1() {
        JsonObject jo1 = new JsonObject();
        jo1.addProperty("k1", "v1");

        JsonObject jo2 = new JsonObject();
        jo2.addProperty("k2", "v2");

        JsonObject mergeResult = JsonUtils.mergeJsonObjectRecursively(jo1, jo2);
        Assert.assertThat(mergeResult.get("k1").getAsString(), CoreMatchers.equalTo("v1"));
        Assert.assertThat(mergeResult.get("k2").getAsString(), CoreMatchers.equalTo("v2"));
    }

    @Test
    public void testMergeJsonObjectWhenMergeLevel1AndHavingSameKey() {
        JsonObject jo1 = new JsonObject();
        jo1.addProperty("k1", "v1");
        jo1.addProperty("k2", "v1");

        JsonObject jo2 = new JsonObject();
        jo2.addProperty("k2", "v2");
        jo2.addProperty("k3", "v2");

        JsonObject mergeResult = JsonUtils.mergeJsonObjectRecursively(jo1, jo2);
        Assert.assertThat(mergeResult.get("k1").getAsString(), CoreMatchers.equalTo("v1"));
        Assert.assertThat(mergeResult.get("k2").getAsString(), CoreMatchers.equalTo("v1"));
        Assert.assertThat(mergeResult.get("k3").getAsString(), CoreMatchers.equalTo("v2"));
    }

    /**
     * 递归合并第二层jo
     */
    @Test
    public void testMergeJsonObjectWhenMergeLevel2JsonObject() {
        JsonObject o1level2JsonObject = new JsonObject();
        o1level2JsonObject.addProperty("id", 100);
        o1level2JsonObject.addProperty("name", "o1name");

        JsonObject jo1 = new JsonObject();
        jo1.addProperty("k1", "v1");
        jo1.add("k2", o1level2JsonObject);

        JsonObject o2level2JsonObject = new JsonObject();
        o2level2JsonObject.addProperty("id", 101);
        o2level2JsonObject.addProperty("desc", "ddd");

        JsonObject jo2 = new JsonObject();
        jo2.addProperty("k1", "v2");
        jo2.add("k2", o2level2JsonObject);

        JsonObject mergeResult = JsonUtils.mergeJsonObjectRecursively(jo1, jo2);
        Assert.assertThat(mergeResult.get("k1").getAsString(), CoreMatchers.equalTo("v1"));
        JsonObject k2 = mergeResult.get("k2").getAsJsonObject();
        Assert.assertThat(k2.get("id").getAsLong(), CoreMatchers.equalTo(100L));
        Assert.assertThat(k2.get("name").getAsString(), CoreMatchers.equalTo("o1name"));
        Assert.assertThat(k2.get("desc").getAsString(), CoreMatchers.equalTo("ddd"));
    }
}
