package com.sankuai.walle.rmanage.config.thrift;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.banma.auk.server.sdk.response.DeviceSecretResponse;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.cmdb.thrift.model.NormalResp;
import com.sankuai.walle.common.Status;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试 CarConfigServiceImpl 的 getVechildSecret 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class CarConfigServiceImplTest {

    @InjectMocks
    private CarConfigServiceImpl carConfigService;

    @Mock
    private CarObjectsMapper carObjectsMapper;

    @Mock
    private MyAukService myAukService;

    @Before
    public void setUp() {
        // 初始化 Mock 对象
    }

    /**
     * 测试 VIN 不存在的场景
     */
    @Test
    public void testGetVechildSecretVinNotExists() throws Throwable {
        // arrange
        String vin = "INVALID_VIN";
        when(carObjectsMapper.selectByExample(any(CarObjectsExample.class)))
                .thenReturn(Collections.emptyList());

        // act
        NormalResp response = carConfigService.getVechildSecret(vin);

        // assert
        assertNotNull(response);
        assertNotNull(response.status);
        assertEquals(CommonConstants.ERROR_CODE, response.status.code);
        assertNull(response.content);
    }

    /**
     * 测试 VIN 存在且查询成功的场景
     */
    @Test
    public void testGetVechildSecretVinExistsQuerySuccess() throws Throwable {
        // arrange
        String vin = "VALID_VIN";
        DeviceSecretResponse secretResponse = new DeviceSecretResponse();
        secretResponse.setData(new com.sankuai.auk.open.api.contracts.service.DeviceSecret("productId", "productKey", "deviceId", "deviceKey", "deviceSecret"));

        when(carObjectsMapper.selectByExample(any(CarObjectsExample.class)))
                .thenReturn(Collections.singletonList(new CarObjects()));
        when(myAukService.queryDeviceSecret(vin)).thenReturn(secretResponse);

        // act
        NormalResp response = carConfigService.getVechildSecret(vin);

        // assert
        assertNotNull(response);
        assertNotNull(response.status);
        assertEquals(CommonConstants.SUCCEED_CODE, response.status.code);
        assertEquals(JSONObject.toJSONString(secretResponse), response.content);
    }

    /**
     * 测试 VIN 存在但查询失败的场景
     */
    @Test
    public void testGetVechildSecretVinExistsQueryFailure() throws Throwable {
        // arrange
        String vin = "VALID_VIN";
        when(carObjectsMapper.selectByExample(any(CarObjectsExample.class)))
                .thenReturn(Collections.singletonList(new CarObjects()));
        when(myAukService.queryDeviceSecret(vin)).thenThrow(new RuntimeException("Query failed"));

        // act
        NormalResp response = carConfigService.getVechildSecret(vin);

        // assert
        assertNotNull(response);
        assertNotNull(response.status);
        assertEquals(CommonConstants.ERROR_CODE, response.status.code);
        assertNull(response.content);
    }
}