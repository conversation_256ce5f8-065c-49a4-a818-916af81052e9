package com.sankuai.walle.rmanage.config.service.appService;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.service.impl.CarDeviceConfigServiceImpl;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * CarDeviceConfigService 单元测试
 * 测试 retryConfigToMqtt 方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CarDeviceConfigServiceTest {

    @Mock
    private MyAukService myAukService;

    @Mock
    private CarConfigMapper carConfigMapper;

    @InjectMocks
    private CarDeviceConfigServiceImpl carDeviceConfigService;

    private CarDeviceConfig testConfig1; // VRESV_RECORD_HISTORY 配置
    private CarDeviceConfig testConfig2; // SSH_CONFIG 配置
    private CarDeviceConfig testConfig3; // IPMI 配置

    @BeforeEach
    void setUp() {
        // 构建测试数据1：VRESV_RECORD_HISTORY 配置（Excel配置，无版本管理）
        testConfig1 = new CarDeviceConfig();
        testConfig1.setId(102910763L);
        testConfig1.setVin("LMTZSV000MC000001");
        testConfig1.setName("VRESV_RECORD_HISTORY");
        testConfig1.setConfig("[{\"approved\":1,\"deleted\":0,\"endTime\":1751626620000,\"startTime\":1751626500000,\"tag\":{\"nearByTestMode\":\"robot\",\"nearbyRescueVHR\":\"gt1\"},\"usedType\":49,\"usedTypeName\":\"实车静态测试\",\"vin\":\"LMTZSV000MC000001\",\"vresvId\":\"road_testing_1940726148199706666_20250704\"}]");
        testConfig1.setAddTime(new Date());
        testConfig1.setUpdateTime(new Date());
        testConfig1.setCreateUser("cmsSystem");
        testConfig1.setConfigId(ConfigConstant.EXCEL_CONFIG_ID); // -1，表示Excel配置
        testConfig1.setStatus((short) 0);
        testConfig1.setFileType("json");

        // 构建测试数据2：SSH_CONFIG 配置（有版本管理的配置）
        testConfig2 = new CarDeviceConfig();
        testConfig2.setId(102910736L);
        testConfig2.setVin("LA71AUB15S0512243");
        testConfig2.setName("SSH_CONFIG");
        testConfig2.setConfig("config MT13X999\noption remotehost '*************'\noption remoteport '55601'");
        testConfig2.setAddTime(new Date());
        testConfig2.setUpdateTime(new Date());
        testConfig2.setCreateUser("zhaojianfeng05");
        testConfig2.setConfigId(351L); // 有版本管理的配置ID
        testConfig2.setConfigIdLast(351L);
        testConfig2.setStatus((short) 0);
        testConfig2.setFileType(null); // 从CarConfig表获取

        // 构建测试数据3：IPMI 配置（Excel配置，无版本管理）
        testConfig3 = new CarDeviceConfig();
        testConfig3.setId(8801L);
        testConfig3.setVin("LMTZSV025NC051270");
        testConfig3.setName("IPMI");
        testConfig3.setConfig("username: ADMIN\naddress: *************\npasswd: JVYONCDABX");
        testConfig3.setAddTime(new Date());
        testConfig3.setUpdateTime(new Date());
        testConfig3.setCreateUser("lianghongda");
        testConfig3.setConfigId(ConfigConstant.EXCEL_CONFIG_ID); // -1，表示Excel配置
        testConfig3.setStatus((short) 1);
        testConfig3.setFileType(null);

        // Mock CarConfig 数据
        CarConfig carConfig = new CarConfig();
        carConfig.setId(351L);
        carConfig.setName("CAR_INFO");
        carConfig.setConfig("{\n\t\"vinfo\": {\n\t\t\"model\": \"D23\",\n\t\t\"type\": \"EP2\",\n\t\t\"compute_name\": \"MADC2.0\",\n\t\t\"switch_name\": \"SLAB2.0\",\n        \"car_name\": \"`${#name}`\",\n      \t\"vin\":\"`${#vin}`\"\n\t}\n}");
        carConfig.setConfigVersion(2L);
        carConfig.setConfigName("D23-MADC-EP1");
        carConfig.setMasterId(336L);
        carConfig.setFileType("json");

        // 设置Mock行为
        when(carConfigMapper.selectByPrimaryKey(351L)).thenReturn(carConfig);
    }

    @Test
    void testRetryConfigToMqtt_VerifyFileNameExtensions() throws InvalidProtocolBufferException {
        // 验证下发的文件名是否包含正确的后缀
        // Given
        ArgumentCaptor<byte[]> configCaptor = ArgumentCaptor.forClass(byte[].class);
        doNothing().when(myAukService).sendConfigToMqtt(anyString(), configCaptor.capture(), any());

        // When & Then
        // 测试1：Excel配置，有fileType="json"
        carDeviceConfigService.retryConfigToMqtt(testConfig1);
        verify(myAukService, times(1)).sendConfigToMqtt(
            eq("LMTZSV000MC000001"),
            any(byte[].class),
            any()
        );

        // 验证第一个配置的文件名
        byte[] capturedConfig1 = configCaptor.getAllValues().get(0);
        CloudCgf.CloudToVehicleReleaseConfig config1 = CloudCgf.CloudToVehicleReleaseConfig.parseFrom(capturedConfig1);
        assert config1.getSFileName().equals("VRESV_RECORD_HISTORY.json") :
            "Expected fileName to be 'VRESV_RECORD_HISTORY.json', but got: " + config1.getSFileName();

        // 测试2：有版本管理的配置，fileType从CarConfig表获取（应该是"json"）
        carDeviceConfigService.retryConfigToMqtt(testConfig2);
        verify(myAukService, times(2)).sendConfigToMqtt(anyString(), any(byte[].class), any());

        // 验证第二个配置的文件名
        byte[] capturedConfig2 = configCaptor.getAllValues().get(1);
        CloudCgf.CloudToVehicleReleaseConfig config2 = CloudCgf.CloudToVehicleReleaseConfig.parseFrom(capturedConfig2);
        assert config2.getSFileName().equals("SSH_CONFIG.json") :
            "Expected fileName to be 'SSH_CONFIG.json', but got: " + config2.getSFileName();

        // 测试3：Excel配置，无fileType
        carDeviceConfigService.retryConfigToMqtt(testConfig3);
        verify(myAukService, times(3)).sendConfigToMqtt(anyString(), any(byte[].class), any());

        // 验证第三个配置的文件名（应该没有后缀）
        byte[] capturedConfig3 = configCaptor.getAllValues().get(2);
        CloudCgf.CloudToVehicleReleaseConfig config3 = CloudCgf.CloudToVehicleReleaseConfig.parseFrom(capturedConfig3);
        assert config3.getSFileName().equals("") :
            "Expected fileName to be '', but got: " + config3.getSFileName();

        // 验证CarConfig查询被正确调用
        verify(carConfigMapper, times(1)).selectByPrimaryKey(351L);
    }

    @Test
    void testRetryConfigToMqtt_VerifyConfigContent() {
        // 验证配置内容是否正确构建
        // Given
        doNothing().when(myAukService).sendConfigToMqtt(anyString(), any(byte[].class), any());

        // When
        carDeviceConfigService.retryConfigToMqtt(testConfig1);

        // Then
        verify(myAukService, times(1)).sendConfigToMqtt(
            eq("LMTZSV000MC000001"),
            any(byte[].class),
            any()
        );
    }

    @Test
    void testRetryConfigToMqtt_ExceptionHandling() {
        // 测试异常处理
        // Given
        doThrow(new RuntimeException("MQTT发送失败")).when(myAukService)
            .sendConfigToMqtt(anyString(), any(byte[].class), any());

        // When & Then
        // 验证方法应该抛出异常
        Exception exception = org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () -> {
            carDeviceConfigService.retryConfigToMqtt(testConfig1);
        });

        // 验证异常消息
        org.junit.jupiter.api.Assertions.assertEquals("MQTT发送失败", exception.getMessage());

        // 验证调用了发送方法
        verify(myAukService, times(1)).sendConfigToMqtt(anyString(), any(byte[].class), any());
    }

} 