package com.sankuai.walle.rmanage.config.controller;

import com.alibaba.fastjson.JSONObject;
import com.meituan.servicecatalog.api.annotations.MediaType;
import com.sankuai.walle.rmanage.config.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@AutoConfigureMockMvc
@Ignore
public class OutputApiControllerTest extends BaseTest {

    @Autowired
    protected MockMvc mockMvc;
    @Test
    public void gradeDXGroupTest() throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("accidentId","2115");
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post("/eve/cmdb/rest/output/moxi/grade-dx-group")
                        .content(JSONObject.toJSONString(param))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

}
