package com.sankuai.walle.rmanage.config;


import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.sankuai.walle.cmdb.thrift.service.CarThriftService;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.rmanage.config.Listener.strategy.MessageHandlerFactory;
import com.sankuai.walle.rmanage.config.Listener.strategy.MessageHandlerStrategy;
import com.sankuai.walle.rmanage.config.config.AukClientConfig;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
//import static org.bouncycastle.tls.ConnectionEnd.client;

/**
 * <AUTHOR> Created on 2021/12/7
 */
@SpringBootTest
//@RunWith(MockitoJUnitRunner.class)
@RunWith(SpringRunner.class)
@Slf4j
public class ThriftClientTest {
    private static CarThriftService.Iface client;


    @Test
    public void test() {
        String sn = "SS-LMTZSV022NC017593-SS";
        String vin = "";
        String[] list = sn.split("-");
        System.out.println(Arrays.toString(list));
        for(String child: list){
            if(child.startsWith("S")){
                continue;
            }
            vin = child;
        }
        System.out.println(vin);
    }
    @Test
    public void cat() throws Exception {
//        System.out.println(openAsset.queryWalleAssets(123,5));
        String status = "success";
        Transaction t = Cat.newTransaction("test", "wallcmdb.auk.config.test");
        try {
            Cat.logEvent("test","hello world");
            Cat.logMetricForCount("wallcmdb.auk.config.test");
            MetricHelper.build().name("wallcmdb.auk.config.test").tag("status", status).count(1);
            t.setStatus(Transaction.SUCCESS);
        }finally {
            t.complete();
        }
    }
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    MessageHandlerFactory messageHandlerFactory;
    @Test
    public void hello() throws Exception {
//        String res = JacksonUtils.serialize("3LN6L5SU1JR622134");
//        System.out.println(res);
        String messageBody = "{\"messageId\":\"AAJNChncQQYftBwWZIAE\",\"productKey\":\"walle\",\"deviceKey\":\"LMTZSV02XNC023061\",\"topic\":\"vehicle-config-manage-release-rsp\",\"message\":\"EhFMTVRaU1YwMlhOQzAyMzA2MSIHVURTX1ZDVSoBMjIjCLf+/////////wESFnZlaGljbGVfc3RhdHVzX25vbWF0Y2g=\"}";

        AukUpCountent message = JSONObject.parseObject(messageBody, AukUpCountent.class);
        MessageHandlerStrategy strategy = messageHandlerFactory.getStrategy(message.getTopic());
        strategy.handle(message);
        

        //proxy初始化完成后推荐缓存起来，发起调用时用同一个proxy即可，不推荐重复创建
//        ThriftClientProxy proxy = new ThriftClientProxy();
//        proxy.setRemoteAppkey("com.sankuai.caros.wallecmdb");
//        proxy.setServiceInterface(Class.forName("com.sankuai.walle.cmdb.thrift.service.CarThriftService") );
//        proxy.setNettyIO(true);
//        proxy.setFilterByServiceName(true);
//        proxy.afterPropertiesSet();  //初始化实例
//
//        CarThriftService.Iface helloService = (CarThriftService.Iface) proxy.getObject(); //获取代理对象
//        System.out.println(helloService.getConfigByVin("123",1));

//        proxy.destroy();  //proxy使用完（即不再需要发起调用）记得销毁实例
        StringBuilder builder = new StringBuilder();
        System.out.println(builder.toString().equals(""));

    }
    @Test
    public void thriftclient() throws Exception {
//        ThriftClientProxy proxy = new ThriftClientProxy();
//        proxy.setRemoteAppkey("com.sankuai.caros.wallecmdb");
//        proxy.setServiceInterface(Class.forName("com.sankuai.walle.cmdb.thrift.service.CarThriftService") );
//        proxy.setNettyIO(true);
//        proxy.setFilterByServiceName(true);
//        proxy.afterPropertiesSet();  //初始化实例
//
//        CarThriftService.Iface helloService = (CarThriftService.Iface) proxy.getObject(); //获取代理对象
//        System.out.println(helloService.getCarType());
//
//        proxy.destroy();  //proxy使用完（即不再需要发起调用）记得销毁实例
//
//        //proxy初始化完成后推荐缓存起来，发起调用时用同一个proxy即可，不推荐重复创建
//        BeanFactory beanFactory = new ClassPathXmlApplicationContext("client.xml");
//        SendMessageService.Iface client = (SendMessageService.Iface) beanFactory.getBean("clientProxy2");
//
//        byte[] bytes = new byte[32];
//        ByteBuffer buffer = ByteBuffer.wrap(bytes);
//        System.out.println(client);
//        client.SendMessage(new SendMessageReq(
//            "LMTZSV029MC088062","vehicle_status", buffer
//        ));

    }
}
