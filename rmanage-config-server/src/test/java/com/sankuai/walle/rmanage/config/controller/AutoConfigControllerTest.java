package com.sankuai.walle.rmanage.config.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.rmanage.config.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MockMvcBuilder;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@AutoConfigureMockMvc
public class AutoConfigControllerTest extends BaseTest {

    @Autowired
    protected MockMvc mockMvc;


    @Resource
    protected AutoConfigController autoConfigController;


    @Before
    public void before() {
        mockMvc = MockMvcBuilders.standaloneSetup(autoConfigController).build();
    }

    @Test
    public void handleExcelConfigTest() throws Exception{
        List<SendExcelDeviceConfigReq> reqList = new ArrayList<>();
        for (int i = 0; i < 1; i++) {
            reqList.add(new SendExcelDeviceConfigReq() {{
                setConfigName("ldsk");
                setVin("LA" + Math.round(Math.random() * 1000));
                setUser("zhuojialin02");
                setDeviceName("device");
                setFileType("yaml");
                setContent("cnouswcbuocbosubaocbocnac");
            }});
        }

        String req = new ObjectMapper().writeValueAsString(reqList);

        MvcResult mvcResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/eve/cmdb/rest/api/cmdb/car/auto/auto_config/excel")
                                .content(req)
                                .contentType(MediaType.APPLICATION_JSON)
                                .accept(MediaType.APPLICATION_JSON))

                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("MVC result {}", mvcResult);


    }
}
