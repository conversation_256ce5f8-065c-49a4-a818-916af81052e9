package com.sankuai.walle.rmanage.config.service;

import com.google.common.collect.Lists;
import com.sankuai.walle.rmanage.config.BaseTest;
import com.sankuai.walle.rmanage.config.service.infrastructureService.SecretKeyService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SecretKeyServiceTest extends BaseTest {

    @Resource
    SecretKeyService secretKeyService;

    @Test
    public void testCreateVisitStrategy() {
        String vin = "xxx";
        secretKeyService.createVisitStrategy(vin);
    }

    @Test
    public void testCreateVehicleSSO() {
        String vin = "xxx";
        secretKeyService.createVehicleSSO(vin);
    }

    @Test
    public void importSecretKey() {
        String vin = "xxx";
        secretKeyService.importSecretKey(vin);
    }

    @Test
    public void importVehicleCloudSecretTest() {
        String vin = "LMTZSV014MC000106";
        secretKeyService.importVehicleCloudSecret(vin);
    }

    @Test
    public void batchGetGenerateResultTest() {
        List<String> body = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            body.add(i + "");
        }
            Map<String, Map<String, Object>> stringMapMap = secretKeyService.batchGetGenerateResult(body);
            System.out.println(stringMapMap);

    }


}
