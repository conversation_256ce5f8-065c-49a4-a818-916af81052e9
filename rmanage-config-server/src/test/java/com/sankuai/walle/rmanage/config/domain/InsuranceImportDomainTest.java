package com.sankuai.walle.rmanage.config.domain;

import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 保险导入领域对象测试
 */
class InsuranceImportDomainTest {

    private InsuranceImportDomain domain;

    @BeforeEach
    void setUp() {
        domain = InsuranceImportDomain.create("TEST123456789", "京A12345", new AssetExecWord());
    }

    @Test
    void testProcessCarExecWord_WithExistingId() {
        // 准备测试数据 - 有id的CarExecWord
        CarExecWord existingCarExecWord = new CarExecWord();
        existingCarExecWord.setId(1L);
        existingCarExecWord.setVin("TEST123456789");
        existingCarExecWord.setCarAssetsExecWords("{\"vin\":\"TEST123456789\"}");

        // 执行测试
        CarExecWord result = domain.processCarExecWord(existingCarExecWord, Collections.emptyList());

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("TEST123456789", result.getVin());
    }

    @Test
    void testProcessCarExecWord_WithoutId_ButFoundByVin() {
        // 准备测试数据 - 没有id的CarExecWord，但通过VIN找到了记录
        CarExecWord existingCarExecWord = null; // 没有传入id
        
        CarExecWord foundByVin = new CarExecWord();
        foundByVin.setId(2L);
        foundByVin.setVin("TEST123456789");
        foundByVin.setCarAssetsExecWords("{\"vin\":\"TEST123456789\"}");
        
        List<CarExecWord> existingCarExecWordsByVin = Arrays.asList(foundByVin);

        // 执行测试
        CarExecWord result = domain.processCarExecWord(existingCarExecWord, existingCarExecWordsByVin);

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getId());
        assertEquals("TEST123456789", result.getVin());
    }

    @Test
    void testProcessCarExecWord_WithoutId_AndNotFoundByVin() {
        // 准备测试数据 - 没有id的CarExecWord，通过VIN也没有找到记录
        CarExecWord existingCarExecWord = null; // 没有传入id
        List<CarExecWord> existingCarExecWordsByVin = Collections.emptyList(); // 没有找到记录

        // 执行测试
        CarExecWord result = domain.processCarExecWord(existingCarExecWord, existingCarExecWordsByVin);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getId()); // 新创建的记录没有id
        assertEquals("TEST123456789", result.getVin());
        assertNotNull(result.getAddTime());
        assertNotNull(result.getUpdateTime());
    }

    @Test
    void testUpdateCarObjectsLicenseNo_WithValidLicenseNo() {
        // 准备测试数据
        domain.setLicenseNo("京B12345");
        domain.setCarObjects(new com.sankuai.walle.carManage.entity.CarObjects());
        domain.getCarObjects().setVin("TEST123456789");
        domain.getCarObjects().setLicenseno("旧车牌号");

        // 执行测试
        domain.updateCarObjectsLicenseNo();

        // 验证结果
        assertEquals("京B12345", domain.getCarObjects().getLicenseno());
        assertNotNull(domain.getCarObjects().getUpdateTime());
    }

    @Test
    void testUpdateCarObjectsLicenseNo_WithEmptyLicenseNo() {
        // 准备测试数据
        domain.setLicenseNo(""); // 空的车牌号
        domain.setCarObjects(new com.sankuai.walle.carManage.entity.CarObjects());
        domain.getCarObjects().setVin("TEST123456789");
        domain.getCarObjects().setLicenseno("旧车牌号");

        // 执行测试
        domain.updateCarObjectsLicenseNo();

        // 验证结果 - 车牌号不应该被更新
        assertEquals("旧车牌号", domain.getCarObjects().getLicenseno());
    }

    @Test
    void testUpdateCarObjectsLicenseNo_WithNullCarObjects() {
        // 准备测试数据
        domain.setLicenseNo("京C12345");
        domain.setCarObjects(null); // 空的CarObjects

        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> domain.updateCarObjectsLicenseNo());
    }
} 