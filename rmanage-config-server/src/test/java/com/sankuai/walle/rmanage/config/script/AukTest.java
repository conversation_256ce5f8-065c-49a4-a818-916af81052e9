package com.sankuai.walle.rmanage.config.script;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.InvalidProtocolBufferException;
import com.sankuai.banma.auk.server.sdk.callback.AukCallback;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.rmanage.config.BaseTest;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Base64;

// 解析车端返回给配置管理的数据
public class AukTest extends BaseTest {


    @Test
    public void redisTest() throws InvalidProtocolBufferException {
//        StoreKey storeKey = new StoreKey("LMTZSV022NC017593");
//        redisStoreClient.get(storeKey);
        String messageBody = "{\"messageId\":\"ABnyCse3ygWAcqwAOnhL\",\"productKey\":\"walle\",\"deviceKey\":\"LMTZSV021MC067304\",\"topic\":\"vehicle-config-manage-release-rsp\",\"message\":\"CiBhZTBmNTdiMTU2NzQ0ODdiOTE1MWFmZDUyODI5ZmUzNRIRTE1UWlNWMDIxTUMwNjczMDQaDFVEU19WQ1UuanNvbiIbN0ZGRkYwMDAwIn0AAAAAAAAAAAAAAAAAAAAAKgExMiMI/v//////////ARIWVXBkYXRlTG9jYWxDb25maWcgZmFpbA==\"}";
        messageBody = "{\"messageId\":\"ABnyCse3ygWEowgAR2Xh\"," +
                "\"productKey\":\"walle\"," +
                "\"deviceKey\":\"LMTZSV021MC067304\"," +
                "\"topic\":\"vehicle-config-manage-release-rsp\"," +
                "\"message\":\"CiA5YTMyZWUzNmI4ZGU0MzY4OTc2ZDY5YTZiZDkxYzkxYxIRTE1UWlNWMDIxTUMwNjczMDQaDFVEU19WQ1UuanNvbiIbN0ZGRkYwMDAwIn0AAAAAAAAAAAAAAAAAAAAAKgExMiMI/v//////////ARIWVXBkYXRlTG9jYWxDb25maWcgZmFpbA==\"}";
        AukUpCountent message = JSONObject.parseObject(messageBody, AukUpCountent.class);
        Base64.Decoder decoder = Base64.getDecoder();
        CloudCgf.VehicleToCloudReleaseConfigRsp countent = CloudCgf.VehicleToCloudReleaseConfigRsp.parseFrom(decoder.decode(message.getMessage()));
        System.out.println(countent);
    }


}
