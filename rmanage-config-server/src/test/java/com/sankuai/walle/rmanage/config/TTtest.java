package com.sankuai.walle.rmanage.config;


import com.sankuai.walle.rmanage.config.adapter.DxGroupRoleNameUpdateAdapter;
import com.sankuai.walle.rmanage.config.adapter.TTOncallUserAdapter;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.constant.RgOncallTypeEnum;
import com.sankuai.walle.rmanage.config.dto.accident.RGOncallUserDTO;
import com.sankuai.walle.rmanage.config.service.impl.TTService;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.junit4.SpringRunner;
//import static org.bouncycastle.tls.ConnectionEnd.client;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
@EnableAsync
public class TTtest {

    @Autowired
    TTService ttService;

    @Resource
    DxGroupHandler dxGroupHandler;

    @Resource
    TTOncallUserAdapter ttOncallUserAdapter;

    @Resource
    DxGroupRoleNameUpdateAdapter dxGroupRoleNameUpdateAdapter;

    @Test
    public void redisTest() throws Exception {
        ttService.buildAndSend("tt的描述");
    }

    /**
     * 根据值班用户信息，建群，需要用mis号
     */
    @Test
    public void testGetOncallUserMisId() throws Exception {
        List<String> misIdList = ttService.getRgOncallUserMisIdList(48611L, RgOncallTypeEnum.BY_GROUP);
        log.info("misIdList={}", misIdList);

        // 建群
        String title = "tt拉人测试群";
        Long groupId = dxGroupHandler.createGroup(title, misIdList);
        log.info("groupId={}", groupId);
    }

    /**
     * 测试获取rg值班组值班信息，并根据lion配置修改成员角色
     * 对未获取到的rg值班组，发送消息通知兜底通知人
     */
    @Test
    public void testGetRgOncallUserMisIdList() throws Exception {
        // 48611L存在，4861L不存在
        List<Long> needAlertRgIdList = new ArrayList<>();
        List<RGOncallUserDTO> result = ttOncallUserAdapter.getTTOncallUserList(needAlertRgIdList);
        log.info("result={},needAlertRgIdList={}", result, needAlertRgIdList);
    }

    @Test
    public void testDxGroupRoleNameUpdateAdapter() throws Exception {
        List<Long> needAlertRgIdList = new ArrayList<>();
        List<RGOncallUserDTO> result = ttOncallUserAdapter.getTTOncallUserList(needAlertRgIdList);
        log.info("result={},needAlertRgIdList={}", result, needAlertRgIdList);
        dxGroupRoleNameUpdateAdapter.updateGroupRoleName(64012474826L, result);
    }

    @Test
    public void testSendMessage() throws Exception {
        dxGroupHandler.sendMessage(64012474826L, "测试消息");
    }
}
