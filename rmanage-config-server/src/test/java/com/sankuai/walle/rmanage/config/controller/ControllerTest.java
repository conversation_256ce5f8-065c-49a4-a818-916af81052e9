package com.sankuai.walle.rmanage.config.controller;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.objects.vo.request.MisGroupReq;
import com.sankuai.walle.rmanage.config.BaseTest;
import com.sankuai.walle.rmanage.config.component.ActionFunc;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.policy.AccidentPolicy;
import com.sankuai.walle.rmanage.config.service.AccidentMessageService;
import com.sankuai.walle.rmanage.config.service.HandlePolicyService;
import com.sankuai.walle.rmanage.config.thread.RedisProductor;
import com.sankuai.walle.rmanage.config.thread.dto.QueueParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Ignore
public class ControllerTest extends BaseTest {
    @Resource
    private HandlePolicyService handlePolicyService;
    @Resource
    AccidentPolicy accidentPolicy;

    @Autowired
    private ActionFunc actionFunc;

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    RedisProductor redisProductor;

    @Resource(name = "ThreadRedisBean")
    RedisStoreClient redisStoreClient;
    @MdpConfig("sc.daxiangvideo.urltext")
    public String videoMsg = "";

    @Test
    public void testAccidentPolicy() throws Exception {
        Map<String, Object> map1 = new HashMap<>();
        map1.put("is_anyone_injured", 1);
        accidentPolicy.run(map1);
    }

    @Test
    public void testPostMethodRun() throws Exception {
        // appConfig.create_group("大象群A", new HashMap<>());
        Class<?> clazz = actionFunc.getClass();
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals("create_group")) {
                method.setAccessible(true);
                //  Object obj = clazz.getDeclaredConstructor().newInstance();
                Object bean = applicationContext.getBean(clazz);
                Object[] parameters = new Object[method.getParameterCount()];
                for (int i = 0; i < method.getParameterCount(); i++) {
                    if (method.getParameterTypes()[i] == String.class) {
                        parameters[i] = "大象群A";
                    } else if (method.getParameterTypes()[i] == Map.class) {
                        parameters[i] = new HashMap<>();
                    } else {
                        // 其他参数类型的处理
                    }
                }
                try {
                    method.invoke(bean, parameters); //适用于方法中有bean的反射
                    // method.invoke(spring .getBean("beanName"), parameters); //无bean的反射
                } catch (InvocationTargetException e) {
                    Throwable cause = e.getCause();
                    // 处理异常，例如打印异常信息或进行其他操作
                    cause.printStackTrace();
                }
                break;
            }
        }
    }

    @Test
    public void testSendDateToRedisQueue() throws Exception {
        String category = "HDvideoParamsQueue";
        redisProductor.sendDateToRedisQueue(category,"LMTZSV021MC002887", new Date().getTime()/1000,videoMsg,videoMsg, 64011629547L, "accident");
    }

    @Test
    public void testGetVedioQueueRedisKey() throws Exception {
        String category = "HDvideoParamsQueue";
        StoreKey storeKey = new StoreKey(category, "key");
        QueueParamDTO object = redisStoreClient.lpop(storeKey);

        if (object != null) {
            log.info(" object : {}", object);
        }
    }
    @Resource
    DxGroupHandler dxGroupHandler;

    @Test
    public void testAddGroupMember() throws Exception {
        List<String> groupMember = new ArrayList<>();

        for(int i = 0; i< 51; i++){
            groupMember.add("liurenjie03");
        }
        dxGroupHandler.addGroupMember(64011649776L,groupMember );
    }
    @Resource
    AccidentMessageService accidentMessageService;

    @Test
    public void getFaultInformationTest(){
      String res =   accidentMessageService.getFaultInformation("S20-502");
      System.out.println("res: "+ res);
    }

    @Test
    public void queryHandlePolicyTest() throws Exception {
        Object object =  handlePolicyService.queryHandlePolicy(1,10);
    }

    @Test
    public void updateMisGroupTest() throws Exception {
        MisGroupReq misGroupReq = new MisGroupReq();
        misGroupReq.setId(32L);
        misGroupReq.setGroupMembers("[\"zhaoduancai/赵端财\",\"pengruoyu/彭若愚\",\"caihuini/蔡慧妮\"]");
        handlePolicyService.updateMisGroup(misGroupReq);
    }

    @Test
    public void  getCollisionDetectionMessageTest() throws Exception {
        String alert = accidentMessageService.getCollisionDetectionAlert("LMTZSV022NC017593", 0L);
        System.out.println("alert: " + alert);
    }

    @Test
    public void utcToLocalTimeTest(){
        String utcTimeString = "2024-03-01T09:49:05.000+0000";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date utcDate = sdf.parse(utcTimeString);
            sdf.applyPattern("yyyy-MM-dd HH:mm:ss");
            sdf.setTimeZone(TimeZone.getDefault());
            String localTimeString = sdf.format(utcDate);
            System.out.println(localTimeString); // 输出本地时间字符串
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

}