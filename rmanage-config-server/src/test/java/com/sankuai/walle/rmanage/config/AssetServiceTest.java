package com.sankuai.walle.rmanage.config;


import com.meituan.finerp.eam.response.OpenQueryWalleAssetsRes;
import com.meituan.finerp.eam.service.OpenAssetQueryService;
import com.sankuai.walle.cmdb.thrift.service.CarThriftService;
import com.sankuai.walle.dal.mrm_manage.entity.MyTagsVin;
import com.sankuai.walle.dal.mrm_manage.example.MyTagsVinExample;
import com.sankuai.walle.dal.mrm_manage.example.MyVehicleExample;
import com.sankuai.walle.dal.mrm_manage.example.MyVehicleExampleList;
import com.sankuai.walle.dal.mrm_manage.mapper.MyTagsMyMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.MyVehicleMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
//import static org.bouncycastle.tls.ConnectionEnd.client;

/**
 * <AUTHOR> Created on 2021/12/7
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AssetServiceTest {
    private static CarThriftService.Iface client;

    @Resource
    OpenAssetQueryService openAssetQueryService;

//    "乘用车","房车","汽车","S项目整车"
    @Test
    public void cat() throws Exception {
        String scrollId = null;
        List<String> smallTypeList = new ArrayList<>(Arrays.asList("硬盘","乘用车","房车","汽车","S项目整车"));
        int size = 10;
        OpenQueryWalleAssetsRes resp = openAssetQueryService.queryWalleAssetsBySmallType(
                smallTypeList, scrollId, size
        );
        System.out.println(resp.getDataList().stream().map(obj->obj.getSmallType()).collect(Collectors.toList()));
        System.out.println(resp.getDataList().stream().map(obj->obj.getBrand()).collect(Collectors.toList()));
    }
    @Resource
    MyTagsMyMapper myTagsMyMapper;
    @Test
    public void getTags() {
        ArrayList<String> vins = new ArrayList<String>() {{
            add("20191228000100002");
        }};
        MyTagsVinExample tagQuery = new MyTagsVinExample();
        tagQuery.createCriteria().andVinIn(vins);
        List<MyTagsVin> tagres = myTagsMyMapper.selectByExample(tagQuery);
        System.out.println(tagres);
    }
    @Resource
    MyVehicleMapper myVehicleMapper;
    @Test
    public void getVinLists() {
        ArrayList<String> vins = new ArrayList<String>(){{
            add("20191228000100002");
        }};
        long t = System.currentTimeMillis();
        MyVehicleExample query1 = new MyVehicleExample();
        query1.createCriteria().andCarNameLike("zcx14-acu");

        MyVehicleExampleList querylist = new MyVehicleExampleList();
        querylist.setCar_tag(query1);
        List<String> res = myVehicleMapper.selectByExample(querylist);
        System.out.println(res);
        System.out.println(System.currentTimeMillis()-t);

        int a = 2;
        System.out.println((long)a);
    }

}
