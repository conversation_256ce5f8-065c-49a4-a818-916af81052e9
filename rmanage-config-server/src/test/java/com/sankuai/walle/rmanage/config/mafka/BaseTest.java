package com.sankuai.walle.rmanage.config.mafka;


import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.sankuai.walle.rmanage.config.service.MafkaRealtimeService;
import com.sankuai.walle.rmanage.config.service.impl.MafkaRealtimeServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Properties;
//import static org.bouncycastle.tls.ConnectionEnd.client;

/**
 * <AUTHOR> Created on 2021/12/7
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class BaseTest {
    private static IProducerProcessor producer;

    @Autowired
    MafkaRealtimeService realtimeService;

    @Test
    public void redisTest() throws Exception {
//        realtimeService.sendRealTime();
    }
}
