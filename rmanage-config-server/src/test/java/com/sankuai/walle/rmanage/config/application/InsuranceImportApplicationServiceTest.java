package com.sankuai.walle.rmanage.config.application;

import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import com.sankuai.walle.objects.vo.request.ImportInsuranceReq;
import com.sankuai.walle.rmanage.config.dto.InsuranceImportResultDTO;
import com.sankuai.walle.rmanage.config.repository.InsuranceImportRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 保险导入应用服务测试
 */
class InsuranceImportApplicationServiceTest {

    @Mock
    private InsuranceImportRepository insuranceImportRepository;

    @InjectMocks
    private InsuranceImportApplicationService insuranceImportApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testImportInsuranceBatch_Success() {
        // 准备测试数据
        ImportInsuranceReq request = new ImportInsuranceReq();
        request.setVin("TEST123456789");
        request.setLicenseno("京A12345");
        
        AssetExecWord assetExecWord = new AssetExecWord();
        assetExecWord.setVin("TEST123456789");
        assetExecWord.setHeavyTrafficInsurance(new Date());
        assetExecWord.setBusinessInsuranceDate(new Date());
        request.setAssetExecWord(assetExecWord);

        // Mock CarObjects
        CarObjects carObject = new CarObjects();
        carObject.setVin("TEST123456789");
        carObject.setLicenseno("旧车牌号");
        
        // Mock repository responses
        when(insuranceImportRepository.findCarObjectsByVin("TEST123456789"))
                .thenReturn(Arrays.asList(carObject));
        when(insuranceImportRepository.findCarAssetsByVin("TEST123456789"))
                .thenReturn(Collections.emptyList()); // 简化测试，不mock CarAssets
        when(insuranceImportRepository.findCarExecWordByVin("TEST123456789"))
                .thenReturn(Collections.emptyList());

        // 执行测试
        InsuranceImportResultDTO result = insuranceImportApplicationService.importInsuranceBatch(Arrays.asList(request));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getNotFoundCount());
        assertEquals(0, result.getErrorCount());
        assertTrue(result.getSuccessVins().contains("TEST123456789"));
        
        // 验证车牌号更新被调用
        verify(insuranceImportRepository, times(1)).updateCarObjects(any(CarObjects.class));
        verify(insuranceImportRepository, times(1)).saveCarExecWord(any());
    }

    @Test
    void testImportInsuranceBatch_VehicleNotFound() {
        // 准备测试数据
        ImportInsuranceReq request = new ImportInsuranceReq();
        request.setVin("NOTFOUND123");
        request.setLicenseno("京B12345");
        
        AssetExecWord assetExecWord = new AssetExecWord();
        assetExecWord.setVin("NOTFOUND123");
        request.setAssetExecWord(assetExecWord);

        // Mock repository responses - 车辆不存在
        when(insuranceImportRepository.findCarObjectsByVin("NOTFOUND123"))
                .thenReturn(Collections.emptyList());

        // 执行测试
        InsuranceImportResultDTO result = insuranceImportApplicationService.importInsuranceBatch(Arrays.asList(request));

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getNotFoundCount());
        assertEquals(0, result.getErrorCount());
        assertTrue(result.getNotFoundVins().contains("NOTFOUND123"));
        
        // 验证没有调用更新方法
        verify(insuranceImportRepository, never()).updateCarObjects(any());
        verify(insuranceImportRepository, never()).saveCarExecWord(any());
    }

    @Test
    void testImportInsuranceBatch_MixedResults() {
        // 准备测试数据 - 一个成功，一个失败
        ImportInsuranceReq request1 = new ImportInsuranceReq();
        request1.setVin("SUCCESS123");
        request1.setLicenseno("京C12345");
        
        AssetExecWord assetExecWord1 = new AssetExecWord();
        assetExecWord1.setVin("SUCCESS123");
        request1.setAssetExecWord(assetExecWord1);

        ImportInsuranceReq request2 = new ImportInsuranceReq();
        request2.setVin("NOTFOUND456");
        request2.setLicenseno("京D12345");
        
        AssetExecWord assetExecWord2 = new AssetExecWord();
        assetExecWord2.setVin("NOTFOUND456");
        request2.setAssetExecWord(assetExecWord2);

        // Mock repository responses
        CarObjects carObject = new CarObjects();
        carObject.setVin("SUCCESS123");
        
        when(insuranceImportRepository.findCarObjectsByVin("SUCCESS123"))
                .thenReturn(Arrays.asList(carObject));
        when(insuranceImportRepository.findCarObjectsByVin("NOTFOUND456"))
                .thenReturn(Collections.emptyList());
        when(insuranceImportRepository.findCarAssetsByVin("SUCCESS123"))
                .thenReturn(Collections.emptyList());
        when(insuranceImportRepository.findCarExecWordByVin("SUCCESS123"))
                .thenReturn(Collections.emptyList());

        // 执行测试
        InsuranceImportResultDTO result = insuranceImportApplicationService.importInsuranceBatch(Arrays.asList(request1, request2));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getSuccessCount());
        assertEquals(1, result.getNotFoundCount());
        assertEquals(0, result.getErrorCount());
        assertTrue(result.getSuccessVins().contains("SUCCESS123"));
        assertTrue(result.getNotFoundVins().contains("NOTFOUND456"));
    }
} 