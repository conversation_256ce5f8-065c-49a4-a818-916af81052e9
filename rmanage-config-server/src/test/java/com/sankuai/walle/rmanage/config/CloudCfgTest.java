package com.sankuai.walle.rmanage.config;



import com.google.protobuf.InvalidProtocolBufferException;

import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import org.junit.Test;

import javax.annotation.Resource;


//@SpringBootTest
//@RunWith(SpringRunner.class)
public class CloudCfgTest {

    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;

    @Test
    public void proto() throws InvalidProtocolBufferException {
        CloudCgf.CloudToVehicleReleaseConfig.Builder req = CloudCgf.CloudToVehicleReleaseConfig.newBuilder();
        CloudCgf.VehicleToCloudReleaseConfigRsp.Builder rep = CloudCgf.VehicleToCloudReleaseConfigRsp.newBuilder();
        rep.setSConfigName("123");
        System.out.println(rep.build());
        System.out.println(rep.build().toByteArray().toString());
        System.out.println(rep.build().toString());
//        CloudCgf.VehicleToCloudReleaseConfigRsp res = CloudCgf.VehicleToCloudReleaseConfigRsp.parseFrom(ByteString.copyFromUtf8(rep.build().toString()));
//        System.out.println(res);

//        CarDeviceConfig target = carDeviceConfigMapper.selectByPrimaryKey(8L);

//        try {
//            carDeviceConfigService.configToMqtt(target);
//        } catch (MqttException e) {
//            throw new RuntimeException(e);
//        }
    }

}
