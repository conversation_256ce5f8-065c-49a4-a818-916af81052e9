package com.sankuai.walle.rmanage.config;

import com.meituan.finerp.eam.dto.OpenAssetDTO;
import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.rmanage.config.helper.convertor.OpenAssetDtoConvert;
import com.sankuai.walle.rmanage.config.service.impl.AssetServiceImpl;

public class AssestFunTest {
    public static void main(String[] args) {
        AssetServiceImpl assetService = new AssetServiceImpl();
//        boolean res = assetService.checkFun("HDL401B87P1002292-1","HDL401B87P1002292-1");
//        System.out.println(res);

        OpenAssetDTO openAssetDTO = new OpenAssetDTO();
        // sn 为空，不能修改vin
        openAssetDTO.setSerialNumber("");
        CarAssets asset = new CarAssets();
        String vin = "LMTZSV016MC000107";
        asset.setSn("LMTZSV016MC000107-S");
        asset.setVin(vin);
        boolean flag = assetService.updateExistAssetInfo(openAssetDTO, asset);
        assert asset.getVin()==vin;
        System.out.println(flag);

        // sn 为不是空，且sn转vin和已有的vin不一致，返回true
        openAssetDTO = new OpenAssetDTO();
        openAssetDTO.setSerialNumber("LMTZSV016MC000107-S");
        asset = new CarAssets();
        asset.setSn("LMTZSV016MC000107-S");
        asset.setVin("LMTZSV016MC000107-S");
        System.out.println(asset.getVin());
        flag = assetService.updateExistAssetInfo(openAssetDTO, asset);
        System.out.println(flag);
        assert asset.getVin()==vin;
        assert flag;
        System.out.println(asset.getVin());
//        String vin = OpenAssetDtoConvert.SnToVin("1-3LN6L5SU1KR631255");
//        System.out.println(vin);


    }
}
