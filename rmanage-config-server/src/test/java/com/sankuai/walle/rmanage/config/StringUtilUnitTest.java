package com.sankuai.walle.rmanage.config;

import com.sankuai.walle.rmanage.config.util.StringUtil;
import org.junit.Test;

import java.util.ArrayList;

public class StringUtilUnitTest {

    @Test
    public void testJoin(){
        ArrayList<String> strings1 = new ArrayList(){{add("abc");add("def");}};
        String vins = StringUtil.joinStr(strings1);
        System.out.println(vins);

        ArrayList<String> strings2 = new ArrayList(){{add("abc");}};
        vins = StringUtil.joinStr(strings2);
        System.out.println(vins);

        ArrayList<String> strings3 = new ArrayList(){{add("");}};
        vins = StringUtil.joinStr(strings3);
        System.out.println(vins);

        ArrayList<String> strings4 = new ArrayList(){{add(null);}};
        vins = StringUtil.joinStr(strings4);
        System.out.println(vins);

        ArrayList<String> strings6 = new ArrayList(){{add(null);add("123");}};
        vins = StringUtil.joinStr(strings6);
        System.out.println(vins);

        ArrayList<String> strings5 = new ArrayList(){{}};
        vins = StringUtil.joinStr(strings5);
        System.out.println(vins);
    }
}
