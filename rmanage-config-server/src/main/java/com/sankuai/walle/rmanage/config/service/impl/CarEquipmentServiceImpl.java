package com.sankuai.walle.rmanage.config.service.impl;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.walle.cms.thrift.service.*;
import com.sankuai.walle.dal.mrm_manage.entity.CarEquipment;
import com.sankuai.walle.dal.mrm_manage.example.CarEquipmentExample;
import com.sankuai.walle.dal.mrm_manage.mapper.CarEquipmentMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.execDo.CarEquipmentExecWord;
import com.sankuai.walle.objects.vo.request.CarEquipmentReq;
import com.sankuai.walle.objects.vo.res.CarEquipmentRes;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.CarEquipmentService;
import lombok.extern.slf4j.Slf4j;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CarEquipmentServiceImpl implements CarEquipmentService{
    @Value("${es.Index}")
    private String esIndex;
    @Resource
    CarEquipmentMapper carEquipmentMapper;

    @MdpThriftClient(remoteAppKey = "com.sankuai.wallemonitor.walle.cms",  timeout = 20000)
    private ESCommonService.Iface esCommonService;
    /*
        单条插入、批量插入、更新
     */
    @Override
    public ResData insertOrUpdate(List<CarEquipmentReq> jsonData) {
        //暂时不用mafka

//        try {
//            List<String> uids = getUids(jsonData);
//            sendRealTime(uids);
//        } catch (Exception e) {
//            log.error("发送实时数据失败，原因：", e);
//        }
        Gson gson = new Gson();
        //将 carEquipments 转换为一个包含 uid 的 Set
        Set<String> dbExistingUids = new HashSet<>();
        Set<String> esExistingUids = new HashSet<>();
        //esUidToAddtime用于映射uid和addTime的关系
        HashMap<String, Long> esUidToAddtime = new HashMap<>();

        //uids和uidSet对应传入参数的所有uid
        List<String> uids = getUids(jsonData);
        HashSet<String> uidsSet = new HashSet<>(uids);

        //设置dbExistingUids
        setDbExistingUids(dbExistingUids, uids);

        //设置esExistingUids和esUidToAddtime
        setEsExistingUidsAndEsUidToAddtime(esExistingUids, esUidToAddtime, uidsSet);

        // 初始化列表，用于存放查到的和没查到的数据
        List<CarEquipmentReq> dbFoundData = new ArrayList<>();
        List<CarEquipmentReq> dbNotFoundData = new ArrayList<>();
        List<CarEquipmentReq> esFoundData = new ArrayList<>();
        List<CarEquipmentReq> esNotFoundData = new ArrayList<>();

        //划分设置dbFoundData、dbNotFoundData、esFoundData、esNotFoundData
        dbSplitList(jsonData,dbFoundData,dbNotFoundData,dbExistingUids);
        esSplitList(jsonData,esFoundData,esNotFoundData,esExistingUids,esUidToAddtime);

        //异步完成db和es的插入和更新
        return FutureInsertUpdate(gson, dbFoundData, dbNotFoundData, esFoundData, esNotFoundData);

    }



    /*
        更新
     */
    @Override
    public void update(CarEquipmentReq data) {

        // 从 JSON 数据中获取设备信息
        String uid = data.getCarEquipment().getUid();
        String vin = data.getCarEquipment().getVin();
        String sn = data.getCarEquipment().getSn();
        String label = data.getCarEquipment().getLabel();
        String bigType = data.getCarEquipment().getBigType();
        String smallType = data.getCarEquipment().getSmallType();
        String mis = data.getCarEquipment().getMis();
        String status = data.getCarEquipment().getStatus();
        String remoteTypeName = data.getCarEquipment().getRemoteTypeName();
        Boolean scrap = data.getCarEquipment().getScrap();
        String park = data.getCarEquipment().getPark();
        Date updateTime = new Date();
        //解析execWord
        CarEquipmentExecWord carEquipmentExecWord = data.getCarEquipmentExecWord();
        Gson gson = new Gson();
        String execWord = gson.toJson(carEquipmentExecWord);
        try {
            // 创建查询条件
            CarEquipmentExample example = new CarEquipmentExample();
            example.createCriteria().andUidEqualTo(uid);
            // 根据条件查询设备信息
            List<CarEquipment> carEquipmentList = carEquipmentMapper.selectByExample(example);
            for (CarEquipment carEquipment : carEquipmentList) {
                // 更新设备信息
                carEquipment.setVin(vin);
                carEquipment.setSn(sn);
                carEquipment.setLabel(label);
                carEquipment.setBigType(bigType);
                carEquipment.setSmallType(smallType);
                carEquipment.setMis(mis);
                carEquipment.setStatus(status);
                carEquipment.setRemoteTypeName(remoteTypeName);
                carEquipment.setScrap(scrap);
                carEquipment.setPark(park);
                carEquipment.setUpdateTime(updateTime);
                carEquipment.setExecWord(execWord);
                // 更新设备信息到数据库中
                carEquipmentMapper.updateByPrimaryKey(carEquipment);
            }

        } catch (Exception e) {
            log.error("update时发生错误："+e);
        }
    }
    /*
        插入
     */
    @Override
    public void insert(CarEquipmentReq data){
        String uid = data.getCarEquipment().getUid();
        String vin = data.getCarEquipment().getVin();
        String sn = data.getCarEquipment().getSn();
        String label = data.getCarEquipment().getLabel();
        String bigType = data.getCarEquipment().getBigType();
        String smallType = data.getCarEquipment().getSmallType();
        String mis = data.getCarEquipment().getMis();
        String status = data.getCarEquipment().getStatus();
        String remoteTypeName = data.getCarEquipment().getRemoteTypeName();
        Boolean scrap =data.getCarEquipment().getScrap();
        String park = data.getCarEquipment().getPark();
        Date addTime = new Date();
        Date updateTime = new Date();
        //解析execWord
        CarEquipmentExecWord carEquipmentExecWord = data.getCarEquipmentExecWord();
        Gson gson = new Gson();
        String execWord = gson.toJson(carEquipmentExecWord);
        try{
            //将设备信息插入到数据库中
            CarEquipment carEquipment = CarEquipment.builder()
                    .sn(sn)
                    .uid(uid)
                    .vin(vin)
                    .label(label)
                    .bigType(bigType)
                    .smallType(smallType)
                    .mis(mis)
                    .status(status)
                    .remoteTypeName(remoteTypeName)
                    .scrap(scrap)
                    .park(park)
                    .addTime(addTime)
                    .updateTime(updateTime)
                    .execWord(execWord)
                    .build();
            carEquipmentMapper.insert(carEquipment);
        }catch (Exception e){
            log.error("insert时发生错误："+e);
        }
    }

    /*
        异步完成db和es的插入和更新
     */
    private ResData FutureInsertUpdate(Gson gson, List<CarEquipmentReq> dbFoundData, List<CarEquipmentReq> dbNotFoundData, List<CarEquipmentReq> esFoundData, List<CarEquipmentReq> esNotFoundData) {
        //异步完成db和es的插入和更新
        CompletableFuture<Void> esFuture = CompletableFuture.runAsync(() -> {
            //走更新
            if (!esFoundData.equals(Collections.emptyList())){
                //转换foundData，用于es更新
                List<Map<String, Object>> foundMapList = list2ListMap(esFoundData);
                for (Map<String, Object> foundMap:foundMapList){
                    HashMap<String, String> phrase = new HashMap<>();
                    phrase.put("keyId",(String) foundMap.get("keyId"));
                    //交互方式1.发送post请求
//                        postRequest(targetUpdateUrl,esUpdate);
//                        log.info("es正在更新");
                    //交互方式2 调用thrift

                    String esUpdateJson = gson.toJson(foundMap);
                    UpdateEagleRequest updateEagleRequest = new UpdateEagleRequest();
                    updateEagleRequest.setIndex(esIndex);
                    updateEagleRequest.setData(esUpdateJson);
                    updateEagleRequest.setPhrase(phrase);

                    //更新频率过快时，返回300，需要重试
                    int retryCount = 0;
                    boolean isSuccess = false;
                    while (!isSuccess && retryCount < 3) {
                        try {
                            CommonEagleResponse updateRes = esCommonService.updateByQueryEagle(updateEagleRequest);
                            int code = updateRes.getCode();
                            log.info("es返回信息："+updateRes);
                            log.info("es正在更新");
                            if (code == 300) {
                                retryCount++;
                                log.info("es更新失败，正在进行第" + retryCount + "次重试");
                            } else {
                                isSuccess = true;
                                log.info("es更新成功");
                            }
                        } catch (TException e) {
                            log.error("es更新发生error："+e);
                            //失败了也应该加次数，要不然就一直失败了，无限循环
                            retryCount++; // 增加重试次数
                        }
                    }
                }
            }
            //用于es批量插入
            if (!esNotFoundData.equals(Collections.emptyList())){
                List<Map<String, Object>> notFoundMapList = list2ListMap(esNotFoundData);
                //交互方式1:发送post请求
                //postRequest(targetBulkUrl,esBulk);
                //log.info("es正在批量插入");
                //交互方式2:thrift接口
                String esBulkJson = gson.toJson(notFoundMapList);
                CommonEagleRequest commonEagleRequest = new CommonEagleRequest();
                commonEagleRequest.setIndex(esIndex);
                commonEagleRequest.setData(esBulkJson);
                try {
                    CommonEagleResponse esRes = esCommonService.bulkInsertEagle(commonEagleRequest);
                    log.info("es返回值："+esRes);
                    log.info("es正在批量插入，插入数据"+notFoundMapList.size()+"条");
                } catch (TException e) {
                    log.error("es批量插入发生error："+e);
                }
            }
        });
        CompletableFuture<Void> dbFuture = CompletableFuture.runAsync(() -> {
            try{
                // DB更新和写入的代码
                for (CarEquipmentReq data : dbFoundData){
                    update(data);
                    log.info("db正在更新");
                }
                for (CarEquipmentReq data : dbNotFoundData){
                    insert(data);
                    log.info("db正在插入");
                }
            }catch (Exception e){
                log.error("db的异步操作发生error："+e);
            }
        });
        //等待db和es插入数据
        CompletableFuture.allOf(esFuture, dbFuture).join();
        log.info("success：db和es的异步操作全部完成");
        ResData res = new ResData();
        res.setCode(CommonConstants.SUCCEED_CODE);
        res.setMsg("执行成功，数据库共插入数据"+dbNotFoundData.size()+"条，更新数据"+dbFoundData.size()+"条。 "+
                "es共插入数据"+esNotFoundData.size()+"条，更新数据"+esFoundData.size()+"条。");
        return res;
    }


    /*
        按uid单条查询
     */
    @Override
    public ResData getByUid(String uid) {
        ResData res = new ResData();
        // 创建查询条件
        CarEquipmentExample example = new CarEquipmentExample();
        example.createCriteria().andUidEqualTo(uid);
        // 根据条件查询设备信息
        List<CarEquipment> carEquipmentList = carEquipmentMapper.selectByExample(example);
        if (carEquipmentList.isEmpty()) {
            res.setCode(CommonConstants.SUCCEED_CODE);
            res.setMsg("按uid进行查询，未查询到数据");
            res.setData(Collections.emptyList());
            return res;
        }
        List<CarEquipmentRes> carEquipmentResList = new ArrayList<>();
        for (CarEquipment list : carEquipmentList) {
            CarEquipmentRes carEquipmentRes = new CarEquipmentRes();
            Gson gson = new Gson();
            CarEquipmentExecWord execWord = gson.fromJson(list.getExecWord(), CarEquipmentExecWord.class);
            carEquipmentRes.setCarEquipmentExecWord(execWord);
            carEquipmentRes.setCarEquipment(list);
            carEquipmentResList.add(carEquipmentRes);
        }
        res.setCode(CommonConstants.SUCCEED_CODE);
        res.setMsg("按uid进行查询");
        res.setData(carEquipmentResList);
        return res;
    }
    /*
        查询list
     */
    @Override
    public ResData getList(Integer pageNum, Integer pageSize) {
        ResData res = new ResData();
        CarEquipmentExample example = new CarEquipmentExample();
        example.setOffset((pageNum - 1) * pageSize);
        example.setRows(pageSize); // 设置查询的记录数
        example.setOrderByClause("id desc");
        List<CarEquipment> carEquipmentList = carEquipmentMapper.selectByExample(example);
        List<CarEquipmentRes> carEquipmentResList = new ArrayList<>();
        for (CarEquipment carEquipment:carEquipmentList){
            CarEquipmentRes carEquipmentRes = new CarEquipmentRes();
            Gson gson = new Gson();
            CarEquipmentExecWord execWord = gson.fromJson(carEquipment.getExecWord(), CarEquipmentExecWord.class);
            carEquipmentRes.setCarEquipmentExecWord(execWord);
            carEquipmentRes.setCarEquipment(carEquipment);
            carEquipmentResList.add(carEquipmentRes);
        }
        PageInfo<CarEquipmentRes> pageInfo = new PageInfo<>(carEquipmentResList);
        HashMap<String, Object> data = new HashMap<>();
        res.setCode(CommonConstants.SUCCEED_CODE);
        res.setMsg("获取分页信息");
        data.put("list", pageInfo.getList());
        data.put("page", pageNum);
        data.put("pageSize", pageSize);
        res.setData(data);
        res.setRows(pageInfo.getTotal());
        return res;
    }

    /*
        获得jsonData包含的所有uid
     */
    @Override
    public List<String> getUids(List<CarEquipmentReq> jsonData) {
        List<String> uids = new ArrayList<>();
        for (CarEquipmentReq data : jsonData) {
            String uid = data.getCarEquipment().getUid();
            uids.add(uid);
        }
        return uids;
    }

    /*
        转为es结构
     */
    public List<Map<String, Object>> list2ListMap(List<CarEquipmentReq> jsonData) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (CarEquipmentReq carEquipmentReq : jsonData) {
            Map<String, Object> esMap = new HashMap<>();
            //拼attributes
            Map<String, Object> attributes = new HashMap<>();
            attributes.put("bigType",carEquipmentReq.getCarEquipment().getBigType());
            attributes.put("remoteTypeName",carEquipmentReq.getCarEquipment().getRemoteTypeName());
            attributes.put("addTime",carEquipmentReq.getCarEquipment().getAddTime().getTime()/1000);
            attributes.put("district",carEquipmentReq.getCarEquipmentExecWord().getDistrict());
            attributes.put("exterior",carEquipmentReq.getCarEquipmentExecWord().getExterior());
            attributes.put("topPictureLink",carEquipmentReq.getCarEquipmentExecWord().getTopPictureLink());
            attributes.put("botPictureLink",carEquipmentReq.getCarEquipmentExecWord().getBotPictureLink());

            //拼esMap
            esMap.put("attributes", attributes);
            esMap.put("purpose",carEquipmentReq.getCarEquipmentExecWord().getPurpose());
            esMap.put("scrap",carEquipmentReq.getCarEquipment().getScrap()==true?"是":"否");
            esMap.put("vin",carEquipmentReq.getCarEquipment().getVin());
            esMap.put("city", carEquipmentReq.getCarEquipmentExecWord().getCity());
            esMap.put("keyId", carEquipmentReq.getCarEquipment().getUid());
            esMap.put("label", carEquipmentReq.getCarEquipment().getLabel());
            esMap.put("mis", carEquipmentReq.getCarEquipment().getMis());
            esMap.put("park", carEquipmentReq.getCarEquipment().getPark());
            esMap.put("sn", carEquipmentReq.getCarEquipment().getSn());
            esMap.put("status", carEquipmentReq.getCarEquipment().getStatus());
            esMap.put("type", carEquipmentReq.getCarEquipment().getSmallType());
            esMap.put("updateTime", carEquipmentReq.getCarEquipment().getUpdateTime().getTime()/1000);
            // 将转换后的 esMap 添加到 resultList 中
            resultList.add(esMap);
        }
        return resultList;
    }
    public String postRequest(String targetUrl,Map<String,Object> map){
        try {
            URL url = new URL(targetUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            //设置是否可以向服务器发送数据
            connection.setDoOutput(true);

            // 将esBulk转换为JSON字符串
            Gson gson = new Gson();
            String json = gson.toJson(map);

            // 发送POST请求
            //使用 connection.getOutputStream() 获取 HttpURLConnection 对象的输出流，用于向服务器发送数据。
            OutputStream outputStream = connection.getOutputStream();
            //通过 outputStream.write(json.getBytes()) 将 JSON 字符串转换为字节数组，并写入输出流中。
            outputStream.write(json.getBytes());
            //调用 outputStream.flush() 刷新输出流，确保所有数据都已发送到服务器。
            outputStream.flush();
            //使用 outputStream.close() 关闭输出流。
            outputStream.close();

            // 获取响应结果
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 请求成功
                // 请求成功
                // 从 HttpURLConnection 获取输入流
                InputStream inputStream = connection.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                log.info("发送成功");
                return response.toString();
            } else {
                // 请求失败
                // 处理错误信息
                log.warn("发送失败");
                return null;
            }
        } catch (Exception e) {
            log.error("postRequest方法失败，原因是:"+e);
            return null;
        }
    }

    /*
    按照是否存在划分es插入还是更新
 */
    private void esSplitList(List<CarEquipmentReq> jsonDataEs, List<CarEquipmentReq> esFoundData,
                             List<CarEquipmentReq> esNotFoundData, Set<String> existingUids,HashMap<String,Long> esUidToAddtime) {
        // 遍历 jsonData，根据 uid 判断数据是否查到，然后添加到相应的列表中
        for (CarEquipmentReq req : jsonDataEs) {
            if (existingUids.contains(req.getCarEquipment().getUid())) {
                //这里改成了文哥推荐的hashmap方法
                Long addTime = esUidToAddtime.get(req.getCarEquipment().getUid());
                req.getCarEquipment().setAddTime(new Date(addTime*1000));
                req.getCarEquipment().setUpdateTime(new Date());
                esFoundData.add(req);
            } else {
                req.getCarEquipment().setAddTime(new Date());
                req.getCarEquipment().setUpdateTime(new Date());
                esNotFoundData.add(req);
            }
        }
    }

    /*
        按照是否存在划分db插入还是更新
     */
    private void dbSplitList(List<CarEquipmentReq> jsonData, List<CarEquipmentReq> dbFoundData,
                             List<CarEquipmentReq> dbNotFoundData, Set<String> existingUids) {
        // 遍历 jsonData，根据 uid 判断数据是否查到，然后添加到相应的列表中
        for (CarEquipmentReq req : jsonData) {
            if (existingUids.contains(req.getCarEquipment().getUid())) {
                dbFoundData.add(req);
            } else {
                dbNotFoundData.add(req);
            }
        }
    }

    /*
        转换城市，暂不需要，通过map映射或脚本解决
     */
    @Override
    public String getCityName(String input) {
        // 去掉 input 中的所有空格
        input = input.replace(" ", "");
        List<Term> termList = HanLP.segment(input);
        for (Term term : termList) {
            if (term.nature.startsWith("ns")) {
                // 如果词性是地名（ns），则返回该词作为城市名
                String cityName = term.word;
                if ((cityName.endsWith("市") || cityName.endsWith("省"))&&cityName.length()!=2) {
                    // 如果城市名以 "市" 或 "省" 结尾，去掉 "市" 或 "省"
                    cityName = cityName.substring(0, cityName.length() - 1);
                }
                return "匹配成功：" + cityName;
            }
        }
        // 如果没有找到匹配的城市名，返回空字符串
        return "匹配不成功："+input;
    }
    /*
        批量查询
     */
    @Override
    public ResData bulkSearch(String index, List<Map<String, String>> uids){
        ResData resData = new ResData();
        Gson gson = new Gson();
        BulkSearchEagleRequest bulkSearchEagleRequest = new BulkSearchEagleRequest();
        bulkSearchEagleRequest.setIndex(esIndex);
        bulkSearchEagleRequest.setPhrase(uids);
        CommonEagleResponse commonEagleResponse = null;
        System.out.println("bulkSearchEagleRequest如下");

        System.out.println(bulkSearchEagleRequest);
        try {
            commonEagleResponse = esCommonService.bulkSearchEagle(bulkSearchEagleRequest);
        } catch (TException e) {
            log.error("批量查询出现错误"+e);
        }
        List<List<Map<String, Object>>> jsonDataES =
                (List<List<Map<String, Object>>>) gson.fromJson(commonEagleResponse.getData(), List.class);
        resData.setCode(CommonConstants.SUCCEED_CODE);
        resData.setData(jsonDataES);
        return resData;
    }

    /*
         设置DbExistingUids
     */
    private void setDbExistingUids(Set<String> dbExistingUids, List<String> uids) {
        CarEquipmentExample example = new CarEquipmentExample();
        CarEquipmentExample.Criteria criteria = example.createCriteria();
        criteria.andUidIn(uids);
        List<CarEquipment> carEquipments = carEquipmentMapper.selectByExample(example);
        for (CarEquipment carEquipment : carEquipments) {
            dbExistingUids.add(carEquipment.getUid());
        }
    }
    /*
        设置esExistingUids和esUidToAddtime
     */
    private void setEsExistingUidsAndEsUidToAddtime(Set<String> esExistingUids, HashMap<String, Long> esUidToAddtime, HashSet<String> uidsSet) {
        Gson gson = new Gson();
        //拼成es批量查询对应的请求格式
        List<Map<String, String>> esSearchUids = new ArrayList<>();
        //因为这里要拼请求，所以现在采用的是HashSet
        for (String uid : uidsSet) {
            Map<String, String> map = new HashMap<>();
            map.put("keyId", uid);
            esSearchUids.add(map);
        }
        log.info("esSearchUids"+esSearchUids);
        List<List<Map<String, Object>>> jsonDataES = (List<List<Map<String, Object>>>) bulkSearch(esIndex, esSearchUids).getData();
        log.info("jsonDataES"+jsonDataES);

        //todo:这里的返回格式是否可以改变一下，应该是因为对于每一个uid查询出来是一个list，但其实只要get0就行了
        //todo：list又嵌套list的形式，有点奇怪
        for (List<Map<String, Object>> listMap : jsonDataES) {
            if (listMap.size()>0){
                String keyId = (String) listMap.get(0).get("keyId");
                Long addTime;
                if (listMap.get(0).containsKey("attributes")) {
                    Map<String, Object> attributes = (Map<String, Object>) listMap.get(0).get("attributes");
                    //需要防止数据没有addTime的情况（正常情况下不应该有，模拟数据会有，所以处理一下）

                    if (attributes.containsKey("addTime")) {
                        addTime = ((Number) attributes.get("addTime")).longValue();
                    } else {
                        addTime = System.currentTimeMillis() / 1000;
                    }
                    esUidToAddtime.put(keyId, addTime);
                    //attributes也有可能没有
                }else{
                    esUidToAddtime.put(keyId, System.currentTimeMillis() / 1000);
                }

            }
        }
        for (List<Map<String, Object>> listMap : jsonDataES) {
            if (listMap.size()>0){
                esExistingUids.add((String) listMap.get(0).get("keyId"));
            }
        }

    }

    public String getPath(String keyId) {
        List<String> labels = carEquipmentMapper.selectByExample(new CarEquipmentExample() {{
            createCriteria().andUidEqualTo(keyId);
        }}).stream().map(CarEquipment::getLabel).collect(Collectors.toList());
        if (labels.size() == 0) {
            return "";
        }
        return labels.get(0) + ".jpeg";
//        return labels.get(0) + ".png";
    }

}
