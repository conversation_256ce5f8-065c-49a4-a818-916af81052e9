package com.sankuai.walle.rmanage.config.util;

import java.lang.reflect.Field;
import java.util.HashMap;

public class ObjectUtil {
    /**
     * 判断该对象是否所有属性为空
     * 返回ture表示所有属性为null，返回false表示不是所有属性都是null
     */
    public static boolean isAllFieldNull(Object object) {
        boolean flag = true;

        Class clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            //设置属性是可以访问的(私有的也可以)
            field.setAccessible(true);
            Object value = null;
            try {
                value = field.get(object);
                // 只要有1个属性为空,那么就返回false
                if (value == null) {
                    flag = false;
                    break;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return flag;
    }

    public static class ObjectFun<T> {

        public void safeSetValueFun(T obj, String name, Long value) throws IllegalAccessException, NoSuchFieldException {
            Field field = obj.getClass().getDeclaredField(name);
            field.setAccessible(true);
            Object history = field.get(obj);
            if (history!=null){
                field.set(obj, history + "," + value);
            } else {
                field.set(obj, String.valueOf(value));
            }
        }

    }

}
