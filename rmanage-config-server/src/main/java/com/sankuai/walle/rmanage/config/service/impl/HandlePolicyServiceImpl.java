package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.org.openapi.model.Match;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.model.domain.items.EmpItems;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.queryservice.domain.base.Paging;
import com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo;
import com.sankuai.walle.dal.mrm_manage.entity.MisGroup;
import com.sankuai.walle.dal.mrm_manage.example.BizHandlePolicyInfoExample;
import com.sankuai.walle.dal.mrm_manage.example.MisGroupExample;
import com.sankuai.walle.dal.mrm_manage.mapper.BizHandlePolicyInfoMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.MisGroupMapper;
import com.sankuai.walle.objects.bo.EmpSimpleInfo;
import com.sankuai.walle.objects.bo.HandleMethodObj;
import com.sankuai.walle.objects.bo.QueryResult;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.request.BizHandlePolicyInfoReq;
import com.sankuai.walle.objects.vo.request.MisGroupReq;
import com.sankuai.walle.objects.vo.res.HandlePolicyInfoQueryRes;
import com.sankuai.walle.rmanage.config.config.BizHandlePolicyRelationshipConfig;
import com.sankuai.walle.rmanage.config.config.ORGOpenSdkConfig;
import com.sankuai.walle.rmanage.config.service.HandlePolicyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HandlePolicyServiceImpl implements HandlePolicyService {

    @Resource
    private BizHandlePolicyInfoMapper bizHandlePolicyInfoMapper;

    @Resource
    private MisGroupMapper misGroupMapper;

    @Autowired
    private ORGOpenSdkConfig orgOpenSdkConfig;

    @Autowired
    private EmpService empService;

    private List<MisGroup> queryMisGroup() {
        MisGroupExample query = new MisGroupExample();
        query.createCriteria().andIsDeletedNotEqualTo(true);
        query.setOrderByClause("update_time DESC");
        return misGroupMapper.selectByExample(query);
    }

    private String getMisGroupMembers(String misGroupName) {
        MisGroupExample query = new MisGroupExample();
        query.createCriteria().andGroupNameEqualTo(misGroupName);
        query.createCriteria().andIsDeletedNotEqualTo(true);
        String res = "";
        try {
            res = misGroupMapper.selectByExample(query).get(0).getGroupMembers();
        } catch (Exception e) {
            log.error("getMisGroupMembers", e);
        }

        return res;
    }

    public List<String> getMisGroupMemberList(String misGroupName) {
        String groupMembers = getMisGroupMembers(misGroupName);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<String> personList = objectMapper.readValue(groupMembers, new TypeReference<List<String>>() {});
            if(personList != null && !personList.isEmpty()){
                List<String> processedList = personList.stream()
                        .map(obj -> obj.split("/")[0])
                        .collect(Collectors.toList());
                return processedList;
            }
        } catch (Exception e) {
            log.error("getMisGroupMemberList is failed, misGroupName = {}",misGroupName,e);
        }

        return new ArrayList<>();
    }

    /**
     * 将mis列表转换成群组名
     * */
    private  String getMisGroupName(String handleMethod) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> handleMethodMap = mapper.readValue(handleMethod, new TypeReference<Map<String, String>>(){});
        Map<String, String> finalHandleMethodMap = new HashMap<>();
        handleMethodMap.forEach((method, methodGroup) -> {
            String groupName = "";
            MisGroupExample query = new MisGroupExample();
            query.createCriteria().andIsDeletedNotEqualTo(true).andGroupMembersLike(methodGroup);
            try {
                List<MisGroup> res = misGroupMapper.selectByExample(query);
                if (res.size() != 0) {
                    groupName = res.get(0).getGroupName();
                } else {
                    log.info("getMisGroupName not match, methodGroup: {}", methodGroup);
                }
            } catch (Exception e) {
                log.error("getMisGroupName", e);
            }
            finalHandleMethodMap.put(method, groupName);
        });
        return mapper.writeValueAsString(finalHandleMethodMap);
    }

    /**
     * 将群组名转换成实际的mis列表
     * */
    private String getGroupMembers(String handleMethod) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, List<String>> handleMethodMap = mapper.readValue(handleMethod, new TypeReference<Map<String, List<String>>>(){});
        Map<String, String> finalHandleMethodMap = new HashMap<>();
        handleMethodMap.forEach((method, methodGroup) -> {
            List<String> groupMembers = new ArrayList<>();
            for(String groupName : methodGroup){
                groupMembers.addAll(getMisGroupMemberList(groupName));
            }
            try {
                String groupMembersStr = mapper.writeValueAsString(groupMembers);
                finalHandleMethodMap.put(method,groupMembersStr );
            } catch (JsonProcessingException e) {
                log.error("writeValueAsString is failed, groupMembers = {}",groupMembers,e);
            }
        });
        return mapper.writeValueAsString(finalHandleMethodMap);
    }

    @Override
    public void addHandlePolicy(BizHandlePolicyInfoReq handlePolicyInfoReq) throws Exception {
        log.info("handlePolicyInfoReq:{}", handlePolicyInfoReq);
        Date now = new Date();
        BizHandlePolicyInfo bizHandlePolicyInfo = new BizHandlePolicyInfo();

        BeanUtils.copyProperties(bizHandlePolicyInfo, handlePolicyInfoReq);
        bizHandlePolicyInfo.setAddTime(now);
        bizHandlePolicyInfo.setUpdateTime(now);
        bizHandlePolicyInfo.setUpdateUser(handlePolicyInfoReq.getCreateUser());
        int insertRes = bizHandlePolicyInfoMapper.insert(bizHandlePolicyInfo);
        if ( insertRes == 0) {
            throw new Exception("insert handlePolicyInfo table Failed");
        }
    }

    @Override
    public  void updateHandlePolicy(BizHandlePolicyInfoReq handlePolicyInfoReq) throws Exception {
        log.info("handlePolicyInfoReq:{}", handlePolicyInfoReq);
        // 参数检查
        if (handlePolicyInfoReq.getId() == null) {
            throw new Exception("id is null");
        }
        if (handlePolicyInfoReq.getCreateUser() != null) {
            throw new Exception("createUser is not null, createUser is not allowed to update");
        }
        if (handlePolicyInfoReq.getUpdateUser() == null) {
            User user = UserUtils.getUser();
            if (user == null) {
                throw new Exception("updateUser is null");
            }
            String mis = UserUtils.getUser().getLogin();
            handlePolicyInfoReq.setUpdateUser(mis);
        }

        Date now = new Date();
        BizHandlePolicyInfo bizHandlePolicyInfo = new BizHandlePolicyInfo();
        BeanUtils.copyProperties(bizHandlePolicyInfo, handlePolicyInfoReq);
        bizHandlePolicyInfo.setUpdateTime(now);
        BizHandlePolicyInfoExample query = new BizHandlePolicyInfoExample();
        query.createCriteria().andIdEqualTo(handlePolicyInfoReq.getId());
        int updateRes = bizHandlePolicyInfoMapper.updateByExampleSelective(bizHandlePolicyInfo, query);
        if ( updateRes == 0) {
            throw new Exception("update handlePolicyInfo table Failed");
        }
    }

    @Override
    public Object queryAccidentAttributes() throws Exception {
        Object config = JSONObject.parse(BizHandlePolicyRelationshipConfig.bizHandlePolicyRelationshipConfig);
        if (config == null) {
            throw new Exception("config is null");
        }
        JSONObject accidentAttributes = (JSONObject)((JSONObject)config).get(CommonConstants.bizAccidentInfoTableName);
        return accidentAttributes;
    }

    @Override
    public Object queryHandleMethodAlias() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, HandleMethodObj> methodObjMap = mapper.readValue(BizHandlePolicyRelationshipConfig.handleMethodAliasConfig, new TypeReference<Map<String, HandleMethodObj>>(){});
        List<MisGroup> misGroupList = queryMisGroup();
        methodObjMap.forEach((method, methodObj) ->   {
            if (methodObj.getOptionValue().isEmpty()) {
                for (MisGroup misGroup : misGroupList) {
                    methodObj.getOptionValue().add(misGroup.getGroupName());
                }
            }
        });
        return methodObjMap;
    }

    @Override
    public Object queryHandlePolicy(Integer page, Integer pageSize) throws Exception {
        try {
            BizHandlePolicyInfoExample query = new BizHandlePolicyInfoExample();
            query.setOrderByClause("is_enable desc, id desc");
            query.createCriteria().andIsDeletedNotEqualTo(true);
            query.page(page-1, pageSize);
            List<BizHandlePolicyInfo> bizHandlePolicyInfoList = bizHandlePolicyInfoMapper.selectByExample(query);
//            bizHandlePolicyInfoList.forEach(bizHandlePolicyInfo -> {
//                try {
//                    bizHandlePolicyInfo.setHandleMethod(getMisGroupName(bizHandlePolicyInfo.getHandleMethod()));
//                } catch (Exception e) {
//                    log.error("queryHandlePolicy, parse group name error, handle_policy id: {}, e: {}", bizHandlePolicyInfo.getId(), e.toString());
//                }
//            });
            HandlePolicyInfoQueryRes res = new HandlePolicyInfoQueryRes();
            res.setItems(bizHandlePolicyInfoList);

            System.out.println(" here ");
            // Todo: 当表太大或频繁查询时，考虑使用缓存
            BizHandlePolicyInfoExample queryItemCount = new BizHandlePolicyInfoExample();
            queryItemCount.createCriteria().andIsDeletedNotEqualTo(true);
            List<BizHandlePolicyInfo> handlePolicyNum = bizHandlePolicyInfoMapper.selectByExample(queryItemCount);
            res.setTotal(handlePolicyNum.size());
            return res;
        } catch (Exception e) {
            log.error("queryHandlePolicy", e);
            throw new Exception("queryHandlePolicy failed");
        }
    }

    @Override
    public Object queryAllEnableHandlePolicy() throws Exception {
        try {
            BizHandlePolicyInfoExample query = new BizHandlePolicyInfoExample();
            query.createCriteria().andIsDeletedNotEqualTo(true).andIsEnableEqualTo(true);
            List<BizHandlePolicyInfo> bizHandlePolicyInfoList = bizHandlePolicyInfoMapper.selectByExample(query);
            for(int i = 0; i< bizHandlePolicyInfoList.size(); i++){
                if (bizHandlePolicyInfoList.get(i).getHandleMethod() != null) {
                    bizHandlePolicyInfoList.get(i).setHandleMethod(getGroupMembers(bizHandlePolicyInfoList.get(i).getHandleMethod()));
                }
            }
            return bizHandlePolicyInfoList;
        } catch (Exception e) {
            log.error("queryHandlePolicy", e);
            throw new Exception("queryHandlePolicy failed");
        }
    }

    @Override
    public Object queryEmpInfos(String misId) throws Exception {
        EmpItems empItems = empService.search(Match.of().mutilMatch(misId, Emp.MIS), null, new Paging());
        QueryResult<EmpSimpleInfo> res = new QueryResult<>();
        empItems.getItems().forEach(emp -> {
            EmpSimpleInfo empSimpleInfo = new EmpSimpleInfo();
            try {
                BeanUtils.copyProperties(empSimpleInfo, emp);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            } catch (InvocationTargetException e) {
                throw new RuntimeException(e);
            }
            res.getItems().add(empSimpleInfo);
        });
        res.setTotal(res.getItems().size());
        return res;
    }

    @Override
    public void addMisGroup(MisGroupReq misGroupReq) throws Exception {
        MisGroup misGroup = new MisGroup();
        BeanUtils.copyProperties(misGroup, misGroupReq);
        misGroup.setUpdateUser(misGroupReq.getCreateUser());
        misGroup.setIsDeleted(false);
        if (misGroupMapper.insert(misGroup) == 0) {
            throw new Exception("insert misGroup table Failed");
        }
    }

    @Override
    public void updateMisGroup(MisGroupReq misGroupReq) throws Exception {
        MisGroup misGroup = new MisGroup();
        BeanUtils.copyProperties(misGroup, misGroupReq);
        misGroup.setUpdateUser(misGroupReq.getUpdateUser());
        MisGroupExample query = new MisGroupExample();
        query.createCriteria().andIdEqualTo(misGroupReq.getId());
        misGroupMapper.updateByExampleSelective(misGroup, query);
    }

    @Override
    public Object queryMisGroup(Integer page, Integer pageSize) throws Exception {
        MisGroupExample query = new MisGroupExample();
        query.setOrderByClause("id desc");
        query.createCriteria().andIsDeletedNotEqualTo(true);
        query.page(page - 1, pageSize);
        List<MisGroup> misGroupList = misGroupMapper.selectByExample(query);
        QueryResult<MisGroup> res = new QueryResult<>();
        res.setItems(misGroupList);

        MisGroupExample queryMisGroupItem = new MisGroupExample();
        queryMisGroupItem.createCriteria().andIsDeletedNotEqualTo(true);
        List<MisGroup> misGroupCountList =  misGroupMapper.selectByExample(queryMisGroupItem);
        res.setTotal(misGroupCountList.size());
        return res;
    }
}