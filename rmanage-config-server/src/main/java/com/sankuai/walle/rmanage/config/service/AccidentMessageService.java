package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.rmanage.config.dto.accident.VehicleRealtimeStatusDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AccidentMessageService {

    public Map<String, String> getCityAndAffiliationFromRecord(String vin);

    public String getCarGroup(String vin);

    public List<String> getGroupMember(Map<String,Object> accidentDetail);

    public VehicleRealtimeStatusDTO callVehicleStatusService(String vin);

    public void reportAccident(Map<String, Object> eventDetailMap);

    public String getFaultInformation(String vehicleName);

    public String getCollisionDetectionAlert(String vin, Long eventTime);

    Map<String, Object> getVehicleHistoryStatus(String vin, Date accidentTime);

    public long gradeGroupScale(Long gid);

    public String getRoadType(Map<String,Object> accidentDetail);

    public String getLocationNameByGps(String locationGps);

}
