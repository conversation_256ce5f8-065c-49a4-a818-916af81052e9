package com.sankuai.walle.rmanage.config.thread;


import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.xframe.threadpool.client.thread.ExecutorWrapper;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.service.AccidentVideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;


@Slf4j
@Component
public class RedisQueueConsumerContainer {
    //redis
    @Resource(name = "ThreadRedisBean")
    private RedisStoreClient redisStoreClient;

    @Resource
    private AccidentVideoService accidentVideoService;

    @Resource
    private DxGroupHandler dxGroupHandler;


    Map<String, RedisQueueConsumer> consumerMap = new HashMap<>();

    // 容器是否初始化完毕的标示
    static Boolean isRun = false;

    RedisThreadPool myRedisThreadPool;

    public void addConsumer(RedisQueueConsumer consumer) {
        if (consumer.getQueueName() != null && !consumerMap.containsKey(consumer.getQueueName())) {
            consumerMap.put(consumer.getQueueName(), consumer);
        }
    }

    public void init() {
        log.info("RedisQueueConsumerContainer begins Initialization");
        isRun = true;
        myRedisThreadPool = RedisThreadPool.getInstance();
       // ExecutorService executorService = ExecutorWrapper.newThreadPoolExecutor("DXGroup-HDVideo");
        consumerMap.forEach((k, v) -> {
            myRedisThreadPool.executor(new RedisQueueListener(v , redisStoreClient, accidentVideoService, dxGroupHandler));
           // executorService.submit(new RedisQueueListener(v , redisStoreClient, accidentVideoService, dxGroupHandler));
        });
    }
}

