package com.sankuai.walle.rmanage.config.thread;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RedisQueueListerConfig implements ApplicationRunner {

    @Resource
    RedisQueueConsumerContainer redisQueueConsumerContainer;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        RedisAccidentConsumer redisAccidentConsumer = new RedisAccidentConsumer();
        //设置待监听队列名称
        redisAccidentConsumer.setQueueName("HDvideoParamsQueue");
        redisQueueConsumerContainer.addConsumer(redisAccidentConsumer);

        redisQueueConsumerContainer.init();
        System.out.println("redisQueueConsumerContainer.init()");
    }
}
