package com.sankuai.walle.rmanage.config.controller;


import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigVehicleTaskStatus;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.dto.config.AutoBatchPreConfigResponseDto;
import com.sankuai.walle.rmanage.config.service.AutoConfigService;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.MafkaRealtimeService;
import com.sankuai.walle.rmanage.config.service.VehicleDataBusService;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.service.infrastructureService.SecretKeyService;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
@RestController
@RequestMapping(path={"/eve/cmdb/rest"})
@Slf4j
public class AutoConfigController {
    @Resource
    MafkaRealtimeService mafkaRealtimeService;
    @Resource
    MyAukService myAukService;

    @Resource
    ConfigService configService;

    @Resource
    AutoConfigService autoConfigService;

    @Resource
    SecretKeyService secretKeyService;

    @Resource
    VehicleDataBusService  vehicleDataBusService;


    @PostMapping(path = "/api/cmdb/car/auto/auto_config/excel")
    public AutoBatchPreConfigResponseDto getImportConfig(
            HttpServletRequest request,
            @RequestBody List<SendExcelDeviceConfigReq> body
            ) {

        AutoBatchPreConfigResponseDto resData = new AutoBatchPreConfigResponseDto();
        resData.setCode( CommonConstants.ERROR_CODE);
        if(body == null || body.size() == 0) {
            resData.setMsg( "传入的excel数据不能为空");
            return resData;
        }

        List<SendExcelDeviceConfigReq> noRepeatBody = body.stream().collect(Collectors.toMap(
                        x -> x.getVin() + "-" + x.getConfigName(),
                        x -> x,
                        (oldValue, newValue) -> newValue
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        resData = autoConfigService.handleExcelConfig(noRepeatBody);
        resData.setCode(CommonConstants.SUCCEED_CODE);
        resData.setMsg("成功");
        return resData;
    }

    @PostMapping(path = "/api/cmdb/car/device/auto_config/query")
    public ResData queryConfig(HttpServletRequest request, @RequestBody List<String> body) {
        if(body == null || body.size() == 0) {
            return ResData.failedWithMsg("查询的Vin不能为空");
        }
        Map<String, AutoConfigVehicleTaskStatus> data = autoConfigService.queryTaskByVinList(body);
        return ResData.successWithData(data);

    }



    // eve配置的批量下发, fb，rtk，car_info
    @PostMapping(path = "/api/cmdb/car/auto/batch/eve")
    public ResData sendEveConfig(HttpServletRequest request, @RequestBody List<String> vins, @RequestParam String configName) {
        if (vins == null || vins.size() == 0) {
            return ResData.failedWithMsg("传入的vins不能为空");
        }
        if (configName == null || configName.isEmpty()) {
            return ResData.failedWithMsg("传入的配置名称不能为空");
        }
        String mis = UserUtils.getUser().getLogin();
        String msg = autoConfigService.sendEveConfig(vins, configName, mis);
        if(Objects.equals(msg, CommonConstants.succeedMsg)) {
            return ResData.successWithMsg();
        } else {
            return ResData.failedWithMsg(msg);
        }
    }

    // eve配置的批量下发, fb，rtk，car_info 结果查询
    @PostMapping(path = "/api/cmdb/car/auto/batch/eve/query")
    public ResData queryEveConfig(HttpServletRequest request, @RequestBody List<String> vins,  @RequestParam String configName) {
        List<CarDeviceConfigRes> data = autoConfigService.queryEveConfig(vins, configName);
        return ResData.successWithData(data);
    }

    // 批量发送配置到s3
    @RequestMapping(path = {"/api/cmdb/auto/car/config/s3/gender"}, method = RequestMethod.POST)
    public ResData uploadS3(@RequestBody List<String> vins) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            autoConfigService.uploadS3(vins);
            res.code = CommonConstants.SUCCEED_CODE;
            res.msg = "成功";
        } catch (Exception e) {
            res.msg = e.toString();
        }
        return res;
    }
    // 批量发送配置到s3，结果查询
    @RequestMapping(path = {"/api/cmdb/auto/car/config/s3/query"}, method = RequestMethod.POST)
    public ResData queryS3(@RequestBody List<String> vins) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            res.data = autoConfigService.queryS3(vins);
            res.code = CommonConstants.SUCCEED_CODE;
            res.msg = "成功";
        } catch (Exception e) {
            res.msg = e.toString();
        }
        return res;
    }

    // 触发脚本下发到车
    @RequestMapping(path = {"/api/cmdb/auto/car/config/script"}, method = RequestMethod.POST)
    public ResData sendScript(@RequestBody List<String> vins) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            autoConfigService.sendScript(vins);
            res.code = CommonConstants.SUCCEED_CODE;
            res.msg = "成功";
        } catch (Exception e) {
            res.msg = e.toString();
        }
        return res;

    }

    @RequestMapping(path = {"/api/cmdb/auto/car/config/sk"}, method = RequestMethod.POST)
    public ResData querySk(@RequestBody List<String> vins) {
        Map data = secretKeyService.batchGetGenerateResult(vins);
        return ResData.successWithData(data);
    }

    @RequestMapping(path = {"/api/cmdb/auto/car/config/generate/sk"}, method = RequestMethod.POST)
    public ResData generateSk(@RequestBody List<String> vins) {
        autoConfigService.batchCreateSecretKey(vins);
        return ResData.successWithData(null);
    }

    @RequestMapping(path = {"/api/cmdb/auto/car/config/vehicle/error"}, method = RequestMethod.GET)
    public ResData getVinStatus(@RequestParam String vin) {
        VehicleDataBusVTO vehicleInfoString = vehicleDataBusService.getVehicleInfoString(vin);
        List<VehicleDataBusVTO.UnExpectedInfo> data = new ArrayList<>();
        if(vehicleInfoString.getIssue() != null) {
            data.addAll(vehicleInfoString.getIssue().getErrorList());
            data.addAll(vehicleInfoString.getIssue().getFatalList());
        }
        return ResData.successWithData(data);
    }

    // 根据vin查carName
    @RequestMapping(path = {"/api/cmdb/auto/car/config/query/car_name"}, method = RequestMethod.POST)
    public ResData queryCarName(@RequestBody List<String> vins) {
        Map<String, String> data = autoConfigService.queryVehicleNameByVin(vins);
        return ResData.successWithData(data);
    }
}