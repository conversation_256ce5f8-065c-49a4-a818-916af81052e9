package com.sankuai.walle.rmanage.config.service.appService;

import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;

import java.util.List;

public interface CarDeviceConfigService {
    void setStatus(CarDeviceConfigRes child, CarDeviceConfig carDeviceConfig);

    // 组装车辆、配置、设备内容
    CarDeviceConfigRes createRes(CarDeviceConfig carDeviceConfig);

    // 筛选有权限的配置项id
    List<Long> filterConfigIds();

    // 把下发的信息转换成pb
    CloudCgf.CloudToVehicleReleaseConfig.Builder handleTargetData(CarDeviceConfig target);

    // 通过auk的mqtt下发配置
    // 通过mqtt下发的属性和配置项，都会走 car_device_config 表
    void configToMqtt(CarDeviceConfig target);

    // 直接下发，配置没有版本
    void configToMqtt(CarDeviceConfig target, String fileType);

    // 通过auk的mqtt下发配置
    // 通过mqtt下发的属性和配置项，都会走 car_device_config 表
    void retryConfigToMqtt(CarDeviceConfig target);
}
