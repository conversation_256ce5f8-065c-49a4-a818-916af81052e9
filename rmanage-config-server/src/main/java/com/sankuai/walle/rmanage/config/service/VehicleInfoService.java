package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.dal.walle_data_center.entity.VehicleInfo;

import java.util.List;

public interface VehicleInfoService {
    void insertVehicleInfo(List<CarObjects> carObjectsList);

    VehicleInfo fetchVehicleInfoByVin(String vin);

    void updateVehicleInfo(String vin);

    void updateVehicleStatus(String vin);

    Byte fetchVehicleStatus(String vin);
}
