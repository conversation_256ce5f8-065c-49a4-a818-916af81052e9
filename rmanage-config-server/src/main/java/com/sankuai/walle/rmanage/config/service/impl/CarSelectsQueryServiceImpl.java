package com.sankuai.walle.rmanage.config.service.impl;

import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import com.sankuai.walle.rmanage.config.service.CarSelectsQueryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CarSelectsQueryServiceImpl implements CarSelectsQueryService {

    @Resource
    CarSelectsMapper carSelectsMapper;
    @Override
    public List<CarSelects> fetchCarSelectsByBelone(String belong){
        String finalBelong = belong;
        return carSelectsMapper.selectByExample(new CarSelectsExample(){{
            createCriteria().andBelongEqualTo(finalBelong);
        }});
    }

    @Override
    public String getSelect(String key, String type_id){
        String res = "";
        if(type_id!=null) {
            CarSelectsExample example = new CarSelectsExample();
            example.createCriteria().andBelongEqualTo(key).andTypeEqualTo(type_id);
            List<CarSelects> car_size_type = carSelectsMapper.selectByExample(example);
            if (car_size_type.size() > 0) {
                res = car_size_type.get(0).getName();
            } else {
                res = "";
            }
        }
        return res;
    }

}
