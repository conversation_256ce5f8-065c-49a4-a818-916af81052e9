package com.sankuai.walle.rmanage.config.aviator;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;

@Slf4j
public class eventFilterFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Map<String,Object> eventDetailMap = (Map<String, Object>) FunctionUtils.getJavaObject(arg1, env);
        if(!eventDetailMap.containsKey("status")){
            return AviatorBoolean.valueOf(false);
        }
        int status = (int) eventDetailMap.get("status");
        if(status == 0){
            return AviatorBoolean.valueOf(true);
        }
        return AviatorBoolean.valueOf(false);
    }
    @Override
    public String getName() {
        return "eventFilterFunction";
    }
}
