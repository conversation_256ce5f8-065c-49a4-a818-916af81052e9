package com.sankuai.walle.rmanage.config.thread;

import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.service.AccidentVideoService;
import com.sankuai.walle.rmanage.config.thread.dto.QueueParamDTO;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class RedisCollisionDetectConsumer implements RedisQueueConsumer {

    private String QueueName;

    public void setQueueName(String name){
        this.QueueName = name;
    }

    @Override
    public String getQueueName() {
        return this.QueueName;
    }

    @Override
    public void getMessage(QueueParamDTO queueParamDTO,
                           AccidentVideoService accidentVideoService,
                           DxGroupHandler dxGroupHandler) {
        HashMap<String, Long> queryTimeConfig =  dxGroupHandler.getQueryTimeConfig();
        log.info("thread is start! queueParamDTO = {}, queryTimeConfig = {}", queueParamDTO,queryTimeConfig);
        Long maxRequestTime = queryTimeConfig.get("maxRequestTime");
        Long validRequestTime = queryTimeConfig.get("validRequestTime");
        //有效时间段内的时间间隔（第一段）
        Long firstRequestInterval = queryTimeConfig.get("firstRequestInterval");
        //第二段时间段内的时间间隔
        Long secondRequestInterval = queryTimeConfig.get("secondRequestInterval");
        Date startTime = new Date();

        List<String> strList = Arrays.asList(queueParamDTO.getVin().split("/"));
        String vin= strList.get(0);
        String vehicleId = strList.get(1);
        String vehicleName = strList.get(2);
        String loopUrl = queueParamDTO.getLoopVideoMsg();
        String concatUrl = queueParamDTO.getConcatVideoMsg();

        //表示超过一分钟后的第一次请求
        Boolean firstFlag  = false;
        while(new Date().getTime() - startTime.getTime() < maxRequestTime){

            Long currentTime = new Date().getTime();
            if(currentTime - startTime.getTime() <= validRequestTime){
                //currentTime 小于 等于 1 分钟的时候 10s间隔发送一次数据, 如果视频获取成功，则直接返回，并结束进程
                try{
                    int videoResponse = accidentVideoService.getCollisionDetectVideoResponse(queueParamDTO.getAccidentTime(), vin);
                    if (videoResponse == 1) {
                        log.info("queueParamDTO = {} is sent to group ", queueParamDTO);
                        //发送群消息,并结束进程
                        dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupUrlText(queueParamDTO.getAccidentTime(), vehicleId, vehicleName, loopUrl, concatUrl));
                        break;
                    }
                    else if(videoResponse == -1){
                        //当返回失败，或者车辆离线，则发送消息到大象群，并结束线程
                        log.info("queueParamDTO is {} , task is failed", queueParamDTO);
                        dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupErrorText(queueParamDTO.getAccidentTime(), vehicleId, vehicleName,3));
                        break;
                    }

                }
                catch (Exception e){
                    log.error("threadDTO.accidentVideoService.getAccidentVideoResponse is failed !",e);
                }
                delayXXXms(firstRequestInterval);
            }
            else{
                //  1分钟的时候 < currentTime < 10分钟的时候 30s间隔发送一次数据
                try{
                    int  videoResponse =  accidentVideoService.getCollisionDetectVideoResponse(queueParamDTO.getAccidentTime(), vin);
                    if(videoResponse == 1){
                        log.info("queueParamDTO = {} is sent to group, >= 1 分钟", queueParamDTO);
                        dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupUrlText(queueParamDTO.getAccidentTime(), vehicleId, vehicleName, loopUrl, concatUrl));
                        break;
                    }
                    else if(videoResponse == 0 && firstFlag == false){
                        //发送获取不到的原因
                        log.info("queueParamDTO = {}, 一分钟内未捞到视频 ", queueParamDTO);
                        dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupErrorText(queueParamDTO.getAccidentTime(), vehicleId, vehicleName,1));
                        firstFlag = true;
                    }

                }
                catch (Exception e){
                    log.error("threadDTO.accidentVideoService.getAccidentVideoResponse is failed !",e);
                }
                delayXXXms(secondRequestInterval);
            }
        }
        log.info("thread is finished ! queueParamDTO = {}", queueParamDTO);
    }
    @Override
    public void error(String error) {
    }

    public void delayXXXms(Long size){
        try {
            Thread.sleep(size); // 暂停30秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 重新设置中断状态
            log.error("threadDTO.accidentVideoService.getAccidentVideoResponse is failed !",e);
        }
    }

    private String getDXGroupUrlText(Long eventTime, String vehicleId, String vehicleName, String loopUrl, String concatUrl ){

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String eventTimeStr = sdf.format(new Date(eventTime*1000));

        String text = String.format(
                "车辆: %s / %s\n" +
                "碰撞时间: %s\n"+
                "高清事故视频:\n" +
                        "[[环视视频|%s]]\n" +
                        "[[前后左右拼接视频|%s]]",
                vehicleName,vehicleId,
                eventTimeStr,
                loopUrl,concatUrl
        );
        return text;
    }

    private String getDXGroupErrorText(Long eventTime, String vehicleId, String vehicleName, int type){

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String eventTimeStr = sdf.format(new Date(eventTime*1000));
        String errorText = String.format(
                "车辆: %s / %s\n" +
                        "碰撞时间: %s\n"+
                        "高清事故视频:\n",
                vehicleName,vehicleId,
                eventTimeStr
        );

        switch (type){
            case 1:
                errorText = errorText + "视频回收中,请耐心等待";
                break;
            case 3:
                errorText = errorText + "视频获取失败，原因: 车辆不在线";
                break;
            case 4:
                errorText = errorText + "请求超时，系统自动重试中，请耐心等待";
                break;
        }
        return errorText;
    }

}
