package com.sankuai.walle.rmanage.config.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Util {
    public static String getMD5(String input) {
        try {
            // 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算MD5值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String input = "username: ADMIN\n" +
                "address: *************\n";
        String md5 = getMD5(input);
        System.out.println("MD5值: " + md5);
        System.out.println(md5.equals("085d1aba6b00b3fcc8004303f6a0830a"));
    }
}
