package com.sankuai.walle.rmanage.config.helper;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sankuai.walle.rmanage.proto.target.DeviceTreeModel;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;

@Slf4j
public class ProtobufHandleHelper {

    public static <K> List<K> deserializeDeviceTree(byte[] deviceTree,
                                                    BiFunction<DeviceTreeModel.Vehicle, DeviceTreeModel.Device, K> convertFunction) {
        if (deviceTree == null) {
            return null;
        }
        List<K> deviceBoList = new ArrayList<>();
        try {
            DeviceTreeModel.Vehicle vehicle = DeviceTreeModel.Vehicle.parseFrom(deviceTree);
            log.info("ProtobufHandleHelper#deserializeDeviceTree: " + vehicle.toString());
            parseDeviceTree(vehicle.getDevlistList(), deviceBoList, vehicle, convertFunction);
            return deviceBoList;
        } catch (InvalidProtocolBufferException e) {
            log.error("ProtobufHandleHelper#deserializeDeviceTree error.", e);
        }

        return null;
    }

    private static <K> void parseDeviceTree(List<DeviceTreeModel.Device> deviceList,
                                            List<K> deviceBOList,
                                            DeviceTreeModel.Vehicle vehicle,
                                            BiFunction<DeviceTreeModel.Vehicle, DeviceTreeModel.Device, K> convertFunction) {
        if (deviceList == null) {
            return;
        }
        for (DeviceTreeModel.Device device : deviceList) {
            deviceBOList.add(convertFunction.apply(vehicle, device));
            parseDeviceTree(device.getSubdevlistList(), deviceBOList, vehicle, convertFunction);
        }
    }
}
