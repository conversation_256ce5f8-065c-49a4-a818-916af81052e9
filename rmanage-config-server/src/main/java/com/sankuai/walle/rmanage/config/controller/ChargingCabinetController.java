package com.sankuai.walle.rmanage.config.controller;

import com.sankuai.auk.open.api.contracts.enums.AukResultCode;
import com.sankuai.banma.auk.server.sdk.request.thing.ThingInvokeServiceRequest;
import com.sankuai.banma.auk.server.sdk.response.thing.ThingInvokeServiceResponse;
import com.sankuai.banma.auk.server.sdk.util.JsonUtil;
import com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand;
import com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.convertor.ChargingCabinetConvert;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.repository.ChargingCabinetRepsitory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path={"/eve/cmdb/rest/"})
@Slf4j
public class ChargingCabinetController {
    
    @Resource
    ChargingCabinetRepsitory chargingCabinetRepsitory;
    @Resource
    MyAukService myAukService;

    @GetMapping(path = "charging/cabinet/status")
    public ResData getImportConfig(@RequestParam String deviceId){
        // 查询数据库中，该vin的最后一条数据，获取其中的开门状态 Remote_ELock_Control
        BatterySwapCabinetProperty dao = chargingCabinetRepsitory.queryDaoByDeviceId(deviceId);
        if(dao == null) {
            return ResData.successWithData(-1);
        }
        String arg = dao.getArg();
        if(!StringUtils.isBlank(arg)) {
            // json字符串处理
            Map jsonMap = JsonUtil.fromJsonString(arg, Map.class);
            String remoteELockControl = String.valueOf(jsonMap.get("Remote:Remote_ELock_Control"));
            return ResData.successWithData(remoteELockControl);
        }
        return ResData.successWithData(-1);
    }

    @PostMapping(path = "charging/cabinet/control/door")
    public ResData openDoor(@RequestParam String deviceId){
        ResData res = new ResData();
        // 下发指令
        ThingInvokeServiceRequest request = ChargingCabinetConvert.buildOpenDoorRequest(deviceId);
        ThingInvokeServiceResponse data = myAukService.invokeThingModel(request);
        // 入库指令数据，等待返回结果，如果data返回成功，则命令入库
        if(data.getCode() == AukResultCode.OK.getCode()) {
            chargingCabinetRepsitory.insertCommand(request.getDeviceKey(), request.getCommandContent(), data.getRequestId());
        }
        res.setCode(data.getCode());
        res.setMsg(data.getMsg());
        return res;
    }

    // 查询充电柜门的开门结果
    @GetMapping(path = "charging/cabinet/control/door/status")
    public ResData getDoorResult(@RequestParam String deviceId){
        // 查询数据库中的指令结果
        List<BatteryCabinetCommand> list = chargingCabinetRepsitory.queryCommandByDeviceKey(deviceId);
        if(list.isEmpty()) {
            return ResData.successWithData(-1);
        }
        BatteryCabinetCommand dao = list.get(0);
        String statusMsg = dao.getStatusMsg();
        Map map = JsonUtil.fromJsonString(statusMsg, Map.class);
        return ResData.successWithData(map);
    }
}
