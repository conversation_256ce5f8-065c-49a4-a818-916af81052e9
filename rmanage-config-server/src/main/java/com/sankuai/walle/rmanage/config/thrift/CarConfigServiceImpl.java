package com.sankuai.walle.rmanage.config.thrift;

import com.alibaba.fastjson.JSONObject;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.banma.auk.server.sdk.response.DeviceSecretResponse;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.cmdb.thrift.model.*;
import com.sankuai.walle.cmdb.thrift.service.CarConfigThriftService;
import com.sankuai.walle.common.Status;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarConfigExample;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AukCert;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.rmanage.config.service.AssetService;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.*;

@MdpThriftServer(
        serviceInterface = CarConfigThriftService.Iface.class,
        useSinglePortMultiServiceMode = true,
        port = 9001
)
@Slf4j
public class CarConfigServiceImpl implements CarConfigThriftService.Iface {

    @Resource
    MyAukService myAukService;
    @Resource
    AssetService service;
    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;

    @Autowired(required = false)
    @Qualifier("squirrel")
    RedisStoreClient redisStoreClient;
    // 给octo提供的下发的测试接口
    @Override
    public void sendConfigToMqtt()  {
        // 测试定时任务
//        try {
//            service.syncRemoteObjectFromOpenAsset();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        // 测试海雀mqtt下发的方法
//        AukCallback callback = new AukCallback() {
//            @Override
//            public void onSuccess(Object o) {
//                log.info("配置下发成功");
//            }
//            @Override
//            public void onFailure(Object o, Throwable throwable) {
//                // 配置下发失败，什么也不做
//                log.info("配置下发失败：【{}】", JSONObject.toJSON(o));
//            }
//        };
//        myAukService.configToMqtt("walle00001",new byte[0xFF], callback);
        // 测试redis
        String t="carManage";
        StoreKey key = new StoreKey("evaseye_metrics", 1, t);
        System.out.println(key);
        redisStoreClient.set(key, "eve", 10);
        String result = redisStoreClient.get(key);
        System.out.println(result);
    }

    // 根据vin查询设备SK
    @Override
    public NormalResp getVechildSecret(String vin) {
        NormalResp res = new NormalResp();
        Status status = new Status();
        res.status = status;
        status.code= CommonConstants.ERROR_CODE;
//        if(!this.checkVin(vin)){
//            return res;
//        }
        try {
            DeviceSecretResponse secretResponse = myAukService.queryDeviceSecret(vin);
            status.code = CommonConstants.SUCCEED_CODE;
            res.content = JSONObject.toJSONString(secretResponse);
        }catch (Exception e){
            log.error("获取auk对应的secret错误：【{}】",JSONObject.toJSON(e));
        }
        return res;
    }

    // 根据vin查询设备证书
    @Override
    public NormalResp getVechildCert(String vin) {
        NormalResp res = new NormalResp();
        Status status = new Status();
        res.status = status;
        status.code= CommonConstants.ERROR_CODE;
//        if(!this.checkVin(vin)){
//            return res;
//        }
        try {
            AukCert cert = myAukService.queryDeviceCert(vin);
            status.code = CommonConstants.SUCCEED_CODE;
            res.content = JSONObject.toJSONString(cert);
        }catch (Exception e){
            log.error("获取auk对应的secret错误：【{}】",JSONObject.toJSON(e));
        }
        return res;
    }

    @Resource
    CarConfigMapper carConfigMapper;
    @Resource
    ConfigService configService;

    @Override
    public NormalResp sendConfigApi(ApiConfigReq req) throws TException {
        NormalResp res = new NormalResp();
        Status status = new Status();
        res.status = status;
        if (ConfigConstant.ConfigApiWhiteList.contains(req.getName())) {
            configService.sendConfigToCar(req.getConfigId(), req.getDeviceId(), req.getUserMis(), req.getTaskName(), req.getVins());
            status.setCode(CommonConstants.SUCCEED_CODE);
        }
        return res;
    }

    @Override
    public NormalResp getCarConfigApi(ApiCarConfigReq req) throws TException {
        NormalResp res = new NormalResp();
        Status status = new Status();
        res.status = status;
        if (ConfigConstant.ConfigApiWhiteList.contains(req.getName())) {
            List<String> vins = req.getVins();
            HashMap<String,HashMap<String,Long>> map = new HashMap<>();
            List<CarDeviceConfig> cars = carDeviceConfigMapper.selectByExample(new CarDeviceConfigExample() {{
                createCriteria().andVinIn(vins);
            }});
            for ( CarDeviceConfig car:cars ) {
                map.put(car.getVin(), new HashMap<String,Long>(){{
                    put("now", car.getConfigId());
                    put("last", car.getConfigIdLast());
                }});
            }
            status.setCode(CommonConstants.SUCCEED_CODE);
            res.setContent(JSONObject.toJSONString(map));
            return res;
        }
        return res;
    }

    @Override
    public NormalResp getNameConfigApi(ApiNameConfigReq req) throws TException {
        NormalResp res = new NormalResp();
        Status status = new Status();
        res.status = status;
        if (ConfigConstant.ConfigApiWhiteList.contains(req.getName())) {
            List<CarConfig> data = carConfigMapper.selectByExample(new CarConfigExample() {{
                createCriteria().andNameEqualTo(req.getName());
            }});
            status.setCode(CommonConstants.SUCCEED_CODE);
            res.setContent(JSONObject.toJSONString(data));
            return res;
        }
        return res;
    }

    @Override
    public VinConfigResp getCarConfigContentApi(ApiCarConfigReq req) throws TException {
        VinConfigResp res = new VinConfigResp();
        Status status = new Status();
        res.status = status;
        if (ConfigConstant.ConfigApiWhiteList.contains(req.getName())) {
            List<String> vins = req.getVins();
            List<VinConfigModel> map = this.fetchConfig(vins, req.getName());
            status.setCode(CommonConstants.SUCCEED_CODE);

            res.setData(map);
            return res;
        }
        return res;
    }

    @Override
    public VinConfigResp getAllCarConfigContentApi(ApiCarConfigReq req) throws TException {
        VinConfigResp res = new VinConfigResp();
        Status status = new Status();
        res.status = status;
        if (ConfigConstant.ConfigApiWhiteList.contains(req.getName())) {
            List<VinConfigModel> list = new ArrayList<>();
            // todo： 这个接口要加缓存，可以缓存5分钟
            List<CarDeviceConfig> cars = carDeviceConfigMapper.selectByExampleWithBLOBs(new CarDeviceConfigExample() {{
                createCriteria().andNameEqualTo(req.getName());
            }});
            for ( CarDeviceConfig car:cars ) {
                VinConfigModel model = new VinConfigModel();
                model.setVin(car.getVin());
                com.sankuai.walle.cmdb.thrift.model.CarConfig config = new com.sankuai.walle.cmdb.thrift.model.CarConfig();
                config.setConfig(car.getConfig());
                config.setName(req.getName());
                model.setConfig(config);
                list.add(model);
            }
            status.setCode(CommonConstants.SUCCEED_CODE);
            res.setData(list);
            return res;
        }
        return res;
    }

    private List<VinConfigModel> fetchConfig(List<String> vins,String name) {
        List<VinConfigModel> list = new ArrayList<>();
        List<CarDeviceConfig> cars = carDeviceConfigMapper.selectByExampleWithBLOBs(new CarDeviceConfigExample() {{
            createCriteria().andVinIn(vins).andNameEqualTo(name);
        }});
        for ( CarDeviceConfig car:cars ) {
            VinConfigModel model = new VinConfigModel();
            model.setVin(car.getVin());
            com.sankuai.walle.cmdb.thrift.model.CarConfig config = new com.sankuai.walle.cmdb.thrift.model.CarConfig();
            config.setConfig(car.getConfig());
            config.setName(name);
            model.setConfig(config);
            list.add(model);
        }
        return list;
    }

    public Boolean checkVin(String vin) {
        List<CarObjects> cars = carObjectsMapper.selectByExample(new CarObjectsExample() {{
            createCriteria().andVinEqualTo(vin);
        }});
        return cars.size() > 0;
    }
}