package com.sankuai.walle.rmanage.config.dto.vehicleManage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarManageDTO {

    /**
     * 车辆名称
     */
    @JsonProperty("vehicle_name")
    private String vehicleName;

    /**
     * 车牌号
     */
    @JsonProperty("license_no")
    private String licenseNo;

    /**
     * 一级车型
     */
    @JsonProperty("first_class_model")
    private String firstClassModel;

    /**
     * 二级车型
     */
    @JsonProperty("second_class_model")
    private String secondClassModel;

    /**
     * 所属场地
     */
    @JsonProperty("place_name")
    private String placeName;

    /**
     * 所属城市
     */
    @JsonProperty("city_name")
    private String cityName;

    /**
     * 数据更新时间
     */
    @JsonProperty("update_time")
    private Long updateTime;

    @JsonProperty("scrap")
    private Boolean scrap;

}
