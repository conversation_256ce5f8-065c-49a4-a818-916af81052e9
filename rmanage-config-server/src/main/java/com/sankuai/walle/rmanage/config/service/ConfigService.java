package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.eve.entity.ConfigPermission;
import com.sankuai.walle.objects.vo.request.RollbackReq;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.dto.config.ConfigPermissionDTO;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ConfigService {
    // 解析变量，获取配置内容
//    String getConfigContent(String input, String vin);

    // 发送配置到车，使用版本
    List<CarDeviceConfig> sendConfigToCar(Long config_id, Long device_id, String userMis,
                                          String taskName, List<String> vinsList
    );
    // 发送配置到车，不使用版本
    List<CarDeviceConfig> sendConfigToCar(List<SendExcelDeviceConfigReq> body);

    // 回滚配置
    void rollbackFun(RollbackReq body);

    // 发送查询请求到车
    void queryConfigAsync(List<String> vins);
    // 查询配置回捞的结果
    ResData configQueryList(int page, int pageSize, List<String> vins, String configName);

    public HashMap<String,String> queryConfigSync(List<String> vins, String configName) throws Exception;

    // 关于autocar-S3的配置，修改或新增车管的车辆，可触发修改，并更新version文件，记录日志
    // 发送配置到S3
    void sendCarInfoConfigToS3(String vin, String name) throws IOException;

    // 获取S3上autocar的配置
    String getAutocarConfigFromS3(String s3Key) throws IOException;


    List getAutocarConfigListFromS3(String carName);

    void sendConfigToS3(String objName, String data) throws IOException;

    void deleteConfigFromS3(String objName) throws IOException;

    // 分页查询配置的权限列表
    Map<String, List<String>> getConfigAuthList();


    // 获取当前用户有权限的配置
    List<String> getConfigAuth();

    // 新增权限
    void addConfigAuth(ConfigPermission configPermission);

    // 更新权限的人员
    void updateConfigAuth(ConfigPermissionDTO body);
}
