package com.sankuai.walle.rmanage.config.util;

import com.dianping.zebra.group.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
@Slf4j
public class DateUtils {

    public final static String UTC_DATE_FORMAT_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";

    public final static String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public final static String DATE_FORMAT_MD = "MMdd";

    public static final Integer ONE_THOUSAND = 1000;
    /**
     * 线程安全的时间格式化器
     */
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_THREAD_LOCAL =
            ThreadLocal.withInitial(() -> new SimpleDateFormat(DATE_FORMAT_PATTERN));


    public static int daysBetween(Date startDate, Date endDate) {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startDate);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        long startMillis = startCalendar.getTimeInMillis();
        long endMillis = endCalendar.getTimeInMillis();
        long diffMillis = endMillis - startMillis;
        return (int) (diffMillis / (24 * 60 * 60 * 1000));
    }

    public static Date addDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        return calendar.getTime();
    }

    public static void main(String[] args) {
        Date now = new Date();
        Date targetDate = addDays(new Date(), -3);
        System.out.println(targetDate);
        int days = daysBetween(now, targetDate);
        System.out.println(days);
    }

    /**
     * utc 转 本地时间
     *
     * @param utcTimeString
     * @return localTime
     */
    public static String utcToLocalTime(String utcTimeString) {
        if (StringUtils.isBlank(utcTimeString)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(UTC_DATE_FORMAT_PATTERN);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date utcDate = sdf.parse(utcTimeString);
            sdf.applyPattern(DATE_FORMAT_PATTERN);
            sdf.setTimeZone(TimeZone.getDefault());
            String localTimeString = sdf.format(utcDate);
            return localTimeString;
        } catch (ParseException e) {
            log.error("utcToLocalTime is error, utcTimeString = {}", utcTimeString, e);
        }
        return "";
    }

    /**
     * 获取当前时间(格式为long,单位为秒)
     *
     * @return
     */
    public static Long getCurrentTime() {
        return new Date().getTime()/ONE_THOUSAND;
    }

    /**
     * 格式化时间
     * @param date 时间
     * @return 格式化后的时间
     */
    public static String format(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_FORMAT_PATTERN).format(date);
    }

    /**
     * 格式化时间，支持自定义格式
     * @param date 时间
     * @param pattern 时间格式化模版
     * @return 格式化后的时间
     */
    public static String format(Date date, String pattern) {
        if (date == null || pattern == null) {
            return null;
        }
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * 线程安全的时间戳格式化方法 (格式: yyyy-MM-dd HH:mm:ss)
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串
     */
    public static String formatTimestampSafe(long timestamp) {
        Date date = new Date(timestamp);
        return DATE_FORMAT_THREAD_LOCAL.get().format(date);
    }

}
