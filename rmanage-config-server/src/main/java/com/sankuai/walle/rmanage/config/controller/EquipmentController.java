package com.sankuai.walle.rmanage.config.controller;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.util.IOUtils;
import com.sankuai.walle.dal.mrm_manage.mapper.CarEquipmentMapper;
import com.sankuai.walle.objects.vo.request.CarEquipmentReq;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.CarEquipmentService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.*;

@RestController
@RequestMapping("/eve/cmdb/rest/equipment")
@Log4j
public class EquipmentController {


    @Autowired
    CarEquipmentService carEquipmentService;

    @Autowired
    @Qualifier("s3PlusClient0")
    private AmazonS3 amazonS3;

    @Value("${es.Index}")
    private String esIndex;

    @Value("${s3.bucket.name}")
    private String bucketName;

    @RequestMapping(path = {"/insertOrUpdate"}, method = RequestMethod.POST)
    public ResData insertOrUpdate(@RequestBody List<CarEquipmentReq> jsonData){
        return carEquipmentService.insertOrUpdate(jsonData);
    }
    @RequestMapping(path = {"/getByUid"}, method = RequestMethod.GET)
    public ResData getByUid(@RequestParam String uid) {
        return carEquipmentService.getByUid(uid);
    }
    @RequestMapping(path = {"/getList"}, method = RequestMethod.GET)
    public ResData getList(@RequestParam Integer pageNum,@RequestParam Integer pageSize) {
        return carEquipmentService.getList(pageNum,pageSize);
    }
    @RequestMapping(path = {"/getCity"}, method = RequestMethod.GET)
    public String getCityName(@RequestParam String city) {
        return carEquipmentService.getCityName(city);
    }
    @RequestMapping(path = {"/bulkSearch"}, method = RequestMethod.POST)
    public ResData getBulkSearch(@RequestBody List<Map<String, String>> uids) {
        return carEquipmentService.bulkSearch(esIndex,uids);
    }

    @RequestMapping(path = {"/add-image"}, method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResData addImage(@RequestParam("image")MultipartFile image, @RequestParam("label") String label, @RequestParam("t") String t) throws Exception {
        ResData resData = new ResData();
        log.info("keyId:" + label);
        log.info("t:" + t);
        String objectName = new String();
        if (t.equals("top")) {
            objectName = "top_1000/top" + label + ".jpeg";
        } else if (t.equals("bot")) {
            objectName = "bot_1000/bot" + label + ".jpeg";
        } else {
            resData.code = 601;
            resData.msg = "t is not right";
            return resData;
        }
        byte[] input = image.getBytes();

        amazonS3.putObject(bucketName, objectName, new ByteArrayInputStream(input), null);
        resData.code = 200;
        resData.msg = "ok";
        resData.data = objectName;
        return resData;
    }

    @RequestMapping(path = {"/get-image"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ResponseBody
    public ResponseEntity<byte[]> getImage(@RequestParam("label") String label, @RequestParam("t") String t) throws Exception {
        List<byte[]> imageBytesList = new ArrayList<>();
        String objectName = new String();
        if (t.equals("top")) {
            objectName = "top_1000/top" + label + ".jpeg";
        } else if (t.equals("bot")) {
            objectName = "bot_1000/bot" + label + ".jpeg";
        } else {
            return null;
        }
        try {
            S3Object s3Object = amazonS3.getObject(bucketName, objectName);
            InputStream content = s3Object.getObjectContent();
            log.info("content:" + content);
            S3ObjectInputStream inputStream = s3Object.getObjectContent();
            byte[] imageBytes = IOUtils.toByteArray(inputStream);

            log.info("image bytes:" + imageBytes);
            imageBytesList.add(imageBytes);

//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            return ResponseEntity.ok().contentType(MediaType.APPLICATION_OCTET_STREAM).body(imageBytes);
        } catch (Exception e) {
            return null;
        }

//        return  new ResponseEntity<>(imageBytesList, headers, HttpStatus.OK);
    }

    @RequestMapping(path = {"/get-image-url"}, method = RequestMethod.GET)
    public ResData getImageUrl(@RequestParam("label") String label) {
        ResData resData = new ResData();

        try {
            Date expiration = new Date();
            long expTimeMillis = expiration.getTime();
            expTimeMillis += 1000*60*60;
            expiration.setTime(expTimeMillis);
            String topObjectName = "top_1000/top" + label + ".jpeg";
            GeneratePresignedUrlRequest topGeneratePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName, topObjectName)
                    .withMethod(HttpMethod.GET).withExpiration(expiration);
            URL topUrl = amazonS3.generatePresignedUrl(topGeneratePresignedUrlRequest);

            String botObjectName = "bot_1000/bot" + label + ".jpeg";
            GeneratePresignedUrlRequest botGeneratePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName, botObjectName)
                    .withMethod(HttpMethod.GET).withExpiration(expiration);
            URL botUrl = amazonS3.generatePresignedUrl(botGeneratePresignedUrlRequest);
            Map<String, String> result = new HashMap<>();
            result.put("top", topUrl.toString());
            result.put("bot", botUrl.toString());
            resData.code = 200;
            resData.msg = "ok";
            resData.data = result;
        } catch (Exception e) {

        }
        return resData;
    }
}
