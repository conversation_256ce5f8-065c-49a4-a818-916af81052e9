package com.sankuai.walle.rmanage.config.service.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.gson.Gson;
import com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype;
import com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample;
import com.sankuai.walle.dal.classify.mapper.CarFirstcartypeSecondcartypeMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteCarTypeMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.OtaResidConstant;
import com.sankuai.walle.rmanage.config.common.convertor.Object2Map;
import com.sankuai.walle.rmanage.ota.common.ReqContext;
import com.sankuai.walle.rmanage.ota.meta.thrift.model.GetLatestOverallVersionReq;
import com.sankuai.walle.rmanage.ota.meta.thrift.model.GetOverallVersionResp;
import com.sankuai.walle.rmanage.ota.meta.thrift.service.OverallVersionThriftService;
import com.sankuai.walle.rmanage.config.service.OverallVersionDraftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class OverallVersionDraftServiceImpl implements OverallVersionDraftService {

    @Resource
    @Qualifier("squirrel")
    RedisStoreClient redisStoreClient;

    @Resource
    OverallVersionThriftService.Iface latestOverallVersion;

    @Resource
    RemoteCarTypeMapper remoteCarTypeMapper;
    @Resource
    CarFirstcartypeSecondcartypeMapper carFirstcartypeSecondcartypeMapper;
    @Resource
    TagsMapper tagsMapper;

    @Override
    public void pushOverallVersionInRedis() throws Exception {
        // 获取各车型的整车版本: 入参-一级车型、二级车型、版本状态
//        String carType = "s20";
        int stableStatus = 3; // 1-Preliminary 2-Verified 3-Stable 4-Released
        int releaseStatus = 4;

        // 获取一级车型
        RemoteCarTypeExample select = new RemoteCarTypeExample();
        List<RemoteCarType> carTypes = remoteCarTypeMapper.selectByExample(select);
        for (RemoteCarType carType : carTypes) {
            Long firstCarTypeId = carType.getId();
            String carTypeName = carType.getTypeName();
            // 获取关联的二级车型
            CarFirstcartypeSecondcartypeExample example = new CarFirstcartypeSecondcartypeExample(){{createCriteria().andFirstCarIdEqualTo(firstCarTypeId);}};
            List<CarFirstcartypeSecondcartype> relations = carFirstcartypeSecondcartypeMapper.selectByExample(example);
            log.info("relations>>{}",relations);
            for (CarFirstcartypeSecondcartype relation:relations){
                Long secondCarTypeId = relation.getSecondCarId();
                Tags subCarType = tagsMapper.selectByPrimaryKey(secondCarTypeId);
                String subCarTypeName = subCarType.getName();
                log.info("subCarTypeName>>{}",subCarTypeName);
                inRedis(carTypeName, subCarTypeName, stableStatus, OtaResidConstant.stable);
                inRedis(carTypeName, subCarTypeName, releaseStatus,OtaResidConstant.release);
            }
        }

    }

    private void inRedis(String carTypeName,String subCarTypeName,int targetstatus,String targetStatusTag) throws TException {
        GetOverallVersionResp versionRep = latestOverallVersion.getLatestOverallVersion(new GetLatestOverallVersionReq() {{
            setCarType(carTypeName);
            setCarSubType(subCarTypeName);
            setStatus(targetstatus);
        }}, new ReqContext());
        log.info("versionRep>>{}",versionRep);
        if(versionRep.status.code == CommonConstants.SUCCEED_CODE) {
            // 存入redis
            StoreKey key = new StoreKey(OtaResidConstant.category, subCarTypeName, targetStatusTag);
            HashMap<String, Object> obj = Object2Map.obj2map(versionRep.data.modules);
            log.info("《写入redis的整车版本-也就是标准版本》:{}",obj);
            Gson gson = new Gson();
            System.out.println(gson.toJson(obj));
            byte[] jsonBytes = gson.toJson(obj).getBytes();
            System.out.println(Arrays.toString(jsonBytes));
            String str = gson.toJson(obj);
            redisStoreClient.setBytes(key, jsonBytes, OtaResidConstant.expire);
        } else {
            log.error("<从整车版本获取数据失败>{}",versionRep);
        }
    }
}
