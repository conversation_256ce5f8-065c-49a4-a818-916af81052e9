package com.sankuai.walle.rmanage.config.thread;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.constant.BasicConfigurationConstant;
import com.sankuai.walle.rmanage.config.service.AccidentVideoService;
import com.sankuai.walle.rmanage.config.thread.dto.QueueParamDTO;
import com.sankuai.walle.rmanage.config.thread.req.VideoReponse;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
public class RedisAccidentConsumer implements RedisQueueConsumer {

    private String QueueName;

    public void setQueueName(String name){
        this.QueueName = name;
    }

    @Override
    public String getQueueName() {
        return this.QueueName;
    }

    @Override
    public void getMessage(QueueParamDTO queueParamDTO,
                           AccidentVideoService accidentVideoService,
                           DxGroupHandler dxGroupHandler) {
        HashMap<String, Long> queryTimeConfig =  dxGroupHandler.getQueryTimeConfig();
        log.info("thread is start! queueParamDTO = {}, queryTimeConfig = {}", queueParamDTO,queryTimeConfig);
        // 开始时间（秒）
        long startTimeSeconds = System.currentTimeMillis();
        Long maxRequestTime = queryTimeConfig.get("maxRequestTime");
        Long validRequestTime = queryTimeConfig.get("validRequestTime");
        //有效时间段内的时间间隔（第一段）
        Long firstRequestInterval = queryTimeConfig.get("firstRequestInterval");
        //第二段时间段内的时间间隔
        Long secondRequestInterval = queryTimeConfig.get("secondRequestInterval");
        Date startTime = new Date();
        //表示超过一分钟后的第一次请求
        Boolean firstFlag  = false;
        // 是否成功
        Boolean successFlag = false;
        String loopUrl = queueParamDTO.getLoopVideoMsg();
        String concatUrl = queueParamDTO.getConcatVideoMsg();
        // 根据url计算需要打捞的视角信息
        List<String> positionList = Arrays.asList(
            Optional.ofNullable(loopUrl).map(url -> "v_loop").orElse(null),
            Optional.ofNullable(concatUrl).map(url -> "v_concat").orElse(null)
        ).stream().filter(Objects::nonNull).collect(Collectors.toList());
        while(new Date().getTime() - startTime.getTime() < maxRequestTime){

            Long currentTime = new Date().getTime();
            if(currentTime - startTime.getTime() <= validRequestTime){
                //currentTime 小于 等于 1 分钟的时候 10s间隔发送一次数据, 如果视频获取成功，则直接返回，并结束进程
                try{
                    VideoReponse videoReponse = accidentVideoService.getAccidentVideoResponse(queueParamDTO.getAccidentTime(), queueParamDTO.getVin() , positionList);
                    if(videoReponse != null) {
                        // 计算哪些返回结果需要处置，即只有打捞谁，谁的结果才需要关系
                        boolean loopValid = loopUrl == null || videoReponse.getV_loop() != null;
                        boolean concatValid = concatUrl == null || videoReponse.getV_concat() != null;
                        if(loopValid && concatValid){
                            log.info("queueParamDTO = {} is sent to group ", queueParamDTO);
                            //发送群消息,并结束进程
                            dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupUrlText(loopUrl, concatUrl));
                            successFlag = true;
                            break;
                        }
                        else if( videoReponse.getStatus() == 3 || videoReponse.getStatus_desc().equals("task in progress vehicle offline, task has been sent to vehicle-end ")){
                            //当返回失败，或者车辆离线，则发送消息到大象群，并结束线程
                            log.info("queueParamDTO is {} , videoReponse = {} , task is failed", queueParamDTO,videoReponse);
                            dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupErrorText(3));
                            break;
                        }
                    }
                }
                catch (Exception e){
                    log.error("threadDTO.accidentVideoService.getAccidentVideoResponse is failed !",e);
                }
                delayXXXms(firstRequestInterval);
            }
            else{
                //  1分钟的时候 < currentTime < 10分钟的时候 30s间隔发送一次数据
                try{
                    VideoReponse  videoReponse =  accidentVideoService.getAccidentVideoResponse(queueParamDTO.getAccidentTime(), queueParamDTO.getVin() , positionList);
                    if(videoReponse != null){
                        // 计算哪些返回结果需要处置，即只有打捞谁，谁的结果才需要关系
                        boolean loopValid = loopUrl == null || videoReponse.getV_loop() != null;
                        boolean concatValid = concatUrl == null || videoReponse.getV_concat() != null;
                        if(loopValid && concatValid){
                            dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupUrlText(loopUrl, concatUrl));
                            successFlag = true;
                            break;
                        }
                        else if(( loopValid || concatValid) && !firstFlag){
                            //只得到一个视频
                            log.info("queueParamDTO = {}, 一分钟内只捞到一个视频 ", queueParamDTO);
                            dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupErrorText(videoReponse.getStatus()));
                            firstFlag = true;
                        }
                        else if(!loopValid && !concatValid && !firstFlag){
                            //发送获取不到的原因
                            log.info("queueParamDTO = {}, 一分钟内未捞到视频 ", queueParamDTO);
                            dxGroupHandler.sendMessage(queueParamDTO.getGroupId(), getDXGroupErrorText(videoReponse.getStatus()));
                            firstFlag = true;
                        }
                    }
                }
                catch (Exception e){
                    log.error("threadDTO.accidentVideoService.getAccidentVideoResponse is failed !",e);
                }
                delayXXXms(secondRequestInterval);
            }
        }
        // cat 客户端上报打捞视频的耗时
        long endTimeSeconds = System.currentTimeMillis();
        Transaction transaction = Cat.newTransactionWithDuration(BasicConfigurationConstant.ACCIDENT_VIDEO_TIME, "time",
                endTimeSeconds - startTimeSeconds);
        log.info("thread is end! startTimeSeconds = {}, endTimeSeconds = {}, duration = {}",
                startTimeSeconds,endTimeSeconds, endTimeSeconds- startTimeSeconds);
        transaction.setStatus(successFlag ? "0":"-1");
        transaction.complete();
    }

    @Override
    public void error(String error) {
    }

    public void delayXXXms(Long size){
        try {
            Thread.sleep(size); // 暂停30秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 重新设置中断状态
            log.error("threadDTO.accidentVideoService.getAccidentVideoResponse is failed !",e);
        }
    }

    /**
     * 拼接大象群消息
     * @param loopUrl
     * @param concatUrl
     * @return
     */
    private String getDXGroupUrlText(String loopUrl, String concatUrl) {
        StringBuilder text = new StringBuilder("高清事故视频:\n");
        if (loopUrl != null) {
            text.append(String.format("[[环视视频|%s]]\n", loopUrl));
        }
        
        if (concatUrl != null) {
            text.append(String.format("[[前后左右拼接视频|%s]]", concatUrl));
        }
        return text.toString();
    }

    private String getDXGroupErrorText(int type){

        String errorText = "";

        switch (type){
            case 1:
                errorText = "高清事故视频: \n" +
                        "视频回收中,请耐心等待";
                break;
            case 3:
                errorText = "高清事故视频: 视频获取失败\n" +
                        "失败原因: 车辆不在线";
                break;
            case 4:
                errorText = "高清事故视频: \n" +
                        "请求超时，系统自动重试中，请耐心等待";
                break;
        }
        return errorText;
    }

}
