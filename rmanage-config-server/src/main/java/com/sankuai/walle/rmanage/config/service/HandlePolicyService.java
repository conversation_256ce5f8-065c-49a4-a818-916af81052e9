package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.objects.vo.request.BizHandlePolicyInfoReq;
import com.sankuai.walle.objects.vo.request.MisGroupReq;

import java.util.List;

public interface HandlePolicyService {

    void addHandlePolicy(BizHandlePolicyInfoReq handlePolicyInfoReq) throws Exception;
    void updateHandlePolicy(BizHandlePolicyInfoReq handlePolicyInfoReq) throws Exception;

    Object queryAccidentAttributes() throws Exception;

    Object queryHandleMethodAlias() throws Exception;

    Object queryHandlePolicy(Integer page, Integer pageSize) throws Exception;

    Object queryAllEnableHandlePolicy() throws Exception;

    Object queryEmpInfos(String misId) throws Exception;

    void addMisGroup(MisGroupReq misGroupReq) throws Exception;

    void updateMisGroup(MisGroupReq misGroupReq) throws Exception;

    Object queryMisGroup(Integer page, Integer pageSize) throws Exception;

    List<String> getMisGroupMemberList(String misGroupName);
}