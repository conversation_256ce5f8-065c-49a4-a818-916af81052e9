package com.sankuai.walle.rmanage.config.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import com.sankuai.walle.dal.eve.entity.CarTypes;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.dal.eve.mapper.CarTypesMapper;
import com.sankuai.walle.dal.eve.mapper.DeviceTypesMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.VehicleModelInfoVO;
import com.sankuai.walle.objects.vo.DeviceCategoryVO;
import com.sankuai.walle.objects.vo.DeviceInfoVO;
import com.sankuai.walle.objects.vo.request.VehicleModelCreateRequest;
import com.sankuai.walle.objects.vo.request.DeviceTypeCreateRequest;
import com.sankuai.walle.objects.vo.request.DeviceTypeQueryRequest;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.common.ErrorCode;
import com.sankuai.walle.rmanage.config.constant.enums.VehicleModelAddTypeEnum;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelAttributesDTO;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelDTO;
import com.sankuai.walle.rmanage.config.dto.vehicleManage.RemoteCarTypeDTO;
import com.sankuai.walle.rmanage.config.service.DeviceTypesService;
import com.sankuai.walle.rmanage.config.service.TypeTreeService;
import com.sankuai.walle.rmanage.config.service.VehicleModelAttributesService;
import com.sankuai.walle.rmanage.config.service.VehicleModelManageService;
import com.sankuai.walle.rmanage.config.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TypeTreeServiceImpl implements TypeTreeService {

    @Resource
    CarTypesMapper carTypesMapper;

    @Resource
    private DeviceTypesService deviceTypesService;

    @Resource
    private DeviceTypesMapper deviceTypesMapper;

    @Resource
    private VehicleModelManageService vehicleModelManageService;

    @Resource
    private VehicleModelAttributesService vehicleModelAttributesService;

    /**
     * 设备类型配置
     */
    @MdpConfig("device_category_config")
    private ArrayList<DeviceCategoryVO> deviceCategoryConfig;


    @Override
    public List<RemoteCarTypeDTO> getTypeTree(){
        List<CarTypes> remoteCarTypes = carTypesMapper.selectByExample(null);
        log.info(String.valueOf(remoteCarTypes));
        // 使用Map按父节点类型分组
        Map<String, List<CarTypes>> groupedByFather = new HashMap<>();
        for(CarTypes carType:remoteCarTypes){
            String key = carType.getFatherType() == null ? "top" : carType.getFatherType();
            groupedByFather.computeIfAbsent(key, k -> new ArrayList<>()).add(carType);
        }
        return this.createTree("top",groupedByFather);
    };

    @Override
    public List<VehicleModelInfoVO> getCarTypeTree(){
        // 1 查询全量车型数据（包含一级车型/二级车型）
        List<CarSelects> remoteCarSelects = vehicleModelManageService.getCarSelects(new CarSelects());
        log.info("getTypeTreeFrom, remoteCarSelects = {}", remoteCarSelects);
        if(CollectionUtils.isEmpty(remoteCarSelects)){
            return new ArrayList<>();
        }
        // 2 过滤掉自闭环数据（对可能存在的脏数据进行拦截）
        remoteCarSelects = remoteCarSelects.stream().filter(carSelects ->
                !Objects.equals(carSelects.getId(), carSelects.getFatherId())).collect(Collectors.toList());

        // 3 创建 id 与 车型的映射关系
        Map<Long,CarSelects> fatherIdMap = remoteCarSelects.stream()
                .collect(Collectors.toMap(CarSelects::getId, carSelects -> carSelects));

        // 4 遍历所有的数据，建立层级关系 groupedByFather（key 为车型type， value 为该车型关联的二级车型列表）
        // 只有type可以，因为type是唯一键
        Map<String, List<CarSelects>> groupedByFather = new HashMap<>();
        for(CarSelects carSelects :remoteCarSelects){
            CarSelects temp = fatherIdMap.getOrDefault(carSelects.getFatherId(), new CarSelects());
            String key = Objects.equals(CommonConstants.DEFAULT_FATHER_ID, carSelects.getFatherId()) ? "top" : temp.getType();
            groupedByFather.computeIfAbsent(key, k -> new ArrayList<>()).add(carSelects);
        }
        log.info("getCarTypeTree, groupedByFather = {}", groupedByFather);
        // 5 递归算法构建车型树
        List<VehicleModelInfoVO> vehicleModelInfoVOList = createCarTypeTree("top",groupedByFather);

        // 6 按照更新时间进行倒序排序
        vehicleModelInfoVOList.sort(Comparator.comparing(VehicleModelInfoVO::getUpdateTime).reversed());

        return vehicleModelInfoVOList;
    };

    @Override
    public CarTypes addCarType(RemoteCarTypeDTO remoteCarTypeDTO){
        CarTypes remoteCarType = this.trance2Entity(remoteCarTypeDTO);
//        remoteCarType.setConfContent("{}");
        carTypesMapper.insertSelective(remoteCarType);
        return remoteCarType;
    }

    /**
     * 添加设备类型
     * @param request 设备类型创建请求
     * @param misId   用户信息
     * @return 返回添加后的设备类型
     */
    @Override
    public ResData addDevice(DeviceTypeCreateRequest request, String misId) {
        // 1 设备类型名重复校验
        List<DeviceTypes> typeNameList = deviceTypesService.getDeviceTypes(DeviceTypes.builder().typeName(request.getTypeName()).build());
        if( CollectionUtils.isNotEmpty(typeNameList)){
            log.info("typeName is exist, request = {}", request);
            return ResData.result(ErrorCode.DEVICE_TYPE_NAME_EXIST, "设备类型名重复,请重新输入");
        }
        // 2 设备类型别名重复校验
        List<DeviceTypes> friendNameList = deviceTypesService.getDeviceTypes(DeviceTypes.builder().friendName(request.getFriendName()).build());
        if( CollectionUtils.isNotEmpty(friendNameList)){
            log.info("friendName is exist, request = {}", request);
            return ResData.result(ErrorCode.DEVICE_FRIEND_NAME_EXIST, "设备类型别名重复,请重新输入");
        }

        // 3 新增设备
        deviceTypesService.insertDeviceType(DeviceTypes.builder().typeName(request.getTypeName())
            .friendName(request.getFriendName())
            .category(request.getCategory())
            .editor(misId).build());
        return ResData.successWithMsg("ok");
    }

    @Override
    public void updateDevice(DeviceTypes deviceType) {
        deviceTypesMapper.updateByPrimaryKeySelective(deviceType);
    }

    @Override
    public void deleteDevice(Long id) {
        deviceTypesMapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据设备类型查询请求查询设备类型列表
     * @param request 设备类型查询请求对象
     * @return 返回设备类型列表
     */
    @Override
    public List<DeviceInfoVO> queryDeviceByDeviceTypeQueryRequest(DeviceTypeQueryRequest request) {
        List<DeviceTypes> deviceTypesList = deviceTypesService.getDeviceTypes(DeviceTypes.builder()
                .typeName(request.getTypeName())
                .friendName(request.getFriendName()).build());
        if(CollectionUtils.isEmpty(deviceTypesList)){
            return new ArrayList<>();
        }
        // 将 category 的list结构转化成 map
        Map<String, String> categoryMap = deviceCategoryConfig.stream()
                .collect(Collectors.toMap(DeviceCategoryVO::getCategoryName, DeviceCategoryVO::getCategoryDesc));

        // 响应格式转化 + 升序排序
        return deviceTypesList.stream()
                .map(deviceTypes ->  {
                    DeviceInfoVO deviceInfoVO = deviceTypesService.trance2DeviceInfoVO(deviceTypes);
                    deviceInfoVO.setCategory(categoryMap.get(deviceInfoVO.getCategory()));
                    return deviceInfoVO;
                })
                .sorted(Comparator.comparingLong(DeviceInfoVO::getId))
                .collect(Collectors.toList());

    }

    /**
     * 获取设备类型配置
     * @return 返回设备类型配置列表
     */
    @Override
    public List<DeviceCategoryVO> getDeviceCategoryConfig() {
        log.info("deviceCategoryConfig = {}", deviceCategoryConfig);
        return deviceCategoryConfig;
    }

    /**
     * 添加车辆类型
     * @param request 车辆类型创建请求
     * @return 返回添加后的车辆类型
     */
    @Override
    @Transactional(transactionManager = "mdpTM1", rollbackFor = Exception.class)
    public ResData addCarType(VehicleModelCreateRequest request, String misId) {
        
        // 1 格式转化 + 二级车型重映射
        VehicleModelDTO vehicleModelDTO = trance2VehicleModelDTO(request);
        // 2 检查一级车型是否存在,一级车型的父级Id为0
        List<CarSelects> fatherTypeList = vehicleModelManageService.getCarSelects(CarSelects.builder()
                .name(vehicleModelDTO.getFatherTypeName()).fatherId(CommonConstants.DEFAULT_FATHER_ID).build());
        log.info("addCarType, fatherTypeList = {}", fatherTypeList);
        // 一级车型存在
        if(CollectionUtils.isNotEmpty(fatherTypeList)){
            // 一级车型存在，二级车型入参为空
            if(StringUtils.isBlank(vehicleModelDTO.getSonTypeName())){
                return ResData.result(ErrorCode.FATHER_CAR_TYPE_NAME_EXIST, "一级车型名称重复,请重新输入");
            }

            // 查询二级车型是否存在
            Long fatherId = fatherTypeList.get(0).getId();
            List<CarSelects> sonTypeList = vehicleModelManageService.getCarSelects(CarSelects.builder()
                    .name(vehicleModelDTO.getSonTypeName()).fatherId(fatherId).build());
            // 二级车型存在
            if(CollectionUtils.isNotEmpty(sonTypeList)){
                return ResData.result(ErrorCode.SON_CAR_TYPE_NAME_EXIST, "二级车型名称重复,请重新输入");
            }
            //二级车型不存在
            else{
                vehicleModelManageService.insertVehicleModelInfo(fatherId,vehicleModelDTO,misId,
                        VehicleModelAddTypeEnum.ADD_SON_CAR_TYPE );
            }
        }
        // 一级车型不存在
        else {
            //一级车型不存在，二级车型入参为空
            if(StringUtils.isNotBlank(vehicleModelDTO.getSonTypeName())){
                vehicleModelManageService.insertVehicleModelInfo(null,vehicleModelDTO,misId,
                        VehicleModelAddTypeEnum.ADD_ALL_CAR_TYPE );
            }
            else{
                vehicleModelManageService.insertVehicleModelInfo(null,vehicleModelDTO,misId,
                        VehicleModelAddTypeEnum.ADD_FATHER_CAR_TYPE );
            }
        }
        return ResData.successWithData("ok");
    }

    private List<RemoteCarTypeDTO> createTree(String fatherType, Map<String, List<CarTypes>> groupedByFather){
        List<RemoteCarTypeDTO> result = new ArrayList<>();
        List<CarTypes> children = groupedByFather.get(fatherType);
        if (children != null) {
            for(CarTypes carType:children){
                    RemoteCarTypeDTO dto = this.trance2DTO(carType);
                    dto.setChildren(createTree(dto.getTypeName(),groupedByFather));
                    result.add(dto);
            }
        }

        return result;
    }

    /**
     * 创建车辆类型树
     * @param fatherType 父级车型类型
     * @param groupedByFather 按父级车型类型分组的数据
     * @return 返回车辆类型树
     */
    private List<VehicleModelInfoVO> createCarTypeTree(String fatherType, Map<String, List<CarSelects>> groupedByFather){
        List<VehicleModelInfoVO> result = new ArrayList<>();
        List<CarSelects> children = groupedByFather.get(fatherType);
        log.info("createCarTypeTree, fatherType = {}, children = {}", fatherType, children);
        if (children != null) {
            for(CarSelects carSelects : children){
                VehicleModelInfoVO vo = buildVehicleModelInfoVO(carSelects);
                vo.setChildren(createCarTypeTree(carSelects.getType(),groupedByFather));
                result.add(vo);
            }
        }
        return result;
    }

    /**
     * 构建车型信息
     * @param carSelects 车型选择对象
     * @return 返回构建好的车型信息对象
     */
    private VehicleModelInfoVO buildVehicleModelInfoVO(CarSelects carSelects){
        // 填充车型信息
        VehicleModelInfoVO infoVO = VehicleModelInfoVO.builder()
                .id(carSelects.getId())
                .carTypeName(carSelects.getName())
                .carType(carSelects.getType())
                .editor(carSelects.getEditor())
                .createTime(DateUtils.format(carSelects.getAddTime(), DateUtils.DATE_FORMAT_PATTERN) )
                .updateTime(DateUtils.format(carSelects.getUpdateTime(), DateUtils.DATE_FORMAT_PATTERN))
                .build();
        // 查询车型属性
        VehicleModelAttributesDTO vehicleModelAttributesDTO = vehicleModelAttributesService.
                getVehicleModelAttributesDTOByCarType(carSelects.getType());
        if(Objects.isNull(vehicleModelAttributesDTO)){
            return infoVO;
        }
        // 填充车型属性
        return VehicleModelAttributesDTO.toVehicleModelInfoVO(vehicleModelAttributesDTO, infoVO);
    }

    private RemoteCarTypeDTO trance2DTO(CarTypes remoteCarType){
        RemoteCarTypeDTO remoteCarTypeDTO = new RemoteCarTypeDTO();
        remoteCarTypeDTO.setId(remoteCarType.getId());
        remoteCarTypeDTO.setFatherType(remoteCarType.getFatherType());
        remoteCarTypeDTO.setTypeName(remoteCarType.getTypeName());
        remoteCarTypeDTO.setFriendName(remoteCarType.getFriendName());
        return remoteCarTypeDTO;
    }

    private CarTypes trance2Entity(RemoteCarTypeDTO remoteCarTypeDTO){
        CarTypes remoteCarType = new CarTypes();
        remoteCarType.setTypeName(remoteCarTypeDTO.getTypeName());
        remoteCarType.setFatherType(remoteCarTypeDTO.getFatherType());
        remoteCarType.setFriendName(remoteCarTypeDTO.getFriendName());
        return remoteCarType;
    }

    /**
     * 车型创建请求对象转换为车型DTO对象，二级车型名称重映射
     * @param request 车型创建请求对象
     * @return 返回车型DTO对象
     */
    private VehicleModelDTO trance2VehicleModelDTO(VehicleModelCreateRequest request){
        String sonType = "";
        // 如果二级车型不为空，则对二级车型的名称进行重映射
        if(StringUtils.isNotBlank(request.getSonTypeName())){
            sonType = request.getFatherTypeName() + request.getSonTypeName();
        }
        return VehicleModelDTO.builder()
                .fatherTypeName(request.getFatherTypeName())
                // 一级车型 name 和 type 相等
                .fatherType(request.getFatherTypeName())
                .sonTypeName(request.getSonTypeName())
                // 二级车型
                .sonType(sonType)
                .carBrand(request.getCarBrand())
                .carMedium(request.getCarMedium())
                .build();
    }
}
