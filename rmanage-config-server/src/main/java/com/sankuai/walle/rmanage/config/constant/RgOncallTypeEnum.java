package com.sankuai.walle.rmanage.config.constant;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 按人 和 按组 排班 NEW_ONCALL_MODE 现在代表兜底方案
 */
@AllArgsConstructor
@NoArgsConstructor
public enum RgOncallTypeEnum {
    BY_GROUP("GROUP_TURN", "按组排班"),
    BY_GROUP_TIME_PERIOD("GROUP_TIME_TURN", "按组排班：时间段值班"),
    BY_USER_SINGLE("SINGLE_TURN", "按人排班：单人在线"),
    BY_USER_MULTI("MULTI_ONLINE", "按人排班（默认）：多人在线"),
    NEW_ONCALL_MODE("NEW_ONCALL_MODE", "新排班模式");    // todo:后续TT值班功能会上这个，并逐渐取代上面四种模式
    public String mode;
    public String desc;

    /**
     * 根据模式获取枚举
     *
     * @param mode
     * @return
     */
    public static RgOncallTypeEnum getByMode(String mode) {
        for (RgOncallTypeEnum rgOncallTypeEnum : RgOncallTypeEnum.values()) {
            if (rgOncallTypeEnum.mode.equals(mode)) {
                return rgOncallTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否需要兜底发消息，null代表未获取到，NEW_ONCALL_MODE代表新排班模式
     *
     * @param mode
     * @return
     */
    public static boolean isNeedFallback(RgOncallTypeEnum mode) {
        return mode == null || NEW_ONCALL_MODE.equals(mode);
    }

    /**
     * 构造调用rg值班人员的url
     *
     * @param url
     * @return
     */
    public String constructUrl(String url) {
        switch (this) {
            case BY_USER_MULTI:
            case BY_USER_SINGLE:
                url += TTOncallAPIConstant.USER_MODE_ONCALL_URL_SUFFIX;
                break;
            case BY_GROUP:
            case BY_GROUP_TIME_PERIOD:
                url += TTOncallAPIConstant.GROUP_MODE_ONCALL_URL_SUFFIX;
                break;
            case NEW_ONCALL_MODE:
                url += TTOncallAPIConstant.NEW_GROUP_MODE_ONCALL_URL_SUFFIX;
                break;
            default:
                return null;
        }
        return url;
    }
}
