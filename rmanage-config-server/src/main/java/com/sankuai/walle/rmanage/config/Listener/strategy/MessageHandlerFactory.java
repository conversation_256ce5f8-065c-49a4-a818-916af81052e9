package com.sankuai.walle.rmanage.config.Listener.strategy;

import com.sankuai.walle.rmanage.config.config.AukClientConfig;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息处理策略工厂类
 * 负责管理和提供不同主题的消息处理策略
 * 使用Spring的依赖注入机制自动装配所有策略实现
 */
@Component
public class MessageHandlerFactory {
    /**
     * 存储主题与对应处理策略的映射关系
     */
    private final Map<String, MessageHandlerStrategy> strategyMap = new HashMap<>();
    private final PathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 构造函数，初始化所有消息处理策略
     * @param strategies Spring自动注入的所有策略实现类
     */
    public MessageHandlerFactory(List<MessageHandlerStrategy> strategies) {
        for (MessageHandlerStrategy strategy : strategies) {
            if (strategy instanceof ConnectMessageHandler) {
                strategyMap.put(AukClientConfig.CONNECT_TOPIC, strategy);
            } else if (strategy instanceof UpMessageHandler) {
                strategyMap.put(AukClientConfig.UP_TOPIC, strategy);
            } else if (strategy instanceof QueryMessageHandler) {
                strategyMap.put(AukClientConfig.QUERY_TOPIC_RESP, strategy);
            } else if (strategy instanceof CheckMd5MessageHandler) {
                strategyMap.put(AukClientConfig.CHECK_MD5_TOPIC, strategy);
            }
        }
    }

    /**
     * 根据主题获取对应的消息处理策略
     * @param topic 消息主题
     * @return 对应的消息处理策略
     */
    public MessageHandlerStrategy getStrategy(String topic) {
        // 首先尝试精确匹配
        MessageHandlerStrategy strategy = strategyMap.get(topic);
        if (strategy != null) {
            return strategy;
        }
        
        // 如果精确匹配失败，尝试通配符匹配
        for (Map.Entry<String, MessageHandlerStrategy> entry : strategyMap.entrySet()) {
            if (pathMatcher.match(entry.getKey(), topic)) {
                return entry.getValue();
            }
        }
        
        return null;
    }
} 