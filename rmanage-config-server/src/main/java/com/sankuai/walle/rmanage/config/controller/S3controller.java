package com.sankuai.walle.rmanage.config.controller;

import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.infrastructureService.S3Service;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import java.io.IOException;

@RestController
@RequestMapping(path = {"/eve/cmdb/rest/api/cmdb/device/car/config/s3"})
@Log4j
public class S3controller {

    @Resource
    S3Service s3Service;

    @Autowired
    Environment env;

    // 上传资源到S3
    @RequestMapping(path = {"/compileTicket"}, method = RequestMethod.POST)
    public ResData uploadDataS3(@RequestParam("image") MultipartFile image, MultipartHttpServletRequest request) throws IOException {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;

        byte[] input = image.getBytes();
        String objectName = "compileTicket/" + image.getOriginalFilename();
        String bucketName = env.getProperty("s3-bucketName");
        s3Service.pushFileToS3(bucketName, objectName, input);

        res.code = 200;
        res.msg = "ok";
        res.data = objectName;
        return res;
    }

    // 删除S3的资源
    @RequestMapping(path = {"/delete"}, method = RequestMethod.GET)
    public ResData deleteDataS3(@RequestParam String bucketName,@RequestParam String objName) {
        ResData res = new ResData();
        s3Service.deleteObjectExample(bucketName, objName);
        res.code = CommonConstants.SUCCEED_CODE;
        return res;

    }

    // 查看S3资源
    @RequestMapping(path = {"/read"}, method = RequestMethod.GET)
    public ResData readDataS3(@RequestParam String bucketName,@RequestParam String objName) throws IOException {
        ResData res = new ResData();
        res.data = s3Service.getObjectAsString(bucketName, objName);
        res.code = CommonConstants.SUCCEED_CODE;
        return res;
    }
}
