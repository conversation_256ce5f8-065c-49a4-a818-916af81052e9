package com.sankuai.walle.rmanage.config.repository;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;

import java.util.List;

/**
 * 保险导入仓储接口
 * 定义数据访问的抽象接口
 */
public interface InsuranceImportRepository {
    
    /**
     * 根据VIN查找车辆对象
     */
    List<CarObjects> findCarObjectsByVin(String vin);
    
    /**
     * 根据VIN查找车辆资产
     */
    List<CarAssets> findCarAssetsByVin(String vin);
    
    /**
     * 根据VIN查找车辆执行词
     */
    List<CarExecWord> findCarExecWordByVin(String vin);
    
    /**
     * 保存车辆执行词
     */
    void saveCarExecWord(CarExecWord carExecWord);
    
    /**
     * 更新车辆执行词
     */
    void updateCarExecWord(CarExecWord carExecWord);
    
    /**
     * 更新车辆对象
     */
    void updateCarObjects(CarObjects carObjects);
} 