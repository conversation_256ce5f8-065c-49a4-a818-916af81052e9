package com.sankuai.walle.rmanage.config.config;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.inf.leaf.thrift.IDGen;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> Created on 2021/12/14
 */
@Configuration
@PropertySource(value = {"classpath:leaf.properties", "classpath:/META-INF/app.properties"})
public class LeafConfig {

    @Value("${app.name}")
    private String appKey;

    @Value("${leaf.remoteAppkey}")
    private String remoteAppkey;

    @Bean(name = "idGenThriftService")
    public ThriftClientProxy idGenService() {
        ThriftClientProxy service = new ThriftClientProxy();
        service.setServiceInterface(IDGen.class);
        service.setAppKey(appKey);
        service.setRemoteAppkey(remoteAppkey);
        service.setTimeout(200);
        service.setNettyIO(true);

        return service;
    }
}
