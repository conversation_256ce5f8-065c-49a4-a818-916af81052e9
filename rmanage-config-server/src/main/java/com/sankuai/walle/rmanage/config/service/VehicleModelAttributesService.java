package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.carManage.entity.VehicleModelAttributes;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelAttributesDTO;

import java.util.List;

public interface VehicleModelAttributesService {
    void batchInsertVehicleModelAttributes(List<VehicleModelAttributes> vehicleModelAttributes);

    List<VehicleModelAttributes> getVehicleModelAttributes(VehicleModelAttributes vehicleModelAttributes);

    VehicleModelAttributesDTO getVehicleModelAttributesDTOByCarType(String carType);
}
