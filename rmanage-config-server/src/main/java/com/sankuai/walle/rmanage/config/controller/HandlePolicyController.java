package com.sankuai.walle.rmanage.config.controller;

import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.request.BizHandlePolicyInfoReq;
import com.sankuai.walle.objects.vo.request.MisGroupReq;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.HandlePolicyService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/eve/cmdb/rest")
@Log4j
public class HandlePolicyController {

    @Autowired
    private HandlePolicyService handlePolicyService;
    @RequestMapping(path = {"/api/cmdb/addHandlePolicy"}, method = RequestMethod.POST)
    public ResData addHandlePolicy(@RequestBody BizHandlePolicyInfoReq handlePolicy) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            handlePolicyService.addHandlePolicy(handlePolicy);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (DuplicateKeyException e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.DUPLICATE_KEY_ERROR_CODE;
            log.error("addHandlePolicy",e);
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("addHandlePolicy",e);
            return req;
        }
    }
    @RequestMapping(path = {"/api/cmdb/updateHandlePolicy"}, method = RequestMethod.PUT)
    public ResData updateHandlePolicy(@RequestBody BizHandlePolicyInfoReq handlePolicy) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            handlePolicyService.updateHandlePolicy(handlePolicy);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (DuplicateKeyException e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.DUPLICATE_KEY_ERROR_CODE;
            log.error("updateHandlePolicyInfo",e);
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("updateHandlePolicyInfo",e);
            return req;
        }
    }

    @RequestMapping(path = {"/api/cmdb/queryAccidentAttributes"}, method = RequestMethod.GET)
    public ResData queryAccidentAttributes() {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = handlePolicyService.queryAccidentAttributes();
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("queryAccidentAttributes",e);
            return req;
        }
    }

    @RequestMapping(path = {"/api/cmdb/queryHandleMethodAlias"}, method = RequestMethod.GET)
    public ResData queryHandleMethodAlias() {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = handlePolicyService.queryHandleMethodAlias();
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("queryHandleMethodAlias error, e: {}",e);
            return req;
        }
    }

    @RequestMapping(path = "/api/cmdb/queryHandlePolicy", method = RequestMethod.GET)
    public ResData queryHandlePolicy(@RequestParam Integer page, @RequestParam Integer pageSize) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = handlePolicyService.queryHandlePolicy(page, pageSize);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            log.error("queryHandlePolicy",e);
            return req;
        }
    }

    @RequestMapping(path = "/api/cmdb/queryEmpInfos", method = RequestMethod.GET)
    public ResData queryEmpInfos(@RequestParam String misId) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = handlePolicyService.queryEmpInfos(misId);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            return req;
        }
    }

    @RequestMapping(path = "/api/cmdb/addMisGroup", method = RequestMethod.POST)
    public ResData addMisGroup(@RequestBody MisGroupReq misGroupReq) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            handlePolicyService.addMisGroup(misGroupReq);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (DuplicateKeyException e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.DUPLICATE_KEY_ERROR_CODE;
            log.error("addMisGroup",e);
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("addMisGroup",e);
            return req;
        }
    }

    @RequestMapping(path = "/api/cmdb/updateMisGroup", method = RequestMethod.PUT)
    public ResData updateMisGroup(@RequestBody MisGroupReq misGroupReq) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            handlePolicyService.updateMisGroup(misGroupReq);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (DuplicateKeyException e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.DUPLICATE_KEY_ERROR_CODE;
            log.error("updateMisGroup",e);
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("updateMisGroup",e);
            return req;
        }
    }

    @RequestMapping(path = "/api/cmdb/queryMisGroup", method = RequestMethod.GET)
    public ResData queryMisGroup(@RequestParam Integer page, @RequestParam Integer pageSize) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = handlePolicyService.queryMisGroup(page, pageSize);
            req.code = CommonConstants.SUCCEED_CODE;
            req.msg = CommonConstants.succeedMsg;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            req.code = CommonConstants.ERROR_CODE;
            log.error("queryMisGroup",e);
            return req;
        }
    }
}