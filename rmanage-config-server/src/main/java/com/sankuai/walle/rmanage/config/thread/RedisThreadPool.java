package com.sankuai.walle.rmanage.config.thread;


import com.meituan.xframe.threadpool.client.thread.ExecutorWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

@Slf4j
public class RedisThreadPool {
    // 线程池创建类
    private ExecutorService mexecutor;

    private static volatile RedisThreadPool instance;

    public RedisThreadPool() {
        if (mexecutor == null) {
            mexecutor = ExecutorWrapper.newThreadPoolExecutor("DXGroup-HDVideo");
        }
    }

    public static RedisThreadPool getInstance() {
        if (instance == null) {
            synchronized (RedisThreadPool.class) {
                if (instance == null) {
                    instance = new RedisThreadPool();
                    log.info("MyRedisThreadPool线程池已经开启");
                }
            }
        }
        return instance;
    }

    public void executor(Runnable runnable) {
        if (null == runnable) {
            return;
        }
        mexecutor.submit(runnable);
    }
}
