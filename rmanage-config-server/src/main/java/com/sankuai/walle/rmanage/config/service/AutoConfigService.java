package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.rmanage.config.dto.config.AutoBatchPreConfigResponseDto;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface AutoConfigService {



     AutoBatchPreConfigResponseDto handleExcelConfig(List<SendExcelDeviceConfigReq> body);

//     List<AutoConfigContent> queryByVinList(List<String> vin);

     String sendEveConfig(List<String> vins, String configName, String mis);

     List<CarDeviceConfigRes> queryEveConfig(List<String> vins, @RequestParam String configName);


     void uploadS3(List<String> vins) throws IOException;

     Map<String, String> queryS3(List<String> vins);

     void sendScript(List<String> vins);

     Map queryTaskByVinList(List<String> vin);

     void batchCreateSecretKey(List<String> vins);

     Map<String, String> queryVehicleNameByVin(List<String> vins);
}
