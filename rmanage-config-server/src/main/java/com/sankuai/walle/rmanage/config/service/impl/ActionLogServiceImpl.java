package com.sankuai.walle.rmanage.config.service.impl;

import com.sankuai.walle.dal.eve.entity.CmdbActionLog;
import com.sankuai.walle.dal.eve.mapper.CmdbActionLogMapper;
import com.sankuai.walle.objects.constants.ActionLogType;
import com.sankuai.walle.rmanage.config.service.ActionLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ActionLogServiceImpl implements ActionLogService {

    @Autowired
    private CmdbActionLogMapper cmdbActionLogMapper;

    @Override
    public void insertCarManageActionLog(String vin, String mis, String actionContent) {
        CmdbActionLog log = new CmdbActionLog();
        log.setVin(vin);
        log.setMis(mis);
        log.setActionContent(actionContent);
        log.setType(ActionLogType.CAR_MANAGE_LOG_TYPE);
        cmdbActionLogMapper.insert(log);
    }

    @Override
    public void insertConfigActionLog(String vin, String mis, String actionContent) {
        CmdbActionLog log = new CmdbActionLog();
        log.setVin(vin);
        log.setMis(mis);
        log.setActionContent(actionContent);
        log.setType(ActionLogType.CONFIG_LOG_TYPE);
        cmdbActionLogMapper.insert(log);
    }

}
