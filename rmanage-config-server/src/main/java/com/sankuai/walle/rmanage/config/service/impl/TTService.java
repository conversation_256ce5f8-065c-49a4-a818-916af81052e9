package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walle.rmanage.config.constant.RgOncallTypeEnum;
import com.sankuai.walle.rmanage.config.dto.rgOncallType.RgOncallGroupModeDTO;
import com.sankuai.walle.rmanage.config.dto.rgOncallType.RgOncallNewModeDTO;
import com.sankuai.walle.rmanage.config.dto.rgOncallType.RgOncallTypeDTO;
import com.sankuai.walle.rmanage.config.dto.rgOncallType.RgOncallUserModeDTO;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource(value = {"classpath:/META-INF/app.properties"})
@Data
@Slf4j
public class TTService {

    @Value("${tt.oncallUrlPrefix}")
    public String oncallUrlPrefix;
    @Value("${tt.createUrl}")
    public String createdUrl;
    @Value("${tt.itemId}")
    public Long itemId; //cti中的三级目录id
    @Value("${tt.manageMis}")
    public String CarAssigned; // 指派人，可以不写，默认为当前oncall
    @Value("${tt.Authorization}")
    public String Authorization; //给你的BA认证
    public String SendMis = "zhaojianfeng05"; //有效的mis
    public String TTname = "车管资产变动"; // tt 标题
    public String TTdesc = "";
    public String ticketType = "事件"; //必须是（事件、服务故障、缺陷、需求、问题咨询、建议）中的一个值
    public String sla = "S4"; //必须是(S1、S2、S3、S4、S5)中的一个值，分别代表(非常紧急、紧急、高、中、低)

    public static final String ONCALL_TYPE_URL_SUFFIX = "api/1.0/oncall/setting/query";

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    private String executeRemotePostCall(Request request) {
        Response response = null;
        String content;
        try {
            response = client.newCall(request).execute();
            content = response.body().string();
        } catch (IOException e) {
            content = "{\"errors\": \"IOException\"}";
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return content;
    }

    private String createdTT(JSONObject bodyObj){
        MediaType MEDIA_TYPE_TEXT = MediaType.parse("application/json;charset=UTF-8");
        String body;
        if (bodyObj != null) {
            body = bodyObj.toJSONString();
        } else {
            body = "";
        }
        Request request = new Request.Builder()
                .url(createdUrl)
                .addHeader("Authorization", this.Authorization)
                .addHeader("Content-Type", "application/json")
                .addHeader("USERNAME", this.SendMis)
                .post(RequestBody.create(MEDIA_TYPE_TEXT, body))
                .build();
        System.out.println(request);
        String response = executeRemotePostCall(request);
        System.out.println(response);
        return response;
    }

    public void buildAndSend(String desc) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("itemId", itemId);
            jsonObject.put("name", TTname);
            jsonObject.put("assigned", CarAssigned);
            jsonObject.put("ticketType", ticketType);
            jsonObject.put("sla", sla);
            jsonObject.put("desc", desc);
            List<String> cc = new ArrayList<>();
            cc.add("mis");
            jsonObject.put("cc", cc);
            createdTT(jsonObject);
        } catch (Exception e){
            log.error("发送TT工单通知失败：", e);
        }
    }

    /**
     * 获取rg值班模式 api/1.0/oncall/setting/query
     *
     * @param rgId
     * @return
     */
    public RgOncallTypeEnum getRgOncallType(Long rgId) {
        if (rgId == null) {
            log.error("getRgOncallType参数, rgId为空");
            return null;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("rgId", rgId);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", this.Authorization);
        headers.put("USERNAME", this.SendMis);
        headers.put("Content-Type", "application/json;charset=utf-8");
        headers.put("Accept", "application/json");

        // 调用带头部的http GET 请求
        String url = oncallUrlPrefix + ONCALL_TYPE_URL_SUFFIX;
        String response = CommonUtil.doGet(url, params, headers);

        // 解析 response
        RgOncallTypeDTO rgOncallTypeDTO = JacksonUtils.from(response, new TypeReference<RgOncallTypeDTO>() {
        });
        if (rgOncallTypeDTO == null || rgOncallTypeDTO.getData() == null) {
            return null;
        }
        return RgOncallTypeEnum.getByMode(rgOncallTypeDTO.getData().getMode());

    }
    /**
     * 获取rg的oncall 分页查询rg-oncall下的user列表 默认是第1页 每页40条 一般都来说查值班都够用
     *
     * @param rgId
     * @return
     */
    public List<String> getRgOncallUserMisIdList(Long rgId, RgOncallTypeEnum rgOncallTypeEnum) {
        if (rgId == null || rgOncallTypeEnum == null) {
            log.error("getRgOncallType参数异常，rgId={}, rgOncallTypeEnum={}", rgId, rgOncallTypeEnum);
            return new ArrayList<>();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("rgId", rgId);

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", this.Authorization);
        headers.put("USERNAME", this.SendMis);
        headers.put("Content-Type", "application/json;charset=utf-8");
        headers.put("Accept", "application/json");

        // 构造url，根据rgOncallTypeEnum
        String url = rgOncallTypeEnum.constructUrl(oncallUrlPrefix);
        if (StringUtils.isBlank(url)) {
            return Collections.emptyList();
        }

        // 调用带头部的http GET 请求
        String response = CommonUtil.doGet(url, params, headers);
        log.info("url={},params={},请求结果={}", url, params, response);
        if (StringUtils.isEmpty(response)) {
            return Collections.emptyList();
        }

        // 反序列化
        List<String> result = null;
        try {
            result = getOncallUserMisIdList(response, rgOncallTypeEnum);
        } catch (Exception e) {
            log.error("解析oncall用户信息异常", e);
        }
        return result;
    }

    /**
     * 获得排班的兜底方案，调用条件：1.获取值班模式失败 2.值班模式不属于按组或按人排班 3.值班模式属于按组排班，但获取值班值班人员失败
     *
     * @param rgId
     * @return 值班人员misId列表
     */
    public List<String> fallbackRgOncallUserMisIdList(Long rgId) {
        if (rgId == null) {
            return Collections.emptyList();
        }
        return getRgOncallUserMisIdList(rgId, RgOncallTypeEnum.NEW_ONCALL_MODE);
    }

    /**
     * 根据rgOncallTypeEnum，解析response，获取值班人misIdList
     *
     * @param response
     * @param rgOncallTypeEnum
     * @return
     */
    private List<String> getOncallUserMisIdList(String response, RgOncallTypeEnum rgOncallTypeEnum) throws Exception {
        if (rgOncallTypeEnum == null || StringUtils.isBlank(response)) {
            return Collections.emptyList();
        }

        switch (rgOncallTypeEnum) {
            case BY_USER_SINGLE:
            case BY_USER_MULTI:
                RgOncallUserModeDTO rgOncallUserModeDTO = JacksonUtils.from(response,
                        new TypeReference<RgOncallUserModeDTO>() {
                        });
                if (rgOncallUserModeDTO == null) {
                    return Collections.emptyList();
                }
                return rgOncallUserModeDTO.getMisIdList();
            case BY_GROUP:
            case BY_GROUP_TIME_PERIOD:
                RgOncallGroupModeDTO rgOncallGroupModeDTO = JacksonUtils.from(response,
                        new TypeReference<RgOncallGroupModeDTO>() {
                        });
                if (rgOncallGroupModeDTO == null) {
                    return Collections.emptyList();
                }
                return rgOncallGroupModeDTO.getMidIdList();
            case NEW_ONCALL_MODE:
                RgOncallNewModeDTO rgOncallNewModeDTO = JacksonUtils.from(response,
                        new TypeReference<RgOncallNewModeDTO>() {
                        });
                if (rgOncallNewModeDTO == null) {
                    return Collections.emptyList();
                }
                return rgOncallNewModeDTO.getMisIdList();
            default:
                return Collections.emptyList();
        }
    }
}
