package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.constant.BasicConfigurationConstant;
import com.sankuai.walle.rmanage.config.dto.accident.VehicleRealtimeStatusDTO;
import com.sankuai.walle.rmanage.config.dto.accident.VehicleRealtimeStatusResponseDTO;
import com.sankuai.walle.rmanage.config.service.AccidentMessageService;
import com.sankuai.walle.rmanage.config.service.HandlePolicyService;
import com.sankuai.walle.rmanage.config.service.RedisService;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class AccidentMessageServiceImpl implements AccidentMessageService {

    @Resource
    private HandlePolicyService handlePolicyService;

    @Resource
    private DxGroupHandler dxGroupHandler;

    @Resource
    RedisService redisService;

    @MdpConfig("accident.group.grade.member")
    private ArrayList<String> groupNameList;

    @MdpConfig("dc.hostname")
    public String dateCenterHostName;

    @MdpConfig("eva.dx.host.name")
    String eveHostName;

    @MdpConfig("iiirc.host.name")
    public String iiircHostName;

    @MdpConfig("accident.speed.time.gap")
    private ArrayList<Integer> accidentSpeedTimeGap;

    @MdpConfig("get.location.name.api.key")
    private String getLocationNameApiKey;

    @Override
    public Map<String, String> getCityAndAffiliationFromRecord(String vin){
        String getCityUrl = "/accident/getCityByVin";

        String url = new StringBuilder(dateCenterHostName).append(getCityUrl).toString();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("vin", vin);
            String response = CommonUtil.doGet(url, params);
            log.info("response = {}", response);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> result = objectMapper.convertValue( objectMapper.readTree(response).get("data"), new TypeReference<Map<String, String>>() {});

            if (result != null) {
                return result;
            }
        }
        catch (Exception e){
            log.error("getCityByVin is from safety-center is failed, vin = {}", vin);
        }
        return new HashMap<>();
    }

    @Override
    public String getCarGroup(String vin){
        String getCityUrl = "eve/cms/rest/carGroupName";
        String url = new StringBuilder(eveHostName).append(getCityUrl).toString();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("vin", vin);
            String response = CommonUtil.doGet(url, params);
            ObjectMapper objectMapper = new ObjectMapper();
            String groupName = objectMapper.readTree(response).get("data").asText();
            if (groupName != null) {
                return groupName;
            }
        }
        catch (Exception e){
            log.error("getCarGroup is from eve is failed, vin = {}", vin);
        }
        return "";
    }

    @Override
    public List<String> getGroupMember( Map<String,Object> accidentDetail){
        String vehicleStatusUri = "/accident/getGroupMember";
        String url = new StringBuilder(dateCenterHostName).append(vehicleStatusUri).toString();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("vin", accidentDetail.get("vin"));
            params.put("recordName",  accidentDetail.get("record_name"));
            params.put("accidentTime",  (Long)accidentDetail.get("accident_time"));
            String response = CommonUtil.doGet(url, params);
            log.info("getGroupMember，params = {},response = {}",params, response);

            List<String> personList = new ArrayList<>();

            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode dataNode = objectMapper.readTree(response).get("data");
            if (dataNode.isArray()) {
                for (JsonNode element : dataNode) {
                    String appointment = element.asText();
                    personList.add(appointment);
                }
            }
            log.info("personList = {}", personList);

            return personList;

        }
        catch (Exception e){
            log.error("getGroupMember is from safety-center is failed");
        }

        return new ArrayList<>();
    }

    @Override
    public VehicleRealtimeStatusDTO callVehicleStatusService(String vin) {

        Map<String, Object> param = new HashMap<>();
        param.put("vinList", vin);
        StopWatch stopWatch = new StopWatch("CallVehicleStatusService");
        String vehicleStatusUri = "/iiirc/openapi/queryVehicleStatusBatch";
        Long stopWatchThreshold = 1000L;
        String url = new StringBuilder(iiircHostName).append(vehicleStatusUri).toString();
        try {
            stopWatch.start("query");
            String response = CommonUtil.doGet(url, param);
            stopWatch.stop();

            stopWatch.start("log_response");
            log.info("查询车辆实时信息-信息中心, request: {}, response: {}", param, response);
            stopWatch.stop();

            stopWatch.start("json_parse");
            VehicleRealtimeStatusResponseDTO vehicleRealtimeStatusDTO = JSON.parseObject(response,
                    VehicleRealtimeStatusResponseDTO.class);
            stopWatch.stop();

            if(stopWatch.getTotalTimeMillis() >= stopWatchThreshold) {
                long totalTimeMs = stopWatch.getTotalTimeMillis();
                String name = "";
                if(totalTimeMs >= 1000 && totalTimeMs < 2000) {
                    name = "1";
                } else if (totalTimeMs >= 2000 && totalTimeMs < 4000) {
                    name = "2";
                } else if (totalTimeMs >= 4000 && totalTimeMs < 8000) {
                    name = "3";
                } else {
                    name = "4";
                }
                Cat.logEvent("vehicle.status", name);
                log.info("[InformationApiService#callvehicleStatusService] StopWatch: {}", stopWatch.prettyPrint());
            }
            List<VehicleRealtimeStatusDTO> resultList = vehicleRealtimeStatusDTO.getData();
            if(!resultList.isEmpty()) {
               return resultList.get(0);
            }
        } catch (Exception e) {
            log.error("call url [{}] error, param: {}", url, param, e);
        }
        return new VehicleRealtimeStatusDTO();
    }

    @Override
    public void reportAccident(Map<String, Object> eventDetailMap){
        String requestUrl   =  iiircHostName + "/data-center/accident/add/v3";
        Map<String, Object> param = new HashMap<>();
        param.put("vin", eventDetailMap.get("vin"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date((Long) eventDetailMap.get("event_time")));
        param.put("accidentTime",formattedDate );
        param.put("checkLatestAccident", false );
        param.put("source", 1);
        param.put("reporter", "system");
        String response = CommonUtil.doPost(requestUrl, param,null);
        log.info("reportAccident, request = {}, response = {}", param, response);
    }

    @Override
    public String getFaultInformation(String vehicleName){

        if(vehicleName == null || vehicleName == ""){
            return "未知";
        }
        String requestUrl   =  "https://eve.sankuai.com/scan/api/v1/scan_task/action/accident_status_check";
        Map<String, Object> param = new HashMap<>();
        param.put("id", 55);
        param.put("owner", "");
        param.put("single_target_name", vehicleName );

        String response = CommonUtil.doPost(requestUrl, param,null);
        log.info("getFaultInformation, request = {}, response = {}", param, response);

        try{
            ObjectMapper objectMapper = new ObjectMapper();
            String faultInformation = objectMapper.readTree(response).get("msg").asText();
            if (faultInformation != null) {
                return faultInformation.trim();
            }
        } catch (Exception e) {
            log.error("getFaultInformation is from safety-center is failed", e);
        }
        return "";
    }

    /**
     * 获取碰撞检测警报信息
     *
     * @param vin       车辆识别号码
     * @param eventTime 事件发生时间
     * @return 碰撞检测警报信息，如果获取失败则返回“未知”
     */
    public String getCollisionDetectionAlert(String vin, Long eventTime) {
        // 构建请求URL
        String requestUrl = iiircHostName + "/cloud_triage/api/collisionDetectionAlert/get";
        // 创建请求参数
        Map<String, Object> param = new HashMap<>();
        param.put("vin", vin);
        param.put("eventTime", eventTime);

        try {
            // 发送GET请求获取碰撞检测警报信息
            String response = CommonUtil.doGet(requestUrl, param);
            log.info("getCollisionDetectionAlert, request = {}, response = {}", param, response);
            // 解析响应中的碰撞标签
            String alert = JacksonUtils.getAsString(response, "data");
            if (StringUtils.isNotBlank(alert) && !alert.equals("null")) {
                return alert;
            }
        } catch (Exception e) {
            // 捕获并记录异常
            log.error("getCollisionDetectionAlert error", e);
        }
        // 如果获取失败，返回“未知”
        return CommonConstants.UNKNOWN_CHINESE;
    }


    @Override
    public Map<String, Object> getVehicleHistoryStatus(String vin, Date accidentTime) {

        // startTimestamp 表示事故发生前的时刻，  endTimestamp 表示事故发生后的时刻
        Long startTimestamp = accidentTime.getTime() - accidentSpeedTimeGap.get(0) * 1000L;
        Long endTimestamp   = accidentTime.getTime() + accidentSpeedTimeGap.get(1) * 1000L;

        String url = String.format("%s/iiirc/openapi/vehicleStatusHistory?vin=%s&startTimestamp=%s&endTimestamp=%s",
               iiircHostName, vin, startTimestamp, endTimestamp);
        int maxRetryTime = 3;
        String response = "";
        for (int i = 0; i < maxRetryTime; i++) {
            response = CommonUtil.doGet(url,null);
            if (StringUtils.isNotBlank(response)) {
                break;
            }
        }
        if (StringUtils.isBlank(response)) {
            log.error("getVehicleHistoryStatus failed, url is [{}]", url);
            return null;
        }
        Map<String, Object> responseMap = (Map<String, Object>) JSON.parseObject(response, Map.class);
        log.info("getVehicleHistoryStatus success, url is [{}], response is [{}]", url, responseMap);
        if ((int) responseMap.getOrDefault("ret", 1000) != 0) {
            return null;
        }
        List<Object> dataList = (List<Object>) responseMap.get("data");
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        //从获取的数据列表中获取数据,取最后一帧数据，此时距离发生时刻最近
        Map<String, Object> data = (Map<String, Object>) dataList.get(dataList.size()-1);
        if (data == null || data.isEmpty()) {
            return null;
        }
        return data;
    }

    @Override
    public long gradeGroupScale(Long id) {
        List<String> members = new ArrayList<>();
        for(String groupName: groupNameList){
            members.addAll( handlePolicyService.getMisGroupMemberList(groupName));
        }
        //获取群号
        long groupId = redisService.getGroupIdInRedis(BasicConfigurationConstant.REDIS_GROUP_ID_REFLECT_CATEGORY, id);
        log.info("升级群聊，groupId = {},members = {}",groupId, members);
        dxGroupHandler.addGroupMember(groupId, members);
        return groupId;
    }

    @Override
    public String getRoadType(Map<String, Object> accidentDetail) {
        String getRoadTypeUri = "/accident/getRoadType";
        String url = new StringBuilder(dateCenterHostName).append(getRoadTypeUri).toString();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("locationGps", accidentDetail.get("location_gps"));
            params.put("recordName",  accidentDetail.get("record_name"));
            String response = CommonUtil.doGet(url, params);
            log.info("getGroupMember，params = {},response = {}",params, response);
            if(StringUtils.isNotBlank(response)){
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readTree(response).get("data").asText();
            }
        }
        catch (Exception e){
            log.error("getRoadType is failed",e);
        }
        return "未知";
    }

    @Override
    public String getLocationNameByGps(String locationGps) {
        if(StringUtils.isBlank(locationGps)){
            return "未知";
        }
        String queryLocationNamePath = "https://lbsapi.sankuai.com/v1/location/regeo";
        Map<String,Object> params = new HashMap<>();
        params.put("location", locationGps);
        params.put("key", getLocationNameApiKey);
        try {
            String response = CommonUtil.doGet(queryLocationNamePath, params);
            log.info("getLocationNameByGps，params = {},response = {}",params, response);
            if(StringUtils.isNotBlank(response)){
                //尽量一次解析json，避免失败
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(response);
                int status = jsonNode.get("status").asInt();
                if(status == 200){
                    return jsonNode.get("regeocode").get(0).get("formatted_address").asText();
                }
            }
        }
        catch (Exception e){
            log.error("getLocationNameByGps is failed, locationGps = {}",locationGps,e);
        }
        return "未知";
    }
}
