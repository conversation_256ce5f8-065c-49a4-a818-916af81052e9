package com.sankuai.walle.rmanage.config.aviator;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;

@Slf4j
public class LevenshteinFunction extends AbstractFunction {
    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
        Map<String,Object> baseMap = (Map<String, Object>) FunctionUtils.getJavaObject(arg1, env);
        Map<String,Object> accidentDetail = (Map<String, Object>) FunctionUtils.getJavaObject(arg2, env);

        for(String str : baseMap.keySet()){
           if(!accidentDetail.containsKey(str) || !accidentDetail.get(str).toString().equals(baseMap.get(str).toString())){
               return AviatorBoolean.valueOf(false);
           }
        }
        return AviatorBoolean.valueOf(true);
    }
    @Override
    public String getName() {
        return "LevenshteinFunction";
    }
}
