package com.sankuai.walle.rmanage.config.Listener.strategy;

import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigVehicleTaskStatus;
import com.sankuai.walle.dal.mrm_manage.example.AutoConfigVehicleTaskStatusExample;
import com.sankuai.walle.dal.mrm_manage.mapper.AutoConfigVehicleTaskStatusMapper;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.objects.bo.RealTimeObj;
import com.sankuai.walle.objects.constants.CarConstant;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.rmanage.config.common.constant.AutoConfigTaskStatusEnum;
import com.sankuai.walle.rmanage.config.common.constant.AutoConfigTaskTypeEnum;
import com.sankuai.walle.rmanage.config.service.MafkaRealtimeService;
import com.sankuai.walle.rmanage.config.service.VehicleDataBusService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 连接消息处理策略
 * 处理客户端连接相关的消息，包括：
 * 1. 更新客户端连接状态
 * 2. 记录连接日志
 * 3. 处理连接异常情况
 */
@Component
@Slf4j
public class ConnectMessageHandler implements MessageHandlerStrategy {
    /**
     * 处理连接消息的具体实现
     * @param message 包含连接信息的消息对象
     * @throws Exception 处理过程中可能抛出的异常
     */
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    AutoConfigVehicleTaskStatusMapper autoConfigVehicleTaskStatusMapper;
    @Resource
    VehicleDataBusService vehicleDataBusService;
    @Resource
    MafkaRealtimeService mafkaRealtimeService;
    @Override
    public void handle(AukUpCountent message) throws Exception {
        connectFunc(message);
        sendSn(message); // 发送
    }

    private void connectFunc(AukUpCountent message ){
        String vin = String.valueOf(message.getDeviceKey());
        // 查看没有下发完的配置
        CarDeviceConfigExample query = new CarDeviceConfigExample();
        query.createCriteria().andVinEqualTo(vin).andStatusIn(Arrays.asList(ConfigConstant.SEND, ConfigConstant.ERROR));
        List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
        log.info("将要下发的消息，{}",configs);
        for (CarDeviceConfig target : configs) {
            // 重新下发
            try {
                carDeviceConfigService.retryConfigToMqtt(target);
            } catch (Exception e) {
                log.error("下发配置失败：【{}】", e.toString());
                throw new RuntimeException(e);
            }
        }
    }

    private void sendSn(AukUpCountent message) {
        String vin = String.valueOf(message.getDeviceKey());
        log.info("#收到绑定车辆vin和sn的消息 : vin = {} #", vin);
        // 查询vin未完成的任务
        AutoConfigVehicleTaskStatusExample autoConfigVehicleTaskStatusExample = new AutoConfigVehicleTaskStatusExample();
        autoConfigVehicleTaskStatusExample.createCriteria()
                .andVinEqualTo(vin)
                .andTaskTypeEqualTo(AutoConfigTaskTypeEnum.GET_SN.getCode())
                .andTaskStatusNotEqualTo(AutoConfigTaskStatusEnum.COMPLETED.getCode());
        List<AutoConfigVehicleTaskStatus> autoConfigVehicleTaskStatuses = autoConfigVehicleTaskStatusMapper.selectByExample(autoConfigVehicleTaskStatusExample);

        // 未创建过获取SN的任务，直接返回
        if(autoConfigVehicleTaskStatuses.size() == 0) return;

        // 如果sn是null，要重试3次
        String sn = null;
        int retryCount = 0;
        while(sn==null && retryCount<=3){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 调用数据总线，获取sn
            VehicleDataBusVTO vehicleInfo = vehicleDataBusService.getVehicleInfoString(vin);
            if (vehicleInfo == null || vehicleInfo.getMonitor() == null) {
                log.error("从总线获取车辆信息失败，vin: {}", vin);
                return;
            }
            // 发送mafka消息
            sn = vehicleInfo.getMonitor().getSn();
            retryCount++;
        }
        if (sn==null){
            log.error("配置管理绑定slab时，从总线获取sn失败，{}",vin);
            return;
        }
        try {
            RealTimeObj realTimeObj = new RealTimeObj();
            realTimeObj.setSn(sn);
            realTimeObj.setVin(vin);
            realTimeObj.setKey(CarConstant.CarRealtimeKey);
            mafkaRealtimeService.sendRealTime(realTimeObj);
        } catch (Exception e) {
            log.error("配置管理绑定slab时，发送mafka消息失败");
            throw new RuntimeException(e);
        }
        // 完成上电任务
        autoConfigVehicleTaskStatuses.stream()
                .findFirst()
                .ifPresent(x -> {
                    x.setTaskStatus(AutoConfigTaskStatusEnum.COMPLETED.getCode());
                    autoConfigVehicleTaskStatusMapper.updateByPrimaryKeySelective(x);
                });
    }
} 