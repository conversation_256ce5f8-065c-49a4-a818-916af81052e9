package com.sankuai.walle.rmanage.config.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import com.sankuai.walle.dal.eve.mapper.CarTypesMapper;
import com.sankuai.walle.dal.eve.mapper.DeviceTypesMapper;
import com.sankuai.walle.dal.eve.mapper.VehicleModelMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectsMapper;
import com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper;
import com.sankuai.walle.rmanage.config.helper.ConfigTokenAnalyze;
import com.sankuai.walle.rmanage.config.service.infrastructureService.LexicalAnalyzerService;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.sankuai.walle.rmanage.config.common.constant.ConfigForVehicleTypeConstants.ConfigForVehicleTypeList;

@Service
@Slf4j
public class LexicalAnalyzerServiceImpl implements LexicalAnalyzerService {

    @Resource
    CarObjectsMapper carObjectsMapper;
    @Resource
    CarSelectsMapper carSelectsMapper;
    @Resource
    CarTypesMapper carTypesMapper;
    @Resource
    VehicleModelMapper vehicleModelMapper;
    @Resource
    DeviceTypesMapper deviceTypesMapper;
    @Resource
    VehicleInfoMapper vehicleInfoMapper;

//    List<String> configForVehicleTypeList = Arrays.asList("h24");

    // S3车型映射关系, 统一使用一样的车型映射关系
    @MdpConfig
    HashMap<String, ArrayList<String>> carTypeMap;
    // 其他车型映射关系
    @MdpConfig
    HashMap<String, ArrayList<String>> otherCarTypeMap;
    // 创建映射关系
    // 车型设备关联关系
    private LexicalAnalyzerServiceImpl() {
        //
//        carTypeMap.put("1", Arrays.asList("s20","EP2"));
//        carTypeMap.put("2", Arrays.asList("s20","EP1-1"));
//        carTypeMap.put("3",Arrays.asList("s20","EP1-2"));
//        carTypeMap.put("4",Arrays.asList("s20","EP1-3"));
//        carTypeMap.put("23",Arrays.asList("D23","EP1"));
//        carTypeMap.put("22",Arrays.asList("D24","EP1"));
        // 移动到S3
//        carTypeMap.put("h24",Arrays.asList("h24","EP1","big","MADC2.1","TBU","jili"));
//        otherCarTypeMap.put("h24",Arrays.asList("h24","EP1","big","MADC2.1","TBU","jili"));
//        otherCarTypeMap.put("ep2",Arrays.asList("s20","EP2","middle","IPC","slab2.0","meituan"));
    }
    protected void finalize() throws Throwable {
        // 在这里进行资源释放等操作
        super.finalize();
    }


    @Override
    public String getConfigContent(String input, String vin) {
        List<CarObjects> carObjList = carObjectsMapper.selectByExample(new CarObjectsExample() {{
            createCriteria().andVinEqualTo(vin);
        }});
        Map<String,CarObjects> carObjList2 = carObjectsMapper.selectByExample(new CarObjectsExample() {{
            createCriteria().andVinEqualTo(vin);
        }}).stream().collect(() -> new HashMap<>(), (map, obj) -> map.put(obj.getVin(), obj), Map::putAll);
        String typename = fetchTypeName(vin, carObjList2.get(vin));
//        if (carTypeMap.get(typename)!=null){
//            // S3的新逻辑
//            CarObjects carObject = carObjList2.get(vin);
//            if(carObject!=null){
//                StringBuilder res = new StringBuilder();
//                ConfigTokenAnalyze tokenAnalyze = new ConfigTokenAnalyze(input);
//                List<ConfigTokenAnalyze.Token> tokens = tokenAnalyze.getTokens();
//                log.info("<变量转换后的tokens：{}>", tokens);
//                for (ConfigTokenAnalyze.Token token : tokens) {
//                    if (Objects.equals(token.getType(), ConfigTokenAnalyze.myTokenType.VARIABLE)) {
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.NAME)) {
//                            res.append(carObject.getName());
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.VIN)) {
//                            res.append(vin);
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.LicenseNo)) {
//                            res.append(carObject.getLicenseno());
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.TYPE)) {
//                            // 车型，先只支持H24
//                            typename = fetchTypeName(vin, carObjList2.get(vin));
//                            if (typename != null && ConfigForVehicleTypeList.contains(typename)) {
//                                res.append(carTypeMap.get(typename).get(0));
//                            }
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.PHASE)) {
//                            // 批次，先只支持H24
//                            if (typename==null){
//                                typename = fetchTypeName(vin, carObjList2.get(vin));
//                            }
//                            if (ConfigForVehicleTypeList.contains(typename)) {
//                                res.append(carTypeMap.get(typename).get(1));
//                            }
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.SIZE)) {
//                            if (typename==null){
//                                typename = fetchTypeName(vin, carObjList2.get(vin));
//                            }
//                            if (ConfigForVehicleTypeList.contains(typename)) {
//                                res.append(carTypeMap.get(typename).get(2));
//                            }
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.COMPUTE_NAME)) {
//                            if (typename==null){
//                                typename = fetchTypeName(vin, carObjList2.get(vin));
//                            }
//                            if (ConfigForVehicleTypeList.contains(typename)) {
//                                res.append(carTypeMap.get(typename).get(3));
//                            }
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.SWITCH_NAME)) {
//                            if (typename==null){
//                                typename = fetchTypeName(vin, carObjList2.get(vin));
//                            }
//                            if (ConfigForVehicleTypeList.contains(typename)) {
//                                res.append(carTypeMap.get(typename).get(4));
//                            }
//                        }
//                        if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.VENDOR)){
//                            if (typename==null){
//                                typename = fetchTypeName(vin, carObjList2.get(vin));
//                            }
//                            if (ConfigForVehicleTypeList.contains(typename)) {
//                                res.append(carTypeMap.get(typename).get(5));
//                            }
//                        }
//                    } else {
//                        res.append(token.getCount());
//                    }
//                }
//                log.info("<变量转换后的数据：{}>", res);
//                return res.toString();
//            } else {
//                log.info("<变量转换时，vin不存在：{}>", vin);
//                return input;
//            }
//        }else{
//        }
        // 发到车端的逻辑
        if (carObjList.size()>0) {
            CarObjects carObject = carObjList.get(0);
            StringBuilder res = new StringBuilder();
            ConfigTokenAnalyze tokenAnalyze = new ConfigTokenAnalyze(input);
            List<ConfigTokenAnalyze.Token> tokens = tokenAnalyze.getTokens();
            log.info("<变量转换后的tokens：{}>", tokens);
            ArrayList<String> configLionMap = otherCarTypeMap.get(typename);
            for (ConfigTokenAnalyze.Token token : tokens) {
                if (Objects.equals(token.getType(), ConfigTokenAnalyze.myTokenType.VARIABLE)) {
                    if (configLionMap==null){
                        log.error("<车型映射关系不存在：{},vin: {}>", typename,vin);
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.NAME)) {
                        res.append(carObject.getName());
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.VIN)) {
                        res.append(vin);
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.LicenseNo)) {
                        res.append(carObject.getLicenseno());
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.TYPE)) {
                        // 车型
                        res.append(configLionMap.get(0));
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.PHASE)) {
                        // 批次
                        res.append(configLionMap.get(1));
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.SIZE)) {
                        // 尺寸
                        res.append(configLionMap.get(2));
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.COMPUTE_NAME)) {
                        // 计算单元
                        res.append(configLionMap.get(3));
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.SWITCH_NAME)) {
                        // 网关
                        res.append(configLionMap.get(4));
                    }
                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.VENDOR)){
                        // 供应商
                        res.append(configLionMap.get(5));
                    }
                } else {
                    res.append(token.getCount());
                }
            }
            log.info("<变量转换后的数据：{}>", res);
            return res.toString();
        } else {
            log.info("<变量转换时，vin不存在：{}>", vin);
            return input;
        }

    }



    private String fetchTypeName(String vin, CarObjects obj){
        List<CarSelects> carSelects = carSelectsMapper.selectByExample(new CarSelectsExample() {{
            createCriteria().andTypeEqualTo(obj.getCarType());
            limit(1);
        }});
        if (carSelects.isEmpty()) {
            return null;
        }
        CarSelects carSelect = carSelects.get(0);
        String typename = Strings.toLowerCase(carSelect.getName());
        return typename;
    }



}
