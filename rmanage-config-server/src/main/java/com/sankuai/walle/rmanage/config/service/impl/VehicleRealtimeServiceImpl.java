package com.sankuai.walle.rmanage.config.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.objects.bo.RealTimeObj;
import com.sankuai.walle.objects.constants.CarConstant;
import com.sankuai.walle.rmanage.config.service.MafkaRealtimeService;
import com.sankuai.walle.rmanage.config.service.VehicleRealtimeService;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.sankuai.walle.rmanage.config.common.constant.ConfigForVehicleTypeConstants.ConfigForVehicleTypeList;

/**
 * 车辆实时消息服务实现
 * 完全保持原有的sendRealTime逻辑
 */
@Service
@Slf4j
public class VehicleRealtimeServiceImpl implements VehicleRealtimeService {
    
    @Resource
    private MafkaRealtimeService realtimeService;
    
    @MdpConfig
    private ArrayList<String> configS3Vname;
    
    @Override
    public void sendRealTime(String vin, CarOperation carOperation, String name, String type) throws Exception {
        // 新增和编辑完成，要做的操作，发送 mafka、生成配置
        if (carOperation == null) {
            carOperation = new CarOperation();
        }
        CarOperation finalCarOperation = carOperation;
        realtimeService.sendRealTime(new RealTimeObj(){{
            setKey(CarConstant.CarRealtimeKey);
            setVin(vin);
            setUpdate_time(new Date());
            setUse_reason(finalCarOperation.getCarUsedTarget());
            if(finalCarOperation.getCarUsedTarget() == null | com.google.common.base.Strings.isNullOrEmpty(finalCarOperation.getCarUsedTarget())
                    | Objects.equals(finalCarOperation.getCarUsedTarget(), "在库")) {
                setUse_status("在库");
            }else{
                setUse_status("出库");
            }
        }});

        // 生成配置
        type = type!=null ? Strings.toLowerCase(type) : null;
        name = name!=null ? Strings.toLowerCase(name) : null;
        // 类型和车名都要在白名单中，白名单要小写
        // TODO 自动更新 CAR_INFO
        if (ConfigForVehicleTypeList.contains(type) && configS3Vname.contains(name)) {
//            configService.sendCarInfoConfigToS3(vin, name);
        }else{
            log.info("sendConfigToS3 info: "+vin+" 不在自动生成S3配置的白名单内");
        }

    }


} 