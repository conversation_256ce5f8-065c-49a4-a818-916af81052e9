package com.sankuai.walle.rmanage.config.dto.vehicleManage;

import lombok.Data;

import java.util.List;

@Data
public class RemoteCarTypeDTO {
    private Long id;

    /**
     *   字段: type_name
     *   说明: 类型名
     */
    private String typeName;

    /**
     *   字段: friend_name
     *   说明: 类型别名
     */
    private String friendName;

    /**
     *   字段: father_type
     *   说明: 父级类型
     */
    private String fatherType;

    // 定义了一个私有变量children，用于存储远程汽车类型的子类型列表。
    private List<RemoteCarTypeDTO> children;
}
