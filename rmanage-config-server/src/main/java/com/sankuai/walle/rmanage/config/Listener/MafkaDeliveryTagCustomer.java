package com.sankuai.walle.rmanage.config.Listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.rmanage.config.constant.ConfigEnumConstant;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service("deliver.tag.customer")
public class MafkaDeliveryTagCustomer {

    //保存拉取到的消息
    @Resource
    ConfigService configService;
    @Autowired
    ConfigEnumConstant configEnumConstant;

    private static final String DELIVERY_TAG_NAME = "DELIVERY_TAG";

    @MdpMafkaMsgReceive
    protected ConsumeStatus receive(String msgBody){
        //判断configName是否有效
        if (!configEnumConstant.contains(DELIVERY_TAG_NAME)) {
            throw new RuntimeException("Request configName is invalid");
        }
        // 将接收到的车辆标签配置信息入库并下发
        List<SendExcelDeviceConfigReq> config = new ArrayList<>();
        log.info("get deliver's tag msg: {}",msgBody);
        // msg序列化为json
        JSONObject jsonObject = JSONObject.parseObject(msgBody);
        String configName = jsonObject.getString("configName");
        if (Strings.isNotBlank(configName)) {
            // 有配置项的，按配置项下发
            String vin = jsonObject.getString("vin");
            String data = jsonObject.getString("data");
            SendExcelDeviceConfigReq req = new SendExcelDeviceConfigReq();
            req.setConfigName(configName);
            req.setContent(data);
            req.setVin(vin);
            req.setFileType(ConfigConstant.JSON_CONFIG_FILE_TYPE);
            req.setUser("cmsSystem");
            config.add(req);
            log.info("MafkaDeliveryTagCustomer 配置下发: {}", config);
        } else {
            // 无配置项的，按标签下发
            JSONArray vins = jsonObject.getJSONArray("vins");
            String tags = jsonObject.getString("tags");
            // 检查是否为空或者null
            if (vins == null || vins.isEmpty() || Strings.isBlank(tags)) {
                log.error("vins or tags is null");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // vins去重
            Set<String> vinSet = new HashSet<>(vins.toJavaList(String.class));
            for (Object vin : vinSet) {
                SendExcelDeviceConfigReq req = new SendExcelDeviceConfigReq();
                req.setConfigName(DELIVERY_TAG_NAME);
                req.setContent(tags);
                req.setFileType(ConfigConstant.JSON_CONFIG_FILE_TYPE);
                req.setVin((String) vin);
                req.setUser("deliverSystem");
                config.add(req);
            }
        }

        configService.sendConfigToCar(config);
        //返回状态说明：①返回CONSUME_SUCCESS，表示消费成功准备消费下一条消息。
        //            ②返回RECONSUME_LATER，表示请求再次消费该消息，默认最多三次，然后跳过此条消息的消费，开始消费下一条。(算上初始最多消费4次）
        //            ③返回CONSUMER_FAILURE，表示请求继续消费，直到消费成功。
        //注意：如果不想在消费异常时一直进行重试，造成消息积压，可以返回RECONSUME_LATER，详细设置可以看下右上角HELP文档->高阶特性->消费异常重试次数设置
        return ConsumeStatus.CONSUME_SUCCESS;
    }


}
