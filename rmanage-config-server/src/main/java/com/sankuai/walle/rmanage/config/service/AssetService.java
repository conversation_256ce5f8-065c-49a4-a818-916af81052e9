package com.sankuai.walle.rmanage.config.service;

import com.meituan.finerp.eam.dto.OpenAssetDTO;
import com.sankuai.walle.carManage.entity.CarAssets;

import java.util.List;

public interface AssetService {
    void syncRemoteObjectFromOpenAsset() throws Exception;

    void checkVin();

    void syncCarObject() throws Exception;

    // 获取所有车的vin
    List<String> queryAllVins();

    void batchUpdateAssetOwnerDepartment();

    void asyncUpdateOperationData();

    // 从海鸥同步车辆信息
    /* 策略
新增：
1、已有label，跳过。
2，新增label，
1）vin存在，对应的label为null，跳过。
2）vin存在，对应的label不是null，新增。
3）vin不存在，新增。

更新：
1、已有label，更新。
2、label是null
1）vin不存在，跳过
2）vin存在，更新*/
    void syncObject(List<OpenAssetDTO> openAssetDTOS) throws Exception;

    CarAssets fetchCarAssetsByVin(String vin);
}
