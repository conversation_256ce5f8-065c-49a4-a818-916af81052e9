package com.sankuai.walle.rmanage.config.convertor;

import com.sankuai.walle.cmdb.thrift.service.CarDeviceModel;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.eve.entity.CarDevices;

import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface VehicleModelMapper {

    VehicleModelMapper MAPPER = Mappers.getMapper(VehicleModelMapper.class);


    CarDeviceModel carDeviceToThriftModel(CarDevices carDevices);

}
