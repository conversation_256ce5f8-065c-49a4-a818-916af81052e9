package com.sankuai.walle.rmanage.config.aviator;

import com.googlecode.aviator.AviatorEvaluator;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;

@Configuration
public class RuleEngine {
    @PostConstruct
    public void aviatorEvaluator() {
        AviatorEvaluator.addFunction(new LevenshteinFunction());
        AviatorEvaluator.addFunction(new eventFilterFunction());
    }
}
