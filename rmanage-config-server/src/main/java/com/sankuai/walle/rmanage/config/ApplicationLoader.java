package com.sankuai.walle.rmanage.config;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Priority;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.sankuai.walle.rmanage.config","com.sankuai.walle.schedule.service"})
@PropertySource("classpath:/META-INF/app.properties")
@EnableAsync
public class ApplicationLoader {

    public static void main(String[] args) {
        String status = "success";
//        Transaction t = Cat.newTransaction("test", "wallcmdb.auk.config.test");
//        try {
//            Cat.logEvent("test","hello world");
//            Cat.logMetricForCount("wallcmdb.auk.config.test");
//
//            t.setStatus(Transaction.SUCCESS);
//        }finally {
//
//            t.complete();
//        }
//        MetricHelper.build().name("wallcmdb.auk.config.test").tag("status", status).count(1);
//        MetricHelper.build().name("wallcmdb.auk.config.test").tag("status", "fail").count(1);
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }
}


