package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.dal.eve.entity.CarDevices;
import com.sankuai.walle.objects.constants.CarDeviceTypeEnum;
import com.sankuai.walle.objects.vo.RelatedDeviceInfoVO;
import com.sankuai.walle.objects.vo.request.VehicleModelCreateRequest;
import com.sankuai.walle.objects.vo.request.VehicleModelDeviceRelationRequest;
import com.sankuai.walle.rmanage.config.common.exception.DuplicateCategoryException;
import com.sankuai.walle.rmanage.config.constant.enums.VehicleModelAddTypeEnum;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelDTO;

import java.util.List;

public interface VehicleModelManageService {

   void insertVehicleModelInfo(Long fatherId, VehicleModelDTO vehicleModelDTO, String misId, VehicleModelAddTypeEnum typeEnum);

   List<CarSelects> getCarSelects(CarSelects carSelects);

   Long insertCarSelect(CarSelects carSelects);

   void relateVehicleModelDevice(VehicleModelDeviceRelationRequest request) throws DuplicateCategoryException;

   void deleteVehicleModelDeviceRelation(Long relatedId);

   List<RelatedDeviceInfoVO> queryDeviceInfoByDeviceType(Long vehicleModelId, Integer level);

   void relateVehicleModelDevice(String vin, String sn, CarDeviceTypeEnum type, String mac);

   List<CarDevices> queryDevicesByVin(String vin);
}
