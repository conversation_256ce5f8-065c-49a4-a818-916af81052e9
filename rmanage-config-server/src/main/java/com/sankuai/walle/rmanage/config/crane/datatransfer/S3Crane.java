package com.sankuai.walle.rmanage.config.crane.datatransfer;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.walle.rmanage.config.service.appService.ConfigStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

@Slf4j
@CraneConfiguration
public class S3Crane {

    @Autowired
    AmazonS3 s3client;
    @Resource
    ConfigStoreService configStoreService;

    @Value("${autocar.s3.bucketName}")
    String bucketName;

    @Crane("cmdb-S3-transfer")
    public void copyObjectExample(){
        List<S3ObjectSummary> data = configStoreService.listObjects("config/vehicle/");
        // 遍历data，如果child结尾是/，则从data中剔除
        Iterator<S3ObjectSummary> iterator = data.iterator();
        while (iterator.hasNext()) {
            S3ObjectSummary child = iterator.next();
            if (child != null) {
                if (child.getKey().endsWith("/")) {
                    iterator.remove();
                }else{
                    System.out.println(child.getKey());
                    String key = child.getKey();
                    String[] list = key.split("config/vehicle");
                    if (list.length > 1) {
                        log.info("遍历S3，获取的文件：{}",list[1]);
                        transfer(key,list[1]);
                    }
                }
            }
        }


    }

    public void transfer(String sourceObjectName,String destFile){

        try{
            String destObjectName = "config_simulation/vehicle"+destFile;
            CopyObjectRequest copyObjRequest = new CopyObjectRequest(
                    bucketName, sourceObjectName, bucketName, destObjectName
            );

            s3client.copyObject(copyObjRequest);

        } catch (AmazonServiceException ase) {
            // 存储服务端处理异常
            log.error("Caught an S3 ServiceException." +"\n"+
            "Error Message:    " + ase.getMessage() +"\n"+
            "HTTP Status Code: " + ase.getStatusCode() +"\n"+
            "Error Code:   " + ase.getErrorCode() +"\n"+
            "Error Type:       " + ase.getErrorType() +"\n"+
            "Request ID:       " + ase.getRequestId());

        } catch (AmazonClientException ace) {
            // 客户端处理异常
            log.error("Caught an S3 ClientException." +"\n"+
            "Error Message: " + ace.getMessage());
        } catch (Exception e) {
            log.error("Caught an S3 Exception." +"\n"+
            "Error Message: " + e.getMessage());
        }
    }


}
