package com.sankuai.walle.rmanage.config.policy;

import com.alibaba.druid.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.walle.dal.mrm_manage.entity.BizHandlePolicyInfo;
import com.sankuai.walle.rmanage.config.component.ActionFunc;
import com.sankuai.walle.rmanage.config.dto.accident.VehicleRealtimeStatusDTO;
import com.sankuai.walle.rmanage.config.service.AccidentMessageService;
import com.sankuai.walle.rmanage.config.service.HandlePolicyService;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("walle_data_center.biz_accident_info")
@Slf4j
public class AccidentPolicy extends PolicyBase {
    @Resource
    private HandlePolicyService handlePolicyService;

    @Autowired
    private ActionFunc actionFunc;

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    AccidentMessageService accidentMessageService;

    @Override
    public boolean run(Map<String, Object> accidentDetailMap) {
        //1 获取表达式
        Expression compiledExpression = setRule();
        Map<String, Pair<String, Object>> actionsMap = new HashMap<>();

        // 2 获取全量在线策略
        List<BizHandlePolicyInfo> policyInfos = getPolicyList();
        if(policyInfos == null){
            return false;
        }

        //3 遍历全量策略，并挑选出命中策略需要执行的处置动作集合，该集合为无重复并集
        //todo：对于范围类型的数据（判断x是否属于(0,10)）,我的想法是将 x 重新赋值后再进行判等操作
        policyInfos.stream().forEach(policyInfo -> {
            Map<String, Object> policyMap = null;
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                policyMap = objectMapper.readValue(policyInfo.getPolicyValue(), new TypeReference<Map<String, Object>>() {
                });
            } catch (Exception e) {
                log.error("json to map is failed");
            }
            Map<String, Object> variables = new HashMap<>();
            variables.put("map1", policyMap);
            variables.put("map2", accidentDetailMap);
            boolean isMatch = (boolean) compiledExpression.execute(variables);
            if (isMatch) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map<String, Object> tempMap = objectMapper.readValue(policyInfo.getHandleMethod(), new TypeReference<Map<String, Object>>() {
                    });

                    for (String action : tempMap.keySet()) {
                        Object param = tempMap.get(action);
                        String key = action + "/" + param.toString();
                        if (actionsMap.containsKey(key)) {
                            continue;
                        }
                        actionsMap.put(key, new Pair<>(action, param));
                    }
                } catch (Exception e) {
                    log.error("json to map is failed");
                }
            }
            log.info("policyInfo is = {}, isMatch = {}", policyInfo.getPolicyName(), isMatch);

        });
        log.info("all action is {}", actionsMap);

        //4 执行动作 创建处置动作类
        try{
            //防止策略不能覆盖所有场景进行兜底，当没有匹配任何策略时，默认拉群
            if(actionsMap.isEmpty()){
                createDXGroupByFallback(accidentDetailMap);
            }
            else{
                for(String key : actionsMap.keySet()) {
                    Pair<String, Object> action = actionsMap.get(key);
                    List<Object> params = new ArrayList<>();
                    if (action.getValue() != null && !action.getValue().equals("")) {
                        params.add(action.getValue());
                    }
                    params.add(accidentDetailMap);
                    runAction(action.getKey(), params);
                }
            }
        }
        catch (Exception e){
            log.error("startAction is error",e );
        }
        return true;
    }

    public List<BizHandlePolicyInfo> getPolicyList(){
        List<BizHandlePolicyInfo> policyInfos = null;
        try {
            policyInfos = (List<BizHandlePolicyInfo>)handlePolicyService.queryAllEnableHandlePolicy();
            log.info("AccidentPolicy, policyInfos = {}", policyInfos);
        } catch (Exception e) {
            log.error("queryAllEnableHandlePolicy is failed");
        }
        return policyInfos;
    }

    @Override
    public Expression setRule(){
        String expression = "LevenshteinFunction(map1, map2)";
        return AviatorEvaluator.compile(expression);
    }

    public void runAction(String functionName, List<Object> params) throws  InvocationTargetException, IllegalAccessException {
        Class<?> clazz = actionFunc.getClass();
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals(functionName)) {
                method.setAccessible(true);
                Object bean = applicationContext.getBean(clazz);
                // 构造参数数组
                Object[] parameters = new Object[method.getParameterCount()];
                for (int i = 0; i < method.getParameterCount(); i++) {
                    // 根据参数类型进行相应的处理
                    if (method.getParameterTypes()[i] == String.class) {
                        parameters[i] = (String)params.get(i); // 设置String类型参数的值
                    } else if (method.getParameterTypes()[i] == Map.class) {
                        parameters[i] = (Map)params.get(i); // 设置Map类型参数的值
                    } else {
                        // 其他参数类型的处理
                    }
                }
                log.info("functionName = {} is running, parameters is {}", functionName, parameters);
                method.invoke(bean, parameters);
                break;
            }
        }
    }

    public void accidentDetailMapAddInfo(Map<String, Object> accidentDetailMap){
        String vin = String.valueOf(accidentDetailMap.get("vin"));
        if(StringUtils.isEmpty(vin)){
            return;
        }
        VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = accidentMessageService.callVehicleStatusService(vin);
        String place =vehicleRealtimeStatusDTO.getParkDesc();
        if(StringUtils.isEmpty(place)){
            place = "未知";
        }

        String purpose =vehicleRealtimeStatusDTO.getPurpose();
        if(StringUtils.isEmpty(purpose)){
            purpose = "未知";
        }
        accidentDetailMap.put("place", place);
        accidentDetailMap.put("purpose",purpose);

        log.info("input data source is {}", accidentDetailMap);
    }

    private void createDXGroupByFallback(Map<String, Object> accidentDetailMap) throws InvocationTargetException, IllegalAccessException {
        log.info("createDXGroupByFallback is running!, accidentDetailMap = {}", accidentDetailMap);
        List<Object> params = new ArrayList<>();
        params.add("");
        params.add(accidentDetailMap);
        runAction("create_group", params);
    }

}
