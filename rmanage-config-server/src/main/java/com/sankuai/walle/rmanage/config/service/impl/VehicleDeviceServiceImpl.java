package com.sankuai.walle.rmanage.config.service.impl;

import com.sankuai.walle.cmdb.thrift.model.Device;
import com.sankuai.walle.cmdb.thrift.model.VehicleDevice;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.dal.eve.entity.VehicleModel;
import com.sankuai.walle.dal.eve.example.VehicleModelExample;
import com.sankuai.walle.dal.eve.mapper.DeviceTypesMapper;
import com.sankuai.walle.dal.eve.mapper.VehicleModelMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.dal.mrm_manage.example.TagsExample;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteDeviceTypeMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.rmanage.config.helper.convertor.ObjectToThrift;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Created on 2022/3/2
 */
@Service
@Slf4j
public class VehicleDeviceServiceImpl {

    @Resource
    TagsMapper tagsMapper;

    @Resource
    VehicleModelMapper vehicleModelMapper;

    @Resource
    RemoteDeviceTypeMapper remoteDeviceTypeMapper;
    @Resource
    DeviceTypesMapper deviceTypesMapper;
    /**
     * 返回车型对应的设备
     *
     * @param
     * @return
     */
    public List<Tags> getTags() {
        TagsExample select = new TagsExample();
        select.createCriteria().andTagTypeEqualTo(4L);
        List<Tags> msg = tagsMapper.selectByExample(select);
        return msg;
    }

    public ArrayList<VehicleDevice> getDevice(Long vehicle_id,int level) throws Exception{
        ArrayList<VehicleDevice> data = new ArrayList<>();
        VehicleModelExample query = new VehicleModelExample();
        query.createCriteria().andVehicleIdEqualTo(vehicle_id).andLevelEqualTo(level);
        List<VehicleModel> car_devices = vehicleModelMapper.selectByExample(query);
        for (VehicleModel car_device:car_devices){
            RemoteDeviceType remoteDevice = remoteDeviceTypeMapper.selectByPrimaryKey(car_device.getDeviceId());
            Device device = ObjectToThrift.remoteDeviceToThfirtDevice(remoteDevice);
            VehicleDevice child = new VehicleDevice();
            child.setDevice(device);
            child.setId(car_device.getId());
            child.setVehicle_id(car_device.getVehicleId());
            data.add(child);
        }
        return data;
    }

    public ArrayList<Map> carDevices(Long vehicleId, int level) throws Exception{
        ArrayList<Map> data = new ArrayList<>();
        VehicleModelExample query = new VehicleModelExample();
        query.createCriteria().andVehicleIdEqualTo(vehicleId).andLevelEqualTo(level);
        List<VehicleModel> car_devices = vehicleModelMapper.selectByExample(query);
        for (VehicleModel car_device:car_devices){
            DeviceTypes deviceType = deviceTypesMapper.selectByPrimaryKey(car_device.getDeviceId());
//            Device device = ObjectToThrift.remoteDeviceToThfirtDevice(remoteDevice);
            Map child = new HashMap();
            child.put("device", deviceType);
            child.put("id", car_device.getId());
            child.put("vehicle_id", car_device.getVehicleId());
            data.add(child);
        }
        return data;
    }
}
