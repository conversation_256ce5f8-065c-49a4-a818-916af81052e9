package com.sankuai.walle.rmanage.config.component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class DxGroupHandler {
    @MdpConfig("sc.daxiangvideo.thread.queryTimeConfig")
    HashMap<String,Long> queryTimeConfig;

    @MdpConfig("eva.dx.host.name")
    String evaHostName;

    public Long createGroup(String title, List<String> groupMembers) {

        String createUrl = evaHostName + "eve/output/rest/push-message/create-group";
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("users", groupMembers);
        requestParam.put("name",title);
        try {
            String response = CommonUtil.doPost(createUrl, requestParam,null);
            if(response == null){
                return 0L;
            }
            log.info("createGroup, response = {}", response);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response);
            Long groupId = jsonNode.get("data").get("gid").asLong();
            if(groupId > 0){
                return groupId;
            }
        } catch (Exception e) {
            log.error("create DXGroup is failed!", e);
        }
        return 0L;
    }

    @Async
    public void sendMessage(Long groupId, String content){
        String sendUrl   =  evaHostName + "eve/output/rest/push-message/gid?appkey=com.sankuai.caros.wallecmdb";
        Map<String, Object> map = new HashMap<>();
        map.put("text", content);
        ObjectMapper objectMapper = new ObjectMapper();
        String json = "";
        try {
            // 将Map对象转换为JSON字符串
           json = objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        Map<String, Object> param = new HashMap<>();
        param.put("messageType", "text");
        param.put("extension", "{}");
        param.put("bodyJson", json );
        param.put("gid", groupId );

        String response = CommonUtil.doPost(sendUrl, param,null);
        log.info("send dxGroup message,param = {}  response = {}",param, response);
    }

    public void addGroupMember(Long groupId, List<String> groupMembers){
        if(groupMembers == null || groupMembers.isEmpty()){
            return;
        }

        int size = groupMembers.size();
        int numGroups = size / 50;
        int remainder = size % 50;

        for (int i = 0; i < numGroups; i++) {
            List<String> subList = groupMembers.subList(i * 50, (i + 1) * 50);
            addMember(groupId, subList);
        }
        if (remainder > 0) {
            List<String> subList = groupMembers.subList(numGroups * 50, size);
            addMember(groupId, subList);
        }
    }
    @Async
    public void addMember(Long groupId, List<String> groupMembers){
        String requestUrl   =  evaHostName + "/eve/output/rest/push-message/add-group";
        Map<String, Object> param = new HashMap<>();
        param.put("members", groupMembers);
        param.put("gid", groupId );

        String response = CommonUtil.doPost(requestUrl, param,null);
        log.info("add dxGroup member,param = {}, response = {}",param, response);
    }

    public HashMap<String, Long> getQueryTimeConfig(){
        return queryTimeConfig;
    }

    @Async
    public void addGroupBots(Long groupId ,List<Long> botsId){
        String requestUrl   =  evaHostName + "/eve/output/rest/push-message/add-group";
        Map<String, Object> param = new HashMap<>();
        param.put("bots", botsId);
        param.put("gid", groupId );

        String response = CommonUtil.doPost(requestUrl, param,null);
        log.info("add dxGroup bots,param = {}, response = {}",param, response);
    }

    public void setGroupNotice(Long groupId, String noticeContent){
        String requestUrl   =  evaHostName + "/eve/output/rest/push-message/update-group-notice";
        Map<String, Object> param = new HashMap<>();
        param.put("noticeContent", noticeContent);
        param.put("gid", groupId );

        String response = CommonUtil.doPost(requestUrl, param,null);
        log.info("set GroupNotice,param = {}, response = {}",param, response);
    }

    //type的取值为 0｜1，0表示机器人，1表示普通用户，并且要保障mis为群成员
    public void transGroupOwner(Long groupId, String mis, int type){
        String requestUrl   =  evaHostName + "/eve/output/rest/push-message/trans-group-owner";
        Map<String, Object> param = new HashMap<>();
        param.put("gid", groupId);
        param.put("mis", mis );
        param.put("type", type);

        String response = CommonUtil.doPost(requestUrl, param,null);
        log.info("transGroupOwner,param = {}, response: {}", param,response);
    }


}
