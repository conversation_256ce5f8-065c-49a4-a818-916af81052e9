package com.sankuai.walle.rmanage.config.Listener.ThingStrategy;

import com.sankuai.walle.rmanage.config.config.AukClientConfig;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ThingModelHandlerFactory {
    private final Map<String, ThingModelHandlerStrategy> strategyMap = new HashMap<>();
    private final PathMatcher pathMatcher = new AntPathMatcher();

    public ThingModelHandlerFactory(List<ThingModelHandlerStrategy> strategies) {
        for (ThingModelHandlerStrategy strategy : strategies) {
            if (strategy instanceof OpenDoorHandler) {
                // 注册动态topic处理策略
                strategyMap.put(AukClientConfig.Charging_Cabinet_TOPIC_RESP, strategy);
            }
            // ... 其他物模型处理策略
        }
    }

    public ThingModelHandlerStrategy getStrategy(String topic) {
        // 精确匹配
        ThingModelHandlerStrategy strategy = strategyMap.get(topic);
        if (strategy != null) {
            return strategy;
        }
        // 通配符匹配
        for (Map.Entry<String, ThingModelHandlerStrategy> entry : strategyMap.entrySet()) {
            if (pathMatcher.match(entry.getKey(), topic)) {
                return entry.getValue();
            }
        }
        return null;
    }
}
