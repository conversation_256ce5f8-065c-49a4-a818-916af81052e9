package com.sankuai.walle.rmanage.config.controller;


import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.auk.open.api.contracts.service.DeviceEnrichment;
import com.sankuai.banma.auk.server.sdk.response.DeviceCreateResponse;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype;
import com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample;
import com.sankuai.walle.dal.classify.mapper.CarFirstcartypeSecondcartypeMapper;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.dal.mrm_manage.entity.DeviceSoftware;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteSoftwareType;
import com.sankuai.walle.dal.mrm_manage.example.DeviceSoftwareExample;
import com.sankuai.walle.dal.mrm_manage.example.RemoteSoftwareTypeExample;
import com.sankuai.walle.dal.mrm_manage.mapper.DeviceSoftwareMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteSoftwareTypeMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.objects.constants.CarDeviceTypeEnum;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.RelatedDeviceInfoVO;
import com.sankuai.walle.objects.vo.VehicleModelInfoVO;
import com.sankuai.walle.objects.vo.DeviceInfoVO;
import com.sankuai.walle.objects.vo.request.*;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.common.exception.DuplicateCategoryException;
import com.sankuai.walle.rmanage.config.common.exception.ParamException;
import com.sankuai.walle.rmanage.config.common.util.InputCheckUtil;
import com.sankuai.walle.rmanage.config.service.VehicleModelManageService;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.service.TypeTreeService;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.web.bind.annotation.*;
import com.sankuai.meituan.auth.util.UserUtils;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping
@Slf4j
public class VehicleModelController {

    @Resource
    CarFirstcartypeSecondcartypeMapper carFirstcartypeSecondcartypeMapper;
    @Resource
    TagsMapper tagsMapper;

    @Resource
    TypeTreeService typeTreeService;

    @Resource
    private VehicleModelManageService vehicleModelManageService;

    @Resource
    DeviceSoftwareMapper deviceSoftwareMapper;

    @Resource
    RemoteSoftwareTypeMapper remoteSoftwareTypeMapper;

    @Resource
    MyAukService myAukService;

    @Resource
    CarObjectsMapper carObjectsMapper;


    @MethodDoc(
            displayName = "获取车型树列表",
            description = "查询全量的车型信息，车型以树形结构关联的形式进行返回",
            parameters = {
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "null",type = List.class, typeName = "VehicleModelInfoVO", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/type/tree"}, method = RequestMethod.GET)
    public ResData getCarsTypeTree() {
        try {
            List<VehicleModelInfoVO> vehicleModelInfoVOList = typeTreeService.getCarTypeTree();
            log.info("getCarsTypeTree, vehicleModelInfoVOList = {}", vehicleModelInfoVOList);
            return ResData.successWithData(vehicleModelInfoVOList);
        }
        catch (Exception e){
            log.error("getCarsTypeTree error:",e);
        }
        return ResData.failedWithMsg("system error");
    }

    @MethodDoc(
            displayName = "新增车型相关信息",
            description = "新增车型信息, 包含一级二级车型以及车辆品牌,车辆尺寸",
            parameters = {
                    @ParamDoc(name = "request", type = VehicleModelCreateRequest.class , description = "车型创建请求结构体")
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "null", typeName = "", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/type/add"}, method = RequestMethod.POST)
    public ResData addCarType(@RequestBody VehicleModelCreateRequest request){
        try {
            // 1 参数校验
            InputCheckUtil.isNotBlank(request.getFatherTypeName(), "一级车型名称不可为空");
            InputCheckUtil.isNotBlank(request.getCarBrand(), "车辆品牌不可为空");
            InputCheckUtil.isNotBlank(request.getCarMedium(), "车辆尺寸不可为空");
            User user = UserUtils.getUser();
            InputCheckUtil.isNotBlank(user.getLogin(), "编辑人不可为空");
            // 2 新增车型
            return typeTreeService.addCarType(request, user.getLogin());
        }catch (Exception e){
            log.error("addCarType error:",e);
        }
        return ResData.failedWithMsg("system error");
    }

    @MethodDoc(
            displayName = "新增设备",
            description = "新增设备信息",
            parameters = {
                    @ParamDoc(name = "request", type = DeviceTypeCreateRequest.class , description = "设备创建请求结构体")
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "null", typeName = "", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/device/add"}, method = RequestMethod.POST)
    public ResData addDevice(@RequestBody DeviceTypeCreateRequest request) {
        try {
            // 1 参数校验
            InputCheckUtil.isNotBlank(request.getTypeName(), "类型名称不可为空");
            InputCheckUtil.isNotBlank(request.getFriendName(), "类型别名不可为空");
            // 获取编辑人信息
            User user = UserUtils.getUser();
            InputCheckUtil.isNotBlank(user.getLogin(), "编辑人不可为空");
            // 2 添加设备
            return typeTreeService.addDevice(request, user.getLogin());
        }
        catch (ParamException e){
            return ResData.failedWithMsg(e.getMessage());
        }
        catch (Exception e){
            log.error("addDevice error:",e);
        }
        return ResData.failedWithMsg("system error");
    }


    /**
     * 修改设备信息接口，该接口为历史接口（未适配最新的表结构），请勿使用
     */
    @Deprecated
    @RequestMapping(path = {"/api/cmdb/vehicle/device/update"}, method = RequestMethod.POST)
    public ResData updateDevice(@RequestBody DeviceTypes remoteDeviceType) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        typeTreeService.updateDevice(remoteDeviceType);
        rep.setCode(CommonConstants.SUCCEED_CODE);
        return rep;
    }

    /**
     * 删除设备信息接口，该接口为历史接口（未适配最新的表结构），请勿使用
     */
    @Deprecated
    @RequestMapping(path = {"/api/cmdb/vehicle/device/delete"}, method = RequestMethod.DELETE)
    public ResData deleteDevice(@RequestParam Integer deviceId ) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        typeTreeService.deleteDevice(Long.valueOf(deviceId));
        rep.setCode(CommonConstants.SUCCEED_CODE);
        return rep;
    }

    @MethodDoc(
            displayName = "查询设备列表",
            description = "根据设备名称/别名/分类查询设备列表",
            parameters = {
                    @ParamDoc(name = "request", type = DeviceTypeQueryRequest.class , description = "设备查询请求结构体")
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "设备详情列表",type = List.class, typeName = "DeviceInfoVO", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/first_devices"}, method = RequestMethod.GET)
    public ResData getDevices(DeviceTypeQueryRequest request) {
        log.info("getDevices request = {}", request);
        try {
            List<DeviceInfoVO> deviceInfoVOList = typeTreeService.queryDeviceByDeviceTypeQueryRequest(request);
            log.info("getDevices deviceInfoVOList = {}", deviceInfoVOList);
            return ResData.successWithData(deviceInfoVOList);
        }
        catch (Exception e){
            log.error("getDevices error:",e);
        }
        return ResData.failedWithMsg("system error");
    }

    // 对象类型列表
    @RequestMapping(path = {"/api/cmdb/object_types"}, method = RequestMethod.GET)
    public ResData getObjectsTypes(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try {
            List<RemoteSoftwareType> data = remoteSoftwareTypeMapper.selectByExample(null);
            req.data = data;
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    @MethodDoc(
            displayName = "新增车型与设备的关联关系",
            description = "为车型新增关联的设备列表",
            parameters = {
                    @ParamDoc(name = "request", type = VehicleModelDeviceRelationRequest.class ,
                            description = "车型设备关联请求结构体")
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "null", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/device"}, method = RequestMethod.POST)
    public ResData relateVehicleModelDevice(@RequestBody VehicleModelDeviceRelationRequest request) {
        try {
            log.info("relateVehicleModelDevice request = {}", request);
            // 参数校验
            InputCheckUtil.isNotNull(request.getVehicleModelId(), "车型id不可为空");
            InputCheckUtil.isNotNull(request.getVehicleModelLevel(), "车型等级不可为空");
            InputCheckUtil.isNotEmpty(request.getDeviceIds(), "设备id列表不可为空");
            vehicleModelManageService.relateVehicleModelDevice(request);
            return ResData.successWithMsg("ok");
        }catch (ParamException e){
            return ResData.failedWithMsg(e.getMessage());
        }
        catch (DuplicateCategoryException e){
            log.error("SetDevice DuplicateCategoryException:",e);
            return ResData.failedWithMsg(e.getMessage());
        } catch (Exception e) {
           log.error("SetDevice error",e);
        }
        return ResData.failedWithMsg("system error");
    }

    @MethodDoc(
            displayName = "查询指定车型关联的设备列表",
            description = "查询指定车型关联的设备列表",
            parameters = {
                    @ParamDoc(name = "vehicleModelId", type = Long.class , description = "车型ID"),
                    @ParamDoc(name = "vehicleModelLevel", type = Integer.class , description = "车型等级")
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "车型设备关联列表",type = List.class,
                            typeName = "RelatedDeviceInfoVO", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/device"}, method = RequestMethod.GET)
    public ResData getVehicleModelDeviceRelation(@RequestParam Long vehicleModelId, @RequestParam Integer vehicleModelLevel) {
        log.info("getVehicleModelDeviceRelation vehicleModelId = {}, vehicleModelLevel = {}", vehicleModelId, vehicleModelLevel);
        try {
            //1 参数校验
            InputCheckUtil.isNotNull(vehicleModelId, "车型id不可为空");
            InputCheckUtil.isNotNull(vehicleModelLevel, "车型等级不可为空");

            List<RelatedDeviceInfoVO> relatedDeviceInfoVOList = vehicleModelManageService.queryDeviceInfoByDeviceType(
                    vehicleModelId, vehicleModelLevel);
            log.info("getVehicleModelDeviceRelation relatedDeviceInfoVOList = {}", relatedDeviceInfoVOList);
            return ResData.successWithData(relatedDeviceInfoVOList);
        }catch (ParamException e){
            return ResData.failedWithMsg(e.getMessage());
        } catch (Exception e) {
            log.error("getVehicleModelDeviceRelation error", e);
        }
        return ResData.failedWithMsg("system error");
    }

    @MethodDoc(
            displayName = "解除车型和设备的关联关系",
            description = "解除车型和设备的关联关系",
            parameters = {
                    @ParamDoc(name = "relatedId", type = Long.class , description = "车型设备关联ID"),
            },
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息",type = String.class, example = {"ok"}),
                    @ParamDoc(name = "data", description = "null", example = {
                    })}
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/device"}, method = RequestMethod.DELETE)
    public ResData deleteDevice(@RequestParam Long relatedId) {
        try {
            vehicleModelManageService.deleteVehicleModelDeviceRelation(relatedId);
            return ResData.successWithMsg("ok");
        } catch (Exception e) {
          log.error("deleteDevice error",e);
        }
        return ResData.failedWithMsg("system error");
    }

    // 关联一级车型与二级车型
    @Deprecated
    @RequestMapping(path = {"/api/cmdb/vehicle/SecondLevelCarTypes"}, method = RequestMethod.POST)
    public ResData SetCarsType(@RequestBody VehicleChildrenRelation body, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try {
            ArrayList<CarFirstcartypeSecondcartype> insertData = new ArrayList<>();
            List<Long> second_vehicle_ids = body.getSecond_vehicle_ids();
            for (Long second_vehicle_id: second_vehicle_ids){
                CarFirstcartypeSecondcartype model = new CarFirstcartypeSecondcartype(){{
                    setSecondCarId(second_vehicle_id);
                    setFirstCarId(body.getFirst_vehicle_id());
                }};
                CarFirstcartypeSecondcartypeExample example = new CarFirstcartypeSecondcartypeExample();
                example.createCriteria().andFirstCarIdEqualTo(body.getFirst_vehicle_id()).andSecondCarIdEqualTo(second_vehicle_id);
                List<CarFirstcartypeSecondcartype> check = carFirstcartypeSecondcartypeMapper.selectByExample(example);
                if (check.size()<1) {
                    insertData.add(model);
                }
            }
            if (insertData.size()>=1) {
                carFirstcartypeSecondcartypeMapper.batchInsert(insertData);
            }
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    // 通过一级车型获取二级车型
    @Deprecated
    @RequestMapping(path = {"/api/cmdb/vehicle/SecondLevelCarTypes"}, method = RequestMethod.GET)
    public ResData GetCarsType(@RequestParam Long firstCarTypeId, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try {
            CarFirstcartypeSecondcartypeExample example = new CarFirstcartypeSecondcartypeExample();
            example.createCriteria().andFirstCarIdEqualTo(firstCarTypeId);
            List<CarFirstcartypeSecondcartype> checks = carFirstcartypeSecondcartypeMapper.selectByExample(example);
            List<HashMap> res = new ArrayList<>();
            for (CarFirstcartypeSecondcartype check: checks){
                HashMap<String,Object> child = new HashMap<>();
                child.put("id",check.getId());
                child.put("firstCarId",check.getFirstCarId());
                child.put("SecondCar",tagsMapper.selectByPrimaryKey(check.getSecondCarId()));
                res.add(child);
            }
            req.data = res;
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    // 获取设备对应的软件列表
    @RequestMapping(path = {"/api/cmdb/vehicle/device/software"}, method = RequestMethod.GET)
    public ResData getDeviceSoftware(HttpServletRequest request, @RequestParam Long device_id) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            ArrayList<RemoteSoftwareType> data = new ArrayList<>();
            DeviceSoftwareExample deviceSoftwareExample = new DeviceSoftwareExample();
            deviceSoftwareExample.createCriteria().andRelatedRemoteDeviceTypeIdEqualTo(device_id);
            List<DeviceSoftware> device_softwares = deviceSoftwareMapper.selectByExample(deviceSoftwareExample);
            for (DeviceSoftware deviceSoftware:device_softwares){
                Long software_id = deviceSoftware.getRelatedRemoteSoftwareTypeId();
                RemoteSoftwareTypeExample query = new RemoteSoftwareTypeExample();
                query.createCriteria().andIdEqualTo(software_id);
                List<RemoteSoftwareType> software = remoteSoftwareTypeMapper.selectByExample(query);
                if (software.size()>0) {
                    data.add(software.get(0));
                }
            }
            req.data = data;
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    // 将设备注册到auk
    @RequestMapping(path = {"/api/cmdb/vehicle/auk/create"}, method = RequestMethod.GET)
    public ResData createAukNum(HttpServletRequest request,@RequestParam String vin){
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try{
            DeviceCreateResponse res = myAukService.createAukDevice(vin);
            req.code = res.getCode();
            req.msg = res.getMsg();
        }catch (Exception e){
            log.error("向auk创建设备失败", e);
        }
        return req;
    }

    // 查询车辆的auk信息
    @RequestMapping(path = {"/api/cmdb/vehicle/auk/querylist"}, method = RequestMethod.POST)
    public ResData queryListAukNum(HttpServletRequest request,@RequestBody ArrayList<String> vins){
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try{
            List<DeviceEnrichment> deviceEnrichments = myAukService.queryDeviceListWithSlice(vins, 20);
            req.code = 200;
            req.data = deviceEnrichments;
            req.msg = "查询成功";
        }catch (Exception e){
            log.error("向auk查询设备失败", e);
        }
        return req;
    }

    // 分页查询车辆信息
    @RequestMapping(path = {"/api/cmdb/vehicle/querylist"}, method = RequestMethod.GET)
    public ResData queryListVehicle(HttpServletRequest request,
                                    @RequestParam(required = false) Integer page,
                                    @RequestParam(required = false) Integer pageSize,
                                    @RequestParam(required = false) String name){
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            CarObjectsExample query = new CarObjectsExample();
            req.rows = carObjectsMapper.countByExample(query);
            if (page != null && pageSize != null) {
                query.setOffset(page * pageSize);
            }
            if (pageSize != null) {
                query.setRows(pageSize);
            }
            if (name!=null && !name.isEmpty()){
                query.createCriteria().andNameLike("%"+name+"%");
            }
            req.data = carObjectsMapper.selectByExample(query);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    /**
     * 获取设备分类列表
     * @return 设备分类列表
     */
    @GetMapping(path = {"/api/cmdb/vehicle/device/category/get"})
    public ResData getDeviceCategoryList() {
       try {
            return ResData.successWithData(typeTreeService.getDeviceCategoryConfig());
       }catch (Exception e){
           log.error("获取设备分类列表失败", e);
       }
       return ResData.failedWithMsg("system error");
    }

    // 绑定设备sn和vin
    @PostMapping(path = {"/api/cmdb/vehicle/devices/message/upload"})
    public ResData bindDevice(@RequestBody DeviceBindRequest request) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        log.info("bindDevice request = {}", request);
        // 参数校验，sn不可为空
        if (Strings.isEmpty(request.getSn())) {
            res.msg = "sn不可为空";
            return res;
        }
        // 判断type应该属于CarDeviceTypeEnum
        if (Strings.isEmpty(request.getType()) || !CarDeviceTypeEnum.belongEnum(request.getType())) {
            res.msg = "type参数错误";
            return res;
        }
        CarDeviceTypeEnum type = CarDeviceTypeEnum.valueOf(request.getType());
        try {
            vehicleModelManageService.relateVehicleModelDevice(request.getVin(), request.getSn(), type, request.getMac());
            res.code = CommonConstants.SUCCEED_CODE;
            res.msg = "ok";
        } catch (Exception e) {
            log.error("bindDevice error", e);
            res.msg = e.getMessage();
        }
        return res;
    }

    // 查询设备sn
    @GetMapping(path = {"/api/cmdb/vehicle/devices/sn/query"})
    public ResData queryDeviceSn(@RequestParam String vin) {
        log.info("queryDeviceSn vin = {}", vin);
        try {
            return ResData.successWithData(vehicleModelManageService.queryDevicesByVin(vin));
        } catch (Exception e) {
            log.error("queryDeviceSn error", e);
        }
        return ResData.failedWithMsg("查询失败");
    }
}
