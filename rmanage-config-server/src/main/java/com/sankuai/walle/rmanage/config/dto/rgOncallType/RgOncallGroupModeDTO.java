package com.sankuai.walle.rmanage.config.dto.rgOncallType;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * 按组值班模式，解析响应结果类
 */
@Data
public class RgOncallGroupModeDTO {

    private int code;
    private String message;
    private GroupData data;

    @Data
    public static class GroupData {

        private int tn;
        private int cn;
        private int sn;
        private List<GroupItem> items;
        private int pn;
    }

    @Data
    public static class GroupItem {

        @JsonProperty("isOnCall")
        private boolean isOnCall;
        private List<OnCallUser> onCallUserList;
    }

    @Data
    public static class OnCallUser {

        private String identify;
    }

    /**
     * 解析组模式
     * @return
     */
    public List<String> getMidIdList() {
        if (this.getData() == null || CollectionUtils.isEmpty(this.getData().getItems())) {
            return Collections.emptyList();
        }
        return this.getData().getItems().stream()
                .filter(Objects::nonNull)
                .filter(item -> item.isOnCall() && !CollectionUtils.isEmpty(item.getOnCallUserList()))
                .flatMap(item -> item.getOnCallUserList().stream())
                .map(RgOncallGroupModeDTO.OnCallUser::getIdentify)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

    }
}
