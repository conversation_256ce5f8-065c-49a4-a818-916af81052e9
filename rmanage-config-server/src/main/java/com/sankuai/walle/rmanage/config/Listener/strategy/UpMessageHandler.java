package com.sankuai.walle.rmanage.config.Listener.strategy;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfigExtend;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExtendExample;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigExtendMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.ConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 上行消息处理策略
 * 处理客户端发送的上行消息，包括：
 * 1. 解析上行消息内容
 * 2. 更新设备状态
 * 3. 处理上行数据
 */
@Component
@Slf4j
public class UpMessageHandler implements MessageHandlerStrategy {
    /**
     * 处理上行消息的具体实现
     * @param message 包含上行信息的消息对象
     * @throws Exception 处理过程中可能抛出的异常
     */
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarConfigMapper carConfigMapper;
    @Resource
    CarDeviceConfigExtendMapper configExtendMapper;

    @Override
    public void handle(AukUpCountent message) throws Exception {
        upRepFunc(message);
    }
    private void upRepFunc(AukUpCountent message) throws InvalidProtocolBufferException {
        // 回调的处理逻辑: 回调的状态是成功，vin和version号匹配，修改carDeviceConfig 状态为1
//                CloudCgf.VehicleToCloudReleaseConfigRsp countent = JSONObject.parseObject(message.getMessage(), CloudCgf.VehicleToCloudReleaseConfigRsp.class);
        Base64.Decoder decoder = Base64.getDecoder();
        com.sankuai.walle.rmanage.proto.target.CloudCgf.VehicleToCloudReleaseConfigRsp content;
        try {
            content = com.sankuai.walle.rmanage.proto.target.CloudCgf.VehicleToCloudReleaseConfigRsp.parseFrom(decoder.decode(message.getMessage()));
        } catch (Exception e) {
            log.error("规则引擎转发上行消息解析出错,message={}", message.getMessage(), e);
            return;
        }
        if (content == null) {
            log.error("规则引擎转发上行消息解析结果为空,message={}", message.getMessage());
            return;
        }
        log.info("规则引擎转发上行消息解析结果：{}", content);
        if (content.getStatus().getNRet() == CommonConstants.SUCCEED_CODE) {
            String vin = content.getSVin();
            CarDeviceConfigExample query = new CarDeviceConfigExample();
            query.createCriteria().andVinEqualTo(vin).andNameEqualTo(content.getSConfigName());
            List<CarDeviceConfig> targets = this.getTargets(query);
            // 修改状态为1，表示下发并已经在车端处理成功
            log.info("配置下发成功:[{}]", targets);
            if (CollectionUtils.isEmpty(targets)) {
                return;
            }
            for (CarDeviceConfig target : targets) {
                // 说明没有配置，是Excel直接下发的属性
                if (Objects.equals(String.valueOf(ConfigConstant.EXCEL_VERSION), content.getSVersion())) {
                    target.setStatus(ConfigConstant.FINISH);
                    carDeviceConfigMapper.updateByPrimaryKeySelective(target);
                    continue;
                }
                CarConfig configOBJ = carConfigMapper.selectByPrimaryKey(target.getConfigId());
                if (configOBJ == null) {
                    continue;
                }
                // 有版本号的，要判断版本号是否一致
                if (Objects.equals(String.valueOf(configOBJ.getConfigVersion()), content.getSVersion())) {
                    target.setStatus(ConfigConstant.FINISH);
                    carDeviceConfigMapper.updateByPrimaryKeySelective(target);
                }
            }
        }
        // done: 增加下发后，处理失败的状态
        else {
            String vin = content.getSVin();
            CarDeviceConfigExample query = new CarDeviceConfigExample();
            query.createCriteria().andVinEqualTo(vin).andNameEqualTo(content.getSConfigName());
            List<CarDeviceConfig> targets = this.getTargets(query);
            log.info("配置在车端处理失败:[{}]", targets);
            if (CollectionUtils.isEmpty(targets)) {
                return;
            }
            for (CarDeviceConfig target : targets) {
                if (Objects.equals(target.getConfigId(), ConfigConstant.EXCEL_CONFIG_ID)){
                    this.writeStatus(target, content);
                    continue;
                }
                CarConfig configOBJ = carConfigMapper.selectByPrimaryKey(target.getConfigId());
                if (configOBJ == null) {
                    continue;
                }
                if (StringUtils.equals(String.valueOf(configOBJ.getConfigVersion()), content.getSVersion())) {
                    this.writeStatus(target, content);
                }
            }
        }
    }

    private List<CarDeviceConfig> getTargets(CarDeviceConfigExample query){
        // done:可能返回速度太快，导致事务还没有提交成功，这里查询不到。要优化
        // 如果没有查询到，则等1秒再重复查询1次
        List<CarDeviceConfig> targets = carDeviceConfigMapper.selectByExample(query);
        if (targets.isEmpty()){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            targets = carDeviceConfigMapper.selectByExample(query);
        }
        return targets;
    }
    private void writeStatus(CarDeviceConfig target, com.sankuai.walle.rmanage.proto.target.CloudCgf.VehicleToCloudReleaseConfigRsp content){
        target.setStatus(ConfigConstant.ERROR);
        carDeviceConfigMapper.updateByPrimaryKeySelective(target);
        List<CarDeviceConfigExtend> configExtends = configExtendMapper.selectByExample(
                new CarDeviceConfigExtendExample() {{
                    createCriteria().andConfigIdEqualTo(target.getId());
                }}
        );
        if (CollectionUtils.isNotEmpty(configExtends)) {
            configExtends.forEach(carDeviceConfigExtend -> {
                carDeviceConfigExtend.setUpdateTime(new Date());
                carDeviceConfigExtend.setErrorStatus(
                        content.getStatus().getNRet() + content.getStatus().getSMsg());
                configExtendMapper.updateByPrimaryKeySelective(carDeviceConfigExtend);
            });
        }else{
            configExtendMapper.insert(new CarDeviceConfigExtend(){{
                setErrorStatus(content.getStatus().getSMsg());
                setAddTime(new Date());
                setUpdateTime(new Date());
                setConfigId(target.getId());
            }});
        }
    }
} 