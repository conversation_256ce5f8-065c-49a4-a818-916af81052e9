package com.sankuai.walle.rmanage.config.inter;

import com.google.common.collect.Lists;
import com.meituan.finerp.eam.dto.OpenAssetDTO;
import com.meituan.finerp.eam.enums.ApiResultEnum;
import com.meituan.finerp.eam.response.OpenQueryAssetsCommonRes;
import com.meituan.finerp.eam.response.OpenQueryWalleAssetsRes;
import com.meituan.finerp.eam.service.OpenAssetQueryService;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.walle.rmanage.config.exception.ErrorCodeEnum;
import com.sankuai.walle.rmanage.config.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zhaojianfeng05 .
 */
@Slf4j
@Component
public class OpenAssetQueryRemoteService {

    @Resource
    OpenAssetQueryService openAssetQueryService;

    public OpenQueryWalleAssetsRes queryWalleAssets(List<String> smallTypeList,String scrollId, Integer size) {

        try {
//            OpenQueryAssetsCommonRes resp = openAssetQueryService.queryWalleAssets(lastId, size);
            OpenQueryWalleAssetsRes resp = openAssetQueryService.queryWalleAssetsBySmallType(
                    smallTypeList, scrollId, size
            );
            if (resp == null ||  !ApiResultEnum.SUCESS.getCode().equals(resp.getCode())) {
                throw new SystemException(ErrorCodeEnum.EXTERNAL_ERROR, "OpenQueryAssetsCommonRes is null.");
            }
            return resp;
        } catch (Exception e) {
            log.error("OpenAssetQueryRemoteService.queryWalleAssets lastId:{} size:{}, e:", smallTypeList, size, e);
            return new OpenQueryWalleAssetsRes();
        }
    }
}
