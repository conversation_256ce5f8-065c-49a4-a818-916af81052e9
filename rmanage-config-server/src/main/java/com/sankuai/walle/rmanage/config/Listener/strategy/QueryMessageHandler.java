package com.sankuai.walle.rmanage.config.Listener.strategy;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.sankuai.walle.dal.classify.entity.CarConfigQuery;
import com.sankuai.walle.dal.classify.example.CarConfigQueryExample;
import com.sankuai.walle.dal.classify.mapper.CarConfigQueryMapper;
import com.sankuai.walle.objects.bo.AukUpCountent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.Date;
import java.util.List;

/**
 * 查询消息处理策略
 * 处理客户端发送的查询请求，包括：
 * 1. 解析查询条件
 * 2. 执行查询操作
 * 3. 返回查询结果
 */
@Component
@Slf4j
public class QueryMessageHandler implements MessageHandlerStrategy {
    /**
     * 处理查询消息的具体实现
     * @param message 包含查询信息的消息对象
     * @throws Exception 处理过程中可能抛出的异常
     */
    @Resource
    CarConfigQueryMapper carConfigQueryMapper;
    @Override
    public void handle(AukUpCountent message) throws Exception {
        queryFunc(message);
    }
    private void queryFunc(AukUpCountent message) throws InvalidProtocolBufferException {
        // 获取当前上线的是哪个车辆，然后下发查询命令。
        log.info("开始处理配置回捞消息：{}",message);
        String vin = String.valueOf(message.getDeviceKey());
        Base64.Decoder decoder = Base64.getDecoder();
        com.sankuai.walle.rmanage.proto.target.CloudCgf.VehicleToCloudQueryConfigRsp countent = com.sankuai.walle.rmanage.proto.target.CloudCgf.VehicleToCloudQueryConfigRsp.parseFrom(
                decoder.decode(message.getMessage()));
        log.info("回捞结果解析：{}",countent);
        // 回捞结果入库
        CarConfigQueryExample query = new CarConfigQueryExample();
        query.createCriteria().andVinEqualTo(countent.getSVin()).andNameEqualTo(countent.getSConfigName());
        List<CarConfigQuery> queryRds = carConfigQueryMapper.selectByExample(query);
        CarConfigQuery carConfigQuery = new CarConfigQuery();
        carConfigQuery.setVin(countent.getSVin());
        carConfigQuery.setName(countent.getSConfigName());
        carConfigQuery.setConfigVersion(countent.getSVersion());
        carConfigQuery.setFileName(countent.getSFileName());
        carConfigQuery.setUpdateTime(new Date());
        ByteString bytes = countent.getBConf();
        carConfigQuery.setConfig(bytes.toStringUtf8());
        if (queryRds.isEmpty()) {
            carConfigQueryMapper.insert(carConfigQuery);
        }else {
            for(CarConfigQuery son: queryRds) {
                long id = son.getId();
                carConfigQuery.setId(id);
                carConfigQueryMapper.updateByPrimaryKeySelective(carConfigQuery);
            }
        }

    }
} 