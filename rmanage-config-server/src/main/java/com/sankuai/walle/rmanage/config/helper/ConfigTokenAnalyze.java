package com.sankuai.walle.rmanage.config.helper;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ConfigTokenAnalyze {
    int index = 0;
    int currentChar;
    String text;
    int start=0;
    int end=0;
    String tokenType;

    public ConfigTokenAnalyze(String input){
        this.text = input;
    }
    @Data
    public static class ConfigVariable{
        public static String NAME = "name";
        public static String VIN = "vin";
        public static String LicenseNo = "licenseNo";
        public static String COMPUTE_NAME = "compute_name"; // 计算单元
        public static String SWITCH_NAME = "switch_name"; // 网关单元
        public static String TYPE = "type"; // 车型，如S20，H24
        public static String PHASE = "phase"; // 阶段，如EP1,ep2
        public static String SIZE = "size"; // 车辆大小，如 big, middle
        public static String VENDOR = "vendor"; // 供应商，如MT,BYD
    }

    @Data
    public static class myTokenType{
        public static String START="";
        public static String END="end";
        public static String STR="str";
        public static String VARIABLE="variable";
    }
    @Data
    public static class Token{
        String type;
        String count;
    }

    private void advance() {
        index++;
        if (index >= text.length()) {
            currentChar = '\0';
        } else {
            currentChar = text.charAt(index);
        }
    }

    public Token getNextToken(){

        while(currentChar != '\0'){
            switch (currentChar){
                case '`':
                    // 进入灰色地带，暂时无法判断是字符串还是变量
                    int tempEnd = index; // 记录前一个token的结束
                    advance();
                    if (currentChar == '$'){
                        advance();
                        if (currentChar == '{'){
                            advance();
                            if(currentChar == '#'){
                                end = tempEnd; // 确定前一个token的结束
                                tokenType = myTokenType.STR; // 前一个token是字符串
                                // 其次将start指向当前的开始值
                                Token token = new Token(){{
                                    setType(tokenType);
                                    setCount(text.substring(start,end));
                                }};
                                advance();
                                start = index; // 确定新token的开始
                                // 确认当前token是变量
                                tokenType = myTokenType.VARIABLE;
                                return token;
                            }
                        }
                    }
                case '}':
                    tempEnd = index; // 记录前一个token的结束
                    advance();
                    if (currentChar == '`'){
                        if (Objects.equals( tokenType, myTokenType.VARIABLE ) ) {
                            // 确定变量已结束
                            end = tempEnd;
                            Token token = new Token(){{
                                setType(tokenType);
                                setCount(text.substring(start,end));
                            }};
                            advance();
                            start = index; // 确定新token的开始
                            tokenType = myTokenType.STR;
                            return token;
                        }
                    }
                default:
                    advance();
            }
        }
        // 结束的时候有两种情况，还有字符串，没有字符串
        return new Token(){{
            if (start>=text.length()){
                setType(myTokenType.END);
                setCount("");
            } else {
                setType(myTokenType.END);
                setCount(text.substring(start));
            }
        }};
    }

    public List<Token> getTokens() {
        List<Token> tokens = new ArrayList<>();
        Token token;
        currentChar = text.charAt(index);
        do {
            token = getNextToken();
            tokens.add(token);
        } while (!Objects.equals(token.getType(), myTokenType.END));
        return tokens;
    }



}
