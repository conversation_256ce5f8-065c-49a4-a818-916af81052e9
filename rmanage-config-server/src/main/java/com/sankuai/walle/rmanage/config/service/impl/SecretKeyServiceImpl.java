package com.sankuai.walle.rmanage.config.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.walle.rmanage.config.service.infrastructureService.SecretKeyService;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import com.sankuai.walle.rmanage.config.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SecretKeyServiceImpl implements SecretKeyService {

    @Autowired
    private Environment environment;

    @Value("${create.vehicle.visit.strategy.url}")
    private String CREATE_VEHICLE_VISIT_STRATEGY_URL;
    @Value("${query.vehicle.visit.strategy.url}")
    private String QUERY_VEHICLE_VISIT_STRATEGY_URL;

    @Value("${create.vehicle.sso.url}")
    private String CREATE_VEHICLE_SSO_URL;

    @Value("${create.vehicle.sso.url}")
    private String QUERY_VEHICLE_SSO_URL;

    @Value("${import.secret.key.url}")
    private String IMPORT_SECRET_KEY_URL;

    @Value("${x.vault.token}")
    private String VAULT_TOKEN;

    @Value("${query.generate.key.result.url}")
    private String QUERY_SECRET_URL ;

    private static final String[] secretKeys = {
                "can_auth_key",
                "SRV_Clp43oAL6QEjgnb8hEsFOm5CPJL4cxrh",
                "HDMapCollectionTrajectoryUploadKey",
                "HDMapCollectionTrajectoryUploadKey_Test",
                "ADMapCollectionTrajectoryUploadKey",
                "PostProcessQXUploadSecret",
                "PostProcessQXUploadKey",
                "J3C_maebctl_client_secret",
                "J3_mediastream_client_secret",
                "J3_teleop_client_secret",
                "slab_broker_access_secret",
            "J3C_maeb_ssh_passwd"
    };

    private static final String[] generateAppKeys = {
            "com.sankuai.walleops.backend.deploy",
            "com.sankuai.walledata.fe.dataview",
            "com.sankuai.walledata.fe.order",
            "com.walle.dataplatform",
            "com.sankuai.caros.rmanage.spider",
            "com.sankuai.walledelivery.vehicle.operation",
            "com.sankuai.caros.re",
            "com.sankuai.walle.vehiclehmi"
    };
    private static final String HMI_APP_KEY = "com.sankuai.walle.vehiclehmi";

    private static final String visitStrategyTemplate = "path \"keys-kv/metadata/*\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/{vin_placeholder}/*\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.walle.dataplatform/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.walle.dataplatform\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walledata.fe.dataview/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walledata.fe.dataview\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walleops.backend.deploy/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walleops.backend.deploy\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/seczmq/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/seczmq\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walledata.fe.order/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walledata.fe.order\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walle.vehiclehmi/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walle.vehiclehmi\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.caros.rmanage.spider/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.caros.rmanage.spider\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.caros.re/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.caros.re\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.caros.hdmap/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.caros.hdmap\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walledelivery.vehicle.operation/{vin_placeholder}\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/com.sankuai.walledelivery.vehicle.operation\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/disk_keys/*\" {\n  capabilities = [\"read\"]\n}\npath \"keys-kv/data/disk_keys\" {\n  capabilities = [\"read\"]\n}\n";
//    private

    private Map<String, String> createHeaders() {
        return new HashMap<String, String>() {{
            put("X-Vault-Token", VAULT_TOKEN);
        }};
    }

    @Override
    public void createVisitStrategy(String vin) {

        Map<String, Object> params = new HashMap<>();
        params.put("policy", visitStrategyTemplate.replace("{vin_placeholder}", vin));


        Map<String, String> headers = createHeaders();

        String response = CommonUtil.doPost(CREATE_VEHICLE_VISIT_STRATEGY_URL + vin, params, headers);
        log.info("#createVisitStrategy# : {}", response);
    }

    @Override
    public String fetchVisitStrategy(String vin) {
        Map<String, String> headers = createHeaders();
        headers.put("X-Vault-Token", VAULT_TOKEN);
        String response = CommonUtil.doGet(QUERY_VEHICLE_VISIT_STRATEGY_URL + vin, null, headers);
        log.info("#fetchVisitStrategy# : {}", response);
        return response;
    }

    @Override
    public void createVehicleSSO(String vin) {
        Map<String, Object> params = new HashMap<>();
        params.put("token_type", "default");
        params.put("token_policies", Lists.newArrayList("default", vin.toLowerCase()));

        log.info("#createVehicleSSO# request params : {} ", params);
        Map<String, String> headers = createHeaders();

        String response = CommonUtil.doPost(CREATE_VEHICLE_SSO_URL + vin, params, headers);
        log.info("#createVisitStrategy# : {}", response);

    }

    public String fetchVehicleSSO(String vin) {
        Map<String, String> headers = createHeaders();
        headers.put("X-Vault-Token", VAULT_TOKEN);
        String response = CommonUtil.doGet(QUERY_VEHICLE_SSO_URL + vin, null, headers);
        log.info("#fetchVehicleSSO# : {}", response);
        return response;
    }

    @Override
    public void importSecretKey(String vin) {
        Map<String, Object> params = new HashMap<>();
        Map<String, String> keys = new HashMap<>();
        for(String secretKey : secretKeys) {
            keys.put(secretKey, environment.getProperty(secretKey));
        }
        keys.put("ipmi_pass", StringUtil.generateRandomString(12)); // 随机生成12位的字符串
        params.put("data", keys);

        log.info("#importSecretKey# request params : {} ", params);

        Map<String, String> headers = createHeaders();
        String response = CommonUtil.doPost(IMPORT_SECRET_KEY_URL + vin, params, headers);

        log.info("#importSecretKey# : {}", response);

        try {
            String info = new ObjectMapper().readTree(response).path("warnings").asText();

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


    }

    public String fetchSecretKey(String vin) {
        Map<String, String> headers = createHeaders();
        headers.put("X-Vault-Token", VAULT_TOKEN);
        String response = CommonUtil.doGet(IMPORT_SECRET_KEY_URL + vin, null, headers);
        log.info("#fetchSecretKey# : {}", response);
        return response;
    }

    @Override
    public void importVehicleCloudSecret(String vin) {
        Map<String, String> headers = createHeaders();
        for(String appKey : generateAppKeys) {

            Map<String, Object> params = new HashMap<>();
            params.put("data", new HashMap<String, String>() {{
                put(appKey.equals(HMI_APP_KEY) ? "hmac_key" : "auth_key", StringUtil.generateRandomString(32));
            }});
            String response = CommonUtil.doPost(IMPORT_SECRET_KEY_URL + appKey + "/" + vin, params, headers);
            log.info("#importVehicleCloudSecret# {}", response);
        }
    }

    public String fetchSingleVehicleCloudSecret(String vin, String appKey) {
        Map<String, String> headers = createHeaders();
        headers.put("X-Vault-Token", VAULT_TOKEN);
        String response = CommonUtil.doGet(IMPORT_SECRET_KEY_URL + appKey + "/" + vin, null, headers);
        log.info("#fetchVehicleCloudSecret# : {}", response);
        return response;
    }

    public List<String> fetchVehicleCloudSecret(String vin) {
        List<String> result = new ArrayList<>();
        for(String appKey : generateAppKeys) {
            String response = fetchSingleVehicleCloudSecret(vin, appKey);
            if(response.contains("html")) {
                // failed generate app key
                result.add(appKey);
            }
        }
        // failed generate app keys
        return  result;
    }



    @Override
    public Map<String, Map<String, Object>> batchGetGenerateResult(List<String> vinList) {
//        Map<String, CompletableFuture<Map<String, Object>>> result = new HashMap<>();
//        for(String vin : vinList) {
//            result.put(vin, getGenerateResultAsync(vin));
//        }
//        return  result.entrySet().stream()
//                .collect(
//                        Collectors.toMap(Map.Entry::getKey, (entry) -> entry.getValue().join())
//                );
        return vinList.stream().map(this::getGenerateResult).collect(Collectors.toMap(x -> x.get("vin").toString(), (x) -> x ));
    }

    @Override
    public Map<String, Object> getGenerateResult(String item) {
        log.info("#sk getGenerateResult# : {}", item);
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("vin", item);
        String visitstrategy = this.fetchVisitStrategy(item);
        String sso = this.fetchVehicleSSO(item);
        String secret = this.fetchSecretKey(item);
        List<String> failedCloudAppKey = this.fetchVehicleCloudSecret(item);
        if(visitstrategy.contains("<html>")) {
            innerMap.put("VisitStrategy", false);
        } else {
            innerMap.put("VisitStrategy", true);
        }
        if(sso.contains("<html>")) {
            innerMap.put("SSO", false);
        } else {
            innerMap.put("SSO", true);
        }
        if(secret.contains("<html>")) {
            innerMap.put("Secret", false);
        } else {
            innerMap.put("Secret", true);
        }
        innerMap.put("CloudSecret", failedCloudAppKey);

        log.info("#sk getGenerateResult结果# : {}", innerMap);
        return innerMap;
    }
    @Async("vinSecretExecutor")
    private CompletableFuture<Map<String, Object>> getGenerateResultAsync(String vin) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getGenerateResult(vin);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }


}
