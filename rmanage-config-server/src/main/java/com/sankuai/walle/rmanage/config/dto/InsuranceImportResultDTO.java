package com.sankuai.walle.rmanage.config.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 保险导入结果DTO
 * 用于传输保险导入的结果数据
 */
@Data
@Builder
public class InsuranceImportResultDTO {
    
    /**
     * 成功导入的车辆数量
     */
    private Integer successCount;
    
    /**
     * 未找到的车辆数量
     */
    private Integer notFoundCount;
    
    /**
     * 导入失败的车辆数量
     */
    private Integer errorCount;
    
    /**
     * 成功导入的VIN列表
     */
    private List<String> successVins;
    
    /**
     * 未找到的VIN列表
     */
    private List<String> notFoundVins;
    
    /**
     * 导入失败的VIN列表
     */
    private List<String> errorVins;
    
    /**
     * 构建结果消息
     */
    public String buildMessage() {
        return String.format("导入完成: 成功%d条, 未找到%d条, 错误%d条", 
                successCount, notFoundCount, errorCount);
    }
} 