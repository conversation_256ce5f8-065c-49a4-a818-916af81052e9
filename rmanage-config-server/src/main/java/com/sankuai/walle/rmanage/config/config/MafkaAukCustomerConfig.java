package com.sankuai.walle.rmanage.config.config;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.walle.rmanage.config.Listener.MafkaAukConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import javax.annotation.Resource;
import java.util.Properties;

@Configuration
@PropertySource(value = {"classpath:leaf.properties", "classpath:/META-INF/app.properties"})
@Slf4j
public class MafkaAukCustomerConfig {
    @Value("${mafka.namespace}")
    String namespace;
    @Value("${mafka.mafkaAK}")
    String mafkaAK;
    @Value("${mafka.topic}")
    String topic;
    @Value("${mafka.SubscribeGroup}")
    String SubscribeGroup;

    private static IConsumerProcessor consumer;

    @Resource
    MafkaAukConsumer mafkaService;

    @Bean(name = "mockMqttUplinkMsgMafkaConsumer")
    public IConsumerProcessor closeToNoticeConsumer() throws Exception {
        // auk的mafka消费者配置
        Properties properties = new Properties();

        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, namespace);
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, mafkaAK);
        properties.setProperty(ConsumerConstants.SubscribeGroup, SubscribeGroup);
        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        consumer = MafkaClient.buildConsumerFactory(properties, topic);
        consumer.recvMessageWithParallel(String.class, mafkaService);

        return consumer;
    }
}

