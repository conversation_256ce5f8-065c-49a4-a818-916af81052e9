package com.sankuai.walle.rmanage.config.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Created on 2021/12/6
 */
@Slf4j
public class JsonUtils {

    public final static JsonObject EMPTY_OBJECT = new JsonObject();
    public final static JsonArray EMPTY_ARRAY = new JsonArray();
    public final static String EMPTY_OBJECT_STRING = "{}";
    public final static String EMPTY_ARRAY_STRING = "[]";
    private final static Gson _GSON = new GsonBuilder()
            .registerTypeAdapter(Date.class, new JsonSerializer<Date>() {
                @Override
                public JsonElement serialize(Date src, Type typeOfSrc, JsonSerializationContext context) {
                    return src == null ? JsonNull.INSTANCE : new JsonPrimitive(src.getTime());
                }
            })
            .registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
                @Override
                public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
                    return (json == null || json.isJsonNull()) ? null : new Date(json.getAsLong());
                }
            })
            .create();

    public static <T> T toJavaObject(JsonElement json, Class<T> clazz) {
        return _GSON.fromJson(json, clazz);
    }

    public static <E> List<E> toJavaObjectList(JsonArray jsonArray, Class<E> clazz) {
        if (jsonArray == null) {
            return null;
        }

        List<E> result = new LinkedList<E>();
        for (JsonElement object : jsonArray) {
            result.add(_GSON.fromJson(object, clazz));
        }
        return result;
    }

    public static <T> List<T> toJavaObjectList(String json, Class<T[]> clazz) {
        if (json == null) {
            return null;
        }

        T[] array = _GSON.fromJson(json, clazz);
        return Arrays.asList(array);
    }

    public static <T> String toJsonString(T javaObject) {
        return _GSON.toJson(javaObject);
    }

    public static <T> JsonElement toJson(T javaObject) {
        return JsonParser.parseString(toJsonString(javaObject));
    }

    public static JsonElement parse(String jsonString) {
        return JsonParser.parseString(jsonString);
    }

    public static JsonObject parseJsonObject(String jsonString) {
        return parse(jsonString).getAsJsonObject();
    }

    public static JsonArray parseJsonArray(String jsonString) {
        return parse(jsonString).getAsJsonArray();
    }

    /**
     * 判断jsonObject中某个字段是否为空
     *
     * @param jsonObject
     * @param key
     * @return
     */
    public static boolean isFieldEmpty(JsonObject jsonObject, String key) {
        return !jsonObject.has(key) || jsonObject.get(key).isJsonNull();
    }

    public static JsonArray newArray(Object... values) {
        assert values != null;

        JsonArray j = new JsonArray();
        for (Object value : values) {
            if (value == null) {
                j.add(JsonNull.INSTANCE);
            } else if (value instanceof JsonElement) {
                j.add((JsonElement) value);
            } else {
                JsonPrimitive wrapped = wrapPrimitive(value);
                if (wrapped != null) {
                    j.add(wrapped);
                } else {
                    throw new IllegalArgumentException("Unsupported class [" + value.getClass() + "]");
                }
            }
        }

        return j;
    }

    public static JsonObject newObject(Object... pairs) {
        assert pairs != null;
        assert pairs.length % 2 == 0;

        JsonObject j = new JsonObject();
        for (int i = 0; i < pairs.length; i += 2) {
            assert pairs[i] instanceof String;
            assert pairs[i] != null;

            String key = (String) pairs[i];
            Object value = pairs[i + 1];
            if (value == null) {
                j.add(key, JsonNull.INSTANCE);
            } else if (value instanceof JsonElement) {
                j.add(key, (JsonElement) value);
            } else {
                JsonPrimitive wrapped = wrapPrimitive(value);
                if (wrapped != null) {
                    j.add(key, wrapped);
                } else {
                    throw new IllegalArgumentException("Unsupported class [" + value.getClass() + "]");
                }
            }
        }

        return j;
    }

    public static JsonPrimitive wrapPrimitive(Object primitive) {
        if (primitive instanceof Number) {
            return new JsonPrimitive((Number) primitive);
        } else if (primitive instanceof Character) {
            return new JsonPrimitive((Character) primitive);
        } else if (primitive instanceof Boolean) {
            return new JsonPrimitive((Boolean) primitive);
        } else if (primitive instanceof String) {
            return new JsonPrimitive((String) primitive);
        } else {
            return null;
        }
    }

    public static JsonObject mergeFields(JsonObject add, JsonObject addend) {
        assert add != null && addend != null;

        Set<Map.Entry<String, JsonElement>> entrySet = addend.entrySet();
        for (Map.Entry<String, JsonElement> entry : entrySet) {
            add.add(entry.getKey(), entry.getValue());
        }
        return add;
    }

    public static JsonObject renameKeys(JsonObject jsonObject, String... names) {
        assert jsonObject != null && names != null;

        int len = names.length;
        assert len % 2 == 0;
        for (int i = 0; i < len; i += 2) {
            String oldKey = names[i], newKey = names[i + 1];
            if (jsonObject.has(oldKey)) {
                JsonElement value = jsonObject.get(oldKey);
                jsonObject.add(newKey, value);
                jsonObject.remove(oldKey);
            }
        }

        return jsonObject;
    }

    public static JsonArray renameKeys(JsonArray jsonArray, String... names) {
        assert jsonArray != null && names != null;

        JsonArray result = new JsonArray();
        for (JsonElement item : jsonArray) {
            assert item instanceof JsonObject;

            result.add(renameKeys((JsonObject) item, names));
        }

        return result;
    }

    public static Map<String, String> parseJsonObjectStringAsStringMap(String configJsonString) {
        if (StringUtils.isBlank(configJsonString)) {
            return Collections.emptyMap();
        }
        JsonObject jsonObject = null;
        try {
            jsonObject = JsonUtils.parseJsonObject(configJsonString);
        } catch (Exception e) {
            log.error("JsonUtils#parseJsonObjectStringAsStringMap error. configJsonString = {}", configJsonString, e);
            return Collections.emptyMap();
        }
        Set<Map.Entry<String, JsonElement>> entries = jsonObject.entrySet();
        if (CollectionUtils.isEmpty(entries)) {
            return Collections.emptyMap();
        }

        Map<String, String> resultMap = new LinkedHashMap<>();
        for (Map.Entry<String, JsonElement> entry : entries) {
            String key = entry.getKey();
            JsonElement value = entry.getValue();
            String valueString = JsonUtils.serializeAsString(value);

            resultMap.put(key, valueString);
        }
        return resultMap;
    }

    public static String serializeAsString(JsonElement jsonElement) {
        if (jsonElement.isJsonNull()) {
            return "";
        } else if (jsonElement.isJsonPrimitive()) {
            return jsonElement.getAsString();
        } else {
            return jsonElement.toString();
        }
    }

    public static boolean isJsonObject(String str) {
        JsonElement jsonElement;
        try {
             jsonElement = parse(str);
        } catch (Exception e) {
            log.debug("JsonUtils#isJsonObject str = {}", str, e);
            return false;
        }
        if (jsonElement == null) {
            return false;
        }
        return jsonElement.isJsonObject();
    }

    /**
     * 合并JsonObject
     * 如果key相同，value为基本类型，以o1为准；
     * 如果key相同，value为JsonObject，递归合并；
     * key不同，merge作为结果。
     *
     * @param o1
     * @param o2
     * @return
     */
    public static JsonObject mergeJsonObjectRecursively(JsonObject o1, JsonObject o2) {
        JsonObject result = new JsonObject();
        for (Map.Entry<String, JsonElement> o1EntrySet : o1.entrySet()) {
            String o1Key = o1EntrySet.getKey();
            JsonElement o1Value = o1EntrySet.getValue();
            JsonElement o2Value = o2.get(o1Key);
            if (o2Value != null && o1Value.isJsonObject() && o2Value.isJsonObject()) {
                // o1 o2都有值，且为JsonObject类型，递归聚合
                result.add(o1Key, mergeJsonObjectRecursively(o1Value.getAsJsonObject(), o2Value.getAsJsonObject()));
            } else {
                // o2没有值或类型不是JsonObject，使用o1的kv
                result.add(o1Key, o1Value);
            }
        }
        for (Map.Entry<String, JsonElement> o2EntrySet : o2.entrySet()) {
            String o2Key = o2EntrySet.getKey();
            JsonElement o1Value = o1.get(o2Key);
            if (o1Value == null || o1Value.isJsonNull()) {
                // o1不存在、o2存在的kv放入结果列表
                result.add(o2Key, o2EntrySet.getValue());
            }
        }
        return result;
    }

    public static String toJsonByObjectMapper(Object object) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(object);
    }
}
