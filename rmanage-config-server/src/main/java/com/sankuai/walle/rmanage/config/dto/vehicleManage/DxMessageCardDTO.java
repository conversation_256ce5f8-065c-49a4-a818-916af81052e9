package com.sankuai.walle.rmanage.config.dto.vehicleManage;

import com.sankuai.walle.rmanage.config.common.constant.BusinessTypeMapEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class DxMessageCardDTO {

    /**
     * 车辆名称
     */
    private String name; // 对应模板中的 "车辆：%s / %s" 的第一部分

    /**
     * 车辆ID
     */
    private String vehicleId; // 对应模板中的 "车辆：%s / %s" 的第二部分

    /**
     * 车辆所属组
     */
    private String group; // 对应模板中的 "车辆所属组：%s"

    /**
     * 用车目的
     */
    private String purpose; // 对应模板中的 "用车目的：%s"

    /**
     * VHR描述
     */
    private String vhrDesc; // 对应模板中的 "VHR：%s"

    /**
     * 云控 VHR
     */
    private String telecontrolVHR;

    /**
     * 近场 VHR
     */
    private String nearbyRescueVHR;

    /**
     * 事故时间
     */
    private String accidentTime; // 对应模板中的 "时间：%s"

    /**
     * 事故城市
     */
    private String city; // 对应模板中的 "城市：%s"

    /**
     * 事故地点名称
     */
    private String accidentLocationName; // 对应模板中的 "地点：%s"

    /**
     * 事发路段
     */
    private String roadType; // 对应模板中的 "事发路段：%s"

    /**
     * 上报人
     */
    private Object reporter; // 对应模板中的 "上报人：%s"

    /**
     * 是否有人受伤
     */
    private String isAnyoneInjured; // 对应模板中的 "是否有人受伤：%s"

    /**
     * 三方是否离开
     */
    private String isThirdPartyLeave; // 对应模板中的 "三方是否离开：%s"

    /**
     * 是否双方事故
     */
    private String isBothPartiesAccident; // 对应模板中的 "是否双方事故：%s"

    /**
     * 近场安全员列表字符串
     */
    private String substituteListString; // 特定信息，可能需要根据上下文调整

    /**
     * 车辆碰撞情况描述
     */
    private String collisionSeverityDesc; // 对应模板中的 "车辆碰撞情况：%s"

    /**
     * 事故描述
     */
    private Object accidentDesc; // 对应模板中的 "事故描述：%s"

    /**
     * 驾驶模式描述
     */
    private String driveModeDesc; // 对应模板中的 "驾驶模式：%s"

    /**
     * 事发车速
     */
    private String speed; // 对应模板中的 "事发车速：%s"

    /**
     * 碰撞原因
     */
    private String collisionReason; // 对应模板中的 "碰撞标签：%s"

    /**
     * 车辆位置地图URL
     */
    @Builder.Default
    private String vehicleMapUrl = "https://walle.meituan.com/m/h5-map/vehicle/"; // 对应模板中的 "[[车辆位置|%s%s]]"

    /**
     * 车辆位置地图URL后缀,vin
     */
    private Object vehicleMapUrlSuffix;

    /**
     * 事故信息提交URL
     */
    @Builder.Default
    private String accidentInfoCommitUrl = "https://walle.meituan.com/m/h5-car-tool/accident/"; // 对应模板中的 "[[事故信息补充|%s%s]]"

    /**
     * 事故信息补充后缀,accidentDetail.get("id")
     */
    private Object accidentInfoCommitUrlSuffix;

    /**
     * CSM事件URL
     */
    @Builder.Default
    private String csmEventUrl = "https://walle.sankuai.com/m/csm/event?vin="; // 对应模板中的 "[[云分诊-实时视频|%s%s]]"

    /**
     * CSM事件URL后缀,vin
     */
    private Object csmEventUrlSuffix;

    /**
     * 事故链接
     */
    private String accidentGroupNotice;

    /**
     * Monitor回放视频完整URL（包含所有参数）
     */
    private String monitorVideoFullUrl;

    /**
     * 车辆所属站点
     */
    private String stationName;

    /**
     * 构造消息模版
     */
    public String buildMessageCard(BusinessTypeMapEnum businessTypeMap) {
        if (BusinessTypeMapEnum.ROAD_TEST.equals(businessTypeMap)) {
            return newTemplate(name, vehicleId, stationName, purpose, telecontrolVHR, nearbyRescueVHR, accidentTime, city, accidentLocationName,
                    roadType, reporter,
                    // 唯一不同
                    substituteListString,
                    isAnyoneInjured, isThirdPartyLeave, isBothPartiesAccident, collisionSeverityDesc, accidentDesc,
                    driveModeDesc, speed, collisionReason, vehicleMapUrl, vehicleMapUrlSuffix, accidentInfoCommitUrl,
                    accidentInfoCommitUrlSuffix, csmEventUrl, csmEventUrlSuffix, accidentGroupNotice, monitorVideoFullUrl);
        } else {
            return oldTemplate(name, vehicleId, stationName, purpose,telecontrolVHR, nearbyRescueVHR, accidentTime, city, accidentLocationName,
                    roadType, reporter,
                    isAnyoneInjured, isThirdPartyLeave, isBothPartiesAccident, collisionSeverityDesc, accidentDesc,
                    driveModeDesc, speed, collisionReason, vehicleMapUrl, vehicleMapUrlSuffix, accidentInfoCommitUrl,
                    accidentInfoCommitUrlSuffix, csmEventUrl, csmEventUrlSuffix, accidentGroupNotice, monitorVideoFullUrl);
        }
    }


    // 老模版
    private String oldTemplate(Object... args) {
        return String.format("【基础信息】\n"
                        + "车辆：%s / %s\n" + "车辆所属站点：%s\n" + "用车目的：%s\n" + "云控VHR：%s\n"+ "近场VHR：%s\n" + "时间：%s\n"
                        + "城市：%s\n" + "地点：%s\n" + "事发路段：%s\n" + "上报人：%s\n"
                        + "【现场信息】\n"
                        + "是否有人受伤：%s\n" + "三方是否离开：%s\n" + "是否双方事故：%s\n"
                        + "车辆碰撞情况：%s\n" + "事故描述：%s\n"
                        + "【其他信息】\n"
                        + "驾驶模式：%s\n" + "事发车速：%s\n" + "碰撞标签：%s\n" + "[[车辆位置|%s%s]]\t\t"
                        + "[[事故信息补充|%s%s]]\n" + "[[云分诊-实时视频|%s%s]]\t\t" + "[[事故链接|%s]]\n" + "[[Monitor回放视频|%s]]",
                args
        );
    }

    // 新模板
    private String newTemplate(Object... args) {
        return String.format("【基础信息】\n"
                        + "车辆：%s / %s\n" + "车辆所属站点：%s\n" + "用车目的：%s\n" + "云控VHR：%s\n"+ "近场VHR：%s\n" + "时间：%s\n"
                        + "城市：%s\n" + "地点：%s\n" + "事发路段：%s\n" + "上报人：%s\n"
                        + "近场安全员：%s\n"
                        + "【现场信息】\n"
                        + "是否有人受伤：%s\n" + "三方是否离开：%s\n" + "是否双方事故：%s\n"
                        + "车辆碰撞情况：%s\n" + "事故描述：%s\n"
                        + "【其他信息】\n"
                        + "驾驶模式：%s\n" + "事发车速：%s\n" + "碰撞标签：%s\n" + "[[车辆位置|%s%s]]\t\t"
                        + "[[事故信息补充|%s%s]]\n" + "[[云分诊-实时视频|%s%s]]\t\t" + "[[事故链接|%s]]\n" + "[[Monitor回放视频|%s]]",
                args
        );
    }

}
