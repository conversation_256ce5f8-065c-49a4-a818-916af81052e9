package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.objects.vo.request.CarEquipmentReq;
import com.sankuai.walle.objects.vo.res.ResData;
import org.apache.thrift.TException;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CarEquipmentService {
    ResData insertOrUpdate(List<CarEquipmentReq> jsonData);
    void insert(CarEquipmentReq jsonData);
    void update(CarEquipmentReq jsonData);
    ResData getByUid(String uid);
    List<String> getUids(List<CarEquipmentReq> jsonData);
    ResData getList(Integer pageNum,Integer pageSize);
    String getCityName(String input);
    ResData  bulkSearch(String index,List<Map<String, String>> uids);

    public String getPath(String keyId);
}
