package com.sankuai.walle.rmanage.config.controller;


import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.uac.sdk.entity.resource.UacFeTemplateResponse;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.meituan.uac.sdk.service.UacResourceRemoteService;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarAssetsExample;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarOperationExample;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.*;
import com.sankuai.walle.dal.mrm_manage.example.MyVehicleExample;
import com.sankuai.walle.dal.mrm_manage.example.MyVehicleExampleList;
import com.sankuai.walle.dal.mrm_manage.mapper.MyVehicleMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectTagsMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample;
import com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.CarDetail;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.VechicleService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.sankuai.walle.objects.constants.UacConstant.tenantCode;

/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping
@Log4j
public class VechicleQueryController {

    @Resource
    RemoteObjectTagsMapper remoteObjectTagsMapper;
    @Resource
    TagsMapper tagsMapper;
    @Resource
    VechicleService vechicleService;
    @Resource
    MyVehicleMapper myVehicleMapper;
    @Resource
    VehicleInfoMapper vehicleInfoMapper;
    @Autowired
    UacAuthRemoteService uacAuthRemoteService;
    @Autowired
    UacResourceRemoteService uacResourceRemoteService;

    @MethodDoc(
            displayName = "获取车辆列表",
            description = "获取车辆列表",
            parameters = {
                    @ParamDoc(name = "request", description = "", example = {})
            },
            returnValueDescription = "CommonBaseResponse",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：")
            }
    )
    // 车辆编码(name)/vin/remote object
    // 资产标签号(label)/
    // 车辆型号(EP2)/typeName
    // 资产责任人部门/资产责任人/
    // 城市/场地/
    // 用车目的
    @RequestMapping(path = {"/api/cmdb/vehicle/1.2/homelist/query"}, method = RequestMethod.GET)
    public ResData getCarList(HttpServletRequest request,
                              @RequestParam(required = true) Integer page,
                              @RequestParam(required = true) Integer pageSize,
                              @RequestParam(required = false) String label,
                              @RequestParam(required = false) String carName,
                              @RequestParam(required = false) String typeName,
                              @RequestParam(required = false) String city,
                              @RequestParam(required = false) String area,
                              @RequestParam(required = false) String ownerMis,
                              @RequestParam(required = false) String ownerDepartment,
                              @RequestParam(required = false) String vin,
                              @RequestParam(required = false) String assemblyParts
    ) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        // 根据角色判断当前用户是否获取所有车辆。资管可以获取全部车辆 / 普通用户可以获取自己责任的车辆
        User user = UserUtils.getUser();
        String menuCode = "carList";
        UacFeTemplateResponse template = uacResourceRemoteService.getAuthFeResource(tenantCode, String.valueOf(user.getId()), menuCode);
        String templateCode = template.getTemplateCode();
        boolean allTag = Objects.equals(templateCode, "edit");
        // 可查询全量数据
        try {
            // 分页获取当前页的vins
            MyVehicleExampleList query = new MyVehicleExampleList();
            MyVehicleExample carExample = new MyVehicleExample();
            MyVehicleExample.Criteria carCriteria = carExample.createCriteria();
            if (notNullEmpty(carName )) carCriteria.andCarNameLike("%" + carName + "%");
            if (notNullEmpty(typeName )) carCriteria.andTypeName(typeName);
            if (notNullEmpty(vin)) carCriteria.andVinIn(new ArrayList<>(Collections.singleton(vin)));
            if (notNullEmpty(assemblyParts)) carCriteria.andAssemblyPartsContains(assemblyParts);
//            carCriteria.andTagTypeEqual(4l);
            query.setCar_tag(carExample);
//            if(notNullEmpty(carName) | notNullEmpty(tagName)) {
//            }
            if(!allTag){ ownerMis = user.getLogin(); }
            if(notNullEmpty(label) || notNullEmpty(ownerMis) || notNullEmpty(ownerDepartment) || !allTag) {
                CarAssetsExample carAssetsExample = new CarAssetsExample();
                CarAssetsExample.Criteria assetCriteria = carAssetsExample.createCriteria();
                if (notNullEmpty(label)) assetCriteria.andLabelLike("%" + label + "%");
                if (notNullEmpty(ownerMis)) assetCriteria.andPersonmisLike("%" + ownerMis + "%");
                if(notNullEmpty(ownerDepartment)) assetCriteria.andOwnerDepartmentLike("%"+ownerDepartment+"%");
                query.setCar_assets(carAssetsExample);
            }
            if(notNullEmpty(city) || notNullEmpty(area)) {
                CarOperationExample carOperationExample = new CarOperationExample();
                CarOperationExample.Criteria criteria = carOperationExample.createCriteria();
                if(notNullEmpty(city)){criteria.andCityLike("%"+city+"%");}
                if(notNullEmpty(area)){criteria.andAreaLike("%"+area+"%");}
                query.setCar_operation(carOperationExample);
            }
            if(page!=null && pageSize!=null){
                query.setOffset((page-1)*pageSize);
                query.setRows(pageSize);
            }
            List<String> vins = myVehicleMapper.selectByExample(query);
            if(vins.size()>0) {
                rep.data = vechicleService.constructCarListData(vins);
            }
            rep.rows = myVehicleMapper.countByExample(query);
            rep.code = CommonConstants.SUCCEED_CODE;
            return rep;
        } catch (Exception e) {
            rep.msg = e.getMessage();
            log.error("《获取车辆列表》",e);
            return rep;
        }
    }

    private boolean notNullEmpty(String str){
        return str!=null && !str.isEmpty();
    }

    @Resource
    CarAssetsMapper carAssetsMapper;
    @Resource
    CarOperationMapper carOperationMapper;

    @Resource
    CarExecWordMapper carExecWordMapper;

    @Resource
    CarObjectsMapper carObjectsMapper;

    @Resource
    CarSelectsMapper carSelectsMapper;

    @MethodDoc(
            displayName = "获取车辆详情",
            description = "获取车辆详情",
            parameters = {
                    @ParamDoc(name = "request", description = "", example = {})
            },
            returnValueDescription = "CommonBaseResponse",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：")
            }
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/1.2/car/detail"}, method = RequestMethod.GET)
    public ResData getCarDetail(HttpServletRequest request, @RequestParam(required = true) String vin,
                                @RequestParam(required = false) String label) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;

        try {
            // 获取车辆详情
            CarDetail res = new CarDetail();
            CarObjectsExample objectsExample = new CarObjectsExample();
            objectsExample.createCriteria().andVinEqualTo(vin);
            List<CarObjects> objects = carObjectsMapper.selectByExample(objectsExample);
            res = vechicleService.fetchVectialDetail(objects,vin,label);
            rep.data = res;
            rep.code = CommonConstants.SUCCEED_CODE;
            return rep;
        } catch (Exception e) {
            rep.msg = e.getMessage();
            log.error(vin+"<获取车辆详情> {}",e);
            return rep;
        }
    }

    @MethodDoc(
            displayName = "获取vehicle_info详情",
            parameters = {
                    @ParamDoc(name = "request", description = "", example = {})
            },
            returnValueDescription = "CommonBaseResponse",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：")
            }
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/1.0/vehicle/detail"}, method = RequestMethod.GET)
    public ResData getVehicleDetail(HttpServletRequest request, @RequestParam(required = false) String vin) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;

        try {
            VehicleInfoExample example = new VehicleInfoExample();
            example.createCriteria().andVinEqualTo(vin);
            rep.data = vehicleInfoMapper.selectByExample(example);
            rep.code = CommonConstants.SUCCEED_CODE;
            return rep;
        } catch (Exception e) {
            rep.msg = e.getMessage();
            log.error(vin+"<获取车辆详情> {}",e);
            return rep;
        }
    }

    @MethodDoc(
            displayName = "获取车辆选项",
            description = "获取车辆选项",
            parameters = {
                    @ParamDoc(name = "request", description = "", example = {})
            },
            returnValueDescription = "CommonBaseResponse",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：")
            }
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/1.2/car/selects"}, method = RequestMethod.GET)
    public ResData getCarSelects(HttpServletRequest request,
                                 @RequestParam(required = false) String belong) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        CarSelectsExample example = new CarSelectsExample();
        if(belong!=null){
            example.createCriteria().andBelongEqualTo(belong);
        }
        rep.data = carSelectsMapper.selectByExample(example);
        rep.code = CommonConstants.SUCCEED_CODE;
        return rep;
    }
}
