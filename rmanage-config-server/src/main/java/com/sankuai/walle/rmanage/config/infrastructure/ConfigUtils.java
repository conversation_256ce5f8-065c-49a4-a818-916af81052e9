package com.sankuai.walle.rmanage.config.infrastructure;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.walle.rmanage.config.infrastructure.lionbean.CustomConfigSubjectType;
import com.sankuai.walle.rmanage.config.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Created on 2021/12/6
 */
@Slf4j
public class ConfigUtils {
    private ConfigUtils() {
    }

    /**
     * 获取全局配置主体类型
     * 配置主体类型: 1-远程对象类型, 11-车辆类型, 12-车辆实例, 13-车辆子类型,
     * 21-设备类型, 22-设备实例, 31-软件类型, 32-软件实例, 42-坐席实例
     *
     * @return
     */
    public static List<CustomConfigSubjectType> getGlobalConfigSubjectTypeList() {
        List<CustomConfigSubjectType> customConfigSubjectTypeList = new ArrayList<>();
        try {
            String subjectTypeListGson = ConfigUtilAdapter.getString("custom.config.subject.types", "[]");

            customConfigSubjectTypeList = JsonUtils.toJavaObjectList(subjectTypeListGson,
                    CustomConfigSubjectType[].class);
            log.info("ConfigUtils#getGlobalConfigSubjectTypeList. result = {}", customConfigSubjectTypeList);
        } catch (Exception e) {
            log.error("ConfigUtils#getGlobalConfigSubjectTypeList error.", e);
        }
        return customConfigSubjectTypeList;
    }

    /**
     * 获取灰度的车辆列表
     *
     * @return 车辆vin列表，英文子母处理为小写 lowerCase
     */
    public static Set<String> getMatchAbTestVinList() {
        try {
            String vinListString = ConfigUtilAdapter.getString("vin.abtest.default", "[]");
            log.info("ConfigUtils#getMatchAbTestVinList value = {}", vinListString);
            List<String> vinList = JsonUtils.toJavaObjectList(vinListString, String[].class);

            Set<String> vinSet = vinList.stream().map(String::toLowerCase).collect(Collectors.toSet());
            log.info("ConfigUtils#getMatchAbTestVinList result = {}", vinSet);
            return vinSet;
        } catch (Exception e) {
            log.error("ConfigUtils#getMatchAbTestVinList error. get config error", e);
            return new HashSet<>();
        }
    }
}
