package com.sankuai.walle.rmanage.config.domain;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.objects.vo.request.CarEditReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 车辆编辑领域对象
 * 包含车辆编辑的核心业务逻辑
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VehicleEditDomain extends BaseVehicleDomain {
    
    private CarOperation carOperation;
    private String carType;
    private String dmsNumber;
    
    /**
     * 创建车辆编辑领域对象
     */
    public static VehicleEditDomain create(CarEditReq carEditReq) {
        VehicleEditDomain domain = new VehicleEditDomain();
        domain.setVin(carEditReq.getCarObjects().getVin());
        domain.setCarType(carEditReq.getCarType().getType());
        domain.setDmsNumber(extractDmsNumber(carEditReq));
        
        // 设置车辆对象
        CarObjects carObject = carEditReq.getCarObjects();
        carObject.setCarType(domain.getCarType());
        carObject.setVin(null); // 不允许更新VIN
        carObject.setUpdateTime(new Date());
        domain.setCarObjects(carObject);
        
        // 设置车辆资产
        CarAssets carAssets = carEditReq.getCarAssets();
        carAssets.setUpdateTime(new Date());
        domain.setCarAssets(carAssets);
        
        // 设置车辆运营信息
        CarOperation carOperation = carEditReq.getCarOperation();
        carOperation.setUpdateTime(new Date());
        domain.setCarOperation(carOperation);
        
        // 设置车辆附加信息
        CarEditReq.CarExecWordConvertor carExecWordConvertor = carEditReq.getCarExecWord();
        CarExecWord carExecWord = CarEditReq.CarExecWordConvertor.objExecToJson(carExecWordConvertor);
        carExecWord.setVin(null);
        carExecWord.setUpdateTime(new Date());
        domain.setCarExecWord(carExecWord);
        
        return domain;
    }
    
    /**
     * 提取DMS设备号
     */
    private static String extractDmsNumber(CarEditReq carEditReq) {
        if (carEditReq.getCarDevice() != null) {
            return carEditReq.getCarDevice().getDmsDeviceNumber();
        }
        return null;
    }
    
    /**
     * 处理车辆运营信息
     * 如果不存在则插入新的，如果存在则更新
     */
    public void processCarOperation(List<CarOperation> existingOperations) {
        if (existingOperations == null || existingOperations.isEmpty()) {
            // 不存在则插入新的
            this.carOperation.setVin(this.vin);
            this.carOperation.setId(null);
        } else {
            // 存在则更新
            this.carOperation.setId(existingOperations.get(0).getId());
            this.carOperation.setVin(null);
        }
    }
    
    /**
     * 处理车辆资产信息
     * 根据是否有ID决定是更新还是插入
     */
    public boolean shouldUpdateCarAssets() {
        return this.carAssets.getId() != null && this.carAssets.getVin() != null;
    }
    
    /**
     * 处理车辆执行词
     * 如果不存在则插入新的，如果存在则更新
     */
    public void processCarExecWord(List<CarExecWord> existingCarExecWords) {
        if (existingCarExecWords == null || existingCarExecWords.isEmpty()) {
            // 不存在则插入新的
            this.carExecWord.setVin(this.vin);
            this.carExecWord.setAddTime(new Date());
        } else {
            // 存在则更新
            this.carExecWord.setId(existingCarExecWords.get(0).getId());
        }
    }
    
    /**
     * 准备车辆资产更新
     * 设置不允许更新的字段为null
     */
    public void prepareCarAssetsForUpdate() {
        this.carAssets.setLabel(null);
        this.carAssets.setSn(null);
    }
    
    /**
     * 准备车辆资产插入
     * 设置VIN
     */
    public void prepareCarAssetsForInsert() {
        this.carAssets.setVin(this.vin);
    }
} 