package com.sankuai.walle.rmanage.config.Listener.strategy;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * MD5校验消息处理策略
 * 处理客户端发送的MD5校验请求，包括：
 * 1. 计算文件MD5值
 * 2. 比对MD5值
 * 3. 返回校验结果
 */
@Component
@Slf4j
public class CheckMd5MessageHandler implements MessageHandlerStrategy {
    /**
     * 处理MD5校验消息的具体实现
     * @param message 包含MD5校验信息的消息对象
     * @throws Exception 处理过程中可能抛出的异常
     */

    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarDeviceConfigService carDeviceConfigService;

    @Override
    public void handle(AukUpCountent message) throws Exception {
        checkMd5Func(message);
    }

    private void checkMd5Func(AukUpCountent message){
        // json的数据结构应该是 {
        //  vin:{
        //      配置项：md5值，
        //  }
        // }
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] content = decoder.decode(message.getMessage());
        String str = new String(content);
        log.info("接收校验md5消息，{}",str);
        JSONObject jsonObject = JSONObject.parseObject(str);
        String vin = jsonObject.keySet().stream().findFirst().orElse(null);
        if (vin == null){
            return;
        }
        JSONObject configs = JSONObject.parseObject(jsonObject.getString(vin));
        // 查询配置
        CarDeviceConfigExample query = new CarDeviceConfigExample();
        query.createCriteria().andVinEqualTo(vin).andStatusEqualTo(ConfigConstant.FINISH);
        List<CarDeviceConfig> configObj = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
        Map<String, CarDeviceConfig> configsmd5 = configObj.stream().collect(Collectors.toMap(
                CarDeviceConfig::getName, Function.identity(),
                (oldValue, newValue) -> {
                    // 比较更新时间，返回最新的一条
                    return oldValue.getUpdateTime().after(newValue.getUpdateTime()) ? oldValue : newValue;
                })
        );
        for (String configName: configsmd5.keySet()) {
            if (Strings.isEmpty(configName))continue;
            CarDeviceConfig target = configsmd5.get(configName);
            String md5Cloud = MD5Util.getMD5(target.getConfig());
            String md5Car = null;
            if (!Objects.isNull(configs)){
                md5Car = configs.getString(configName);
            }
            if (!Objects.equals(md5Cloud, md5Car)){
                // 重新下发
                carDeviceConfigService.retryConfigToMqtt(target);
            }
        }

    }
} 