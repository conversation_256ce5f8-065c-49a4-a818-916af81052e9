package com.sankuai.walle.rmanage.config.policy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.dto.accident.VehicleRealtimeStatusDTO;
import com.sankuai.walle.rmanage.config.service.AccidentMessageService;
import com.sankuai.walle.rmanage.config.thread.RedisProductor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component("cloud_triage.biz_cloud_triage_event")
@Slf4j
public class CollisionDetectionPolicy extends PolicyBase{
    @Resource
    DxGroupHandler dxGroupHandler;

    @MdpConfig("colision.detection.event.check.group")
    Long SafetyCheckGroupId = 0L;

    @Resource
    RedisProductor redisProductor;

    @MdpConfig("check.group.HD.video.urltext")
    public String videoMsg = "";

    @MdpConfig("check.group.video.timeGap")
    private ArrayList<Integer> checkGroupVideoTimeGap;

    @MdpConfig("check.group.card.report.accident.url")
    String reportAccidentUrl;

    @Resource
    AccidentMessageService accidentMessageService;

    @Override
    public boolean run(Map<String, Object> eventDetailMap) {

        //1 使用规则引擎过滤vhr = 1 的事件，该事件会在云分诊进行
        Expression compiledExpression = setRule();
        Map<String, Object> variables = new HashMap<>();
        variables.put("map1", eventDetailMap);
        boolean isFilter = (boolean) compiledExpression.execute(variables);
        if(!isFilter){
            log.info("status != 0, not operation， eventDetailMap = {}", eventDetailMap);
            return false;
        }
        // 1 获取异常事件类型
        if(!eventDetailMap.containsKey("event_type")){
            return false;
        }
        Integer eventType = (Integer)eventDetailMap.get("event_type");
        switch (eventType){
            //当异常事件为1，向指定群发送大象消息，以及发送大象群视频
            case 1:
            case 24:
            case 26:
                dxGroupHandler.sendMessage(SafetyCheckGroupId, makeUncheckedAccidentNoticeMsg(eventDetailMap));
                writeMessageDataToRedis(SafetyCheckGroupId, eventDetailMap);
                break;
        }
        return true;
    }
    @Override
    public Expression setRule() {
        String expression = "eventFilterFunction(map1)";
        return AviatorEvaluator.compile(expression);
    }

    private String makeUncheckedAccidentNoticeMsg(Map<String,Object> eventDetailMap ) {
        String vehicle_map_url = "https://walle.meituan.com/m/h5-map/vehicle/";
        String csm_event_url   = "https://walle.sankuai.com/m/csm/event?vin=";
        String vin = String.valueOf(eventDetailMap.get("vin"));

        // 0 获取车辆的实时状态
        VehicleRealtimeStatusDTO realtimeStatusDTO = accidentMessageService.callVehicleStatusService(vin);
        log.info("realtimeStatusDTO = {}", realtimeStatusDTO);

        // 1 获取异常事件的发生时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String eventTime = sdf.format(new Date((Long) eventDetailMap.get("event_time")));

        // 2 获取城市字段
        Map<String, String> recordMessage = accidentMessageService.getCityAndAffiliationFromRecord(vin);
        String city = "未知";
        if(recordMessage.containsKey("city")){
            city = recordMessage.get("city");
        }
        // 3 获取车辆所属组
        String group = accidentMessageService.getCarGroup(vin);

        // 4 获取车辆vhr
        Integer vhr = realtimeStatusDTO.getIsVhrMultiple();
        String vhrDesc = "未知";
        if(vhr != null){
            vhrDesc = vhr == 1?">1" : "=1";
        }

        // 5 获取速度 和 驾驶模式
        String speed = "未知";
        String driveModeDesc = "未知";
        Map<String,Object> vehicleHistoryStatus = accidentMessageService.getVehicleHistoryStatus(vin, new Date((Long) eventDetailMap.get("event_time")));
        if (vehicleHistoryStatus != null) {
            double speedNum = Double.valueOf(vehicleHistoryStatus.getOrDefault("speed", "0").toString())*3.6;
            speed = String.format("%.2f", speedNum) + " km/h";
            driveModeDesc = vehicleHistoryStatus.getOrDefault("drive_mode_desc", "未知").toString();
        }

        // 6 获取硬件错误信息
        String faultMessage = accidentMessageService.getFaultInformation(realtimeStatusDTO.getName());

        // 7 获取碰撞标签
        String collisionReason = "未知";
        String remark = String.valueOf(eventDetailMap.get("remark"));
        if (!StringUtils.isEmpty(remark)) {
            JSONObject jsonObject = JSON.parseObject(remark);
            if (jsonObject.containsKey("alert")) {
                collisionReason = jsonObject.get("alert").toString();
            }
        }

        String bodyText = String.format("【基础信息】\n"+
                        "车辆：%s / %s\n" +
                        "车辆所属组：%s\n" +
                        "用车目的：%s\n" +
                        "VHR：%s\n" +
                        "时间：%s\n" +
                        "城市：%s\n" +
                        "地点：%s\n" +
                        "【其他信息】\n"+
                        "驾驶模式：%s\n" +
                        "事发车速：%s\n" +
                        "硬件检测故障：\n%s\n" +
                        "碰撞标签：%s\n" +
                        "[[车辆位置|%s%s]]\t\t" +
                        "[[云分诊-实时视频|%s%s]]\n"+
                        "[[上报事故|%s%s]]",

                realtimeStatusDTO.getName(),realtimeStatusDTO.getVehicleId(),
                group,
                realtimeStatusDTO.getPurpose(),
                vhrDesc,
                eventTime,
                city,
                realtimeStatusDTO.getParkDesc(),

                driveModeDesc,
                speed,
                faultMessage,
                collisionReason,
                vehicle_map_url,vin,
                csm_event_url,vin,
                reportAccidentUrl, vin);
        return bodyText;
    }

    private void writeMessageDataToRedis(Long groupId, Map<String,Object> eventDetailMap){
        Long eventTime = (Long) eventDetailMap.get("event_time") / 1000; //时间需要转化成秒级
        String vin = String.valueOf(eventDetailMap.get("vin"));
        String vehicleId = String.valueOf(eventDetailMap.get("vehicle_id"));
        String eventId =  String.valueOf(eventDetailMap.get("event_id"));
        String vehicleName = eventId.substring(eventId.lastIndexOf("_") + 1);

        String concatStr = vin + "/" + vehicleId + "/" + vehicleName;
        try{
            String loopUrl = String.format(videoMsg,vin,eventTime-checkGroupVideoTimeGap.get(0), eventTime+checkGroupVideoTimeGap.get(1) ,"v_loop");
            String concatUrl = String.format(videoMsg,vin,eventTime-checkGroupVideoTimeGap.get(2), eventTime+checkGroupVideoTimeGap.get(3),"v_concat");
            redisProductor.sendDateToRedisQueue("HDvideoParamsQueue", concatStr, eventTime, loopUrl,concatUrl, groupId, "collisionDetect");
            log.info("writeMessageDataToRedis, vin = {}, accidentTime = {}, videoMsg = {}, groupId = {} ", vin, eventTime,videoMsg, groupId);
        }
        catch (Exception e){
            log.error("writeMessageDataToRedis, writeMessageDataToRedis is failed",e);
        }
    }

}
