package com.sankuai.walle.rmanage.config.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CollisionSeverity {

    YISI(2, "疑似", "疑似碰撞"),
    QINGWEI(1, "轻微", "轻微碰撞"),
    YANZHONG(0, "严重", "严重碰撞"),
    UNKONW(-1, "默认值", "");


    private Integer code;
    private String desc;
    private String groupNameSuffix;

    public static String getDescByCode(Integer code) {
        for (CollisionSeverity collisionSeverity : CollisionSeverity.values()) {
            if (collisionSeverity.getCode().equals(code)) {
                return collisionSeverity.getDesc();
            }
        }
        return CollisionSeverity.UNKONW.desc; // 如果找不到对应的 code 值，则返回 null
    }

    public static String getGroupNameSuffixByCode(Integer code) {
        for (CollisionSeverity collisionSeverity : CollisionSeverity.values()) {
            if (collisionSeverity.getCode().equals(code)) {
                return collisionSeverity.getGroupNameSuffix();
            }
        }
        return CollisionSeverity.UNKONW.groupNameSuffix;
    }
}
