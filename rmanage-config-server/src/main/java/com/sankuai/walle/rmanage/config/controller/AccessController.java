package com.sankuai.walle.rmanage.config.controller;

import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.AssetService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * 后端接口
 */
@RestController
@RequestMapping(path = {"/eve/cmdb/rest/access"})
public class AccessController {

    @Resource
    AssetService assetService;

    @RequestMapping(path = {"/api/allvins"}, method = RequestMethod.GET)
    public List<String> allVins() {
        return assetService.queryAllVins();
    }
}
