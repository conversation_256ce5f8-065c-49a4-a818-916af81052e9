package com.sankuai.walle.rmanage.config.repository.impl;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.carManage.example.CarExecWordExample;
import com.sankuai.walle.carManage.example.CarOperationExample;
import com.sankuai.walle.carManage.mapper.CarAssetsMapper;
import com.sankuai.walle.carManage.mapper.CarExecWordMapper;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.carManage.mapper.CarOperationMapper;
import com.sankuai.walle.rmanage.config.repository.VehicleEditRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 车辆编辑仓储实现
 * 实现具体的数据访问逻辑
 */
@Repository
public class VehicleEditRepositoryImpl implements VehicleEditRepository {
    
    @Resource
    private CarOperationMapper carOperationMapper;
    
    @Resource
    private CarExecWordMapper carExecWordMapper;
    
    @Resource
    private CarObjectsMapper carObjectsMapper;
    
    @Resource
    private CarAssetsMapper carAssetsMapper;
    
    @Override
    public List<CarOperation> findCarOperationsByVin(String vin) {
        CarOperationExample example = new CarOperationExample();
        example.createCriteria().andVinEqualTo(vin);
        return carOperationMapper.selectByExample(example);
    }
    
    @Override
    public List<CarExecWord> findCarExecWordsByVin(String vin) {
        CarExecWordExample example = new CarExecWordExample();
        example.createCriteria().andVinEqualTo(vin);
        return carExecWordMapper.selectByExample(example);
    }
    
    @Override
    public void updateCarObjects(CarObjects carObjects) {
        carObjectsMapper.updateByPrimaryKeySelective(carObjects);
    }
    
    @Override
    public void updateCarAssets(CarAssets carAssets) {
        carAssetsMapper.updateByPrimaryKeySelective(carAssets);
    }
    
    @Override
    public void insertCarAssets(CarAssets carAssets) {
        carAssetsMapper.insertSelective(carAssets);
    }
    
    @Override
    public void insertCarOperation(CarOperation carOperation) {
        carOperationMapper.insertSelective(carOperation);
    }
    
    @Override
    public void updateCarOperation(CarOperation carOperation) {
        carOperationMapper.updateByPrimaryKeySelective(carOperation);
    }
    
    @Override
    public void insertCarExecWord(CarExecWord carExecWord) {
        carExecWordMapper.insertSelective(carExecWord);
    }
    
    @Override
    public void updateCarExecWord(CarExecWord carExecWord) {
        carExecWordMapper.updateByPrimaryKeySelective(carExecWord);
    }
} 