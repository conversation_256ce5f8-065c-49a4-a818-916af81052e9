package com.sankuai.walle.rmanage.config.crane.datatransfer;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import lombok.extern.slf4j.Slf4j;

/**
 * 迁移remoteObject表数据
 * remote_objects >> remote_custom_config
 *
 * <AUTHOR> Created on 2022/2/25
 */
@Slf4j
@CraneConfiguration
public class RemoteObjectDataTransferCraneTask {
    private static final int BATCH_COUNT = 20;

    /**
     * 迁移remoteObject数据
     */
    @Crane("rmanage-config-remote-object-config-transfer")
    public void transferRemoteObjects() {

    }

}
