package com.sankuai.walle.rmanage.config.helper.convertor;

import com.meituan.finerp.eam.dto.OpenAssetDTO;
import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class OpenAssetDtoConvert {
    public static List<CarAssets> toCarAssets(List<OpenAssetDTO> addTarget){
        return addTarget.stream().map(openAssetDTO -> {
            String sn = openAssetDTO.getSerialNumber().trim();
            String vin = SnToVin(sn);
            CarAssets child = new CarAssets();
            child.setVin(vin);
            child.setSn(sn);
            child.setSmallType(openAssetDTO.getSmallType());
            child.setScrap(openAssetDTO.getScrap());
            child.setPersonmis(openAssetDTO.getPersonMis());
            child.setLabel(openAssetDTO.getLabel());
            child.setBrand(openAssetDTO.getBrand());
            child.setComment("");
            child.setAddTime(new Date());
            child.setUpdateTime(new Date());
            return child;
        }).filter(carObject -> carObject.getVin().length()>0).collect(Collectors.toList());

    }

//    public static List<RemoteObjects> toRemoteObject(List<OpenAssetDTO> addTarget){
//        return addTarget.stream().map(openAssetDTO -> {
//            RemoteObjects child = new RemoteObjects();
//            child.setVin(openAssetDTO.getSerialNumber());
//            // 必填项
//            child.setName("empty");
//            child.setLicenseNo("empty");
//            child.setRemoteObjectTypeId(2l);
//            child.setRemoteCarTypeId(3l);
//            // 新增车辆，设置默认值
//            child.setRemoteObjectConfigure("{}");
//            child.setRemoteRelease("{}");
//            child.setStatus(1);
//            return child;
//        }).collect(Collectors.toList());
//    }

    public static String SnToVin(String sn){
        String vin = "";
        String[] list = sn.split("-");
//        System.out.println(Arrays.toString(list));
        for(String child: list){
            if(child.startsWith("S")){
                continue;
            } else if (child.length()<=5) {
                continue;
            }
            vin = child;
        }
        return vin;
    }

    public static List<CarObjects> toCarObject(List<OpenAssetDTO> addTarget){
        return addTarget.stream().map(openAssetDTO -> {
            CarObjects child = new CarObjects();
            String sn = openAssetDTO.getSerialNumber().trim();
            String vin = SnToVin(sn);
            child.setVin(vin);
            // 必填项
            child.setName("empty");
            child.setLicenseno("empty");
            return child;
        }).filter(carObjects -> carObjects.getVin().length()>0).collect(Collectors.toList());
    }
}
