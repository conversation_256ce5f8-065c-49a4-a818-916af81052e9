package com.sankuai.walle.rmanage.config.repository;

import java.util.List;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarOperation;

/**
 * 车辆编辑仓储接口
 * 定义数据访问的抽象接口
 */
public interface VehicleEditRepository {
    
    /**
     * 根据VIN查找车辆运营信息
     */
    List<CarOperation> findCarOperationsByVin(String vin);
    
    /**
     * 根据VIN查找车辆扩展信息
     */
    List<CarExecWord> findCarExecWordsByVin(String vin);
    
    /**
     * 更新车辆对象
     */
    void updateCarObjects(CarObjects carObjects);
    
    /**
     * 更新车辆资产
     */
    void updateCarAssets(CarAssets carAssets);
    
    /**
     * 插入车辆资产
     */
    void insertCarAssets(CarAssets carAssets);
    
    /**
     * 插入车辆运营信息
     */
    void insertCarOperation(CarOperation carOperation);
    
    /**
     * 更新车辆运营信息
     */
    void updateCarOperation(CarOperation carOperation);
    
    /**
     * 插入车辆执行词
     */
    void insertCarExecWord(CarExecWord carExecWord);
    
    /**
     * 更新车辆执行词
     */
    void updateCarExecWord(CarExecWord carExecWord);
} 