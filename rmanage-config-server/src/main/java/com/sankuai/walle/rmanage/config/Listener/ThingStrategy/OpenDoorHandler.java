package com.sankuai.walle.rmanage.config.Listener.ThingStrategy;

import com.sankuai.walle.objects.bo.AukChargingCarbinetUpContentBO;
import com.sankuai.walle.rmanage.config.config.AukClientConfig;
import com.sankuai.walle.rmanage.config.constant.ChargingCarbinetConstant;
import com.sankuai.walle.rmanage.config.repository.ChargingCabinetRepsitory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class OpenDoorHandler implements ThingModelHandlerStrategy {
    private final PathMatcher pathMatcher = new AntPathMatcher();
    @Resource
    ChargingCabinetRepsitory chargingCabinetRepsitory;

    @Override
    public void handle(AukChargingCarbinetUpContentBO message) throws Exception {

        String productKey = message.getProductKey();
        String deviceKey = message.getDeviceKey();
        String moduleIdentifier = message.getModuleIdentifier();// Remote_Control
        String funcIdentifier = message.getFuncIdentifier();

        // 根据提取的变量进行业务处理
        log.info("处理充电柜开门指令的回复消息 - message: {}",  message);
        if (moduleIdentifier.equals(ChargingCarbinetConstant.Remote_Control)
                && funcIdentifier.equals(ChargingCarbinetConstant.Remote_ELock_Control)
                && productKey.equals(ChargingCarbinetConstant.CHARGING_CABINET_PRODUCT_KEY)) {
            // 开门指令结果入库
            chargingCabinetRepsitory.updateDao(message.getMsgId(), message.getOutValue(), deviceKey);
        }
    }


}
