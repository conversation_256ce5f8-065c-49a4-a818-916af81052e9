package com.sankuai.walle.rmanage.config.controller;


import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.util.IOUtils;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.classify.entity.CarClassifyDeviceConfigView;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarConfigTask;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.*;
import com.sankuai.walle.dal.classify.mapper.*;
import com.sankuai.walle.dal.mrm_manage.mapper.*;
import com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfo;
import com.sankuai.walle.dal.walle_data_center.entity.VehicleInfo;
import com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample;
import com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample;
import com.sankuai.walle.dal.walle_data_center.mapper.BizPlaceInfoMapper;
import com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper;
import com.sankuai.walle.dal.wallevresv.example.VresvCityParkExample;
import com.sankuai.walle.dal.wallevresv.mapper.VresvCityParkMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.objects.vo.request.*;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.constant.ConfigEnumConstant;
import com.sankuai.walle.rmanage.config.convertor.CarTypeConvert;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.service.CarSelectsQueryService;
import com.sankuai.walle.rmanage.config.service.impl.VehicleDeviceServiceImpl;
import com.sankuai.walle.rmanage.config.service.impl.CarTypeService;
import com.sankuai.walle.rmanage.config.common.convertor.Object2Map;
import com.sankuai.walle.rmanage.config.service.infrastructureService.LexicalAnalyzerService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping
@Log4j
public class ConfigController {

    @Resource
    RemoteCarTypeMapper remoteCarTypeMapper;

    @Resource
    RemoteObjectTagsMapper remoteObjectTagsMapper;
    @Resource
    RemoteObjectsMapper remoteObjectsMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Resource
    TagsMapper tagsMapper;
    @Resource
    VehicleDeviceServiceImpl carTagDeviceService;
    @Resource
    BizPlaceInfoMapper bizPlaceInfoMapper;
    // 三级分类：1 城市 2 运营区域 3 运营组别 4 车辆编号
    @Resource
    VresvCityParkMapper vresvCityParkMapper;
    @Resource
    VehicleInfoMapper vehicleInfoMapper;
//    @Resource
//    RemoteDeviceTypeMapper remoteDeviceTypeMapper;
//    @Resource
//    CarClassifyMapper carClassifyMapper;
    @Resource
    CarClassifyThirdModelMapper carClassifyThirdModelMapper;
    @Resource
    CarConfigMapper carConfigMapper;
    @Resource
    CarClassifyDeviceConfigViewMapper carClassifyDeviceConfigViewMapper;
    @Resource
    CarConfigMyMapper configMyMapper;
    @Resource
    CarTypeService carTypeService;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarConfigTaskMapper carConfigTaskMapper;
    @Resource
    ConfigService configService;
    @Resource
    LexicalAnalyzerService lexicalAnalyzerService;
    @Resource
    CarSelectsQueryService carSelectsQueryService;
    @Autowired
    Environment env;

    @Autowired
    @Qualifier("s3PlusClient0")
    private AmazonS3 amazonS3;

    @Autowired
    ConfigEnumConstant configEnumConstant;
    
    // 获取一级车型
    @RequestMapping(path = {"/api/cmdb/car_type"}, method = RequestMethod.GET)
    public ResData getCarType(HttpServletRequest request) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;

        try {
//            RemoteCarTypeExample select = new RemoteCarTypeExample();
//            RemoteCarType remoteCarType = remoteCarTypeMapper.selectByExample(select);
            List<CarSelects> carSelects = carSelectsQueryService.fetchCarSelectsByBelone("car_type");
            rep.data = carSelects.stream().map(CarTypeConvert::carSelect2CarTypeRep).collect(Collectors.toList());
            rep.code = CommonConstants.SUCCEED_CODE;
            return rep;
        } catch (Exception e) {
            rep.msg = e.getMessage();
            return rep;
        }
    }

    // 获取二级车型
    @RequestMapping(path = {"/api/cmdb/second_car_type"}, method = RequestMethod.GET)
    public ResData getSecondCarType(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = carTagDeviceService.getTags();
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    // 获取三级车型(城市)???
    @RequestMapping(path = {"/api/cmdb/citys"}, method = RequestMethod.GET)
    public ResData getCitys(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            VresvCityParkExample select = new VresvCityParkExample();
            req.data = vresvCityParkMapper.selectByExample(select);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    // 所有园区
    @RequestMapping(path = {"/api/cmdb/parks"}, method = RequestMethod.GET)
    public ResData getPlaces(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            List<String> msg = new ArrayList<>();
            BizPlaceInfoExample select = new BizPlaceInfoExample();
            select.createCriteria().andStatusEqualTo((byte) 1);
            req.data = bizPlaceInfoMapper.selectByExample(select);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }

    // 所有车辆编号
    @RequestMapping(path = {"/api/cmdb/cars"}, method = RequestMethod.GET)
    public ResData getCars(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
//            List<VehicleInfo> msg = new ArrayList<>();
//            VehicleInfoExample select = new VehicleInfoExample();
            CarObjectsExample query = new CarObjectsExample();
            req.data = carObjectsMapper.selectByExample(query);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }


    // 获取配置项
    @RequestMapping(path = {"/api/cmdb/config/names"}, method = RequestMethod.GET)
    public ResData getConfigName(HttpServletRequest request){
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            req.data = configEnumConstant.getConfigs();
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            return req;
        }
    }


    /***** 配置相关api *****/

    // 创建配置
    /* 配置项：IPMI、RTK、SSH公钥、USB白名单、OBU上传、RFID的IP、SIM卡配置
    */
    @RequestMapping(path = {"/api/cmdb/device/config"}, method = RequestMethod.POST)
    public ResData createConfig(@RequestBody ClassifyConfigReq body, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            // 插入一级、二级
            Date now = new Date();

            // 插入配置项，再将分类、三级车型分类、设备、配置项关联
            // 20221121-change: 插入配置项，再将设备、配置项关联
//            for(CarClassifyThirdModel thirdModel:thirdModels) {
            for (ClassifyConfigReq.DeviceConfigVo deviceConfigVo : body.getDeviceConfig()) {
                CarConfig config = new CarConfig() {{
                    setName(deviceConfigVo.getName());
                    setConfig(deviceConfigVo.getConfig());
                    setConfigName(deviceConfigVo.getConfig_name());
                    setConfigVersion(ConfigConstant.DEV_VERSION);
                }};
                carConfigMapper.insert(config);
                CarClassifyDeviceConfigView deviceView = new CarClassifyDeviceConfigView() {{
//                        setCarClassifyId(classify_id);
//                        setCarClassifyThirdModelId(thirdModel.getId());
                    setFirstDeviceTypeId(deviceConfigVo.getDevice_type_id());
                    setConfigId(config.getId());
                    setAddTime(now);
                    setUpdateTime(now);
                }};
                carClassifyDeviceConfigViewMapper.insert(deviceView);
            }
//            }
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            log.error("<创建配置>",e);
            req.msg = e.getMessage();
            return req;
        }
    }

    // 修改配置
    @RequestMapping(path = {"/api/cmdb/device/config"}, method = RequestMethod.PUT)
    public ResData updateConfig(@RequestBody CarConfig body, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            CarConfig data = new CarConfig(){{
                setConfigName(body.getConfigName());
                setConfig(body.getConfig());
                setId(body.getId());
                setFileType(body.getFileType());
            }};
            req.data = carConfigMapper.updateByPrimaryKeySelective(data);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            log.error("<修改配置>",e);
            req.msg = e.getMessage();
            return req;
        }
    }

    // 配置列表
    // 如果传递config_id，则返回该配置的dev版本的具体内容
    @RequestMapping(path = {"/api/cmdb/device/classify"}, method = RequestMethod.GET)
    public ResData getClassify(@RequestParam(required = false) Integer page,
                               @RequestParam(required = false) Integer pageSize,
                               @RequestParam(required = false) Long config_id,
//                               @RequestParam(required = false) Long classify_id,
//                               @RequestParam(required = false) Boolean allRelease,
                               @RequestParam(required = false) String configName,
                               @RequestParam(required = false) Long device_id,
                               HttpServletRequest request) {
//        if (classify_name!=null && !classify_name.isEmpty()){
//            CarClassifyExample carClassifyExample = new CarClassifyExample();
//            carClassifyExample.createCriteria().andNameEqualTo(classify_name);
//            List<CarClassify> carClassify = carClassifyMapper.selectByExample(carClassifyExample);
//            if (carClassify.size()>0){
//                classify_id=carClassify.get(0).getId();
//            }
//        }

        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        HashMap<String, Object> data = new HashMap<>();
        CarClassifyDeviceConfigViewExample query = new CarClassifyDeviceConfigViewExample();
        if (page != null && pageSize != null) {
            query.setOffset(page * pageSize);
        }
        if (pageSize != null) {
            query.setRows(pageSize);
        }
        query.setOrderByClause("id desc");
        CarClassifyDeviceConfigViewExample.Criteria sql = query.createCriteria();

        List<Long> ids = carDeviceConfigService.filterConfigIds();
        if (configName != null){
            List<Long> ids2 = carConfigMapper.selectByExample(new CarConfigExample(){{
                createCriteria().andNameEqualTo(configName);
            }}).stream().map(CarConfig::getId).collect(Collectors.toList());
            ids.retainAll(ids2);
        }

        if(ids.size()>0) {
            sql.andConfigIdIn(ids);
        }
        if (config_id != null){ sql.andConfigIdEqualTo(config_id); }
        List<CarClassifyDeviceConfigView> views = carClassifyDeviceConfigViewMapper.selectByExample(query);

        List<HashMap> list = new ArrayList<>();
        for (CarClassifyDeviceConfigView view: views) {
            HashMap<String,Object> child = new HashMap<>();

//            long deviceId = view.getFirstDeviceTypeId();
            long configId = view.getConfigId();

            // 设备查询转移到 eve 仓库
//            RemoteDeviceType device = remoteDeviceTypeMapper.selectByPrimaryKey(deviceId);
            CarConfig config = carConfigMapper.selectByPrimaryKey(configId);
            if(config!=null) {
                child.put("view", view);
                child.put("device", null);
                child.put("config", config);
                list.add(child);
            }
        }

        data.put("page", page);
        data.put("pageSize", pageSize);
        data.put("count", carClassifyDeviceConfigViewMapper.countByExample(query));
        data.put("data", list);
        req.code = CommonConstants.SUCCEED_CODE;
        req.data = data;
        return req;
    }

    // 删除配置
    @RequestMapping(path = {"/api/cmdb/device/config/{config_id}"}, method = RequestMethod.DELETE)
    public ResData deleteConfig(@PathVariable Long config_id, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            carConfigMapper.selectByPrimaryKey(config_id);
            CarClassifyDeviceConfigViewExample viewExample = new CarClassifyDeviceConfigViewExample();
            viewExample.createCriteria().andConfigIdEqualTo(config_id);
            List<CarClassifyDeviceConfigView> viewsObject = carClassifyDeviceConfigViewMapper.selectByExample(viewExample);
            List<Long> carClassifyThirdModels = viewsObject.stream().map(CarClassifyDeviceConfigView::getCarClassifyThirdModelId).collect(Collectors.toList());
            // 删除
            carConfigMapper.deleteByPrimaryKey(config_id);
            carClassifyDeviceConfigViewMapper.deleteByExample(viewExample);
            CarClassifyThirdModelExample thirdModelExample = new CarClassifyThirdModelExample();
            thirdModelExample.createCriteria().andIdIn(carClassifyThirdModels);
            carClassifyThirdModelMapper.deleteByExample(thirdModelExample);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            log.error("<删除配置>",e);
            req.msg = e.getMessage();
            return req;
        }
    }


    /***** 版本相关api *****/

    // 发布配置，生成配置的版本号
    @RequestMapping(path = {"/api/cmdb/car/config/release"}, method = RequestMethod.POST)
    public ResData createTagConfig(@RequestBody CarConfig body, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            Long big_version = configMyMapper.getBigVersion(body.getId());
            if (big_version == null){
                big_version = 0L;
            }
            CarConfig dev = carConfigMapper.selectByPrimaryKey(body.getId());
            Long finalBig_version = big_version + 1L;
            CarConfig config_new = new CarConfig(){{
                setFileType(dev.getFileType());
                setConfig(dev.getConfig());
                setName(dev.getName());
                setConfigName(dev.getConfigName());
                setMasterId(dev.getId());
                setConfigVersion(finalBig_version);
            }};
            req.data = carConfigMapper.insert(config_new);
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            log.error("<修改配置>",e);
            req.msg = e.getMessage();
            return req;
        }
    }

    // 配置版本列表
    @RequestMapping(path = {"/api/cmdb/car/config/release"}, method = RequestMethod.GET)
    public ResData getReleaseConfigHistory(@RequestParam(required = false) Integer page,
                                           @RequestParam(required = false) Integer pageSize,
                                           @RequestParam(required = false) Long config_id,
                                           HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try{
            CarConfigExample sql = new CarConfigExample();
            sql.createCriteria().andMasterIdEqualTo(config_id);
            List<CarConfig> data = carConfigMapper.selectByExampleWithBLOBs(sql);
            req.data = data;
            req.code = CommonConstants.SUCCEED_CODE;
        }catch (Exception e){
            log.error(e);
        }

        return req;
    }

    // 查询当前分类下的所有车辆（最小单位是4级分类）
//    @RequestMapping(path = {"/api/cmdb/clasify/device/car"}, method = RequestMethod.GET)
//    public ReqData getClassifyCars(@RequestParam Long viewId,HttpServletRequest request) {
//        ReqData req = new ReqData();
//        req.code = Constants.errorCode;
//        CarClassifyDeviceConfigView view = carClassifyDeviceConfigViewMapper.selectByPrimaryKey(viewId);
//        long clasifythirdModelId = view.getCarClassifyThirdModelId();
//        // 一级、二级分类筛选
//        Long carClassifyId = view.getCarClassifyId();
//        CarClassify carClassify = carClassifyMapper.selectByPrimaryKey(carClassifyId);
//        Long firstCarModelId = carClassify.getFirstCarModel(); // 一级车，必填
//        List<String> vins = carTypeService.GetFirstTypeCars(firstCarModelId);
//        Long secondCarModelId = carClassify.getSecondCarModel(); // 二级车，选填
//        if ( secondCarModelId!=null ){
//            List<String> vins_second = carTypeService.GetSecondTypeCars(secondCarModelId);
//            vins.retainAll(vins_second);
//        }
//
//        // 三级、四级分类筛选
//        CarClassifyThirdModel carClassifyThirdModel = carClassifyThirdModelMapper.selectByPrimaryKey(clasifythirdModelId);
//        Short thirdModel = carClassifyThirdModel.getThirdModel();
//        String thirdModelNextId = carClassifyThirdModel.getThirdModelNext();
//
//        switch (Constants.thirdModels.class.getEnumConstants()[thirdModel]){
//            case CITY:
//                req.data = this.getCarsByCity(thirdModelNextId,vins);
//                req.code = Constants.succeedCode;
//                break;
//            case PARK:
//                req.data = this.getCarsByPark(thirdModelNextId,vins);
//                req.code = Constants.succeedCode;
//                break;
//            case PURPOSE:
//                req.data = this.getCarsByPurpose(String.valueOf(thirdModel),vins);
//                req.code = Constants.succeedCode;
//                break;
//            case VNAME:
//                req.data = new ArrayList<>(Collections.singletonList(vehicleInfoMapper.selectByPrimaryKey(Long.valueOf(thirdModelNextId))));
//                req.code = Constants.succeedCode;
//                break;
//        }
//        return req;
//    }

    /***** 任务相关api *****/

    // 通过一级、二级、城市、园区、车辆编号筛选车辆
    @RequestMapping(path = {"/api/cmdb/release/cars"}, method = RequestMethod.POST)
    public ResData getReleaseCars(HttpServletRequest request, @RequestBody ReleaseCarsRequest body) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        String firstLevelType = body.getFirstLevelType();
        Long secondType = body.getSecondType();
        List<List<Object>> thirdLevelCarTypes = body.getThirdLevelCarTypes();
        List<String> vins = new ArrayList<>();
        // 一级、二级车型筛选
        if (firstLevelType!=null) {
            vins = carTypeService.fetchFirstTypeCars(firstLevelType);
        }
//        if ( secondType!=null ) {
//            List<String> vins_second = carTypeService.fetchSecondTypeCars(secondType);
//            if(vins.size()>0 && vins_second.size()>0) {
//                vins.retainAll(vins_second); // 取交集
//            }else if (vins.size()==0){
//                vins = vins_second;
//            }
//        }
        // 三级、四级分类筛选（城市、园区、运营组别、车辆名称）
        if (thirdLevelCarTypes !=null && thirdLevelCarTypes.size()>0){
            ArrayList<String> new_vins = new ArrayList<>();
            for (List<Object> son: thirdLevelCarTypes) {
                if(son.get(0) instanceof  Number) {
                    Short thirdModel = ((Number)son.get(0)).shortValue();
                    String thirdModelNextId = son.get(1).toString();
                    List<String> each_cars = new ArrayList<>();
                    switch (CommonConstants.thirdModels.class.getEnumConstants()[thirdModel]){
                        case CITY:
                            each_cars = this.getCarsByCity(thirdModelNextId, vins);
                            break;
                        case PARK:
                            each_cars = this.getCarsByPark(thirdModelNextId,vins);
                            break;
                        case PURPOSE:
                            each_cars = this.getCarsByPurpose(String.valueOf(thirdModel),vins);
                            break;
                        case VNAME:
                            each_cars = this.getCarsByVin(thirdModelNextId);
                            break;
                    }
                    new_vins.removeAll(each_cars);
                    new_vins.addAll(each_cars);
                }
            }
            vins = new_vins;
        }
        CarObjectsExample query = new CarObjectsExample();
        //配置，换到从eve取数
        CarObjectsExample.Criteria criteria = query.createCriteria();
        if (!vins.isEmpty()) {
            criteria.andVinIn(vins);
        }
        req.data = carObjectsMapper.selectByExample(query);
        req.code = CommonConstants.SUCCEED_CODE;
        return req;
    }

    // 通过城市获取车辆
    private List<String> getCarsByCity(String city,List<String> vins){
        // 获取城市名对应的园区

        BizPlaceInfoExample bizPlaceInfoExample = new BizPlaceInfoExample();

        bizPlaceInfoExample.createCriteria().andCityEqualTo(city);

        List<BizPlaceInfo> parksObjects = bizPlaceInfoMapper.selectByExample(bizPlaceInfoExample);
        List<String> parks = parksObjects.stream().map(BizPlaceInfo::getCode).collect(Collectors.toList());

        // 通过园区获得对应的车辆列表
        VehicleInfoExample vehicleInfoExample=new VehicleInfoExample();
        if (vins.size()>0){
            vehicleInfoExample.createCriteria().andParkIn(parks).andVinIn(vins);
        }else {
            vehicleInfoExample.createCriteria().andParkIn(parks);
        }
        return vehicleInfoMapper.selectByExample(vehicleInfoExample).stream().map(VehicleInfo::getVin).distinct().collect(Collectors.toList());
    }

    // 通过运营园区获取车辆
    private List<String> getCarsByPark(String park,List<String> vins){
        VehicleInfoExample parkSelect = new VehicleInfoExample();
        if (vins.size()>0){
            parkSelect.createCriteria().andParkEqualTo(park).andVinIn(vins);
        }else {
            parkSelect.createCriteria().andParkEqualTo(park);
        }
        return vehicleInfoMapper.selectByExample(parkSelect).stream().map(VehicleInfo::getVin).distinct().collect(Collectors.toList());
    }

    // 通过运营组别获取车辆
    private List<String> getCarsByPurpose(String purposeID, List<String> vins){
        Byte purposeID_new = Byte.valueOf(purposeID);
        VehicleInfoExample purposeSelect = new VehicleInfoExample();
        if (vins.size()>0){
            purposeSelect.createCriteria().andPurposeIdEqualTo(purposeID_new).andVinIn(vins);
        } else {
            purposeSelect.createCriteria().andPurposeIdEqualTo(purposeID_new);
        }
        return vehicleInfoMapper.selectByExample(purposeSelect).stream().map(VehicleInfo::getVin).distinct().collect(Collectors.toList());
    }

    // 通过vin获取车辆
    private List<String> getCarsByVin(String vin){
        VehicleInfoExample query = new VehicleInfoExample();
        query.createCriteria().andVinEqualTo(vin);
        return vehicleInfoMapper.selectByExample(query).stream().map(VehicleInfo::getVin).distinct().collect(Collectors.toList());
    }


    // 发起配置的申请，访问快搭流程api
//    public ResData sendProcess()

    // 将配置分发到车，配置release
    // 20221122-change:创建下发任务
    @RequestMapping(path = {"/api/cmdb/car/device/send_config"}, method = RequestMethod.POST)
    public ResData sendDeviceConfig(@RequestBody SendConfigReq body, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try {
            configService.sendConfigToCar(body.getConfig_id(), body.getDevice_id(), body.getCreate_user(), body.getTaskName(), body.getVins());
            req.msg = "success";
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            log.error("<创建配置任务>",e);
            return req;
        }
    }

    // excel 导入的配置，前端将Excel解析为json，传给后端，Excel格式共4个字段：vin 设备 配置项 配置的值
    @RequestMapping(path = {"/api/cmdb/car/device/send_config/excel"}, method = RequestMethod.POST)
    public ResData sendExcelDeviceConfig(@RequestBody List<SendExcelDeviceConfigReq> body, HttpServletRequest request) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;

        try {
            // 不创建导入任务
            // 直接在 car_device_config 配置中增加配置结果
            // 目前只有导入excel会把文件类型写入这个表
            res.data = configService.sendConfigToCar(body);
            res.msg = "success";
            res.code = CommonConstants.SUCCEED_CODE;
            return res;
        } catch (Exception e) {
            res.msg = e.getMessage();
            log.error("<属性下发>",e);
            return res;
        }
    }

    // 查询所有任务列表
    @RequestMapping(path = {"/api/cmdb/car/config/task"}, method = RequestMethod.GET)
    public ResData getTaskList(@RequestParam Integer page, @RequestParam Integer pageSize){
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try {
            CarConfigTaskExample query = new CarConfigTaskExample();
            long count = carConfigTaskMapper.countByExample(query);
            page -= 1;
            query.setOffset(pageSize * page);
            query.setRows(pageSize);
            query.setOrderByClause("id desc");
//            RowBounds rowBounds = new RowBounds(page, pageSize);
//            List<CarConfigTask> list = carConfigTaskMapper.selectByExampleWithBLOBsWithRowbounds(query, rowBounds);
            List<CarConfigTask> list = carConfigTaskMapper.selectByExampleWithBLOBs(query);
            ArrayList<HashMap> data = new ArrayList<>();
            for (CarConfigTask task: list ) {
                Long config_id = task.getConfigId();
                Long device_id = task.getDeviceId();
                String[] vins = task.getVins() != null ? task.getVins().split(",") : new String[]{};
                CarObjectsExample objectsExample = new CarObjectsExample();
                ArrayList<String> names = new ArrayList<>();
                if(vins.length>0) {
                    objectsExample.createCriteria().andVinIn(Arrays.asList(vins));
                    List<CarObjects> cars = carObjectsMapper.selectByExample(objectsExample);
                    for (CarObjects car : cars) {
                        names.add(car.getName()+",");
                    }
                }

                CarConfig config = carConfigMapper.selectByPrimaryKey(config_id);
//                RemoteDeviceType device = remoteDeviceTypeMapper.selectByPrimaryKey(device_id);
                List<CarDeviceConfig> car_device_config = carDeviceConfigMapper.selectByExample(new CarDeviceConfigExample() {{
                    createCriteria().andTaskIdEqualTo(task.getId());
                }});
                HashMap child = Object2Map.obj2map(task);
                child.put("config",config);
                child.put("device",null);
                child.put("cars",names);
                child.put("car_device_config",car_device_config);
                data.add(child);
            }
            req.rows = count;
            req.data = data;
            req.code = CommonConstants.SUCCEED_CODE;
        }catch (Exception e){
            log.error("<查询所有任务列表>",e);
        }
        return req;
    }


    /***** 具体车辆的配置相关api *****/

    // 车辆已配置信息查询，查询数据库中，与当前车辆关联的配置信息，来源于配置 release
    @RequestMapping(path = {"/api/cmdb/car/device/config"}, method = RequestMethod.GET)
    public ResData getCarDeviceConfig(@RequestParam Integer page, @RequestParam Integer pageSize,
                                      @RequestParam(required = false) String firstCarModel_id,
                                      @RequestParam(required = false) Long secondCarModel_id,
                                      @RequestParam(required = false) String city,
                                      @RequestParam(required = false) String park,
                                      @RequestParam(required = false) Byte purpose_id,
                                      @RequestParam(required = false) String name,
                                      @RequestParam(required = false) String configName,
                                      @RequestParam(required = false) String taskName,
                                      HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            VehicleInfoExample vehicleInfoExample=new VehicleInfoExample();
            VehicleInfoExample.Criteria vehicleSql = vehicleInfoExample.createCriteria();
            if (city!=null && !city.isEmpty()){
                // 获取城市名对应的园区
                BizPlaceInfoExample bizPlaceExample = new BizPlaceInfoExample();
                bizPlaceExample.createCriteria().andCityEqualTo(city);
                List<BizPlaceInfo> parksObjects = bizPlaceInfoMapper.selectByExample(bizPlaceExample);
                List<String> parks = parksObjects.stream().map(BizPlaceInfo::getCode).collect(Collectors.toList());
                // 通过园区获得对应的车辆列表
                vehicleSql.andParkIn(parks);
            }
            if (park!=null && !park.isEmpty()){
                vehicleSql.andParkEqualTo(park);
            }
            if (purpose_id!=null){
                vehicleSql.andPurposeIdEqualTo(purpose_id);
            }
            List<String> vins = new ArrayList<>();
            if (city!=null || park!=null || purpose_id!=null) {
                List<VehicleInfo> vehicles = vehicleInfoMapper.selectByExample(vehicleInfoExample);
                for (VehicleInfo vehicle: vehicles){
                    vins.add(vehicle.getVin());
                }
            }

            List<CarObjects> objects = new ArrayList<>(); // 所有筛选到的车辆列表
            CarObjectsExample remoteObjectsExample = new CarObjectsExample();
            CarObjectsExample.Criteria objectSql = remoteObjectsExample.createCriteria();
            if (firstCarModel_id!=null){
                objectSql.andCarTypeEqualTo(firstCarModel_id);

            }
//            if (secondCarModel_id!=null){
//                // 获取到所有tag对应的车辆id
//                RemoteObjectTagsExample objectTagsExample = new RemoteObjectTagsExample();
//                objectTagsExample.createCriteria().andTagIdEqualTo(secondCarModel_id);
//                List<RemoteObjectTags> objectsTag = remoteObjectTagsMapper.selectByExample(objectTagsExample);
//                ArrayList<Long> objectsID = new ArrayList<>();
//                for (RemoteObjectTags object:objectsTag){
//                    objectsID.add(object.getRemoteObjectId());
//                }
//                // 通过id获取到车辆，如果 objects 列表不是空，则取交集
//                objectSql.andIdIn(objectsID);
//            }
            if (vins.size()>0){
                objectSql.andVinIn(vins);
            }
            if(firstCarModel_id!=null || secondCarModel_id!=null || vins.size()>0) {
                objects = carObjectsMapper.selectByExample(remoteObjectsExample);
            }
            // 将vins重新赋值
            vins.clear();
            for (CarObjects object:objects){
                vins.add(object.getVin());
            }
//            CarDeviceConfigExample queryAll = new CarDeviceConfigExample();
//            if (vins.size()>0){
//                queryAll.createCriteria().andVinIn(vins);
//            }
            if (name != null && !name.isEmpty()){
                CarObjectsExample objquery = new CarObjectsExample();
                objquery.createCriteria().andNameEqualTo(name);
                objects = carObjectsMapper.selectByExample(objquery);
                if (objects.size()>0) {
                    vins.clear();
                    vins.add(objects.get(0).getVin());
                }
            }
            if ( taskName!=null && !taskName.isEmpty()){
                // 如果取到多条，就用最后一条
                List<CarConfigTask> tasks = carConfigTaskMapper.selectByExample(new CarConfigTaskExample() {{
                    createCriteria().andTaskNameEqualTo(taskName);
                    setOrderByClause("id DESC");
                }});
                if (tasks.size()>0){
                    CarConfigTask task = tasks.get(0);
                    task = carConfigTaskMapper.selectByPrimaryKey(task.getId());
                    String[] taskVins = task.getVins().split(",");
                    List<String> vinsTemp = new ArrayList<>();
                    if( vins.size()>0 & taskVins.length>0 ){ // 取交集
                        for(String vin: taskVins){
                            if(vins.contains(vin)){
                                vinsTemp.add(vin);
                            }
                        }
                        vins = vinsTemp;
                    } else if(vins.size()==0 & taskVins.length>0) { // 如果 vins 空，taskvins 不是空，则把 taskvins 给vins，其他情况不处理
                        vins = Arrays.asList(taskVins);
                    }
                }
            }

            HashMap<String,Object> res = new HashMap<>();
            List<CarDeviceConfigRes> data = new ArrayList<>();
            CarDeviceConfigExample query = new CarDeviceConfigExample();

            CarDeviceConfigExample.Criteria sql = query.createCriteria();
            if (vins.size()>0){
                sql.andVinIn(vins);
            }
            if (configName!=null && !configName.isEmpty()){
                sql.andNameEqualTo(configName);
            }
            long count = carDeviceConfigMapper.countByExample(query); // 总数量
            res.put("count",count);
            res.put("page",page);
            res.put("pageSize",pageSize);
            query.setOrderByClause("update_time desc");
            query.setOffset(page * pageSize);
            query.setRows(pageSize);
            List<CarDeviceConfig> carDeviceConfigs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
            // 组装车辆、配置、设备内容
            for (CarDeviceConfig carDeviceConfig: carDeviceConfigs) {
                CarDeviceConfigRes child = carDeviceConfigService.createRes(carDeviceConfig);
                data.add(child);
            }
            res.put("data",data);
            req.data = res;
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            log.error("<查询车辆、设备当前配置>",e);
            req.msg = e.getMessage();
            return req;
        }
    }

    @Resource
    CarDeviceConfigService carDeviceConfigService;

    // 查询配置历史
    @RequestMapping(path = {"/api/cmdb/car/device/config/history"},method = RequestMethod.GET)
    public ResData getHistoryConfig(@RequestParam String vin,@RequestParam(required = false) Long device_type_id,
                                    @RequestParam String name,HttpServletRequest request){
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            CarDeviceConfigExample query = new CarDeviceConfigExample();
            CarDeviceConfigExample.Criteria criteria = query.createCriteria().andVinEqualTo(vin)
                    .andNameEqualTo(name);
            if(device_type_id!=null){
                criteria.andDeviceTypeIdEqualTo(device_type_id);
            }
            List<CarDeviceConfig> cars = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
            res.data = null;
            if (cars.size() > 0) {
                CarDeviceConfig car = cars.get(0);
                if(car.getConfigIdHistory()!=null) {
                    String[] configHistoryIds = car.getConfigIdHistory().split(",");
                    List<Long> config_ids = new ArrayList<>();
                    for (String historyId : configHistoryIds) {
                        long config_id = Long.parseLong(historyId);
                        config_ids.add(config_id);
                    }
                    List<CarConfig> configs = new ArrayList<>();
                    for(long id:config_ids){
                        CarConfig tmp = carConfigMapper.selectByPrimaryKey(id);
                        if(tmp!=null)configs.add(tmp);
                    }
                    HashMap data = Object2Map.obj2map(car);
                    data.put("configs", configs);
                    res.data = data;
                }
            }
            res.code = CommonConstants.SUCCEED_CODE;

        }catch (Exception e){
            log.error("<获取配置历史>",e);
        }
        return res;

    }

    // 回滚 配置到上一个版本，回滚会创建一个回滚任务，任务的 config_id 为0
    // 任务历史，当前任务id，配置历史，当前配置id，历史配置id，上一次配置id
    @RequestMapping(path = {"/api/cmdb/car/device/config/rollback"}, method = RequestMethod.POST)
    public ResData rollbackConfig(@RequestBody RollbackReq body, HttpServletRequest request) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            configService.rollbackFun(body);
            res.code = CommonConstants.SUCCEED_CODE;
        }catch (Exception e){
            log.error(e);
        }
        return res;
    }

    // 对任务进行回滚
    @RequestMapping(path = {"/api/cmdb/car/device/config/task/rollback"}, method = RequestMethod.POST)
    public ResData rollbackTask(@RequestBody RollbackReq body,@RequestParam Long task_id, HttpServletRequest request){
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            CarDeviceConfigExample query = new CarDeviceConfigExample();
            query.createCriteria().andTaskIdEqualTo(task_id);
            List<Long> car_device_config_ids = carDeviceConfigMapper.selectByExample(query).stream().map(CarDeviceConfig::getId).collect(Collectors.toList());
            if(car_device_config_ids.size()>0) {
                body.setCar_device_config_ids((ArrayList<Long>) car_device_config_ids);
                configService.rollbackFun(body);
            }
            res.code = CommonConstants.SUCCEED_CODE;
        } catch (Exception e){
            log.error(e);
        }
        return res;
    }



    // 删除数据库中，与车辆关联的配置
    @RequestMapping(path = {"/api/cmdb/device/car/config/{config_id}"}, method = RequestMethod.DELETE)
    public ResData deleteCarConfig(@PathVariable Long config_id, HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;
        try {
            carDeviceConfigMapper.deleteByPrimaryKey(config_id);
            req.code = CommonConstants.SUCCEED_CODE;
        } catch (Exception e) {
            log.error("<删除车辆、设备当前配置>",e);
            req.msg = e.getMessage();
            return req;
        }
        return req;
    }


    
    // 上传资源到S3
    @RequestMapping(path = {"/eve/cmdb/rest/api/cmdb/device/car/config/s3"}, method = RequestMethod.POST)
    public ResData uploadDataS3(@RequestParam("image") MultipartFile image, HttpServletRequest request) throws IOException {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;

        // todo: 为车机设置对应的 objectname，
        byte[] input = image.getBytes();
        String objectName = "Vehicle_Update/s20_remote_resource/HVI_V1/" + image.getOriginalFilename();

        String bucketName = env.getProperty("s3-bucketName");

        PutObjectResult data = amazonS3.putObject(bucketName, objectName, new ByteArrayInputStream(input), null);
        res.code = 200;
        res.msg = "ok";
        res.data = objectName;
        return res;
    }

    @RequestMapping(path = {"/eve/cmdb/rest/api/cmdb/device/car/config/s3/get-image"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ResponseBody
    public ResponseEntity<byte[]> getImage(@RequestParam("objectName") String objectName) throws Exception {
        List<byte[]> imageBytesList = new ArrayList<>();

        try {

            String bucketName = env.getProperty("s3-bucketName");
            S3Object s3Object = amazonS3.getObject(bucketName, objectName);
            InputStream content = s3Object.getObjectContent();
            log.info("content:" + content);
            S3ObjectInputStream inputStream = s3Object.getObjectContent();
            byte[] imageBytes = IOUtils.toByteArray(inputStream);

            log.info("image bytes:" + imageBytes);
            imageBytesList.add(imageBytes);
            String[] namePath = objectName.split("/");
            String fileName = namePath[namePath.length-1]; // 这里设置你想要的文件名
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDisposition(ContentDisposition.builder("attachment").filename(fileName).build());

            return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_OCTET_STREAM).body(imageBytes);

        } catch (Exception e) {
            return null;
        }

    }

    
    // 发送配置到s3
    @RequestMapping(path = {"/eve/cmdb/rest/api/cmdb/device/car/config/s3/gender"}, method = RequestMethod.GET)
    public ResData uploadS3(@RequestParam String vin) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;
        try {
            CarObjectsExample query = new CarObjectsExample();
            query.createCriteria().andVinEqualTo(vin);
            List<CarObjects> carObjects = carObjectsMapper.selectByExample(query);
            if (carObjects.size() == 0) {
                res.msg = "未找到车辆";
                return res;
            }
            String name = carObjects.get(0).getName();
            configService.sendCarInfoConfigToS3(vin, name);
            res.code = CommonConstants.SUCCEED_CODE;
        } catch (Exception e) {
            res.msg = e.toString();
        }
        return res;
    }
}
