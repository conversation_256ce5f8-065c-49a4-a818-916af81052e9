package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.sankuai.walle.rmanage.config.service.MafkaRealtimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.Properties;

@Service
@Slf4j
public class MafkaRealtimeServiceImpl implements MafkaRealtimeService {

    @Resource(name = "realtimeMafkaProducter")
    private IProducerProcessor producer;

    @Override
    public void sendRealTime(Object obj) throws Exception {
        Base64.Encoder encoder = Base64.getEncoder();
        String value = encoder.encodeToString(JSONObject.toJSONBytes(obj));
        log.info("【发送到realtimeMafkaProducter mafka的消息】, {}",obj);
        ProducerResult result = producer.sendMessage(value);
    }

    @Override
    public void callTT() {

    }


}
