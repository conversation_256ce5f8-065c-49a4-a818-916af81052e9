package com.sankuai.walle.rmanage.config.util.MQTT;

import com.sankuai.walle.rmanage.config.config.MqttConfig;

import static com.sankuai.walle.rmanage.config.util.MQTT.MyMqttClient.sendMsg;

public class MymqttMain {


    public static void main(String[] args) throws Exception {

        try {
            sendMsg("hello world","LMTZSV023NC092853"+ MqttConfig.topicMark);
            Thread.sleep(100);
            sendMsg("hello 2","LMTZSV023NC092853"+MqttConfig.topicMark);
            System.out.println("finish");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
