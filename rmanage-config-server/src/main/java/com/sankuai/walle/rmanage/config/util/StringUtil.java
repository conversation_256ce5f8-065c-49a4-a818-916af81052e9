package com.sankuai.walle.rmanage.config.util;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

public class StringUtil {
    // 用于拼接列表中的字符串
    public static String joinStr(List<String> strings){
        String vins = null;
        int length = strings.size();
        if (length > 0) {
            int m = 0;
            vins = strings.get(m);
            while(vins==null && m< length-1){
                m++;
                vins = strings.get(m);
            }
            if (vins!=null && !vins.equals("")) {
                for (int i = m+1; i < length; i++) {
                    vins += ",";
                    vins += strings.get(i);
                }
            }
        }
        if (Objects.equals(vins, ""))vins=null;
        return vins;
    }

    public static String generateRandomString(final int BYTE_LENGTH) {
        SecureRandom random = new SecureRandom();
        byte[] randomBytes = new byte[BYTE_LENGTH];
        random.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes).substring(0, BYTE_LENGTH);
    }
}
