package com.sankuai.walle.rmanage.config.controller;


import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.meituan.uac.sdk.service.UacResourceRemoteService;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.ResData;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.atomic.AtomicLong;

import static com.sankuai.walle.objects.constants.UacConstant.tenantCode;


/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping
@Log4j
public class UserController {
    private static final String template = "Hello, %s!";
    private final AtomicLong counter = new AtomicLong();


    @Autowired
    UacAuthRemoteService uacAuthRemoteService;

    @Autowired
    UacResourceRemoteService uacResourceRemoteService;

    // getUserMenus
    @RequestMapping(path = {"/api/cmdb/getUserMenus"}, method = RequestMethod.GET)
    public ResData getUserMenus(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            User user = UserUtils.getUser();
            req.data = uacAuthRemoteService.getUserMenus(String.valueOf(user.getId()));
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            log.error("getuserMenu",e);
            return req;
        }
    }

    // 获取用户的前端模板
    @RequestMapping(path = {"/api/cmdb/getAuthFeResource"}, method = RequestMethod.GET)
    public ResData getUserFeResource(HttpServletRequest request, @RequestParam(required = true) String menuCode) {
        ResData res = new ResData();
        res.code = CommonConstants.ERROR_CODE;

        try {
            User user = UserUtils.getUser();
            res.data = uacResourceRemoteService.getAuthFeResource(tenantCode, String.valueOf(user.getId()), menuCode);
            res.code = CommonConstants.SUCCEED_CODE;
            return res;
        } catch (Exception e) {
            res.msg = e.getMessage();
            log.error("getuserMenu",e);
            return res;
        }
    }

    // 查看用户是否有当前页面访问权限
    @RequestMapping(path = {"/api/cmdb/checkUserPermission"}, method = RequestMethod.GET)
    public ResData checkUserPermission(HttpServletRequest request) {
        ResData req = new ResData();
        req.code = CommonConstants.ERROR_CODE;

        try {
            User user = UserUtils.getUser();
            req.data = uacAuthRemoteService.getUserMenus(String.valueOf(user.getId()));
            req.code = CommonConstants.SUCCEED_CODE;
            return req;
        } catch (Exception e) {
            req.msg = e.getMessage();
            log.error("getuserMenu",e);
            return req;
        }
    }



}
