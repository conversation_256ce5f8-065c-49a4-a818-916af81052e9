package com.sankuai.walle.rmanage.config.util.MQTT;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.nio.charset.StandardCharsets;

public class MyMqttStatusCallBack implements MqttCallback {
    //连接丢失时触发（不包括主动disconnect）
    @Override
    public void connectionLost(Throwable throwable) {
        System.out.println("连接失败，请重新连接");
    }
    //接收到内容触发
    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) throws Exception {
        String msg = new String(mqttMessage.getPayload(), StandardCharsets.UTF_8);
        System.out.println(topic);
        System.out.println(msg);
//        if (topic.endsWith("disconnected")){
//            System.out.println(topic);
//            System.out.println("客户下线:"+msg);
//        }else{
//            System.out.println(topic);
//            System.out.println("客户上线:"+msg);
//        }
//        try{
//            JSONObject jsonObject = JSON.parseObject(msg);
//            String clientID= String.valueOf(jsonObject.get("clientid"));
//
//        } catch (Exception e){
//            e.printStackTrace();
//        }
    }
    //发布完消息触发
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        System.out.println("发布消息成功");
    }

}
