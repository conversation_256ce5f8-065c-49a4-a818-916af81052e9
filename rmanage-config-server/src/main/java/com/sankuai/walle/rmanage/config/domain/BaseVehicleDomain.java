package com.sankuai.walle.rmanage.config.domain;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 车辆领域对象基类
 * 包含车辆相关的通用字段和基础方法
 */
@Data
public abstract class BaseVehicleDomain {
    
    /**
     * 车辆识别号
     */
    protected String vin;
    
    /**
     * 车辆对象
     */
    protected CarObjects carObjects;
    
    /**
     * 车辆资产
     */
    protected CarAssets carAssets;
    
    /**
     * 车辆扩展字段
     */
    protected CarExecWord carExecWord;
    
    /**
     * 创建时间
     */
    protected Date createTime;
    
    /**
     * 更新时间
     */
    protected Date updateTime;
    
    /**
     * 构造函数，初始化时间字段
     */
    protected BaseVehicleDomain() {
        this.createTime = new Date();
        this.updateTime = new Date();
    }
    
    /**
     * 准备车辆资产更新
     * 设置不允许更新的字段为null
     */
    public void prepareCarAssetsForUpdate() {
        if (this.carAssets != null) {
            this.carAssets.setLabel(null);
            this.carAssets.setSn(null);
        }
    }
    
    /**
     * 准备车辆资产插入
     * 设置VIN
     */
    public void prepareCarAssetsForInsert() {
        if (this.carAssets != null) {
            this.carAssets.setVin(this.vin);
        }
    }
    
    /**
     * 判断是否应该更新车辆资产
     * @return 是否应该更新
     */
    public boolean shouldUpdateCarAssets() {
        return this.carAssets != null && 
               this.carAssets.getId() != null && 
               this.carAssets.getVin() != null;
    }
} 