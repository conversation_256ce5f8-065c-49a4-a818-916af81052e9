package com.sankuai.walle.rmanage.config.aop;

import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.sankuai.walle.common.Status;
import com.sankuai.walle.rmanage.config.common.exception.BasicException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

import static com.sankuai.walle.rmanage.config.common.ErrorCode.SERVICE_INTERNAL_ERROR;
import com.fasterxml.jackson.databind.ObjectMapper;
/**
 * <AUTHOR> Created on 2021/11/29
 */
@Aspect
@Component
public class ThriftAspect {
    private static final Logger log = LoggerFactory.getLogger(ThriftAspect.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 定义切面，所有thrift包下public方法
     */
    @Pointcut("execution(public * com.sankuai.walle.rmanage.config.thrift..*.*(..))")
    public void thriftPointCut() {

    }

    @Around("thriftPointCut()")
    public Object handleThriftMethod(ProceedingJoinPoint pjp) {
        try {
            // 打印入参
            logInput(pjp);

            Object proceed = pjp.proceed();

            // 打印结果
            logOutput(pjp, proceed);

            return proceed;
        } catch (BasicException be) {
            log.error("ThriftAspect#handleThriftMethod BasicException. msg = {}",
                    be.getMessage(), be);
            // 异常上报
            Cat.logError(be);

            Object failureResp = buildFailureResp(pjp, be.getCode(), be.getMsg());
            logOutput(pjp, failureResp);

            return failureResp;
        } catch (Throwable e) {
            // 异常通知
            log.error(e.getMessage(), e);
            Cat.logError(e);

            Object failureResp = buildFailureResp(pjp, SERVICE_INTERNAL_ERROR.getCode(),
                    SERVICE_INTERNAL_ERROR.getDefaultMsg());
            logOutput(pjp, failureResp);

            return failureResp;
        }
    }

    private Object buildFailureResp(ProceedingJoinPoint pjp, int errorCode, String msg) {
        try {
            Class respClass = ((org.aspectj.lang.reflect.MethodSignature) (pjp.getSignature())).getReturnType();
            Object resp = respClass.newInstance();
            Field field = respClass.getDeclaredField("status");

            Status status = new Status();
            status.setCode(errorCode);
            status.setMsg(msg);

            if (field != null) {
                field.setAccessible(true);
                field.set(resp, status);
            }
            return resp;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

    private void logOutput(ProceedingJoinPoint pjp, Object proceed) {
        String className = pjp.getTarget().getClass().getName();
        String methodName = pjp.getSignature().getName();
        String argsString = buildArgsString(pjp.getArgs());
        String outputStr = "";
        try {
            // 尝试使用 Jackson 序列化
            outputStr = objectMapper.writeValueAsString(proceed);
        } catch (Exception e) {
            outputStr = String.valueOf(proceed);
        }

        log.info("aspect [({},{})]: {} {} input=[{}] output = {}",
                Tracer.id(), Cat.getCurrentMessageId(), className, methodName, argsString, outputStr);
    }

    private void logInput(ProceedingJoinPoint pjp) {
        String className = pjp.getTarget().getClass().getName();
        String methodName = pjp.getSignature().getName();
        String argsString = buildArgsString(pjp.getArgs());

        log.info("[({},{})]: {} {} input=[{}]", Tracer.id(), Cat.getCurrentMessageId(), className, methodName, argsString);
    }

    /**
     * 组装参数字符串
     *
     * @param args
     * @return
     */
    private String buildArgsString(Object[] args) {
        if (args == null || args.length == 0) {
            return "";
        }

        StringBuilder argsString = new StringBuilder();
        for (Object arg : args) {
            argsString.append(arg).append(" ");
        }
        if (argsString.length() > 0) {
            argsString.deleteCharAt(argsString.length() - 1);
        }
        return argsString.toString();
    }

}
