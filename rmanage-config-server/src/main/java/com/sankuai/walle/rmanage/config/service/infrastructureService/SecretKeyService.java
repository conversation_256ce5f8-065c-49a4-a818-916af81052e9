package com.sankuai.walle.rmanage.config.service.infrastructureService;

import java.util.List;
import java.util.Map;

public interface SecretKeyService {

    /*
    * 创建车辆密钥访问策略
    * */
    void createVisitStrategy(String vin) ;


    String fetchVisitStrategy(String vin);

    /*
    *
    *  创建车辆的鉴权信息 vin需要小写
    * */
    void createVehicleSSO(String vin) ;


    /*
    * 导入密钥
    * */
    void importSecretKey(String vin);


    /*
    * 导入车云密钥
    * */

    void importVehicleCloudSecret(String vin);

    /*
    * 批量查询vin 生成的密钥结果
    * */
    Map<String, Map<String, Object>> batchGetGenerateResult(List<String> vinList);

    /*
    * 获取vin 查询结果
    * */
    Map<String, Object> getGenerateResult(String vin);

}
