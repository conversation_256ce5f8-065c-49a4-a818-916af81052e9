package com.sankuai.walle.rmanage.config.config;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Properties;

@Configuration
@PropertySource(value = {"classpath:leaf.properties", "classpath:/META-INF/app.properties"})
@Slf4j
public class MafkaRealTimeProducterConfig {
    @Value("${mafka2.namespace}")
    String namespace;
    @Value("${mafka2.mafkaAK}")
    String mafkaAK;
    @Value("${mafka2.topic}")
    String topic;

    private static IProducerProcessor producer;

    @Bean(name = "realtimeMafkaProducter")
    public IProducerProcessor producter() throws Exception {
        // 车辆变动，消息生产者配置
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "waimai");
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.carosscan.realtimeinfo");

        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildProduceFactory(properties, "mad-vehicle.real.status.use");

        return producer;
    }
}

