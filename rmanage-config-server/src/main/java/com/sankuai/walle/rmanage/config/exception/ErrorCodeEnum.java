package com.sankuai.walle.rmanage.config.exception;

import lombok.Getter;

@Getter
public enum ErrorCodeEnum {
    SUCCESS(0, "成功"),
    LOGIN_ERROR(5, "登录失效"),
    AUTH_ERROR(12, "权限异常"),
    PARAM_ERROR(400, "参数错误"),
    BUSINESS_ERROR(1000, "业务错误"),
    BUSINESS_CRITICAL_ERROR(1500, "严重业务错误"),
    INTERNAL_ERROR(500, "内部错误"),
    EXTERNAL_ERROR(550, "网关错误"),
    ;

    private int code = 0;
    private String defaultMessage = "操作成功";

    ErrorCodeEnum() {
    }

    ErrorCodeEnum(int code) {
        this.code = code;
    }


    ErrorCodeEnum(int code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }

    public Integer getCode() {
        return code;
    }

    public String getDefaultMessage() {
        return defaultMessage;
    }
}