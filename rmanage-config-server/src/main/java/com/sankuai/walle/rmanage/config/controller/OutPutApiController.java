package com.sankuai.walle.rmanage.config.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.constant.BasicConfigurationConstant;
import com.sankuai.walle.rmanage.config.service.AccidentMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/eve/cmdb/rest/output")
@Slf4j
public class OutPutApiController {

    @Resource
    AccidentMessageService accidentMessageService;

    @PostMapping("/moxi/grade-dx-group")
    ResData GradeDXGroup(@RequestBody Map<String,Object> params){
        log.info("/moxi/grade-dx-group, GradeDXGroup,accidentId = {} ", params);
        ResData resData = new ResData();
        if(params == null || !params.containsKey("accidentId")){
            resData.setData("参数错误");
            return resData.failed();
        }
        String accidentId = String.valueOf(params.get("accidentId"));
        Transaction t = Cat.newTransaction(BasicConfigurationConstant.ACCIDENT_GROUP, "grade");
        long accidentIdLong = 0;

        try{
            accidentId = accidentId.trim();
            accidentIdLong = Long.parseLong(accidentId);
            long gid = accidentMessageService.gradeGroupScale(accidentIdLong);
            if(gid == 0){
               t.setStatus("error,gid = 0");
                resData.setData("升级群聊失败, gid = 0");
                return resData.failed();
            }
            t.setSuccessStatus();
            t.addData(String.valueOf(gid));
            resData.setData("升级群聊成功");
            return resData.success();

        }
        catch (Exception e){
            t.setStatus(e);
            Cat.logError(e);
            resData.setData("升级群聊失败");
            return resData.failed();
        }finally {
            t.complete();
        }
    }
}
