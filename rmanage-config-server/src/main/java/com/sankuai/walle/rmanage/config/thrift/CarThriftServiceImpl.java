package com.sankuai.walle.rmanage.config.thrift;


import com.dianping.cat.util.MetricHelper;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.walle.cmdb.thrift.model.*;
import com.sankuai.walle.cmdb.thrift.service.CarThriftService;
import com.sankuai.walle.common.Status;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.entity.CarFirstcartypeSecondcartype;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.example.CarFirstcartypeSecondcartypeExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarFirstcartypeSecondcartypeMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.dal.mrm_manage.example.RemoteCarTypeExample;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteCarTypeMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.helper.convertor.ObjectToThrift;
import com.sankuai.walle.rmanage.config.service.impl.CarTypeService;
import com.sankuai.walle.rmanage.config.service.impl.VehicleDeviceServiceImpl;
import lombok.extern.log4j.Log4j;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR> Created on 2022/9/15
 */
@MdpThriftServer(
        serviceInterface = CarThriftService.Iface.class,
        useSinglePortMultiServiceMode = true,
        port = 9001
)
@Log4j
public class CarThriftServiceImpl implements CarThriftService.Iface {


    @Resource
    TagsMapper tagsMapper;

    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;

    @Resource
    RemoteCarTypeMapper remoteCarTypeMapper;

/*  1 级车型 remote_car_type 4
    2 级车型 tags 4
    3 级车型
    逻辑：下发车辆的配置功能（ 配置release ）将配置分发到对应的车辆，车辆重启时，将配置拉取到车端
  */
    @Override
    public CarConfigResp getConfigByVin(String vin, String name) throws TException {
        CarConfigResp req = new CarConfigResp();
        Status status = new Status();
        status.code= CommonConstants.ERROR_CODE;
        req.status = status;
        req.name = "";
        req.config = "";

        try {
            CarDeviceConfigExample query = new CarDeviceConfigExample();
            query.createCriteria().andVinEqualTo(vin).andNameEqualTo(name);
            List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
            if (configs.size()>0) {
                CarDeviceConfig config = configs.get(0);
                req.name = config.getName();
                req.config = config.getConfig() != null ? config.getConfig() : "{}";
                req.status.code = CommonConstants.SUCCEED_CODE;
            }
            return req;
        } catch (Exception e) {
            req.status.msg = e.getMessage();
            return req;
        }
    }

    @Override
    public CarConfigAllResp getConfigsByVin(String vin) throws TException {
        CarConfigAllResp req = new CarConfigAllResp();
        Status status = new Status();
        status.code= CommonConstants.ERROR_CODE;
        req.status = status;
        req.data = new ArrayList<>();

        List<CarConfig> configs = new ArrayList<>();
        try {
//            String[] allowNames = new String[2] ;//{"QIANXUN","IPMI"};
            ArrayList<String> allowNames = new ArrayList<String>(){{
                add("QIANXUN");add("IPMI");
            }};

            CarDeviceConfigExample query = new CarDeviceConfigExample();
            query.createCriteria().andVinEqualTo(vin);
            List<CarDeviceConfig> deviceConfigs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
            for (CarDeviceConfig config: deviceConfigs) {
                if(allowNames.contains(config.getName())) {
                    String value = config.getConfig() != null ? config.getConfig() : "{}";
                    CarConfig child = new CarConfig();
                    child.setName(config.getName());
                    child.setConfig(value);
                    configs.add(child);
                }
            }
            // 一旦查询成功，便更新状态
//            for (CarDeviceConfig config: deviceConfigs){
//                config.setStatus(Constants.FINISH);
//                carDeviceConfigMapper.updateByPrimaryKeySelective(config);
//            }
            req.data = configs;
            req.status.code = CommonConstants.SUCCEED_CODE;
            // raptor上报空配置数据
            if (req.getData().size()<1 || req.getData().get(0).getConfig()==null || Objects.equals(req.getData().get(0).getConfig(), "{}")) {
                MetricHelper.build().name("wallcmdb.auk.config").tag("config", "empty").count(1);
                log.error("《根据vin获取配置："+vin+"》获取到空配置"+req.getData());
            } else {
                MetricHelper.build().name("wallcmdb.auk.config").tag("config", "good").count(1);
            }
            return req;
        } catch (Exception e) {
            req.status.msg = e.getMessage();
            log.error("《根据vin获取配置："+vin+"》",e);
            return req;
        }
    }

    @Resource
    CarFirstcartypeSecondcartypeMapper carFirstcartypeSecondcartypeMapper;

    @Resource
    VehicleDeviceServiceImpl carTagDeviceService;

    @Resource
    CarTypeService carTypeService;


    @Override
    public CarModelResp getCarType() throws TException {

        // 返回值初始化
        CarModelResp req = new CarModelResp();
        Status status = new Status();
        status.code= CommonConstants.ERROR_CODE;
        req.status = status;

        try {
            List<CarModel> res = new ArrayList<>();
            // 获取一级车型
            RemoteCarTypeExample select = new RemoteCarTypeExample();
            List<RemoteCarType> carTypes = remoteCarTypeMapper.selectByExample(select);
            for (RemoteCarType carType : carTypes) {
                Long firstCarTypeId = carType.getId();
                FirstCarType child = ObjectToThrift.carTypeToThrift(carType);
                CarModel carModel = new CarModel();
                carModel.setFirstCarType(child);
                // 获取关联的二级车型
                CarFirstcartypeSecondcartypeExample example = new CarFirstcartypeSecondcartypeExample();
                example.createCriteria().andFirstCarIdEqualTo(firstCarTypeId);
                List<CarFirstcartypeSecondcartype> relations = carFirstcartypeSecondcartypeMapper.selectByExample(example);
                List<SecondCarType> secondCarTypeList = new ArrayList<>();
                for (CarFirstcartypeSecondcartype relation:relations){
                    Long secondCarTypeId = relation.getSecondCarId();
                    Tags tag = tagsMapper.selectByPrimaryKey(secondCarTypeId);
                    SecondCarType secondCarType = ObjectToThrift.tagToSecondCarType(tag);
                    secondCarTypeList.add(secondCarType);
                }
                carModel.setSecondCarTypeList(secondCarTypeList);
                res.add(carModel);
            }

            req.status.code = CommonConstants.SUCCEED_CODE;
            req.data = res;
            MetricHelper.build().name("wallcmdb.RPC").tag("num", "getCarType").count(1);
            return req;
        } catch (Exception e) {
            req.status.msg = e.getMessage();
            log.error("《获取车型》",e);
            return req;
        }
    }

    @Override
    public CarDeviceResp getCarDevices(long tag_id,int level) throws TException {
        CarDeviceResp req = new CarDeviceResp();
        Status status = new Status();
        status.code= CommonConstants.ERROR_CODE;
        req.status = status;
        req.devices = new ArrayList<>();
        try{
            req.status.code = CommonConstants.SUCCEED_CODE;
            req.devices = carTagDeviceService.getDevice(tag_id,level);
            MetricHelper.build().name("wallcmdb.RPC").tag("num", "getCarDevices").count(1);
        } catch (Exception e) {
            req.status.msg = e.getMessage();
            log.error("《获取车型关联的硬件：" + tag_id + "》",e);
            return req;
        }
        return req;
    }

//    @Override
//    public VinsResp getTypeCars(long type_id, int level) throws TException {
//        VinsResp req = new VinsResp();
//        Status status = new Status();
//        status.code= CommonConstants.errorCode;
//        req.status = status;
//        try{
//            List<String> vins = new ArrayList<>();
//            if (level== CommonConstants.firstCarType){ //
////                vins = carTypeService.GetFirstTypeCars(type_id);
//            } else if (level== CommonConstants.secondCarType) {
//                vins = carTypeService.GetSecondTypeCars(type_id);
//            }
//            req.status.code = CommonConstants.succeedCode;
//            req.data = vins;
//            MetricHelper.build().name("wallcmdb.RPC").tag("num", "getTypeCars").count(1);
//        }catch (Exception e) {
//            req.status.msg = e.getMessage();
//            log.error("《获取车型关联的vins》",e);
//            return req;
//        }
//        return req;
//    }

}
