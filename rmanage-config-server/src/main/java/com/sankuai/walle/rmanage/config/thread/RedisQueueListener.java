package com.sankuai.walle.rmanage.config.thread;


import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.service.AccidentVideoService;
import com.sankuai.walle.rmanage.config.thread.dto.QueueParamDTO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedisQueueListener implements Runnable {

    private RedisQueueConsumer redisQueueConsumer;
    private RedisStoreClient redisStoreClient;
    private AccidentVideoService accidentVideoService;
    private DxGroupHandler dxGroupHandler;
    RedisThreadPool myRedisThreadPool;

    //参数传递
    public RedisQueueListener(RedisQueueConsumer redisQueueConsumer,
                              RedisStoreClient redisStoreClient,
                              AccidentVideoService accidentVideoService,
                              DxGroupHandler dxGroupHandler
    ) {
        this.redisQueueConsumer          =  redisQueueConsumer;
        this.redisStoreClient            =  redisStoreClient;
        this.accidentVideoService        =  accidentVideoService;
        this.dxGroupHandler              =  dxGroupHandler;
    }

    @Override
    public void run() {
        log.info("RedisQueueListener is starting : {}", redisQueueConsumer.getQueueName());
        while (RedisQueueConsumerContainer.isRun) {
            try {
                startThreadByKey("accident");
                startThread("collisionDetect");
            } catch (Exception e) {
                log.error("RedisQueueListener is failed",e);
            }
        }
    }

    void startThreadByKey(String key){
        StoreKey storeKey = new StoreKey(redisQueueConsumer.getQueueName(), key);
        QueueParamDTO object = redisStoreClient.lpop(storeKey);
        if (object != null && object.getVin() != null) {
            myRedisThreadPool = RedisThreadPool.getInstance();
            RedisAccidentConsumer consumerA = new RedisAccidentConsumer();
            myRedisThreadPool.executor(()->{
                consumerA.getMessage( object ,accidentVideoService, dxGroupHandler);
            });
        }
    }

    void startThread(String key){
        StoreKey storeKey = new StoreKey(redisQueueConsumer.getQueueName(), key);
        QueueParamDTO object = redisStoreClient.lpop(storeKey);
        if (object != null && object.getVin() != null) {
            myRedisThreadPool = RedisThreadPool.getInstance();
            RedisCollisionDetectConsumer consumerA = new RedisCollisionDetectConsumer();
            myRedisThreadPool.executor(()->{
                consumerA.getMessage( object ,accidentVideoService, dxGroupHandler);
            });
        }
    }
}
