package com.sankuai.walle.rmanage.config.service.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.walle.carManage.mapper.CarOperationMapper;
import com.sankuai.walle.objects.constants.OtaResidConstant;
import com.sankuai.walle.rmanage.config.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class RedisServiceImpl implements RedisService {

    @Autowired(required = false)
    @Qualifier("squirrel")
    RedisStoreClient redisStoreClient;

    @Resource(name = "ThreadRedisBean")
    RedisStoreClient redisClient;


    @Resource
    CarOperationMapper carOperationMapper;
    

    @Override
    public void pushCarInRedis(String k, Object v) {

        StoreKey key = new StoreKey(OtaResidConstant.category, k, OtaResidConstant.t);
//        if (carUsedTarget==null){
//            carUsedTarget="在库";
//        }
        redisStoreClient.set(key, v);
    }

    public void pushCarInRedisBytes(String k, byte[] v) {

        StoreKey key = new StoreKey(OtaResidConstant.category, k, OtaResidConstant.t);
//        if (carUsedTarget==null){
//            carUsedTarget="在库";
//        }
        redisStoreClient.setBytes(key, v);
    }

    @Override
    public void pushGroupIdInRedis(String category, Map<String, Object> param) {
        if(!param.containsKey("id") || !param.containsKey("groupId")){
            return;
        }
        long gid = (long)param.get("groupId");
        if(gid == 0){
            return;
        }
        StoreKey key = new StoreKey(category, param.get("id"));
        redisClient.set(key,gid);
    }

    @Override
    public long getGroupIdInRedis(String category, long id) {
        StoreKey key = new StoreKey(category, id);
        if (!redisClient.exists(key)) {
            return 0;
        }
        return redisClient.get(key);
    }

    @Override
    public byte[] queryValue(String k) {
        StoreKey key = new StoreKey(OtaResidConstant.category, k, OtaResidConstant.t);
        return redisStoreClient.getBytes(key);
    }
}
