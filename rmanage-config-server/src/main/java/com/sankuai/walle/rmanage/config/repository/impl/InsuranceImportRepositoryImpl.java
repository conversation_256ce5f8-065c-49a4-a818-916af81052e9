package com.sankuai.walle.rmanage.config.repository.impl;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarAssetsExample;
import com.sankuai.walle.carManage.example.CarExecWordExample;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarAssetsMapper;
import com.sankuai.walle.carManage.mapper.CarExecWordMapper;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.rmanage.config.repository.InsuranceImportRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 保险导入仓储实现类
 * 使用MyBatis mappers访问数据库
 */
@Repository
public class InsuranceImportRepositoryImpl implements InsuranceImportRepository {
    
    @Resource
    private CarObjectsMapper carObjectsMapper;
    
    @Resource
    private CarAssetsMapper carAssetsMapper;
    
    @Resource
    private CarExecWordMapper carExecWordMapper;
    
    @Override
    public List<CarObjects> findCarObjectsByVin(String vin) {
        CarObjectsExample example = new CarObjectsExample();
        example.createCriteria().andVinEqualTo(vin);
        return carObjectsMapper.selectByExample(example);
    }
    
    @Override
    public List<CarAssets> findCarAssetsByVin(String vin) {
        CarAssetsExample example = new CarAssetsExample();
        example.createCriteria().andVinEqualTo(vin);
        return carAssetsMapper.selectByExample(example);
    }
    
    @Override
    public List<CarExecWord> findCarExecWordByVin(String vin) {
        CarExecWordExample example = new CarExecWordExample();
        example.createCriteria().andVinEqualTo(vin);
        return carExecWordMapper.selectByExample(example);
    }
    
    @Override
    public void saveCarExecWord(CarExecWord carExecWord) {
        carExecWordMapper.insertSelective(carExecWord);
    }
    
    @Override
    public void updateCarExecWord(CarExecWord carExecWord) {
        carExecWordMapper.updateByPrimaryKeySelective(carExecWord);
    }
    
    @Override
    public void updateCarObjects(CarObjects carObjects) {
        carObjectsMapper.updateByPrimaryKeySelective(carObjects);
    }
} 