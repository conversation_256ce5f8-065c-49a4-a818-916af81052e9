package com.sankuai.walle.rmanage.config.helper.convertor;

import com.sankuai.walle.cmdb.thrift.model.Device;
import com.sankuai.walle.cmdb.thrift.model.FirstCarType;
import com.sankuai.walle.cmdb.thrift.model.SecondCarType;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteCarType;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteDeviceType;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;

public class ObjectToThrift {
    public static SecondCarType tagToSecondCarType(Tags tag){
        SecondCarType secondCarType = new SecondCarType();
        if (tag.getId()!=null) {
            secondCarType.setId(tag.getId());
        }
        if (tag.getName()!=null) {
            secondCarType.setName(tag.getName());
        }
        if(tag.getColor()!=null) {
            secondCarType.setColor(tag.getColor());
        }
        if(tag.getTagType()!=null) {
            secondCarType.setTagType(tag.getTagType());
        }
        return secondCarType;
    }

    public static Device remoteDeviceToThfirtDevice(RemoteDeviceType remoteDevice){
        Device device = new Device();
        if (remoteDevice!=null) {
            device.setId(remoteDevice.getId());
            if(remoteDevice.getTypeName()!=null) {
                device.setTypeName(remoteDevice.getTypeName());
            }
            if(remoteDevice.getFriendName()!=null) {
                device.setFriendName(remoteDevice.getFriendName());
            }
            if(remoteDevice.getConfContent()!=null) {
                device.setConfContent(remoteDevice.getConfContent());
            }
            if(remoteDevice.getLatestVersionId()!=null) {
                device.setLatestVersionId(remoteDevice.getLatestVersionId());
            }
        }
        return device;
    }

    public static FirstCarType carTypeToThrift(RemoteCarType carType){
        FirstCarType child = new FirstCarType();
        child.setId(carType.getId());
        child.setTypeName(carType.getTypeName());
        child.setFriendName(carType.getFriendName());
        return child;
    }
}
