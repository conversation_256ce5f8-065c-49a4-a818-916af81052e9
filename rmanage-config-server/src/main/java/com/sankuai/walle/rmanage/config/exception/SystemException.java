package com.sankuai.walle.rmanage.config.exception;

import lombok.Data;

/**
 * Created by xiafei03 on 2022/5/25.
 */
@Data
public class SystemException extends RuntimeException {

    private int code;

    public SystemException(int code, String showMessage) {
        super(showMessage);
        this.code = code;
    }

    public SystemException(int code, String showMessage, Throwable cause) {
        super(showMessage, cause);
        this.code = code;
    }

    public SystemException(ErrorCodeEnum code) {
        super(code.getDefaultMessage());
        this.code = code.getCode();
    }

    public SystemException(ErrorCodeEnum code, String showMessage) {
        super(showMessage);
        this.code = code.getCode();
    }

    public SystemException(ErrorCodeEnum code, String showMessage, Throwable cause) {
        super(showMessage, cause);
        this.code = code.getCode();
    }

    public SystemException(ErrorCodeEnum code, Throwable cause) {
        super(code.getDefaultMessage(), cause);
        this.code = code.getCode();
    }

    public int getCode() {
        return code;
    }
}
