package com.sankuai.walle.rmanage.config.service.impl;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.sankuai.walle.rmanage.config.service.appService.ConfigStoreService;
import com.sankuai.walle.rmanage.config.service.infrastructureService.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@Slf4j
@Service
public class ConfigStoreServiceImpl implements ConfigStoreService {

    @Resource
    S3Service s3Service;
    @Value("${config-bucketName}")
    private String bucketName;

    @Override
    public ObjectMetadata getMetaData(String objectName){
        return s3Service.getMetaData(bucketName, objectName);
    };
    @Override
    public List<S3ObjectSummary> listObjects(String objectName){
        return s3Service.listObjects(bucketName, objectName);
    }

    @Override
    public void writeFileToS3(String objectName, String content){
        s3Service.writeFileToS3(bucketName, objectName, content);
    }

    @Override
    public void deleteFileFromS3(String objectName) {
        s3Service.deleteObjectExample(bucketName, objectName);
    }

    @Override
    public void updateVersion() throws IOException {
        String objectName = "config/vehicle/version";
        String version = s3Service.getObjectAsString(bucketName, objectName);
        // 实现1.0.0加一
        version = addOne(version);
        s3Service.writeFileToS3(bucketName, objectName, version);
    }
    @Override
    public void updateVersion(String carName) throws IOException {
        // 不更新version，而是删除对应的zip文件
        String zipFileName = "config_"+carName+".zip";
        s3Service.deleteObjectExample(bucketName, zipFileName);
    }


    // 获取S3对应文件的内容
    @Override
    public String getFileContent(String objectName) throws IOException {
        return s3Service.getObjectAsString(bucketName, objectName);
    }

    @Override
    public String getObjectAsString(String objectName) throws IOException {
        return s3Service.getObjectAsString(bucketName, objectName);
    }

    public static String addOne(String version) {
        String[] parts = version.split("\\.");
        int index = parts.length-1;
        int lastPart = Integer.parseInt(parts[index]);
        lastPart++;
//        if (lastPart>=10){
//            lastPart = lastPart%10;
//            secondPart++;
//            if (secondPart>=10){
//                secondPart = secondPart%10;
//                firstPart++;
//            }
//        }
        parts[index] = String.valueOf(lastPart);
        return String.join(".", parts);
    }

    public static void main(String[] args) {
        String version = "1.0.100.200";
        System.out.println(addOne(version));
    }
}
