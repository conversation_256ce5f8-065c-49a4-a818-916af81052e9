package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.dianping.cat.util.MetricHelper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.protobuf.util.JsonFormat;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.banma.auk.server.sdk.callback.AukCallback;
import com.sankuai.cloud.mos.iam.access.api.util.CommonCode;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarConfigQuery;
import com.sankuai.walle.dal.classify.entity.CarConfigTask;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarConfigExample;
import com.sankuai.walle.dal.classify.example.CarConfigQueryExample;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarConfigQueryMapper;
import com.sankuai.walle.dal.classify.mapper.CarConfigTaskMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.eve.entity.CmdbActionLog;
import com.sankuai.walle.dal.eve.entity.ConfigPermission;
import com.sankuai.walle.dal.eve.example.ConfigPermissionExample;
import com.sankuai.walle.dal.eve.mapper.CmdbActionLogMapper;
import com.sankuai.walle.dal.eve.mapper.ConfigPermissionMapper;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.objects.vo.request.RollbackReq;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.constant.ConfigEnumConstant;
import com.sankuai.walle.rmanage.config.dto.config.ConfigPermissionDTO;
import com.sankuai.walle.rmanage.config.geteway.MyAukServiceImpl;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.service.appService.ConfigStoreService;
import com.sankuai.walle.rmanage.config.service.infrastructureService.LexicalAnalyzerService;
import com.sankuai.walle.rmanage.config.util.ObjectUtil;
import com.sankuai.walle.rmanage.config.util.StringUtil;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ConfigServiceImpl implements ConfigService {

//
//    @Override
//    public String getConfigContent(String input, String vin) {
//        List<RemoteObjects> carObjList = remoteObjectsMapper.selectByExample(new RemoteObjectsExample() {{
//            createCriteria().andVinEqualTo(vin);
//        }});
//        if (carObjList.size()>0) {
//            RemoteObjects carObject = carObjList.get(0);
//            StringBuilder res = new StringBuilder();
//            ConfigTokenAnalyze tokenAnalyze = new ConfigTokenAnalyze(input);
//            List<ConfigTokenAnalyze.Token> tokens = tokenAnalyze.getTokens();
//            log.info("<变量转换后的tokens：{}>", tokens);
//            for (ConfigTokenAnalyze.Token token : tokens) {
//                if (Objects.equals(token.getType(), ConfigTokenAnalyze.myTokenType.VARIABLE)) {
//                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.NAME)) {
//                        res.append(carObject.getName());
//                    }
//                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.VIN)) {
//                        res.append(vin);
//                    }
//                    if (Objects.equals(token.getCount(), ConfigTokenAnalyze.ConfigVariable.LicenseNo)) {
//                        res.append(carObject.getLicenseNo());
//                    }
//                } else {
//                    res.append(token.getCount());
//                }
//            }
//            log.info("<变量转换后的数据：{}>", res);
//            return res.toString();
//        } else {
//            log.info("<变量转换时，vin不存在：{}>", vin);
//            return input;
//        }
//    }

    @Resource
    CarConfigMapper carConfigMapper;
    @Resource
    CarConfigTaskMapper carConfigTaskMapper;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    CarConfigQueryMapper carConfigQueryMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Autowired
    MyAukServiceImpl myAukClient;
//    @Resource
//    RemoteDeviceTypeMapper remoteDeviceTypeMapper;
    @Resource
    CmdbActionLogMapper cmdbActionLogMapper;

    @Resource
    LexicalAnalyzerService lexicalAnalyzerService;
    @Resource
    ConfigStoreService  configStoreService;
    @Resource
    ConfigPermissionMapper configPermissionMapper;
    @Resource(name = "sqlSessionFactory0")
    SqlSessionFactory sqlSessionFactory;
    // autocar 配置的模板
    @MdpConfig
    String autocarConfigTemplete = "vin: \"`${#vin}`\" \n" +
            "name: \"`${#name}`\" \n" +
            "type: \"`${#size}`\" \n" +
            "vendor: \"`${#vendor}`\" \n" +
            "batch: \"`${#type}`-`${#phase}`\"";

    // 车辆开关配置，新增开关的时候修改配置即可
    @MdpConfig("vehicle_switch_controles")
    ObjectNode vehicleSwitchControl;
    // 二进制配置项的默认值
    @MdpConfig("control.conf.default")
    ObjectNode controlDefaultConfig;
    // 配置项对应的内容是否要按 json、二进制合并
    @MdpConfig("config.generate.rules")
    HashMap<String, ArrayList<String>> configRules;
    @Autowired
    ConfigEnumConstant configEnumConstant;// 配置项迁移到lion

    @Override
    public ResData configQueryList(int page, int pageSize, List<String> vins, String configName){
        CarConfigQueryExample query = new CarConfigQueryExample();
        query.setOffset((page-1)*pageSize);
        query.setRows(pageSize);
        CarConfigQueryExample.Criteria criteria = query.createCriteria();
        if (vins!=null && !vins.isEmpty()){
            criteria.andVinIn(vins);
        }
        if (configName!=null){
            criteria.andNameEqualTo(configName);
        }
        List<CarConfigQuery> list = carConfigQueryMapper.selectByExampleWithBLOBs(query);
        // 将当前list转换出一个map的list
        List<Map> resList = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        for (CarConfigQuery car: list) {
            Map<String, Object> map = objectMapper.convertValue(car, Map.class);
            resList.add(map);
        }
        // 查询vin对应的name
        List<String> listVins = list.stream().map(CarConfigQuery::getVin).collect(Collectors.toList());
        List<CarObjects> cars = carObjectsMapper.selectByExample(new CarObjectsExample() {{
            createCriteria().andVinIn(listVins);
        }});
        Map<String, String> vinNameMap = new HashMap<>();
        for (CarObjects car : cars) {
            vinNameMap.put(car.getVin(), car.getName());
        }
        // 把name装入map
        for (Map map: resList ) {
            String vin = String.valueOf(map.get("vin"));
            if(!Strings.isBlank(vin))map.put("objName", vinNameMap.get(vin));
        }
        long pages = carConfigQueryMapper.countByExample(query);
        ResData resData = new ResData();
        resData.setData(resList);
        resData.setRows(pages);
        resData.setCode(CommonCode.OK.getCode());
        return resData;
    }

    @Override
    public List<CarDeviceConfig> sendConfigToCar(Long config_id, Long device_id, String userMis,
                                           String taskName, List<String> vinsList
                                   ) {
        List<CarDeviceConfig> res = new ArrayList<>();
        try {
            CarConfig config = carConfigMapper.selectByPrimaryKey(config_id);
            Date now = new Date();
            // 创建任务
            String vins = StringUtil.joinStr(vinsList);
            CarConfigTask newTask = new CarConfigTask() {{
                setConfigId(config_id);
//                setDeviceId(device_id);
                setAddTime(now);
                setCreateUser(userMis);
                setTaskName(taskName);
                if (vins != null) setVins(vins);
            }};
            carConfigTaskMapper.insert(newTask);
//            vinsList去重
            vinsList = vinsList.stream().distinct().collect(Collectors.toList());
            // 修改车辆配置
            for (String vin : vinsList) {
                CarDeviceConfigExample query = new CarDeviceConfigExample();
                //                query.createCriteria().andVinEqualTo(vin).andNameEqualTo(config.getName()); // 每个车的每个配置项，一条记录
                query.createCriteria().andVinEqualTo(vin).andNameEqualTo(config.getName());
//                if(device_id!=null){
//                    criteria.andDeviceTypeIdEqualTo(device_id);
//                }
                List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
                // 配置内容
                String configCountent = lexicalAnalyzerService.getConfigContent(config.getConfig(), vin);
                if (configs.size() > 0) { // 更新
                    CarDeviceConfig target = configs.get(0);
                    Long last_config_id = target.getConfigId(); //上次配置的config_id
                    target.setName(config.getName());
                    target.setConfig(configCountent); // done: 增加变量，将变量替换为值 `$(#attr)`
//                    target.setDeviceTypeId(device_id);
                    target.setUpdateTime(now);
                    target.setUpdateUser(userMis);
                    target.setTaskId(newTask.getId());
                    target.setConfigId(config_id); // 这次配置的config_id
                    target.setConfigIdLast(last_config_id);
                    target.setStatus(ConfigConstant.SEND);
                    ObjectUtil.ObjectFun<CarDeviceConfig> objfun = new ObjectUtil.ObjectFun<>();
                    objfun.safeSetValueFun(target, "taskHistoryId", newTask.getId());
                    objfun.safeSetValueFun(target, "configIdHistory", config_id);
                    carDeviceConfigMapper.updateByPrimaryKeySelective(target);
                    carDeviceConfigService.configToMqtt(target);
                    res.add(target);
                } else { // 新增
                    CarDeviceConfig target = new CarDeviceConfig() {{
                        setName(config.getName());
                        setConfig(configCountent);
                        setVin(vin);
//                        setDeviceTypeId(device_id);
                        setCreateUser(userMis);
                        setAddTime(now);
                        setUpdateTime(now);
                        setTaskId(newTask.getId());
                        setTaskHistoryId(String.valueOf(newTask.getId()));
                        setConfigId(config_id); // 这次配置的config_id，没有上一次的配置，不设置ConfigIdLast
                        setConfigIdHistory(String.valueOf(config_id));
                        setStatus(ConfigConstant.SEND);
                    }};
                    this.insertCarDeviceConfig(target);
                    carDeviceConfigService.configToMqtt(target);
                    res.add(target);
                }
            }
            return res;
        }catch (Exception e){
            log.error("<创建配置任务>",e);
            throw new RuntimeException(e);

        }
    }

    @Override
    public List<CarDeviceConfig> sendConfigToCar(List<SendExcelDeviceConfigReq> body) {
        List<CarDeviceConfig> res = new ArrayList<>();
        for(SendExcelDeviceConfigReq req: body){
            Date now = new Date();
            String vin = req.getVin();
            String attributeName = req.getConfigName();
            String attributeCount = req.getContent();
            String fileType = req.getFileType();

            // 判断导入的值是否合理
            if (attributeCount == null || attributeCount.isEmpty()) {
                log.error("Request content is null, vin:{}, attributeName:{}, fileType:{}",
                        vin, attributeName, fileType);
                continue;
            }
            if (!configEnumConstant.contains(attributeName)) {
                log.error("Request configName is invalid, vin:{}, attributeName:{}, fileType:{}",
                        vin, attributeName, fileType);
                continue;
            }
            //判断属于哪种类型的配置
            boolean isJSONConfig = false;
            boolean isBinConfig = false;
            for (HashMap.Entry<String, ArrayList<String>> entry : configRules.entrySet()) {
                if (entry.getValue().contains(attributeName)) {
                    switch (entry.getKey().toUpperCase()) {
                        case "JSON":
                            isJSONConfig = true;
                            break;
                        case "BINARY":
                            isBinConfig = true;
                            break;
                    }
                }
            }

            // 修改车辆属性
            CarDeviceConfigExample query = new CarDeviceConfigExample();
            // 每个车的每个配置项，一条记录
            query.createCriteria().andVinEqualTo(vin).andNameEqualTo(attributeName);
            List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);

            if (configs.size() > 0) { // 更新
                CarDeviceConfig target = configs.get(0);
                target.setName( attributeName );
                try {
                    if (isJSONConfig) {
                        target.setConfig(generateJsonConfig(attributeCount, target.getConfig()));
                    } else if (isBinConfig) {
                        target.setConfig(generateBinConfig(attributeCount, target.getConfig(),
                                vehicleSwitchControl, controlDefaultConfig));
                    } else {
                        target.setConfig(attributeCount);
                    }
                }
                catch (Exception e){
                    log.error("update error when buildConfigResult", e);
                    log.error("Input config content error, vin:{}, attributeName:{}, fileType:{}",
                            vin, attributeName, fileType);
                    continue;
                }
//                target.setDeviceTypeId( deviceId );
                target.setUpdateTime( now );
                target.setUpdateUser( req.getUser() );
                target.setStatus( ConfigConstant.SEND );
                target.setConfigId(ConfigConstant.EXCEL_CONFIG_ID);
                target.setFileType(fileType);
                carDeviceConfigMapper.updateByPrimaryKeySelective(target);
                carDeviceConfigService.configToMqtt(target, fileType);
                res.add(target);
            } else { // 新增
                String addConfig;
                try {
                    if (isJSONConfig) {
                        addConfig = generateJsonConfig(attributeCount, null);
                    } else if (isBinConfig) {
                        addConfig = generateBinConfig(attributeCount, null,
                                vehicleSwitchControl, controlDefaultConfig);
                    } else {
                        addConfig = attributeCount;
                    }
                } catch (Exception e) {
                    log.error("add error when buildConfigResult", e);
                    log.error("Input config content error, vin:{}, attributeName:{}, fileType:{}",
                            vin, attributeName, fileType);
                    continue;
                }
                CarDeviceConfig target = new CarDeviceConfig() {{
                    setName( attributeName );
                    setConfig( addConfig );
                    setVin(vin);
//                    setDeviceTypeId(finalDeviceId);
                    setCreateUser( req.getUser() );
                    setUpdateUser(req.getUser());
                    setAddTime(now);
                    setUpdateTime(now);
                    setStatus(ConfigConstant.SEND);
                }};
                target.setFileType(fileType);
                target.setConfigId(ConfigConstant.EXCEL_CONFIG_ID);
                this.insertCarDeviceConfig(target);
                carDeviceConfigService.configToMqtt(target, fileType);
                res.add(target);
            }

        }
        return res;
    }

    private void insertCarDeviceConfig(CarDeviceConfig target){
        try (SqlSession session = sqlSessionFactory.openSession()) {
            carDeviceConfigMapper.insert(target);
            session.commit();
        }catch (Exception e){
            log.error("<新建配置任务失败: {}>",target,e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public void rollbackFun(RollbackReq body) {
        try {
            // 1 创建回滚任务
            Date date = new Date();
            String vins = null;
            vins = StringUtil.joinStr(body.getVins());
            String finalVins = vins;
            CarConfigTask task = new CarConfigTask() {{
                setTaskName(body.getTaskName());
                setConfigId(ConfigConstant.ROLL_BACK_TASK);
                if (finalVins != null) setVins(finalVins);
                setCreateUser(body.getCreateUser());
                setAddTime(date);
                setDeviceId(body.getDeviceId());
            }};
            // 创建了任务也不一定会下发，因为不满足回滚条件：
            // 回滚需要满足，有当前和上一次的配置，且当前配置与上次配置不同
            carConfigTaskMapper.insert(task);
            // 2 对选择的车辆进行回滚
            // 1) 有上一次的配置 2）没有上一次的配置 3）当前配置和上一次配置相同。
            for (Long car_device_config_id : body.getCar_device_config_ids()) {
                CarDeviceConfig target = carDeviceConfigMapper.selectByPrimaryKey(car_device_config_id);
                if (target.getConfigId() != null && target.getConfigIdLast() != null && !Objects.equals(target.getConfigId(), target.getConfigIdLast())) {
                    Long lastId = target.getConfigIdLast();
                    CarConfig lastConfig = carConfigMapper.selectByPrimaryKey(lastId);
                    // 配置内容
                    String configCountent = lexicalAnalyzerService.getConfigContent(lastConfig.getConfig(), target.getVin());
                    target.setConfigId(lastId);
                    target.setConfig(configCountent);
                    target.setTaskId(task.getId());
                    target.setStatus(ConfigConstant.SEND);
                    ObjectUtil.ObjectFun<CarDeviceConfig> objfun = new ObjectUtil.ObjectFun<>();
                    objfun.safeSetValueFun(target, "configIdHistory", lastId);
                    objfun.safeSetValueFun(target, "taskHistoryId", task.getId());
                    carDeviceConfigMapper.updateByPrimaryKeySelective(target);
                    carDeviceConfigService.configToMqtt(target);
//                if(obj.getConfigIdHistory()!=null) {
//                    obj.setConfigIdHistory(obj.getConfigIdHistory() + "," + lastId);
//                } else {
//                    obj.setConfigIdHistory( String.valueOf(lastId) );
//                }
//                if (obj.getTaskHistoryId()!=null) {
//                    obj.setTaskHistoryId(obj.getTaskHistoryId() + "," + task.getId());
//                } else {
//                    obj.setTaskHistoryId( String.valueOf(task.getId()) );
//                }
                }
            }
        }catch (Exception e){
            log.error("config rollbackFun error: ",e);
        }
    }

    @Override
    @Async
    public void queryConfigAsync(List<String> vins){
        // 发起回捞配置的请求
        List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExample(new CarDeviceConfigExample() {{
            createCriteria().andVinIn(vins).andStatusEqualTo(ConfigConstant.FINISH);
        }});
        log.info("将要查询的车辆,{}",vins);

        CloudCgf.CloudToVehicleQueryConfig.Builder builder = CloudCgf.CloudToVehicleQueryConfig.newBuilder();

        for(int i=0; i< configs.size(); i++) {
            CarDeviceConfig config = configs.get(i);
            String vin = config.getVin();
            builder.setSVin(vin);
            CarConfig carConfig = carConfigMapper.selectByPrimaryKey(config.getConfigId());

            if(carConfig!=null && carConfig.getFileType()!=null){
                builder.setSFileName( config.getName()+"."+carConfig.getFileType() );
                builder.setSConfigName( config.getName() );
            }else{
                builder.setSFileName( config.getName()+"."+config.getFileType() );
                builder.setSConfigName( config.getName() );
            }
            // 主动下发查询请求，查询哪个车的哪些配置
            myAukClient.queryFromMqtt(vin, builder.build().toByteArray(), new AukCallback() {
                @Override
                public void onSuccess(Object o) {
                    log.info("SUCCESS:下发回捞请求成功，{},配置项：{}, 配置内容：{}",vin,config.getName(),builder);
                }

                @Override
                public void onFailure(Object o, Throwable throwable) {
                    log.error("ERROR:下发回捞请求失败，{},配置项：{}",vin,config.getName());
                }
            });
        }
    }

    @Override
    public HashMap<String,String> queryConfigSync(List<String> vins,String configName) throws Exception {
        // 发起回捞配置的请求
        List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExample(new CarDeviceConfigExample() {{
            createCriteria().andVinIn(vins).andNameEqualTo(configName).andStatusEqualTo(ConfigConstant.FINISH);
        }});
        log.info("将要查询的车辆,{},配置项：{}",vins, configName);
        List<Long> lConfigIds = configs.stream().map(CarDeviceConfig::getConfigId).collect(Collectors.toList());
        List<CarConfig> carConfigs = lConfigIds.size()>0 ? carConfigMapper.selectByExample(new CarConfigExample() {{
            createCriteria().andIdIn(lConfigIds);
        }}) : new ArrayList<>();
        Map<String, CarConfig> carConfigMap = carConfigs.stream().collect(Collectors.toMap(CarConfig::getName, Function.identity()));
        HashMap<String,String> res = new HashMap<>();
        for (CarDeviceConfig config : configs) {
            CarConfig carConfig = carConfigMap.get(config.getName());
            String vin = config.getVin();
            CloudCgf.CloudToVehicleQueryConfig.Builder builder = CloudCgf.CloudToVehicleQueryConfig.newBuilder();
            builder.setSVin(vin);

            if(carConfig!=null && carConfig.getFileType()!=null){
                builder.setSFileName( config.getName()+"."+carConfig.getFileType() );
                builder.setSConfigName( config.getName() );
            }else{
                builder.setSFileName( config.getName()+"."+config.getFileType() );
                builder.setSConfigName( config.getName() );
            }
            // 主动下发查询请求，查询哪个车的哪些配置
            byte[] rep = myAukClient.queryFromMqttSync(vin, builder.build().toByteArray());
            if(rep==null){
                log.error("ERROR:下发回捞请求为空，{},配置项：{}",vin,config.getName());
                continue;
            }
            CloudCgf.VehicleToCloudQueryConfigRsp countent = CloudCgf.VehicleToCloudQueryConfigRsp.parseFrom(rep);
            String bConf = countent.getBConf().toStringUtf8();
            JSONObject bConfigJson = JSON.parseObject(bConf);
            log.info("回捞结果解析：{}",countent);
            String json = JsonFormat.printer().print(countent);
            JSONObject jsonObject = JSON.parseObject(json);
            jsonObject.put("bConf",bConfigJson);
            res.put(vin, jsonObject.toJSONString());
        }
        return res;
    }

    @Override
    public void sendCarInfoConfigToS3(String vin, String carName) throws IOException {
        // 判断是否在车型白名单中，如果不在则直接返回

        carName = carName.replace(" ", "");
        carName = carName.toLowerCase(Locale.ROOT);
        String fileName = "config/vehicle/"+carName+"/vehicle_info.pb.txt";
        String configTemplate = this.autocarConfigTemplete;
        // 为S3生成v-info配置文件
        try {
            String data = lexicalAnalyzerService.getConfigContent(configTemplate, vin);
            if (Strings.isEmpty(data)){
                log.info("sendConfigToS3 info: {} data 为空",vin);
                MetricHelper.build().name("wallcmdb.cmdb.config.s3").tag("status", "bad").count(1);
                return;
            }
            // 配置是否已经存在，且内容一致，如果是，则跳过写入
            String fileContent = configStoreService.getFileContent(fileName);
            if (StringUtils.equals(fileContent, data)){
                log.info("sendConfigToS3 info: {} 配置文件内容一致，跳过写入",vin);
                return;
            }
            configStoreService.writeFileToS3(fileName, data);
            configStoreService.updateVersion(carName);
            // 记录日志
            String mis = UserUtils.getUser().getLogin();
            log.info("sendConfigToS3 info: "+vin+" "+mis);
            CmdbActionLog log = new CmdbActionLog();
            log.setVin(vin);
            log.setMis(mis);
            log.setActionContent("update vin info,change vehicle-info.pb");
            cmdbActionLogMapper.insert(log);
        }catch (Exception e){
            log.error("sendConfigToS3 error: ",e);
            // 上报 raptor
            MetricHelper.build().name("wallcmdb.cmdb.config.s3").tag("status", "bad").count(1);
            throw e;
        }
    }

    @Override
    public String getAutocarConfigFromS3(String s3Key) throws IOException {
//        String fileName = "config/vehicle/"+carName+"/vehicle_info.pb.txt";
//        return configStoreService.getMetaData(fileName);
        String data = configStoreService.getObjectAsString(s3Key);
        return data;
    }

    @Override
    public List getAutocarConfigListFromS3(String carName){
        List<S3ObjectSummary> data = configStoreService.listObjects("config/vehicle/"+carName);
        // 遍历data，如果child结尾是/，则从data中剔除
        Iterator<S3ObjectSummary> iterator = data.iterator();
        while (iterator.hasNext()) {
            S3ObjectSummary child = iterator.next();
            if (child != null) {
                if (child.getKey().endsWith("/")) {
                    iterator.remove();
                }
            }
        }
        return data;
    }

    @Override
    public void sendConfigToS3(String objName, String data) throws IOException {
        String fileContent = configStoreService.getFileContent(objName);
        if (StringUtils.equals(fileContent, data)){
            log.info("sendConfigToS3 info: {} 配置文件内容一致，跳过写入",objName);
            return;
        }
        // 完整路径 config/vehicle/carNmae/file ,从其中取出carName
        String carName = getCarNameFromObjName(objName);
        configStoreService.writeFileToS3(objName, data);
        configStoreService.updateVersion(carName);

        String mis = UserUtils.getUser().getLogin();
        log.info("sendConfigToS3 info: "+ objName +" "+mis);
        CmdbActionLog log = new CmdbActionLog();
        log.setVin(objName);
        log.setMis(mis);
        log.setActionContent(data);
        cmdbActionLogMapper.insert(log);
    }

    @Override
    public void deleteConfigFromS3(String objName) throws IOException {
        configStoreService.deleteFileFromS3(objName);
        String carName = getCarNameFromObjName(objName);
        configStoreService.updateVersion(carName);
        writeLog(objName, "delete");
    }

    private String getCarNameFromObjName(String objName){
        String[] parts = objName.split("/");
        if (parts.length < 3) {
            throw new IllegalArgumentException("The object name '" + objName + "' does not contain a valid car name.");
        }
        return parts[2];
    }

    private void writeLog(String objName, String data){
        // 记录日志
        String mis = UserUtils.getUser().getLogin();
        log.info("sendConfigToS3 info: "+ objName +" "+mis);
        CmdbActionLog log = new CmdbActionLog();
        log.setVin(objName);
        log.setMis(mis);
        log.setActionContent(data);
        cmdbActionLogMapper.insert(log);
    }

    // 分页查询配置的权限列表
    @Override
    public Map<String, List<String>> getConfigAuthList() {
        // 分页查询所有权限，将同一个file的权限，mis组成一个list
        List<ConfigPermission> configPermissions = configPermissionMapper.selectByExample(null);
        Map<String, List<String>> filePermissionMap = new HashMap<>();
        for (ConfigPermission permission : configPermissions) {
            String file = permission.getFile();
            String mis = permission.getMis();
            if (filePermissionMap.containsKey(file)) {
                filePermissionMap.get(file).add(mis);
            } else {
                List<String> misList = new ArrayList<>();
                misList.add(mis);
                filePermissionMap.put(file, misList);
            }
        }
        return filePermissionMap;
    }
    // 获取当前用户有权限的配置
    @Override
    public List<String> getConfigAuth() {
        String mis = UserUtils.getUser().getLogin();
        List<String> fileList = configPermissionMapper.selectByExample(new ConfigPermissionExample() {{
            createCriteria().andMisEqualTo(mis);
        }}).stream().map(ConfigPermission::getFile).collect(Collectors.toList());
        return fileList;
    }
    // 新增权限
    @Override
    public void addConfigAuth(ConfigPermission configPermission) {
        configPermissionMapper.insert(configPermission);

    }
    // 更新权限的人员
    @Override
    public void updateConfigAuth(ConfigPermissionDTO body) {
        String file = body.getFile();
        //  获取请求中的MIS列表
        List<String> mis = body.getMis();
        //  查询rds当前权限
        List<String> rdsMis = configPermissionMapper.selectByExample(new ConfigPermissionExample() {{
            createCriteria().andFileEqualTo(file);
        }}).stream().map(ConfigPermission::getMis).collect(Collectors.toList());
        // 比对请求中的mis和rds中的mis
        List<String> addMis = mis.stream().filter(misList -> !rdsMis.contains(misList)).collect(Collectors.toList());
        List<String> deleteMis = rdsMis.stream().filter(misList -> !mis.contains(misList)).collect(Collectors.toList());
        // 更新rds中的mis
        for (String misStr : deleteMis) {
            ConfigPermissionExample example = new ConfigPermissionExample();
            example.createCriteria().andFileEqualTo(file).andMisEqualTo(misStr);
            configPermissionMapper.deleteByExample(example);
        }
        for (String misStr : addMis) {
            ConfigPermission configPermission = new ConfigPermission();
            configPermission.setFile(file);
            configPermission.setMis(misStr);
            configPermissionMapper.insert(configPermission);
        }

    }

    // 合并前端传入和数据库中保存的字符串配置
    private String generateJsonConfig(String inputString, String dbString) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode inputNode = objectMapper.readValue(inputString, ObjectNode.class);
        ObjectNode dbNode = StringUtils.isBlank(dbString)
                ? objectMapper.createObjectNode()
                : objectMapper.readValue(dbString, ObjectNode.class);
        // 遍历 input 中的所有字段
        inputNode.fields().forEachRemaining(field -> {
            String key = field.getKey();
            JsonNode value = field.getValue();
            if (value.isTextual()) {
                if (!value.asText().isEmpty()) {
                    dbNode.set(key, value);
                }
            } else if (value.isArray()) {
                if (value.isEmpty()) {
                    dbNode.remove(key);
                } else {
                    dbNode.set(key, value);
                }
            } else {
                dbNode.set(key, value);
            }
        });
        // 清理待返回结果中的空值
        List<String> keysToRemove = new ArrayList<>();
        Iterator<Map.Entry<String, JsonNode>> dbFields = dbNode.fields();
        while (dbFields.hasNext()) {
            Map.Entry<String, JsonNode> entry = dbFields.next();
            JsonNode value = entry.getValue();
            if (value.isNull() ||
                    (value.isTextual() && value.asText().isEmpty()) ||
                    (value.isArray() && value.isEmpty())) {
                keysToRemove.add(entry.getKey());
            }
        }
        for (String key : keysToRemove) {
            dbNode.remove(key);
        }
        return dbNode.toString();
    }

    // 从前端传入的配置中筛选出二进制配置并处理合并
    private String generateBinConfig(
            String inputString,
            String dbString,
            JsonNode lionConfigDesc,
            JsonNode defaultBinConfig
    ) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode inputNode = objectMapper.readValue(inputString, ObjectNode.class);
        ObjectNode dbNode = StringUtils.isBlank(dbString)
                ? objectMapper.createObjectNode()
                : objectMapper.readValue(dbString, ObjectNode.class);
        // 过滤掉输入的空值
        List<String> keysToRemove = new ArrayList<>();
        Iterator<Map.Entry<String, JsonNode>> inputFields = inputNode.fields();
        while (inputFields.hasNext()) {
            Map.Entry<String, JsonNode> entry = inputFields.next();
            JsonNode value = entry.getValue();
            if (value.isNull() ||
                    (value.isTextual() && value.asText().isEmpty()) ||
                    (value.isArray() && value.isEmpty())) {
                keysToRemove.add(entry.getKey());
            }
        }
        for (String key : keysToRemove) {
            inputNode.remove(key);
        }
        // 处理每个输入配置
        inputNode.fields().forEachRemaining(field -> {
            String binValue = field.getValue().asText();
            String control = lionConfigDesc.get(field.getKey()).get("control").asText();
            int index = lionConfigDesc.get(field.getKey()).get("index").asInt();
            // 库中没有的话属于新增，先初始化control对应的二进制配置
            if (!dbNode.has(control)) {
                if (!defaultBinConfig.has(control)) {
                    return;
                }
                dbNode.set(control, defaultBinConfig.get(control));
            }
            // 更新二进制配置对应位的值
            String origin = dbNode.withArray(control).get(0).asText();
            StringBuilder sb = new StringBuilder(origin);
            sb.setCharAt(index, binValue.charAt(0));
            dbNode.withArray(control).set(0, TextNode.valueOf(sb.toString()));
        });
        return dbNode.toString();
    }

}
