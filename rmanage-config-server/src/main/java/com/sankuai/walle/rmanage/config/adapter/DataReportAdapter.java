package com.sankuai.walle.rmanage.config.adapter;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.common.exception.RemoteErrorException;
import com.sankuai.walle.rmanage.config.dto.vehicleManage.DataReportFormatDTO;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import com.sankuai.walle.rmanage.config.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class DataReportAdapter {

    /**
     * 数据总线服务域名
     */
    @MdpConfig("eve.hostname")
    String dataReportHost;

    /**
     * 上报实时数据接口PATH
     */
    private static final String REPORT_REAL_DATA = "/eve/online/rest/data/bus/batch/incoming";

    /**
     * 上报实时数据接口
     *
     * @param dataList
     * @return
     */
    public void reportRealData(List<DataReportFormatDTO> dataList, String businessKey) {
        String reportRealDataUrl = dataReportHost + REPORT_REAL_DATA;
        // 构建上报数据格式
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("business_key", businessKey);
        contentMap.put("come_data", dataList);
        try {
            String contentMapToJson = JsonUtils.toJsonByObjectMapper(contentMap);
            log.info("reportRealData# content is : {}", contentMapToJson);
            String reportRealDataResponse = CommonUtil.doPost(reportRealDataUrl, contentMapToJson, null);
            log.info("reportRealData# response is : {}", reportRealDataResponse);

            ResData responseData = JSONObject.parseObject(reportRealDataResponse, ResData.class);
            if (Objects.isNull(responseData) || Objects.isNull(responseData.getCode())
                    || responseData.getCode() != CommonConstants.RESPONSE_SUCCESS_CODE) {
                throw new RemoteErrorException(reportRealDataResponse);
            }
        } catch (RemoteErrorException e) {
            log.error("RemoteErrorException, reportRealData is failed, inputParam is : {}", contentMap, e);
        } catch (Exception e) {
            log.error("SystemException, reportRealData is failed, inputParam is : {}", contentMap, e);
        }
    }

    /**
     * 该接口有批量限制，拆分批次调用
     *
     * @param dataList
     * @return
     */
    public void reportRealDataBatch(List<DataReportFormatDTO> dataList, String businessKey) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        int batchSize = CommonConstants.DATA_BUS_BATCH_SIZE;
        int totalSize = dataList.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize;

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, totalSize);

            List<DataReportFormatDTO> batchDataList = dataList.subList(fromIndex, toIndex);
            reportRealData(batchDataList, businessKey);
        }
    }
}
