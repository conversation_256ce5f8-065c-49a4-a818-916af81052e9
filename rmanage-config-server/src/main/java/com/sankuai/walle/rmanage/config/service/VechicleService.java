package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.dal.eve.entity.CarDevices;
import com.sankuai.walle.objects.vo.CarDeviceVO;
import com.sankuai.walle.objects.vo.res.CarDetail;
import com.sankuai.walle.objects.vo.res.CarListRes;

import java.util.ArrayList;
import java.util.List;

public interface VechicleService {

    ArrayList<CarListRes> constructCarListData(List<String> vins);

    CarDetail fetchVectialDetail(List<CarObjects> objects, String vin, String label) throws Exception;

    ArrayList<String> getAllVins(String name,String label,String tagName,String ownerMis,String city,String area,String carUsedTarget);

    CarDevices fetchCarDevices(String vin);
    CarDevices fetchCarDevicesBySn(String sn);
}
