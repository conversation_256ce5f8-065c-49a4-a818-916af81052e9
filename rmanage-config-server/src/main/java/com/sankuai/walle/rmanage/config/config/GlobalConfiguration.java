package com.sankuai.walle.rmanage.config.config;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.walle.rmanage.ota.meta.thrift.service.OverallVersionThriftService;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.RpcDelivererQueryThriftService;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.RpcVehicleQueryThriftService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GlobalConfiguration {

    @Bean(name = "overallVersionThriftService")
    public ThriftClientProxy overallVersionThriftService() throws Exception {
        ThriftClientProxy service = new ThriftClientProxy();
        service.setServiceInterface(OverallVersionThriftService.class);
        service.setAppKey("com.sankuai.caros.wallecmdb");
        service.setRemoteAppkey("com.sankuai.caros.rmanage.otabe");
        service.setTimeout(2000);
        service.setNettyIO(true);
        return service;
    };

    @MdpThriftClient(remoteAppKey = "com.sankuai.walledelivery.basic", timeout = 20000)
    RpcVehicleQueryThriftService rpcVehicleQueryThriftService;
}

