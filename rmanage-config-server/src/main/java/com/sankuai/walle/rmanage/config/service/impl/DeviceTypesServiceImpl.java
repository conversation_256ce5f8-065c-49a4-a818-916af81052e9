package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.dal.eve.example.DeviceTypesExample;
import com.sankuai.walle.objects.vo.DeviceInfoVO;
import com.sankuai.walle.objects.vo.RelatedDeviceInfoVO;
import com.sankuai.walle.rmanage.config.service.DeviceTypesService;
import com.sankuai.walle.rmanage.config.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.sankuai.walle.dal.eve.mapper.DeviceTypesMapper;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class DeviceTypesServiceImpl implements DeviceTypesService {

    @Resource
    private DeviceTypesMapper deviceTypesMapper;

    /**
     * 获取设备类型列表
     * @param deviceType 设备类型对象
     * @return 返回设备类型列表
     */
    @Override
    public List<DeviceTypes> getDeviceTypes(DeviceTypes deviceType){
        DeviceTypesExample example = new DeviceTypesExample();
        DeviceTypesExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(false);
        if(StringUtils.isNotBlank(deviceType.getTypeName())){
            criteria.andTypeNameEqualTo(deviceType.getTypeName());
        }
        if(StringUtils.isNotBlank(deviceType.getFriendName())){
            criteria.andFriendNameEqualTo(deviceType.getFriendName());
        }
        List<DeviceTypes> deviceTypes = deviceTypesMapper.selectByExample(example);
        log.info("getDeviceTypes, deviceTypes = {}", deviceTypes);
        return deviceTypes;
    }

    /**
     * 根据设备Id列表批量获取设备详情
     * @param deviceTypeIds 设备Id列表
     * @return 返回设备详情列表
     */
    @Override
    public List<DeviceTypes> batchGetDeviceTypesByIds(List<Long> deviceTypeIds) {
        if(CollectionUtils.isEmpty(deviceTypeIds)){
            return Collections.emptyList();
        }
        DeviceTypesExample example = new DeviceTypesExample();
        example.createCriteria().andIsDeletedEqualTo(false).andIdIn(deviceTypeIds);

        List<DeviceTypes> deviceTypes = deviceTypesMapper.selectByExample(example);
        log.info("batchGetDeviceTypesByIds, deviceTypes = {}", deviceTypes);
        return deviceTypes;
    }

    /**
     * 插入设备类型
     * @param deviceType 设备类型对象
     */
    @Override
    public void insertDeviceType(DeviceTypes deviceType) {
        deviceTypesMapper.insertSelective(deviceType);
    }

    /**
     * 将设备类型对象转换为设备信息对象
     * @param deviceTypes 设备类型对象
     * @return 返回转换后的设备信息对象
     */
    @Override
    public DeviceInfoVO trance2DeviceInfoVO(DeviceTypes deviceTypes){
        return DeviceInfoVO.builder()
                .id(deviceTypes.getId())
                .typeName(deviceTypes.getTypeName())
                .friendName(deviceTypes.getFriendName())
                .category(deviceTypes.getCategory())
                .editor(deviceTypes.getEditor())
                .createTime(DateUtils.format(deviceTypes.getAddTime(), DateUtils.DATE_FORMAT_PATTERN))
                .updateTime(DateUtils.format(deviceTypes.getUpdateTime(), DateUtils.DATE_FORMAT_PATTERN))
                .build();
    }

    /**
     * 将设备类型对象转换为关联设备信息对象
     * @param deviceTypes  设备类型对象
     * @param relatedId 车型和设备的关联ID
     * @return 返回转换后的关联设备信息对象
     */
    @Override
    public RelatedDeviceInfoVO trance2RelatedDeviceInfoVO(DeviceTypes deviceTypes, Long relatedId) {
        return RelatedDeviceInfoVO.builder()
                .relatedId(relatedId)
                .id(deviceTypes.getId())
                .typeName(deviceTypes.getTypeName())
                .friendName(deviceTypes.getFriendName())
                .category(deviceTypes.getCategory())
                .editor(deviceTypes.getEditor())
                .createTime(DateUtils.format(deviceTypes.getAddTime(), DateUtils.DATE_FORMAT_PATTERN))
                .updateTime(DateUtils.format(deviceTypes.getUpdateTime(), DateUtils.DATE_FORMAT_PATTERN))
                .build();
    }
}
