package com.sankuai.walle.rmanage.config.config;

import com.sankuai.banma.auk.server.sdk.AukClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;

// 集成海雀ServerSDK
@Configuration
@PropertySource(value = {"classpath:leaf.properties", "classpath:/META-INF/app.properties"})
public class AukClientConfig {
    @Value("${auk.aukClientAK}")
    private String aukClientAK;
    @Value("${auk.aukClientSK}")
    private String aukClientSK;

    @Bean(name = "aukClient")
    public AukClient aukClient() {
        return new AukClient(aukClientAK, aukClientSK);
    }

    public static int TIMEOUT = 60;
    public static final String PRODUCT_KEY = "walle";
    public static final String TOPIC = "vehicle-config-manage-release";
    public static final String UP_TOPIC = "vehicle-config-manage-release-rsp";
    public static final String QUERY_TOPIC = "vehicle-config-manage-query";
    public static final String QUERY_TOPIC_RESP = "vehicle-config-manage-query-rsp";
    public static final String CHECK_MD5_TOPIC = "vehicle-config-manage-check_md5";
    public static final String  CONNECT_TOPIC = "$auk/**/event/presence/connected";
    public static final String Charging_Cabinet_TOPIC_RESP = "Remote_Control.Remote_ELock_Control"; // 充电柜消息监听
    public static final String Charging_Cabinet_TOPIC_TMP = "/sys/{productKey}/{deviceKey}/thing/service/{moduleIdentifier}/invoke_reply"; // 提取变量使用的模板
    public static final int CertDays = 360; // 证书有效期
}
