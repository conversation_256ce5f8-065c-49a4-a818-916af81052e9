package com.sankuai.walle.rmanage.config.component;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.zebra.group.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.adapter.BasicAdapter;
import com.sankuai.walle.rmanage.config.adapter.DxGroupRoleNameUpdateAdapter;
import com.sankuai.walle.rmanage.config.adapter.TTOncallUserAdapter;
import com.sankuai.walle.rmanage.config.common.constant.BusinessTypeMapEnum;
import com.sankuai.walle.rmanage.config.constant.BasicConfigurationConstant;
import com.sankuai.walle.rmanage.config.constant.CollisionSeverity;
import com.sankuai.walle.rmanage.config.dto.accident.RGOncallUserDTO;
import com.sankuai.walle.rmanage.config.dto.accident.VehicleRealtimeStatusDTO;
import com.sankuai.walle.rmanage.config.dto.vehicleManage.DxMessageCardDTO;
import com.sankuai.walle.rmanage.config.service.AccidentMessageService;
import com.sankuai.walle.rmanage.config.service.RedisService;
import com.sankuai.walle.rmanage.config.service.VehicleDataBusService;
import com.sankuai.walle.rmanage.config.thread.RedisProductor;
import com.sankuai.walle.rmanage.config.util.DateUtils;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ActionFunc {
    @Resource
    DxGroupHandler dxGroupHandler;

    @Resource
    RedisProductor redisProductor;

    @Resource
    RedisService redisService;

    @Resource
    AccidentMessageService accidentMessageService;

    @Resource
    VehicleDataBusService vehicleDataBusService;

    @Resource
    TTOncallUserAdapter ttOncallUserAdapter;

    @Resource
    DxGroupRoleNameUpdateAdapter dxGroupRoleNameUpdateAdapter;

    @Resource
    private BasicAdapter basicAdapter;

    @MdpConfig("sc.daxiangvideo.urltext")
    public String videoMsg = "";

    @MdpConfig("sc.video.timeGap")
    private ArrayList<Integer> timeGap;

    @MdpConfig("is.add.area.maintenance.member")
    Boolean isAddAreaMaintenanceMember;

    @MdpConfig("accident.dx.group.bots.list")
    ArrayList<Long> dxGroupBots;

    @MdpConfig("accident.group.owner")
    String accidentGroupOwner = "liuqingwei02";

    @MdpConfig("accident.detail.url")
    private String accidentDetailUrl = "https://sunfuxiang-wsyft-sl-walledata.mad.test.sankuai.com/workstation/safety";

    @MdpConfig("accident.disable.creat.dx.group.vin.list")
    ArrayList<String> disableCreatDXGroupVinList;

    /**
     * 车型-HD视频配置
     */
    @MdpConfig("accident.carType.HDVideo.config")
    HashMap<String,ArrayList<String>> carTypeHDVideoMap;

    /**
     * Monitor回放视频URL配置
     */
    @MdpConfig("sc.monitor.video.url")
    public String monitorVideoUrl ;

    /**
     * Monitor视频时间范围配置（前后时间间隔，单位：秒）
     */
    @MdpConfig("sc.monitor.video.timeGap")
    private Integer monitorVideoTimeGap;

    /**
     * 每次向大象群添加人数的大小限制
     */
    private static final int ADD_MEMBER_SIZE = 50;

    public void create_group(String groupMembersStr, Map<String, Object> accidentDetail) throws Exception {
        log.info("create_group is running! groupMembersStr = {}", groupMembersStr);

        // 0 根据 vin 进行特定车辆的建群逻辑屏蔽
        String vin = String.valueOf(accidentDetail.get("vin"));
        if (disableCreatDXGroupVinList.contains(vin)) {
            log.info("vin = [{}] need to filter", vin);
            return;
        }

        // 1 获取配置人员名单
        List<String> groupMembers = getGroupMembers(groupMembersStr);

        // 2 添加上报人,第一阶段，暂不开放此功能
        String reporter = String.valueOf(accidentDetail.get("reporter"));

        // 3 保留原有的拉群逻辑,可配置，第一阶段可暂不开放此功能
        List<String> temp = accidentMessageService.getGroupMember(accidentDetail);
        if (isAddAreaMaintenanceMember) {
            groupMembers.add(reporter);
            groupMembers.addAll(temp);
        }

        // 引用需要告警的RgIdList消息
        List<Long> needAlertRgIdList = new ArrayList<>();
        // 获取TT值班人员列表
        List<RGOncallUserDTO> rgOncallUserDTOList = ttOncallUserAdapter.getTTOncallUserList(needAlertRgIdList);
        if (CollectionUtils.isNotEmpty(rgOncallUserDTOList)) {
            rgOncallUserDTOList.stream()
                    .filter(Objects::nonNull)
                    .forEach(rgOncallUserDTO -> groupMembers.addAll(rgOncallUserDTO.getOncallUserList()));
        }
        // 发送告警消息
        ttOncallUserAdapter.sendAlertMsg(needAlertRgIdList);


        // 3.1 拉群是不能有重复mis号，拉群前需要去重
        Set<String> set = new HashSet<>(groupMembers);
        List<String> createGroupList = new ArrayList<>(set);
        List<String> addGroupList = new ArrayList<>();

        // 3.2 分批次进群，前50mis号在建群时进入，剩余mis号调用 addGroupMember 函数分批次进群
        if (createGroupList.size() > ADD_MEMBER_SIZE) {
            addGroupList.addAll(createGroupList.subList(ADD_MEMBER_SIZE, createGroupList.size()));
            createGroupList.subList(ADD_MEMBER_SIZE, createGroupList.size()).clear();
        }

        // 从数据总线获取车辆实时数据
        VehicleDataBusVTO vehicleDataBusVTO = vehicleDataBusService.getVehicleInfoString(vin);
        // 4 设置群组名称
        Map<String, String> result = makeDXMessageCard(accidentDetail, vehicleDataBusVTO);
        String title = result.get("title");
        String faultMessage = "硬件检测故障:\n" + result.get("faultMessage");
        String content = result.get("bodyText");

        // 5 创建事故详情链接
        String accidentId = String.valueOf(accidentDetail.get("id"));
        String accidentGroupNotice = String.format("事故ID=%s\n" + "[事故链接|%s?id=%s&mode=drawer]", accidentId,
                accidentDetailUrl, accidentId);

        Long groupId = dxGroupHandler.createGroup(title, createGroupList);
        Transaction t = Cat.newTransaction(BasicConfigurationConstant.ACCIDENT_GROUP, "create");
        if (groupId > 0) {
            t.setSuccessStatus();
            t.addData(String.valueOf(groupId));
            // 发送大象消息
            dxGroupHandler.sendMessage(groupId, content);
            // 添加群成员
            dxGroupHandler.addGroupMember(groupId, addGroupList);
            // 设置成员角色
            dxGroupRoleNameUpdateAdapter.updateGroupRoleName(groupId, rgOncallUserDTOList);
            // 添加机器人
            dxGroupHandler.addGroupBots(groupId, dxGroupBots);
            // 发送路障检测信息
            dxGroupHandler.sendMessage(groupId, faultMessage);
            // 设置群公告
            dxGroupHandler.setGroupNotice(groupId, accidentGroupNotice);
            // 转移群主
            dxGroupHandler.transGroupOwner(groupId, accidentGroupOwner, 2);
            // 将事故信息写入缓存
            //todo: 从数据总线获取车型信息
            writeMessageDataToRedis(groupId, accidentDetail, vehicleDataBusVTO);
            writeGroupIdReflectToRedis(groupId, accidentDetail);
        } else {
            t.setStatus("建群失败");
        }
        t.complete();
    }

    public void call(String groupMembersStr, Map<String, Object> accidentDetail) throws Exception {
        log.info("call is running");
    }

    private Map<String, String> makeDXMessageCard(Map<String, Object> accidentDetail, VehicleDataBusVTO vehicleDataBusVTO) {

        Map<String, String> dxMessageMap = new HashMap<>();
        String vin = String.valueOf(accidentDetail.get("vin"));
        // 0 查询车辆实时信息
        VehicleRealtimeStatusDTO realtimeStatusDTO = accidentMessageService.callVehicleStatusService(vin);

        DxMessageCardDTO.DxMessageCardDTOBuilder dxMessageCard = DxMessageCardDTO.builder();

        // 1 获取碰撞程度
        String collisionSeverityDesc = CommonConstants.UNKNOWN_CHINESE;
        if (accidentDetail.containsKey("collision_severity")) {
            collisionSeverityDesc = CollisionSeverity.getDescByCode((int)accidentDetail.get("collision_severity"));
        }
        dxMessageCard.collisionSeverityDesc(collisionSeverityDesc);

        // 2 获取异常事件的发生时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String accidentTime = sdf.format(new Date((Long) accidentDetail.get("accident_time")));
        dxMessageCard.accidentTime(accidentTime);

        // 3 获取城市字段
        Map<String, String> recordMessage = accidentMessageService.getCityAndAffiliationFromRecord(vin);
        String city = CommonConstants.UNKNOWN_CHINESE;
        if (recordMessage.containsKey("city")) {
            city = recordMessage.get("city");
        }
        dxMessageCard.city(city);


        // 获取车辆业务类型映射值
        BusinessTypeMapEnum businessTypeMap = vehicleDataBusService.getBusinessType(vehicleDataBusVTO);

        // 4 业务类型如果是路测，才有近场安全员字段
        if (BusinessTypeMapEnum.ROAD_TEST.equals(businessTypeMap)) {
            String substituteListString = vehicleDataBusService.getSubstituteListString(vehicleDataBusVTO);
            dxMessageCard.substituteListString(StringUtils.isNotBlank(substituteListString)?
                    substituteListString: CommonConstants.UNKNOWN_CHINESE);
        }

        // 5 获取车辆所属站点信息
        List<String> stationNameList = basicAdapter.queryBusinessStationListByVin(vin);
        String stationNameStr = String.join(",", stationNameList);
        dxMessageCard.stationName(StringUtils.isNotBlank(stationNameStr)?
                stationNameStr: CommonConstants.UNKNOWN_CHINESE);

        // 6 获取车辆vhr
        Integer vhr = realtimeStatusDTO.getIsVhrMultiple();
        String vhrDesc = CommonConstants.UNKNOWN_CHINESE;
        if (vhr != null) {
            vhrDesc = vhr == 1 ? ">1" : "=1";
        }
        dxMessageCard.vhrDesc(vhrDesc);

        // 获取车辆近场/云控VHR信息
        String telecontrolVHR = vehicleDataBusVTO.getTelecontrolVHRValue();
        dxMessageCard.telecontrolVHR(StringUtils.isNotBlank(telecontrolVHR)?
                telecontrolVHR: CommonConstants.UNKNOWN_CHINESE);

        String nearbyRescueVHR = vehicleDataBusVTO.getNearbyRescueVHRValue();
        dxMessageCard.nearbyRescueVHR(StringUtils.isNotBlank(nearbyRescueVHR)?
                nearbyRescueVHR: CommonConstants.UNKNOWN_CHINESE);


        // 7 三方是否离开
        String isThirdPartyLeave = CommonConstants.UNKNOWN_CHINESE;
        if (accidentDetail.containsKey("is_third_party_leave")) {
            int temp = (int)accidentDetail.get("is_third_party_leave");
            isThirdPartyLeave = (temp == 1) ? "是" : (temp == 0) ? "否" : "默认值";
        }
        dxMessageCard.isThirdPartyLeave(isThirdPartyLeave);

        // 8 是否有人受伤
        String isAnyoneInjured = CommonConstants.UNKNOWN_CHINESE;
        if (accidentDetail.containsKey("is_anyone_injured")) {
            int temp = (int)accidentDetail.get("is_anyone_injured");
            isAnyoneInjured = (temp == 1) ? "是" : (temp == 0) ? "否" : "默认值";
        }
        dxMessageCard.isAnyoneInjured(isAnyoneInjured);

        // 9 是否双方事故
        String isBothPartiesAccident = CommonConstants.UNKNOWN_CHINESE;
        if (accidentDetail.containsKey("is_both_parties_accident")) {
            int temp = (int)accidentDetail.get("is_both_parties_accident");
            isBothPartiesAccident = (temp == 1) ? "是" : (temp == 0) ? "否" : "默认值";
        }
        dxMessageCard.isBothPartiesAccident(isBothPartiesAccident);

        // 10 获取速度 和 驾驶模式
        String speed = CommonConstants.UNKNOWN_CHINESE;
        String driveModeDesc = CommonConstants.UNKNOWN_CHINESE;
        Map<String, Object> vehicleHistoryStatus = accidentMessageService.getVehicleHistoryStatus(vin,
                new Date((Long)accidentDetail.get("accident_time")));
        if (vehicleHistoryStatus != null) {
            double speedNum = Double.valueOf(vehicleHistoryStatus.getOrDefault("speed", "0").toString()) * 3.6;
            speed = String.format("%.2f", speedNum) + " km/h";
            driveModeDesc = vehicleHistoryStatus.getOrDefault("drive_mode_desc", "未知").toString();

            // 填充 location_gps 字段，兼容缺少经纬度信息的事故工单
            if (StringUtils.isBlank(String.valueOf(accidentDetail.get("location_gps")))
                    && !StringUtils.isBlank(String.valueOf(vehicleHistoryStatus.get("longitude")))
                    && !StringUtils.isBlank(String.valueOf(vehicleHistoryStatus.get("latitude")))) {
                accidentDetail.put("location_gps",
                        vehicleHistoryStatus.get("longitude") + "," + vehicleHistoryStatus.get("latitude"));
            }
        }
        dxMessageCard.speed(speed);
        dxMessageCard.driveModeDesc(driveModeDesc);

        // 11 获取事发路段信息
        String roadType = accidentMessageService.getRoadType(accidentDetail);
        dxMessageCard.roadType(roadType);

        // 12 获取地址信息
        String accidentLocationName = String.valueOf(accidentDetail.get("location_name"));
        if (StringUtils.isEmpty(accidentLocationName)) {
            accidentLocationName = accidentMessageService
                    .getLocationNameByGps(String.valueOf(accidentDetail.get("location_gps")));
        }
        dxMessageCard.accidentLocationName(accidentLocationName);

        // 13 碰撞标签信息
        String collisionReason = accidentMessageService.getCollisionDetectionAlert(vin,
                new Date((Long) accidentDetail.get("accident_time")).getTime());
        if (StringUtils.isBlank(collisionReason)) {
            collisionReason = CommonConstants.UNKNOWN_CHINESE;
        }
        dxMessageCard.collisionReason(collisionReason);

        // 14 事故链接
        String accidentId = String.valueOf(accidentDetail.get("id"));
        String accidentGroupNotice = String.format("%s?id=%s&mode=drawer", accidentDetailUrl, accidentId);
        dxMessageCard.accidentGroupNotice(accidentGroupNotice);


        // 15 构建剩余模版信息
        dxMessageCard.vehicleMapUrlSuffix(accidentDetail.get("vin"))
                .accidentInfoCommitUrlSuffix(accidentDetail.get("id"))
                .csmEventUrlSuffix(accidentDetail.get("vin"))
                .reporter(accidentDetail.get("reporter"))
                .accidentDesc(accidentDetail.get("accident_desc"))
                .name(realtimeStatusDTO.getName())
                .vehicleId(realtimeStatusDTO.getVehicleId())
                .purpose(StringUtils.isNotBlank(realtimeStatusDTO.getPurpose())?
                        realtimeStatusDTO.getPurpose(): CommonConstants.UNKNOWN_CHINESE);

        // 15.1 构建monitor回放视频URL
        Long startTimeMs = (Long) accidentDetail.get("accident_time")- monitorVideoTimeGap * 1000L;
        String startTimeStr = DateUtils.formatTimestampSafe(startTimeMs);

        // 构建完整的monitor视频URL（使用配置的模板格式）
        String monitorVideoFullUrl = String.format(monitorVideoUrl,vin, startTimeStr);

        // 处理时间字符串中的空格，替换为URL编码
        monitorVideoFullUrl = monitorVideoFullUrl.replace(" ", "%20");
        log.info("monitorVideoFullUrl = {}", monitorVideoFullUrl);
        dxMessageCard.monitorVideoFullUrl(monitorVideoFullUrl);

        dxMessageCard.build();
        DxMessageCardDTO dxMessageCardDTO = dxMessageCard.build();

        // 构建消息卡片
        String bodyText = dxMessageCardDTO.buildMessageCard(businessTypeMap);
        log.info("DXMessageCardBodyText = {}", bodyText);
        dxMessageMap.put("bodyText", bodyText);

        // 获取群名
        String title = getAccidentGroupTitle(accidentDetail,businessTypeMap,city,realtimeStatusDTO);
        log.info("title = {}", title);
        dxMessageMap.put("title", title);

        // 硬件检测故障
        String faultMessage = accidentMessageService.getFaultInformation(realtimeStatusDTO.getName());
        dxMessageMap.put("faultMessage", faultMessage);

        return dxMessageMap;
    }

    /**
     * 高清视频回捞相关信息 写入 redis
     * @param groupId
     * @param accidentDetail
     */
    private void writeMessageDataToRedis(Long groupId, Map<String, Object> accidentDetail, VehicleDataBusVTO vehicleDataBusVTO) {

        Long accidentTime = (Long)accidentDetail.get("accident_time") / 1000; // 时间需要转化成秒级
        String vin = String.valueOf(accidentDetail.get("vin"));
        String loopUrl = null;
        String concatUrl = null;
        try {
            if(Objects.isNull(vehicleDataBusVTO)
                    || Objects.isNull(vehicleDataBusVTO.getVehicleManage())
                    || StringUtils.isBlank(vehicleDataBusVTO.getVehicleManage().getFirstClassModel())){
                //  车辆相关的车型信息为空时，抛出异常
                throw new IllegalArgumentException(String.format("未获取到车辆相关的车型信息,vin:%s",vin));
            }
            String firstClassModel = vehicleDataBusVTO.getVehicleManage().getFirstClassModel();
            Set<String> positionList = Optional.ofNullable(carTypeHDVideoMap.get(firstClassModel))
                    .map(value -> new HashSet<>((List<String>) value))
                    .orElse(new HashSet<>());
            if(CollectionUtils.isEmpty(positionList)){
                return;
            }
            if (positionList.contains("v_loop")) {
                loopUrl = String.format(videoMsg, vin, accidentTime - timeGap.get(0), accidentTime + timeGap.get(1), "v_loop");
            }
            if (positionList.contains("v_concat")) {
                concatUrl = String.format(videoMsg, vin, accidentTime - timeGap.get(0), accidentTime + timeGap.get(1), "v_concat");
            }
            redisProductor.sendDateToRedisQueue("HDvideoParamsQueue", vin, accidentTime, loopUrl, concatUrl, groupId,
                    "accident");
            log.info("writeMessageDataToRedis, vin = {}, accidentTime = {}, videoMsg = {}, groupId = {} ", vin,
                    accidentTime, videoMsg, groupId);
        } catch (Exception e) {
            log.error("writeMessageDataToRedis, writeMessageDataToRedis is failed", e);
        }
    }

    private void writeGroupIdReflectToRedis(Long groupId, Map<String, Object> accidentDetail) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", accidentDetail.get("id"));
        param.put("groupId", groupId);
        try {
            redisService.pushGroupIdInRedis(BasicConfigurationConstant.REDIS_GROUP_ID_REFLECT_CATEGORY, param);
            log.info("writeGroupIdReflectToRedis, param = {}", param);
        } catch (Exception e) {
            log.error("writeGroupIdReflectToRedis is failed, param is {}", param, e);
        }
    }

    /**
     * 获取群名
     * @param accidentDetail     事故详情
     * @param businessTypeMap 业务类型映射
     * @param city 城市
     * @param realtimeStatusDTO 车辆实时状态
     * @return 群名
     */
    private String getAccidentGroupTitle(Map<String, Object> accidentDetail, BusinessTypeMapEnum businessTypeMap,
                                         String city, VehicleRealtimeStatusDTO realtimeStatusDTO) {
        // 事故发生日期
        String monthDay = DateUtils.format(new Date((Long) accidentDetail.get("accident_time")), DateUtils.DATE_FORMAT_MD);
        // 车辆业务类型 -获得业务类型映射值，如果没有映射值则为空
        String businessTypeMapValue = businessTypeMap != null? businessTypeMap.getValue() : "";

        // 车牌号
        String vehicleId = realtimeStatusDTO.getVehicleId();

        String groupTitle =  "【待定级】" + monthDay + city + "-" + businessTypeMapValue + vehicleId;

        // 获取碰撞程度作为群名结尾，如果不为空，则添加到title中
        String groupNameSuffix = CollisionSeverity.getGroupNameSuffixByCode(
                (int) accidentDetail.get("collision_severity"));
        if(StringUtils.isNotBlank(groupNameSuffix)){
            groupTitle = groupTitle + "-" + groupNameSuffix;
        }
        return groupTitle;
    }

    /**
     * 获取群成员列表
     * @param groupMembersStr 群成员字符串
     * @return  群成员列表
     */
    private List<String> getGroupMembers(String groupMembersStr){
        
        // 参数校验
        if(StringUtils.isBlank(groupMembersStr)){
            return new ArrayList<>();
        }
        //
        List<String> groupMembers = new ArrayList<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 解析配置的成员列表
            List<String> settingsMemberlist = objectMapper.readValue(groupMembersStr,
                    new TypeReference<List<String>>() {});
            groupMembers.addAll(settingsMemberlist);
            log.info("settingsMemberlist =  {}", settingsMemberlist);
        } catch (Exception e) {
            log.error("getGroupMembers error", e);
        }

        return groupMembers;
    }

}