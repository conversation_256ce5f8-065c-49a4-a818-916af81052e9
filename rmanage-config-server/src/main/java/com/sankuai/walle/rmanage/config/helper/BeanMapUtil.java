package com.sankuai.walle.rmanage.config.helper;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class BeanMapUtil {

    public static Map beanToMap(Object object) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field: fields){
            field.setAccessible(true);
            Object fieldObj = field.get(object);
            String fieldName = field.getName();
            if ( fieldName.equals("sign") || fieldName.equals("sign_type")
                    || fieldName.equals("$jacocoData")) {
                continue;
            }
            map.put(fieldName, fieldObj);
        }
        return map;
    }
}
