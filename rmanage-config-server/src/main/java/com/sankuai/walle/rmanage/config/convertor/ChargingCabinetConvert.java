package com.sankuai.walle.rmanage.config.convertor;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.banma.auk.server.sdk.request.thing.ThingInvokeServiceRequest;

import java.util.HashMap;

import static com.sankuai.walle.rmanage.config.constant.ChargingCarbinetConstant.*;

public class ChargingCabinetConvert {

    public static ThingInvokeServiceRequest buildOpenDoorRequest(String deviceKey) {
        /*{
            "cmd": true, // true 开锁，false 关锁
            "time": 1, //1min
            "ServerTime": ms时间戳
        }*/
        // 开h24充电柜的柜门
        ThingInvokeServiceRequest request = new ThingInvokeServiceRequest();
        request.setProductKey(CHARGING_CABINET_PRODUCT_KEY);
        request.setDeviceKey(deviceKey);
        request.setModuleIdentifier(Remote_Control);
        request.setFuncIdentifier(Remote_ELock_Control);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ServerTime", System.currentTimeMillis());
        jsonObject.put("cmd", 0);
        jsonObject.put("time", 10);
        String json = jsonObject.toJSONString();
        request.setCommandContent(json);
        return request;
    }
}
