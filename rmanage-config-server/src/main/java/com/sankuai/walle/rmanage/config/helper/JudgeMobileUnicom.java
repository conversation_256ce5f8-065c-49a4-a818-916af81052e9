package com.sankuai.walle.rmanage.config.helper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class JudgeMobileUnicom {
    enum mobile {}

    // 根据iccid判断是移动还是联通,1 移动 2 联通
    public static int judge(String iccid){
        int res = 0;
        List<String> mobile = new ArrayList<>();
        mobile.addAll(Arrays.asList("898600","898602","898604","898607"));
        List<String> unicom = new ArrayList<>();
        unicom.addAll(Arrays.asList("898601","898606","898609"));
        String judgeNum = iccid.substring(0,6);
        if (mobile.contains(judgeNum)){
            res = 1;
        }
        if (unicom.contains(judgeNum)){
            res = 2;
        }
        return res;
    }

    public static void main(String[] args) {
        int res = JudgeMobileUnicom.judge("898604890121C0000344");
        System.out.println(res);
    }
}
