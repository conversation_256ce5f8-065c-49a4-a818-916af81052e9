package com.sankuai.walle.rmanage.config.config;


import com.dianping.squirrel.asyncclient.core.spring.SquirrelClientBeanFactory;
import com.dianping.squirrel.client.impl.redis.RedisClientBuilder;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

// 集成海雀ServerSDK
@Configuration
@PropertySource(value = {"classpath:squirrel.properties", "classpath:/META-INF/app.properties"})
public class KVClientConfig {
    @Value("${squirrel.clusterName}")
    private String clusterName;

    @Bean(name = "squirrel")
    public RedisStoreClient redisClient() {
        RedisStoreClient client =  new RedisClientBuilder(clusterName).build();
        return client;
    }

}
