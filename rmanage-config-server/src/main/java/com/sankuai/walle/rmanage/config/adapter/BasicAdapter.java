package com.sankuai.walle.rmanage.config.adapter;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.walle.rmanage.config.common.exception.RemoteErrorException;
import com.sankuai.walledelivery.basic.client.request.deliverer.DeliverRpcRequest;
import com.sankuai.walledelivery.basic.client.response.businessStation.BusinessStationPositionResponse;
import com.sankuai.walledelivery.basic.client.response.businessStation.dto.BusinessStationDTO;
import com.sankuai.walledelivery.basic.client.response.inner.deliverer.VehicleInfoResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.RpcVehicleQueryThriftService;
import com.sankuai.walledelivery.basic.client.enums.DelivererTypeEnum;
import com.sankuai.walledelivery.commons.enums.ErrorCode;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class BasicAdapter {

    @Resource
    private RpcVehicleQueryThriftService rpcVehicleQueryThriftService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.walledelivery.basic", timeout = 5000)
    private DelivererQueryThriftService delivererQueryThriftService;

    public List<VehicleInfoResponse> batchQueryByVinList(List<String> vinList) throws BizThriftException {
        if (CollectionUtils.isEmpty(vinList)) {
            log.error("batchQueryByVinList# 'vinList' must not be null or empty");
            return Collections.emptyList();
        }
        log.info("batchQueryByVinList# vinList: {}", JacksonUtils.to(vinList));
        ThriftResponse<List<VehicleInfoResponse>> response = rpcVehicleQueryThriftService.batchQueryByVinList(vinList);
        if (Objects.isNull(response) || response.getCode() != ErrorCode.StandardErrorCode.OK.getCode()
                || CollectionUtils.isEmpty(response.getData())) {
            log.error("batchQueryByVinList# Failed to query by vinList: {}, response: {}", JacksonUtils.to(vinList),
                    JacksonUtils.to(response));
            return Collections.emptyList();
        }
        return response.getData();
    }

    /**
     * 根据车辆vin查询业务站
     * @param vin
     * @return
     */
    public List<String> queryBusinessStationListByVin(String vin) {
        DeliverRpcRequest request = DeliverRpcRequest.builder()
                .account(vin)
                .delivererTypeEnum(DelivererTypeEnum.VEHICLE_POSITION_TYPE).build();
        try {
            ThriftResponse<BusinessStationPositionResponse> response =
                    delivererQueryThriftService.queryBusinessStationAndPositionByDeliver(request);
            log.info("queryBusinessStationListByVin# request: {}, response: {}", JacksonUtils.to(request),
                    JacksonUtils.to(response));
            if (Objects.isNull(response) || response.getCode() != ErrorCode.StandardErrorCode.OK.getCode()
                    || Objects.isNull(response.getData())) {
                throw new RemoteErrorException(String.format("查询业务站失败,vin:%s", vin));
            }
            List<BusinessStationDTO> businessStationDTOList = response.getData().getBusinessStationDTO();
            if (CollectionUtils.isEmpty(businessStationDTOList)) {
                return Collections.emptyList();
            }
            return businessStationDTOList.stream().map(BusinessStationDTO::getName).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("queryBusinessStationAndPositionByDeliver# Failed to query by vin: {}, response: {}", vin, e);
        }
        return Collections.emptyList();
    }
}
