package com.sankuai.walle.rmanage.config.component;

import com.meituan.dbus.MQ.MQUtils;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.mdp.boot.starter.mafka.consumer.AbstractMdpListener;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.policy.PolicyBase;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AccidentEventConsumer {

    @Resource
    private Map<String , PolicyBase>  PolicyMap;

    /**
     * 数据表变更类型-插入
     */
    private static final String DB_CHANGE_TYPE_INSERT = "insert";

    @MdpMafkaMsgReceive
    public ResData consume(String message, AbstractMdpListener.MdpMqContext context){
        log.info("msgId: {}, receive information: {}", context.getMessage().getMessageID(), message);
        ResData resData = new ResData();
        // 1 解析 mafka 消息
        DbusUtils utils = null;
        try{
            utils = MQUtils.newInstanceForMQ(message);
        } catch (Exception e) {
            log.error("getData from Mq is failed");
            return resData.failed();
        }
        //2 获取数据源与操作类型
        StaticUtils.EventType eventType = utils.getEventType();
        if (eventType == null || !DB_CHANGE_TYPE_INSERT.equals(eventType.name())) {
            return resData.success();
        }
        Map<String, Object> dataDetail =  utils.getDataMap();
        String tableName = utils.getTableName();

        //3 使用策略模式，根据不同的业务去执行不同的规则引擎，具体策略为根据 binlog监听的表名 去找对应的策略，并设置对应的执行规则

        if(PolicyMap.containsKey(tableName)){
            PolicyBase policyBaseRuleEngin = PolicyMap.get(tableName);
            policyBaseRuleEngin.run(dataDetail);
        }
        return resData.success();
    }

}
