package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.objects.vo.DeviceInfoVO;
import com.sankuai.walle.objects.vo.RelatedDeviceInfoVO;

import java.util.List;

public interface DeviceTypesService {
    List<DeviceTypes> getDeviceTypes(DeviceTypes deviceType);

    List<DeviceTypes> batchGetDeviceTypesByIds(List<Long> deviceTypeIds);

    void insertDeviceType(DeviceTypes deviceType);

    DeviceInfoVO trance2DeviceInfoVO(DeviceTypes deviceTypes);

    RelatedDeviceInfoVO trance2RelatedDeviceInfoVO(DeviceTypes deviceTypes, Long relatedId);
}
