package com.sankuai.walle.rmanage.config.service.impl;

import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigContent;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigVehicleTaskStatus;
import com.sankuai.walle.dal.mrm_manage.example.AutoConfigContentExample;
import com.sankuai.walle.dal.mrm_manage.example.AutoConfigVehicleTaskStatusExample;
import com.sankuai.walle.dal.mrm_manage.mapper.AutoConfigContentMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.AutoConfigVehicleTaskStatusMapper;
import com.sankuai.walle.objects.bo.RealTimeObj;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.request.SendExcelDeviceConfigReq;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.rmanage.config.common.constant.AutoConfigTaskStatusEnum;
import com.sankuai.walle.rmanage.config.common.constant.AutoConfigTaskTypeEnum;
import com.sankuai.walle.rmanage.config.common.constant.VehicleOnlineStatusEnum;
import com.sankuai.walle.rmanage.config.constant.ConfigEnumConstant;
import com.sankuai.walle.rmanage.config.dto.config.AutoBatchPreConfigResponseDto;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.service.*;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.service.appService.ConfigStoreService;
import com.sankuai.walle.rmanage.config.service.infrastructureService.SecretKeyService;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AutoConfigServiceImpl implements AutoConfigService {
    @Resource
    MafkaRealtimeService mafkaRealtimeService;
    @Resource
    MyAukService myAukService;
    @Resource
    ConfigService configService;
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    ConfigStoreService configStoreService;
    @Resource
    private VehicleDataBusService vehicleDataBusService;
    @MdpConfig("vehicle.auto.config")
    private HashMap<String, Long> AutoConfigLion;
    @MdpConfig("vehicle.auto.script")
    private String AutoScriptLion;
    @Resource
    AutoConfigContentMapper autoConfigContentMapper;
    @Resource
    AutoConfigVehicleTaskStatusMapper autoConfigVehicleTaskStatusMapper;
    @Resource
    SecretKeyService secretKeyService;
    @Resource
    CarSelectsQueryService carSelectsQueryService;
    @Autowired
    ConfigEnumConstant configEnumConstant;

    private static final String SCRIPT_NAME = "SCRIPT";


    @Override
    @SneakyThrows
    public AutoBatchPreConfigResponseDto handleExcelConfig(List<SendExcelDeviceConfigReq> body) {
        AutoBatchPreConfigResponseDto resData = new AutoBatchPreConfigResponseDto();
        resData.setCode(CommonConstants.ERROR_CODE);
        List<String> vins = body.stream()
                .map(SendExcelDeviceConfigReq::getVin).distinct().collect(Collectors.toList());

        // 查询vin和车名的映射
        // resData.setCarVinName(queryVehicleNameByVin(vins));

        // 批量自动vdcm的device key
        List<String> failedAukVin = myAukService.batchCreateAukDevices(vins);
        resData.setFailedAukVin(failedAukVin);

        // 批量注册状态监控 mqtt
        List<String> vsmonitorAuks = vins.parallelStream().map(vin -> vin + "_vstatus").collect(Collectors.toList());
        List<String> failedAukVsmonitorVin = myAukService.batchCreateAukDevices(vsmonitorAuks);
        resData.setFailedAukVin(failedAukVsmonitorVin);

        // 发送导入的配置到车
        List<String> failedConfigVin = batchSendConfigToCar(body, vins);
        resData.setFailedConfigVin(failedConfigVin);

        // 插入或更新配置信息
        updateOrInsertProperty(body);

        // (key : vin ), (value : 车在线并且已获取sn ?  sn : "")
        Map<String, String> vinOnlineStatus = vins.parallelStream().collect(Collectors.toMap(
                x -> x,
                x -> {
                    VehicleDataBusVTO vehicleInfoString = vehicleDataBusService.getVehicleInfoString(x);
                    String sn = vehicleInfoString.getMonitor().getSn();
                    return StringUtils.isNotEmpty(sn) &&
                            vehicleInfoString.getMonitorCompute().getOnlineStatus() == VehicleOnlineStatusEnum.ONLINE.getCode() ? sn : StringUtils.EMPTY;
                },
                (oldValue, newValue) -> newValue
        ));

        log.info("vehicle on line status : {}", vinOnlineStatus);


        // 发送mafka消息
        batchSendMafkaMessage(vinOnlineStatus);

        // 创建车辆任务
        createPowerOnTask(vinOnlineStatus);

        resData.setCode(CommonConstants.SUCCEED_CODE);
        resData.setMsg("OK");
        return resData;
    }



    @Override
    public String sendEveConfig(List<String> vins, String configName, String mis) {
        // 过滤不存在的vin
        List<String> existVin = filterVin(vins);
        if(existVin.size() == 0) {
            return "不存在车辆";
        }
        Long configId = AutoConfigLion.get(configName);
        configService.sendConfigToCar(configId, null, mis, "[自动化配置]"+configName, existVin);
        return CommonConstants.succeedMsg;
    }

    @Override
    public List<CarDeviceConfigRes> queryEveConfig(List<String> vins, String configName) {
        CarDeviceConfigExample query = new CarDeviceConfigExample();
        query.createCriteria().andVinIn(vins).andNameEqualTo(configName);
        List<CarDeviceConfig> carDeviceConfigs = carDeviceConfigMapper.selectByExampleWithBLOBs(query);
        // 组装车辆、配置、设备内容
        List<CarDeviceConfigRes> data = new ArrayList<>();
        for (CarDeviceConfig carDeviceConfig: carDeviceConfigs) {
            CarDeviceConfigRes child = carDeviceConfigService.createRes(carDeviceConfig);
            data.add(child);
        }
        return data;
    }

    @Override
    public void uploadS3(List<String> vins) throws IOException {
        CarObjectsExample query = new CarObjectsExample();
        query.createCriteria().andVinIn(vins);
        List<CarObjects> carObjects = carObjectsMapper.selectByExample(query);
        for (CarObjects carObject : carObjects) {
            String vin = carObject.getVin();
            String name = carObject.getName();
            configService.sendCarInfoConfigToS3(vin, name);
        }
        // 执行外之后，结果更新到rds中
    }

    @Override
    public Map<String,String> queryS3(List<String> vins) {
        Map<String,String> res = new HashMap<>();
        CarObjectsExample query = new CarObjectsExample();
        query.createCriteria().andVinIn(vins);
        List<CarObjects> cars = carObjectsMapper.selectByExample(query);
        for (CarObjects car : cars) {
            String carName = car.getName().toLowerCase(Locale.ROOT);
            String objectPath = "config/vehicle/"+carName+"/vehicle_info.pb.txt";
            String meta = null;
            try {
                meta = configStoreService.getFileContent(objectPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            res.put(car.getVin(), meta);
        }
        return res;

    }

    @Override
    public void sendScript(List<String> vins) {
        //判断configName是否有效
        if (!configEnumConstant.contains(SCRIPT_NAME)) {
            throw new RuntimeException("Request configName is invalid");
        }

        List<SendExcelDeviceConfigReq> sendExcelDeviceConfigReqs = vins.stream().map(vin->{
            SendExcelDeviceConfigReq sendExcelDeviceConfigReq = new SendExcelDeviceConfigReq();
            sendExcelDeviceConfigReq.setVin(vin);
            sendExcelDeviceConfigReq.setContent(AutoScriptLion);
            sendExcelDeviceConfigReq.setConfigName(SCRIPT_NAME);
            sendExcelDeviceConfigReq.setFileType("sh");
            return sendExcelDeviceConfigReq;
        }).collect(Collectors.toList());
        List<CarDeviceConfig> carDeviceConfigs = configService.sendConfigToCar(sendExcelDeviceConfigReqs);
        Set<String> successConfigVin = carDeviceConfigs.stream()
                .map(CarDeviceConfig::getVin)
                .collect(Collectors.toSet());
        log.info("发送脚本成功vin: {}", successConfigVin);
    }
//    @Override
//    public List<AutoConfigContent> queryByVinList(List<String> vin) {
//        AutoConfigContentExample autoConfigContentExample = new AutoConfigContentExample();
//        autoConfigContentExample
//                .createCriteria().andVinIn(vin).andIsDeleteEqualTo(false);
//
//        return autoConfigContentMapper.selectByExample(autoConfigContentExample);
//    }

    @Override
    public Map<String, AutoConfigVehicleTaskStatus> queryTaskByVinList(List<String> vin) {
        AutoConfigVehicleTaskStatusExample query = new AutoConfigVehicleTaskStatusExample();
        query.createCriteria().andVinIn(vin);
        List<AutoConfigVehicleTaskStatus> tasks = autoConfigVehicleTaskStatusMapper.selectByExample(query);
        // 组装任务信息为map，key是vin
        Map<String, AutoConfigVehicleTaskStatus> taskMap = new HashMap<>();
        for (AutoConfigVehicleTaskStatus task : tasks) {
            taskMap.put(task.getVin(), task);
        }
        return taskMap;
    }

    public void batchCreateSecretKey(List<String> vins) {
        vins.forEach(item -> {
            secretKeyService.createVisitStrategy(item);
            secretKeyService.createVehicleSSO(item);
            secretKeyService.importSecretKey(item);
            secretKeyService.importVehicleCloudSecret(item);
        });
    }

    private void batchSendMafkaMessage(Map<String, String> vinOnlineStatus) {
        CarObjectsExample carObjectsExample = new CarObjectsExample();
        // 查询存在的vin
        carObjectsExample.createCriteria().andVinIn(new ArrayList<>(vinOnlineStatus.keySet()));
        List<CarObjects> carObjects = carObjectsMapper.selectByExample(carObjectsExample);

        // carobjects -> realTimeObj
        List<RealTimeObj> realTimeObjList = carObjects.stream()
                .map(obj -> {
                    String sCarType = carSelectsQueryService.getSelect(CommonConstants.CAR_TYPE, obj.getCarType());
                    String vin = obj.getVin();
                    // 如果获取到了sn，并且车辆处于在线状态，sn会被发送，否则，不发送sn
                    return  StringUtils.isEmpty(vinOnlineStatus.get(vin)) ?
                            RealTimeObj.carObjectsToRealTimeObj(obj, sCarType) :
                            RealTimeObj.carObjectsToRealTimeObj(obj, sCarType, vinOnlineStatus.get(vin));
                })
                .collect(Collectors.toList());
        // (2) 发送Mafka消息
        try {
            for(RealTimeObj realTimeObj : realTimeObjList) {
                log.info("发送mafka消息 : {}", realTimeObj);
                mafkaRealtimeService.sendRealTime(realTimeObj);
            }
        } catch (Exception e) {
            log.info("发送mafka消息失败, {}", e.toString());
        }
    }

    @Override
    public Map<String, String> queryVehicleNameByVin(List<String> vins) {
        CarObjectsExample carObjectsExample = new CarObjectsExample();
        carObjectsExample.createCriteria().andVinIn(vins);

        List<CarObjects> carObjects = carObjectsMapper.selectByExample(carObjectsExample);

        return carObjects.stream().collect(Collectors.toMap(
                CarObjects::getVin,
                CarObjects::getName,
                (oldValue, newValue) -> oldValue
        ));
    }

    private List<String> batchSendConfigToCar(List<SendExcelDeviceConfigReq> body, List<String> vins) {
        List<CarDeviceConfig> carDeviceConfigs   = configService.sendConfigToCar(body);
        Set<String> successConfigVin = carDeviceConfigs.stream()
                .map(CarDeviceConfig::getVin)
                .collect(Collectors.toSet());

        // 返回失败的vin
        List<String> failedConfigVin = vins.stream()
                .filter(x -> !successConfigVin.contains(x))
                .collect(Collectors.toList());

        return failedConfigVin;
    }


    private void createPowerOnTask(Map<String, String> vinOlineStatus) {

        AutoConfigVehicleTaskStatusExample autoConfigVehicleTaskStatusExample = new AutoConfigVehicleTaskStatusExample();

        List<String> vinList = new ArrayList<>(vinOlineStatus.keySet());
        // 查询已经存在的vin
        autoConfigVehicleTaskStatusExample.createCriteria()
                .andVinIn(vinList).andTaskTypeEqualTo(AutoConfigTaskTypeEnum.GET_SN.getCode());


        List<AutoConfigVehicleTaskStatus> exitBody = autoConfigVehicleTaskStatusMapper.selectByExample(autoConfigVehicleTaskStatusExample);
        Set<String>  existVinList = exitBody.stream().map(AutoConfigVehicleTaskStatus::getVin).collect(Collectors.toSet());
        // 过滤得到不存在的vin， 需要插入
        List<String> unExesitVinList = vinList.stream()
                .filter(x -> ! existVinList.contains(x))
                .collect(Collectors.toList());

        // 对于已经存在的， 如果在线，更新为已完成，否则更新为未完成
        exitBody.forEach(item -> {
            item.setTaskStatus(AutoConfigTaskStatusEnum.CREATED.getCode());
            if(StringUtils.isNotEmpty(vinOlineStatus.get(item.getVin()))) {
                item.setTaskStatus(AutoConfigTaskStatusEnum.COMPLETED.getCode());
            }
            item.setUpdateTime(new Date());
            autoConfigVehicleTaskStatusMapper.updateByPrimaryKeySelective(item);
        });

        if(unExesitVinList.size() == 0) {
            return;
        }
        List<AutoConfigVehicleTaskStatus> autoConfigVehicleTask = unExesitVinList.stream().map(x -> new AutoConfigVehicleTaskStatus() {{
            setTaskStatus(AutoConfigTaskStatusEnum.CREATED.getCode());
            if(StringUtils.isNotEmpty(vinOlineStatus.get(x))) {
                // map中有SN， 说明在线，设置为已完成
                setTaskStatus(AutoConfigTaskStatusEnum.COMPLETED.getCode());
            }
            setVin(x);
            setTaskType(AutoConfigTaskTypeEnum.GET_SN.getCode());
        }}).collect(Collectors.toList());

        autoConfigVehicleTaskStatusMapper.batchInsert(autoConfigVehicleTask);
    }
    /*
    * 获取vin对应的配置表 例
    * [
    *   {vin : "v1", config : "cf1"},
    *   {vin : "v1", config : "cf2"},
    *   {vin : "v2", config : "cf2"}
    * ] => {"v1" : ["cf1", "cf2], "v2" : {"cf2"}}
    * */
    private Map<String, Set<String>> getVinConfigSet(List<SendExcelDeviceConfigReq> body) {
        Map<String, Set<String>> map = new HashMap<>();
        body.forEach(item -> {
            String vin = item.getVin();
            if(map.containsKey(item.getVin())) {
                Set<String> configNames = map.get(vin);
                configNames.add(item.getConfigName());
            } else {
                map.put(vin, Sets.newHashSet(item.getConfigName()));
            }
        });
        return map;
    }

    private List<String> updateOrInsertProperty(List<SendExcelDeviceConfigReq> body) {

        // 获取所有Vin
        List<String> vinList = body.stream()
                .map(SendExcelDeviceConfigReq::getVin).collect(Collectors.toList());

        // Vin - ConfigSet
        Map<String, Set<String>> vinConfigSet = getVinConfigSet(body);

        // 查询已经存在的vin 设置为update
        AutoConfigContentExample toUpdateConfigExample = new AutoConfigContentExample();
        toUpdateConfigExample.createCriteria().andVinIn(vinList).andIsDeleteEqualTo(false);
        // vin已经存在的配置
        List<AutoConfigContent> toUpdateConfigContent = autoConfigContentMapper.selectByExample(
                toUpdateConfigExample
        );
        // 更新的vin配置列表
        List<String> updateVinList = toUpdateConfigContent.stream()
                .map(AutoConfigContent::getVin).collect(Collectors.toList());

        // 将所有已经存在的vin 如果已经存在对应配置,的is_delete设置为1
        toUpdateConfigContent.forEach(item -> {
            if(vinConfigSet.get(item.getVin()).contains(item.getPropertyContent())) {
                item.setIsDelete(true);
                item.setUpdateTime(new Date());
                autoConfigContentMapper.updateByPrimaryKeySelective(item);
            }
        });

        // sendExcel 转化为 autoConfigContent
        List<AutoConfigContent> autoConfigContentList = body.stream()
                .map(AutoConfigContent::fromExcelReqBody).collect(Collectors.toList());

        // batchInsert
        autoConfigContentMapper.batchInsert(autoConfigContentList);

        return updateVinList;
    }

    private List<String> filterVin(List<String> vins) {
        CarObjectsExample carObjectsExample = new CarObjectsExample();
        carObjectsExample.createCriteria().andVinIn(vins);
        return carObjectsMapper.selectByExample(carObjectsExample)
                .stream().map(CarObjects::getVin)
                .collect(Collectors.toList());
    }


}