package com.sankuai.walle.rmanage.config.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.walle.rmanage.config.common.constant.BusinessTypeMapEnum;
import com.sankuai.walle.rmanage.config.service.VehicleDataBusService;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.BusinessType;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.Label;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.ReserveVehicle;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.Tags;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO.VresvItem;
import com.sankuai.wallecmdb.monitor.online.VehicleDataBusBatchGetRequest;
import com.sankuai.wallecmdb.monitor.online.VehicleDataBusBatchGetResponse;
import com.sankuai.wallecmdb.monitor.online.VehicleDataBusThriftService;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class VehicleDataBusServiceImpl implements VehicleDataBusService {

    private static final String ENTITY_NAME = "vehicle";
    private static final String KEY = "cmdb";

    @MdpThriftClient(remoteAppKey = "com.sankuai.wallecmdb.monitor.online", timeout = 2000)
    private VehicleDataBusThriftService.Iface vehicleDataBusThriftService;

    public VehicleDataBusVTO getVehicleInfoString(String vin) {
        VehicleDataBusBatchGetRequest request = new VehicleDataBusBatchGetRequest(
                Collections.singletonList(vin),
                KEY,
                ENTITY_NAME
        );
        VehicleDataBusVTO vehicleDataBusVTO = null;
        try {
            VehicleDataBusBatchGetResponse response = vehicleDataBusThriftService.batchGet(request);
            log.info("getVehicleInfoString, response={}", JacksonUtils.to(response));
            String vehicleInfo = response.getData().stream().findFirst().orElse(null);
            if (StringUtils.isBlank(vehicleInfo)) {
                log.info("调用数据总线失败，vin={}", vin);
                return null;
            }
            // 反序列化
            vehicleDataBusVTO = JacksonUtils.from(vehicleInfo, new TypeReference<VehicleDataBusVTO>() {
            });
            log.info("调用数据总线成功，vehicleDataBusVTO = {}", vehicleDataBusVTO);
        } catch (Exception e) {
            log.error("调用数据总线失败，vin={}", vin, e);
        }
        return vehicleDataBusVTO;
    }

    @Override
    public BusinessTypeMapEnum getBusinessType(VehicleDataBusVTO vehicleDataBusVTO) {
        if (ObjectUtils.isEmpty(vehicleDataBusVTO)) {
            return BusinessTypeMapEnum.UNKNOWN;
        }

        // 获得业务类型字段label.tags.businessType.value，例如"路测"，再根据业务类型值,获取对应的业务类型映射枚举值
        String businessTypeValue = Optional
                .ofNullable(vehicleDataBusVTO.getLabel())
                .map(Label::getTags)
                .map(Tags::getBusinessType)
                .map(BusinessType::getValue)
                .orElse(null);
        return BusinessTypeMapEnum.findByBusinessType(businessTypeValue);
    }

    @Override
    public String getSubstituteListString(VehicleDataBusVTO vehicleDataBusVTO) {
        // 获得 reserve_vehicle.vresv_list 列表中所有的 substitute 近场安全员信息
        List<VresvItem> substituteList = Optional.ofNullable(vehicleDataBusVTO)
                .map(VehicleDataBusVTO::getReserveVehicle)
                .map(ReserveVehicle::getVresvList)
                .orElse(null);

        // 如果没有安全员则为"-"
        String substituteListString = "-";
        if (CollectionUtils.isNotEmpty(substituteList)) {
            // 转换为需要显示的字符串
            substituteListString = substituteList.stream()
                    .map(VresvItem::getSubstitute)
                    .collect(Collectors.joining(","));
        }
        return substituteListString;
    }
}
