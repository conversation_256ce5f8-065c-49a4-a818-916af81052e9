package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.walle.carManage.entity.*;
import com.sankuai.walle.carManage.example.*;
import com.sankuai.walle.carManage.mapper.*;
import com.sankuai.walle.dal.eve.entity.CarDevices;
import com.sankuai.walle.dal.eve.example.CarDevicesExample;
import com.sankuai.walle.dal.eve.mapper.CarDevicesMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags;
import com.sankuai.walle.dal.mrm_manage.entity.Tags;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectTagsExample;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample;
import com.sankuai.walle.dal.mrm_manage.example.TagsExample;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectTagsMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.MyTagsMyMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.CarDeviceVO;
import com.sankuai.walle.objects.vo.request.CarEditReq;
import com.sankuai.walle.objects.vo.res.CarDetail;
import com.sankuai.walle.objects.vo.res.CarListRes;
import com.sankuai.walle.rmanage.config.service.CarSelectsQueryService;
import com.sankuai.walle.rmanage.config.service.VechicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VechicleServiceImpl implements VechicleService {

    @Resource
    MyTagsMyMapper tagsVinMapper;
    @Resource
    CarAssetsMapper carAssetsMapper;
    @Resource
    CarOperationMapper carOperationMapper;
    @Resource
    CarSelectsQueryService carSelectsQueryService;

    public ArrayList<String> getAllVins(String name,String label,String tagName,
                                        String ownerMis,String city,String area,String carUsedTarget){
        ArrayList<String> vins = new ArrayList<>();
        // 筛选集成数据
        RemoteObjectsExample objectsExample = new RemoteObjectsExample();
        objectsExample.createCriteria().andNameEqualTo(name);
        // 筛选标签数据
        // 筛选资产数据
        // 筛选运营数据
        return vins;
    }
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Autowired(required = false)
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient;
    public ArrayList<CarListRes> constructCarListData(List<String> vins){

        ArrayList<CarListRes> resData = new ArrayList<>();
        TreeMap<String, CarListRes> treeMap = new TreeMap<>();

        // 基础数据
        RemoteObjectsExample query = new RemoteObjectsExample();
        query.createCriteria().andVinIn(vins);
//        List<RemoteObjects> baseData = remoteObjectsMapper.selectByExample(query);
        CarObjectsExample example = new CarObjectsExample();
        example.createCriteria().andVinIn(vins);
        List<CarObjects> baseData = carObjectsMapper.selectByExample(example);
        baseData.stream().forEach(obj->{
            treeMap.put(obj.getVin(),new CarListRes(){{
                setName(obj.getName());
                setVin(obj.getVin());
                setCarType(carSelectsQueryService.getSelect(CommonConstants.CAR_TYPE, obj.getCarType()));
                setRemoteObjectId(obj.getId());
                setAssemblyParts(obj.getAssemblyParts());
            }});
        });
        // 自定义的 mapper, 获取标签数据
//        MyTagsVinExample tagQuery = new MyTagsVinExample();
//        tagQuery.createCriteria().andVinIn(vins);
//        List<MyTagsVin> tags = tagsVinMapper.selectByExample(tagQuery);
//        tags.forEach(tag->{
//            CarListRes obj = treeMap.get(tag.getVin());
//            if(obj!=null && obj.getVin().equals(tag.getVin())) {
//                if(obj.getTags()==null) obj.setTags(new ArrayList<>());
//                obj.getTags().add(tag);
//            };
//        });
        // 获取资产数据
        CarAssetsExample assetQuery = new CarAssetsExample();
        assetQuery.createCriteria().andVinIn(vins).andScrapEqualTo(false);
        List<CarAssets> assets = carAssetsMapper.selectByExample(assetQuery);
        assets.forEach(asset->{
            CarListRes obj = treeMap.get(asset.getVin());
            obj.setLabel(asset.getLabel());
            obj.setOwnerMis(asset.getPersonmis());
            obj.setOwnerDepartment(asset.getOwnerDepartment());
        });
        // 获取运营数据
        CarOperationExample operationQuery = new CarOperationExample();
        operationQuery.createCriteria().andVinIn(vins);
        carOperationMapper.selectByExample(operationQuery).forEach(operationObj->{
            CarListRes obj = treeMap.get(operationObj.getVin());
            obj.setCity(operationObj.getCity());
            obj.setArea(operationObj.getArea());
            obj.setCarUsedTarget(operationObj.getCarUsedTarget());
        });
        treeMap.forEach((String vin,CarListRes obj)->{
            resData.add(obj);
        });
        // 获取状态数据
        return resData;
    }
    @Resource
    CarSelectsMapper carSelectsMapper;


    @Resource
    RemoteObjectTagsMapper remoteObjectTagsMapper;

    @Resource
    TagsMapper tagsMapper;
    @Resource
    CarExecWordMapper carExecWordMapper;
    @Resource
    CarDevicesMapper carDevicesMapper;

    public CarDetail fetchVectialDetail(List<CarObjects> objects, String vin, String label) throws Exception {
        CarDetail res = new CarDetail();
        if(objects.size()>0) {
            // object
            CarObjects obj = objects.get(0);
            res.setCarObjects(obj);
            // selects
            CarSelectsExample selects = new CarSelectsExample();
            if(obj.getCarType()!=null) {
                selects.createCriteria().andBelongEqualTo("car_type").andTypeEqualTo(obj.getCarType());
                res.setCarType( carSelectsMapper.selectByExample(selects) );
            }
            // tag
            RemoteObjectTagsExample ObjectTagsExample = new RemoteObjectTagsExample();
            ObjectTagsExample.createCriteria().andRemoteObjectIdEqualTo(objects.get(0).getId());
            List<Long> tagids = remoteObjectTagsMapper.selectByExample(ObjectTagsExample).stream().map(RemoteObjectTags::getTagId).collect(Collectors.toList());
            TagsExample tagsExample = new TagsExample();
            if(tagids.size()>0) {
                tagsExample.createCriteria().andIdIn(tagids);
                List<Tags> tags = tagsMapper.selectByExample(tagsExample);
                res.setTags(tags);
            }
            // assert
            CarAssetsExample assetsExample = new CarAssetsExample();
            CarAssetsExample.Criteria criteria = assetsExample.createCriteria();
            criteria.andVinEqualTo(vin);
            if(Objects.equals(label, "null")){
                label = null;
            }
            if(label!=null) {
                criteria.andLabelEqualTo(label);
            }else{
                criteria.andLabelIsNull();
            }
            List<CarAssets> assets = carAssetsMapper.selectByExample(assetsExample);
            if(assets.size()>0) {
                Map<String,Object> map = JSON.parseObject(JSON.toJSONString(assets.get(0)),Map.class);
                res.setCarAssets(map);
            }else {
                res.setCarAssets(new HashMap<>());
            }
            // operation
            CarOperationExample operationExample = new CarOperationExample();
            operationExample.createCriteria().andVinEqualTo(vin);
            List<CarOperation> operation = carOperationMapper.selectByExample(operationExample);
            res.setCarOperation(operation);
            // 扩展字段
            CarExecWordExample carExecWordExample = new CarExecWordExample();
            carExecWordExample.createCriteria().andVinEqualTo(vin);
            List<CarExecWord> carExec = carExecWordMapper.selectByExample(carExecWordExample);
            res.setCarExecWords(CarEditReq.CarExecWordConvertor.jsonToObjExec(carExec));
            // 设备
            CarDevices carDevice = fetchCarDevices(vin);
            if(carDevice!=null){
                res.setCarDevice(CarDeviceVO.carDevice2CarDeviceVO(carDevice));
            }
        }
        return res;
    };

    public  Map<String,Object> Obj2Map(Object obj) throws Exception{
        Map<String,Object> map=new HashMap<String, Object>();
        String jsonstr = JSON.toJSONString(obj);
        map = JSON.parseObject(jsonstr, Map.class);
        return map;
    }

    @Override
    public CarDevices fetchCarDevices(String vin){
        // 设备
        CarDevicesExample carDevicesExample = new CarDevicesExample();
        carDevicesExample.createCriteria().andVinEqualTo(vin);
        List<CarDevices> carDevices = carDevicesMapper.selectByExample(carDevicesExample);

        if (carDevices.size() > 0) {
            return carDevices.get(0);
        } else {
            return null;
        }
    }

    @Override
    public CarDevices fetchCarDevicesBySn(String sn) {
        CarDevicesExample carDevicesExample = new CarDevicesExample();
        carDevicesExample.createCriteria().andSnEqualTo(sn);
        List<CarDevices> carDevices = carDevicesMapper.selectByExample(carDevicesExample);

        if (carDevices.size() > 0) {
            return carDevices.get(0);
        } else {
            return null;
        }
    }
}
