package com.sankuai.walle.rmanage.config.crane.datatransfer;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.eve.entity.VehicleBaseInfo;
import com.sankuai.walle.dal.eve.example.VehicleBaseInfoExample;
import com.sankuai.walle.dal.eve.mapper.VehicleBaseInfoMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.adapter.BasicAdapter;
import com.sankuai.walle.rmanage.config.adapter.DataReportAdapter;
import com.sankuai.walle.rmanage.config.dto.vehicleManage.CarManageDTO;
import com.sankuai.walle.rmanage.config.dto.vehicleManage.DataReportFormatDTO;
import com.sankuai.walle.rmanage.config.service.AssetService;
import com.sankuai.walle.rmanage.config.util.DateUtils;
import com.sankuai.walledelivery.basic.client.response.inner.deliverer.VehicleInfoResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@CraneConfiguration
public class AssetCraneTask {

    @Resource
    AssetService service;

    @Resource
    CarObjectsMapper carObjectsMapper;

    @Resource
    DataReportAdapter dataReportAdapter;

    @Resource
    private BasicAdapter basicAdapter;

    @Resource
    private VehicleBaseInfoMapper vehicleBaseInfoMapper;

    // basic查询车辆信息接口入参限制
    private static final Integer MAX_NUM_PER_BATCH = 100;

    @Crane("cmdb-remote-object-car-transfer")
    public void transferRemoteObjects() throws Exception {
        // 同步海鸥的车辆数据，到cmdb
        service.syncRemoteObjectFromOpenAsset();
        // 同步运力的场地数据到cmdb
//        service.asyncUpdateOperationData();
    }

    // 检查车管的vin是否符合规则，符合规则为sn前后去除-s，剩下的部分不应该包含特殊符号
    @Crane("cmdb-remote-object-check-vin-transfer")
    public void checkCarAssest() throws Exception {
        service.checkVin();
    }

    /**
     * 定时任务
     * 更新存量的资产责任人所属部门
     */
    @Crane("update_asset_owner_dept")
    public void updateAssetOwnerDept() {
        log.info("updateAssetOwnerDept start");
        service.batchUpdateAssetOwnerDepartment();
    }

    // 刷写 所有车辆的vin 到redis
    @Crane("cmdb-push-carobject-redis")
    public void transferCarObject() throws Exception {
        service.syncCarObject();
    }

    /**
     * 定时任务
     * 用于定时刷写全量的车管基础数据--总线
     */
    @Crane("vehicle_manage_data_report")
    public void reportVehicleManageData() {
        try {
            log.info("reportVehicleManageData start");
            // 1 查询全量数据
            List<DataReportFormatDTO> dataReportFormatDTOS = buildVehicleBaseInfoDataReportFormatDTOList();
            if (CollectionUtils.isEmpty(dataReportFormatDTOS)) {
                return;
            }
            // 2 上报
            dataReportAdapter.reportRealDataBatch(dataReportFormatDTOS, CommonConstants.CAR_MANAGE_BUSINESS_KEY);
        } catch (Exception e) {
            log.error("reportVehicleManageData error", e);
        }
    }

    /**
     * 获取全量车辆基础数据，并构建为上报格式
     * @return 车辆基础数据列表
     */
    private List<DataReportFormatDTO> buildVehicleBaseInfoDataReportFormatDTOList() {
        // 1 查询全量车辆信息
        List<CarObjects> vehicleInfos = carObjectsMapper.selectByExample(new CarObjectsExample());
        List<VehicleBaseInfo> vehicleBaseInfos = vehicleBaseInfoMapper.selectByExample(new VehicleBaseInfoExample());

        Map<String, VehicleBaseInfo> vin2VehicleBaseInfoMap = vehicleBaseInfos.stream()
                .collect(Collectors.toMap(VehicleBaseInfo::getVin, Function.identity()));

        // 2 分批查询车辆信息(每100辆车查询一次)
        List<String> vinList = vehicleInfos.stream().map(CarObjects::getVin).collect(Collectors.toList());
        List<VehicleInfoResponse> vehicleInfoResponses = new ArrayList<>();
        Lists.partition(vinList, MAX_NUM_PER_BATCH).forEach(subList -> {
            try {
                List<VehicleInfoResponse> response = basicAdapter.batchQueryByVinList(subList);
                if (!CollectionUtils.isEmpty(response)) {
                    vehicleInfoResponses.addAll(response);
                }
            } catch (Exception e) {
                log.error("Error querying VIN list: {}", JacksonUtils.to(subList), e);
            }
        });

        Map<String, VehicleInfoResponse> vin2VehicleInfoResponseMap = vehicleInfoResponses.stream()
                .collect(Collectors.toMap(VehicleInfoResponse::getIdentifyNum, Function.identity(), (o1, o2) -> o1));

        // 3 构建上报数据
        return vehicleInfos.stream().map(carObjects -> {
            if (StringUtil.isNullOrEmpty(carObjects.getVin())) {
                log.error("vin is not found , carObjects = {}", JacksonUtils.to(carObjects));
                return null;
            }
            VehicleBaseInfo vehicleBaseInfo = vin2VehicleBaseInfoMap.getOrDefault(carObjects.getVin(),
                    new VehicleBaseInfo());
            VehicleInfoResponse vehicleInfoResponse = vin2VehicleInfoResponseMap.getOrDefault(carObjects.getVin(),
                    new VehicleInfoResponse());

            String vehicleName = StringUtil.isNullOrEmpty(carObjects.getName()) ? CommonConstants.UNKNOWN
                    : carObjects.getName();
            String licenseNo = vehicleBaseInfo != null ? vehicleBaseInfo.getLicenseNo() : CommonConstants.UNKNOWN;
            String firstClassModel = vehicleBaseInfo != null ? vehicleBaseInfo.getFirstClassModel()
                    : CommonConstants.UNKNOWN;
            String secondClassModel = vehicleBaseInfo != null ? vehicleBaseInfo.getSecondClassModel()
                    : CommonConstants.UNKNOWN;
            Boolean scrap = vehicleBaseInfo != null ? vehicleBaseInfo.getScrap() : null;
            String placeName = vehicleInfoResponse.getBusinessStationCityAreaResponse() != null
                    ? vehicleInfoResponse.getBusinessStationCityAreaResponse().getPlaceName() : CommonConstants.UNKNOWN;
            String cityName = vehicleInfoResponse.getBusinessStationCityAreaResponse() != null
                    ? vehicleInfoResponse.getBusinessStationCityAreaResponse().getCityName() : CommonConstants.UNKNOWN;

            return DataReportFormatDTO.builder().vin(carObjects.getVin())
                    .data(CarManageDTO.builder()
                            .vehicleName(vehicleName).licenseNo(licenseNo).firstClassModel(firstClassModel)
                            .secondClassModel(secondClassModel).placeName(placeName).cityName(cityName)
                            .updateTime(DateUtils.getCurrentTime()).scrap(scrap).build())
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
