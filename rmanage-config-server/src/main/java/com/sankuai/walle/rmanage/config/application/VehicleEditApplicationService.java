package com.sankuai.walle.rmanage.config.application;

import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.objects.constants.CarDeviceTypeEnum;
import com.sankuai.walle.objects.vo.request.CarEditReq;
import com.sankuai.walle.rmanage.config.domain.VehicleEditDomain;
import com.sankuai.walle.rmanage.config.dto.VehicleEditResultDTO;
import com.sankuai.walle.rmanage.config.repository.VehicleEditRepository;
import com.sankuai.walle.rmanage.config.service.*;
import com.sankuai.walle.rmanage.config.util.SafeUserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 车辆编辑应用服务
 * 协调领域对象和仓储，处理业务用例
 */
@Service
@Slf4j
public class VehicleEditApplicationService {
    
    @Resource
    private VehicleEditRepository vehicleEditRepository;
    
    @Resource
    private VehicleInfoService vehicleInfoService;
    
    @Resource
    private VehicleModelManageService vehicleModelManageService;
    
    @Resource
    private ActionLogService actionLogService;
    
    @Resource
    private MafkaRealtimeService realtimeService;
    
    @Resource
    private VehicleRealtimeService vehicleRealtimeService;
    
    /**
     * 编辑车辆信息
     */
    @Transactional
    public VehicleEditResultDTO editVehicle(CarEditReq carEditReq) {
        try {
            // 创建领域对象
            VehicleEditDomain domain = VehicleEditDomain.create(carEditReq);
            
            // 获取编辑前的数据用于比较
            List<CarOperation> existingOperations = vehicleEditRepository.findCarOperationsByVin(domain.getVin());
            List<CarExecWord> existingCarExecWords = vehicleEditRepository.findCarExecWordsByVin(domain.getVin());
            
            // 处理车辆运营信息
            domain.processCarOperation(existingOperations);
            
            // 处理车辆附加信息
            domain.processCarExecWord(existingCarExecWords);
            
            // 执行数据持久化
            persistVehicleData(domain);
            
            // 处理关联业务
            handleRelatedBusiness(domain);
            
            // 记录详细的操作日志
            recordDetailedActionLog(domain, existingOperations, existingCarExecWords);
            
            log.info("车辆编辑成功, vin: {}", domain.getVin());
            
            return VehicleEditResultDTO.success(domain.getVin());
        } catch (Exception e) {
            log.error("车辆编辑失败, vin: {}", carEditReq.getCarObjects().getVin(), e);
            return VehicleEditResultDTO.failure(carEditReq.getCarObjects().getVin(), e.getMessage());
        }
    }
    
    /**
     * 执行数据持久化
     */
    private void persistVehicleData(VehicleEditDomain domain) {
        // 更新车辆对象
        vehicleEditRepository.updateCarObjects(domain.getCarObjects());
        
        // 处理车辆资产
        if (domain.shouldUpdateCarAssets()) {
            domain.prepareCarAssetsForUpdate();
            vehicleEditRepository.updateCarAssets(domain.getCarAssets());
        } else if (domain.getCarAssets().getId() == null) {
            domain.prepareCarAssetsForInsert();
            vehicleEditRepository.insertCarAssets(domain.getCarAssets());
        }
        
        // 处理车辆运营信息
        if (domain.getCarOperation().getId() == null) {
            vehicleEditRepository.insertCarOperation(domain.getCarOperation());
        } else {
            vehicleEditRepository.updateCarOperation(domain.getCarOperation());
        }
        
        // 处理车辆执行词
        if (domain.getCarExecWord().getId() == null) {
            vehicleEditRepository.insertCarExecWord(domain.getCarExecWord());
        } else {
            vehicleEditRepository.updateCarExecWord(domain.getCarExecWord());
        }
    }
    
    /**
     * 处理关联业务
     */
    private void handleRelatedBusiness(VehicleEditDomain domain) {
        // 关联车辆型号设备
        vehicleModelManageService.relateVehicleModelDevice(
            domain.getVin(), 
            domain.getDmsNumber(), 
            CarDeviceTypeEnum.DMS, 
            null
        );
        
        // 发送实时消息
        try {
            vehicleRealtimeService.sendRealTime(
                    domain.getVin(),
                    domain.getCarOperation(),
                    domain.getCarObjects().getName(),
                    domain.getCarType()
            );
        } catch (Exception e) {
            log.error("发送实时消息失败, vin: {}", domain.getVin(), e);
            throw new RuntimeException("发送实时消息失败", e);
        }
    }
    
    /**
     * 记录详细的操作日志
     */
    private void recordDetailedActionLog(VehicleEditDomain domain, List<CarOperation> existingOperations, List<CarExecWord> existingCarExecWords) {
        try {
            // 安全获取用户信息
            String userLogin = SafeUserUtils.getSafeUserLogin();
            
            // 记录操作日志
            actionLogService.insertCarManageActionLog(domain.getVin(), userLogin, "编辑车辆");
            
        } catch (Exception e) {
            log.error("记录操作日志失败, vin: {}", domain.getVin(), e);
            // 记录操作日志失败不应该影响主业务流程
        }
    }
} 