package com.sankuai.walle.rmanage.config.service.appService;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;

import java.io.IOException;
import java.util.List;

public interface ConfigStoreService {
    ObjectMetadata getMetaData(String objectName);

    List<S3ObjectSummary> listObjects(String objectName);

    void writeFileToS3(String objectName, String content);

    void deleteFileFromS3(String objectName);

    void updateVersion() throws IOException;

    // 修改version版本
    void updateVersion(String carName) throws IOException;

    // 获取S3对应文件的内容
    String getFileContent(String objectName) throws IOException;

    String getObjectAsString(String objectName) throws IOException;
}
