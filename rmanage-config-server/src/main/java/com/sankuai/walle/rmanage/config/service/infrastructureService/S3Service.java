package com.sankuai.walle.rmanage.config.service.infrastructureService;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;

import java.io.IOException;
import java.util.List;

public interface S3Service {

    void pushFileToS3(String bucketName, String objectName, byte[] input);

    // 查询文件列表，看目标目录，以及目标文件是否存在
    List<S3ObjectSummary> listObjects(String bucketName, String prefix);

    // 查询文件列表，看目标目录，以及目标文件是否存在
    ObjectMetadata getMetaData(String bucketName, String objectName);

    // 写入S3
    void writeFileToS3(String bucketName, String objectName, String content);

    // 获取object
    String getObjectAsString(String bucketName, String objectName) throws IOException;

    // 删除
    void deleteObjectExample(String bucketName, String objectName);
}
