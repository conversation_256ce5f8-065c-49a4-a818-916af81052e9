package com.sankuai.walle.rmanage.config.adapter;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.rmanage.config.constant.CharConstant;
import com.sankuai.walle.rmanage.config.constant.RgOncallTypeEnum;
import com.sankuai.walle.rmanage.config.dto.accident.RGOncallUserDTO;
import com.sankuai.walle.rmanage.config.service.impl.TTService;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import com.sankuai.walle.rmanage.config.vto.RgId2RoleNameVTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class TTOncallUserAdapter {

    /**
     * 获取值班组值班人员失败后，兜底消息发送模版
     */
    private static final String ALERT_MSG = "未获取到%s排班，请联系相关同学进事故群oncall";

    /**
     * 兜底消息发送uri
     */
    private static final String ALERT_MSG_SEND_URI = "eve/output/rest/push-message/uids";

    @Value("${evehost}")
    private String eveHost;

    @Resource
    private TTService ttService;

    // 值班组对应值班人员需要设置的角色名列表
    @MdpConfig("accident.tt.rgid.list:[]")
    private ArrayList<RgId2RoleNameVTO> rgId2RoleNameList = new ArrayList<>();

    // 兜底消息通知人
    @MdpConfig("get.duty.failure.notification.list:[]")
    private ArrayList<String> alertReceivers = new ArrayList<>();

    /**
     * 根据指定TT排班RG分组ID(RE排班) 查询对应的当前值班人信息，并且在值班列表信息为空时进行告警
     *
     * @param
     * @return
     */
    public List<RGOncallUserDTO> getTTOncallUserList(List<Long> alertMsgList) {
        Map<Long, List<String>> rgId2MisIdListMap = new HashMap<>();
        // 确保rgId2RoleNameList不为空
        rgId2RoleNameList.stream().filter(Objects::nonNull)
                .forEach(rgId2RoleNameVTO -> processRgId(rgId2RoleNameVTO.getRgId(), rgId2MisIdListMap, alertMsgList));

        List<RGOncallUserDTO> rgOncallUserDTOList = rgId2MisIdListMap.entrySet().stream()
                .map(entry -> new RGOncallUserDTO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        log.info("查询值班列表结果: {}", JacksonUtils.to(rgOncallUserDTOList));
        return rgOncallUserDTOList;
    }

    /**
     * 处理每个RgId 对应的值班组类型，如果是 新值班，或者 值班组类型、值班人 获取不到，则进行兜底处理
     *
     * @param rgId
     * @param rgId2MisIdListMap
     */
    private void processRgId(Long rgId, Map<Long, List<String>> rgId2MisIdListMap, List<Long> alertMsgList) {
        boolean isNeedFallback = false;
        try {
            RgOncallTypeEnum rgOncallTypeEnum = ttService.getRgOncallType(rgId);
            if (RgOncallTypeEnum.isNeedFallback(rgOncallTypeEnum)) {
                log.error("未获取到值班类型，需要兜底, rgId={}", rgId);
                isNeedFallback = true;
            } else {
                List<String> misIdList = ttService.getRgOncallUserMisIdList(rgId, rgOncallTypeEnum);
                if (CollectionUtils.isEmpty(misIdList)) {
                    log.info("查询值班列表为空，需要兜底, rgId={}", rgId);
                    isNeedFallback = true;
                } else {
                    rgId2MisIdListMap.computeIfAbsent(rgId, k -> new ArrayList<>()).addAll(misIdList);
                }
            }
        } catch (Exception e) {
            log.error("处理错误，需要兜底, rgId" + rgId, e);
            isNeedFallback = true;
        }

        // 如果不需要兜底，则直接返回，不进行后续操作。
        if (!isNeedFallback) {
            return;
        }
        //  将rgId添加到告警消息列表中。
        alertMsgList.add(rgId);
        try {
            // 使用兜底接口，获取指定rgId的值班人员MisId列表
            List<String> fallbackMisIdList = ttService.fallbackRgOncallUserMisIdList(rgId);
            if (CollectionUtils.isNotEmpty(fallbackMisIdList)) {
                rgId2MisIdListMap.computeIfAbsent(rgId, k -> new ArrayList<>()).addAll(fallbackMisIdList);
            }
        } catch (Exception e) {
            log.error("进入兜底仍然错误, rgId" + rgId, e);
        }
    }

    /**
     * 根据失败的排班组rgId，发送给兜底通知人，例如：未获取到%s,%s,%s排班，请联系相关同学进事故群oncall
     *
     * @param failedRgIdList
     */
    public void sendAlertMsg(List<Long> failedRgIdList) {
        if (CollectionUtils.isEmpty(failedRgIdList)) {
            return;
        }

        // 从lion配置 获取TT排班 rgId -> rgLinker
        Map<Long, String> rgId2RgLinkerMap = rgId2RoleNameList.stream()
                .filter(Objects::nonNull)
                .filter(rgId2RoleNameVTO -> rgId2RoleNameVTO.getRgId() != null &&
                        StringUtils.isNotBlank(rgId2RoleNameVTO.getRgNameLinker()))
                .collect(Collectors.toMap(RgId2RoleNameVTO::getRgId,
                        RgId2RoleNameVTO::getRgNameLinker, (v1, v2) -> v2));

        // 拿到需要发送的失败信息
        List<String> failedRgMsgList = new ArrayList<>();
        failedRgIdList.forEach(rgId -> {
            String rgNameLinker = rgId2RgLinkerMap.get(rgId);
            if (StringUtils.isEmpty(rgNameLinker)) {
                log.error("lion配置rgId={}对应的rgNameLinker有误", rgId);
                return;
            }
            failedRgMsgList.add(rgNameLinker);
        });

        String url = eveHost + ALERT_MSG_SEND_URI;

        // 拼接每一个超链接，超链接之间使用,分隔
        String content = String.join(CharConstant.CHAR_DD, failedRgMsgList);
        String msg = String.format(ALERT_MSG, content);

        // 拼接请求体body
        Map<String, Object> map = new HashMap<>();
        Map<String, String> text = new HashMap<>();
        text.put("text", msg);
        map.put("messageType", "text");
        map.put("bodyJson", JacksonUtils.to(text));
        map.put("extension", "");
        map.put("misList", alertReceivers);
        String jsonBody = JacksonUtils.to(map);

        // 发送POST请求
        String response = CommonUtil.doPost(url, jsonBody, null);

        log.info("sendAlertMsg request={},response={}", jsonBody, response);
    }
}