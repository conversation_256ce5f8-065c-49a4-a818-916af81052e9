package com.sankuai.walle.rmanage.config.geteway;

import com.alibaba.fastjson.JSON;
import com.sankuai.walle.rmanage.config.geteway.req.ReCmdbReq;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import com.sankuai.walledelivery.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class REApi {

    @Value("${re.host}")
    String host;

    String RE_URL = "/alert_data_bus/api/v1/alert_callback/event_report";


    public String sendConfigChangeMsg(ReCmdbReq reCmdbReq) {
        String url = host + RE_URL;
        String req = JSON.toJSONString(reCmdbReq);
        log.info("sendConfigChangeMsg req:{}", req);
        String res = CommonUtil.doPost(url, req, null);
        log.info("sendConfigChangeMsg res:{}", res);
        return res;
    }

}
