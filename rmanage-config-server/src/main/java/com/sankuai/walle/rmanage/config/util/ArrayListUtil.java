package com.sankuai.walle.rmanage.config.util;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;



public abstract class ArrayListUtil {

    public static List retainAll(Collection<?> b, Collection<?> c) {
        Objects.requireNonNull(c);
        boolean complement = true;
        final Object[] elementData = b.toArray();
        int size = elementData.length;
        int r = 0, w = 0;
        boolean modified = false;
        try {
            for (; r < size; r++)
                if (c.contains(elementData[r]) == complement)
                    elementData[w++] = elementData[r];
        } finally {
            // Preserve behavioral compatibility with AbstractCollection,
            // even if c.contains() throws.
            if (r != size) {
                System.arraycopy(elementData, r,
                        elementData, w,
                        size - r);
                w += size - r;
            }
            if (w != size) {
                // clear to let GC do its work
                for (int i = w; i < size; i++)
                    elementData[i] = null;
                size = w;
                modified = true;
            }
        }
        return Arrays.asList(elementData);
    }
}
