package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.dal.eve.entity.CarTypes;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.objects.vo.VehicleModelInfoVO;
import com.sankuai.walle.objects.vo.DeviceCategoryVO;
import com.sankuai.walle.objects.vo.DeviceInfoVO;
import com.sankuai.walle.objects.vo.request.VehicleModelCreateRequest;
import com.sankuai.walle.objects.vo.request.DeviceTypeCreateRequest;
import com.sankuai.walle.objects.vo.request.DeviceTypeQueryRequest;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.dto.vehicleManage.RemoteCarTypeDTO;

import java.util.List;

public interface TypeTreeService {

    public List<RemoteCarTypeDTO> getTypeTree();

    public CarTypes addCarType(RemoteCarTypeDTO remoteCarTypeDTO);

    ResData addDevice(DeviceTypeCreateRequest request, String misId);

    void updateDevice(DeviceTypes deviceType);

    void deleteDevice(Long id);

    List<DeviceInfoVO> queryDeviceByDeviceTypeQueryRequest(DeviceTypeQueryRequest request);

    List<DeviceCategoryVO> getDeviceCategoryConfig();

    ResData addCarType(VehicleModelCreateRequest request, String misId);

    List<VehicleModelInfoVO> getCarTypeTree();
}