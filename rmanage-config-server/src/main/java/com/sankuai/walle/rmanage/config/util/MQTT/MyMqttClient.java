package com.sankuai.walle.rmanage.config.util.MQTT;




import com.sankuai.walle.rmanage.config.config.MqttConfig;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;


public class MyMqttClient  {

    // 开源的mqtt下发
    public static void sendMsg(String content,String topic) throws MqttException {
//        String content  = "主题内容xxxxxxxxxxx"; //发送的信息
//        String topic   = "testtopic";    //主题
        String clientId = "configManager"; //客户ID，自己起名，不可重复
        int qos         = 2;    //服务质量等级
        String broker = MqttConfig.broker; //服务器地址
        MqttClient client = new MqttClient(broker, clientId, new MemoryPersistence());//创建客户类
        MqttConnectOptions connect = new MqttConnectOptions();//创建连接类
        connect.setConnectionTimeout(60);//超时时间
        connect.setKeepAliveInterval(10);//心跳时间
        connect.setCleanSession(false);//清除会话
        client.connect(connect);//客户类通过连接类连接
        MqttMessage message = new MqttMessage(content.getBytes());//发布消息类
        message.setQos(qos);//发布消息类设置服务质量等级
        client.publish(topic, message);//发布消息
//        client.disconnect();//客户类关闭连接
//        System.exit(0);//退出
    }

    public static void getMsg() throws MqttException {
        String clientId = "JavaSample2"; //客户ID，不可重复
        String topic   = "LMTZSV023NC092853"+MqttConfig.topicMark;    //主题
        int qos         = 2;    //服务质量等级
//        String broker  = "tcp://test.ranye-iot.net:1883";  //服务器地址
        String broker = MqttConfig.broker;
        MqttClient client = new MqttClient(broker, clientId, new MemoryPersistence());//创建客户累
        client.setCallback(new MyMqttCallBack());
        MqttConnectOptions connect = new MqttConnectOptions();//创建连接类
        connect.setConnectionTimeout(60);//超时时间
        connect.setKeepAliveInterval(10);//心跳时间
        connect.setCleanSession(false);//清除会话
        client.connect(connect);//客户类通过连接类连接
        client.subscribe(topic, qos);// qos：服务质量等级
    }

    public static void getStatus() throws MqttException {
//        String broker  = "tcp://test.ranye-iot.net:1883";  //服务器地址
        String clientId = "mqttStatusClient"; //客户ID，不可重复
        String topic   = "$SYS/NOTICE/STATUS/#";    //主题
        int qos         = 2;    //服务质量等级
        String broker = MqttConfig.broker;
        MqttClient client = new MqttClient(broker, clientId, new MemoryPersistence());//创建客户类
        client.setCallback(new MyMqttStatusCallBack());
        MqttConnectOptions connect = new MqttConnectOptions();//创建连接类
        connect.setConnectionTimeout(60);//超时时间
        connect.setKeepAliveInterval(10);//心跳时间
        connect.setCleanSession(false);//清除会话
        client.connect(connect);//客户类通过连接类连接
        client.subscribe(topic, qos);// qos：服务质量等级
//        client.subscribe("LMTZSV023NC092853"+MqttConfig.topicMark,2);
    }

    public static void main(String[] args) {
        try {
            getMsg();
        } catch (MqttException e) {
            throw new RuntimeException(e);
        }
    }



}

