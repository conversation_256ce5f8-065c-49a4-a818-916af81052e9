package com.sankuai.walle.rmanage.config.application;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import com.sankuai.walle.objects.vo.request.ImportInsuranceReq;
import com.sankuai.walle.rmanage.config.domain.InsuranceImportDomain;
import com.sankuai.walle.rmanage.config.dto.InsuranceImportResultDTO;
import com.sankuai.walle.rmanage.config.repository.InsuranceImportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 保险导入应用服务
 * 协调领域对象和仓储，处理业务用例
 */
@Service
@Slf4j
public class InsuranceImportApplicationService {
    
    @Resource
    private InsuranceImportRepository insuranceImportRepository;
    
    /**
     * 批量导入保险信息
     */
    @Transactional
    public InsuranceImportResultDTO importInsuranceBatch(List<ImportInsuranceReq> insuranceReqs) {
        List<String> successVins = new ArrayList<>();
        List<String> notFoundVins = new ArrayList<>();
        List<String> errorVins = new ArrayList<>();
        
        for (ImportInsuranceReq insuranceReq : insuranceReqs) {
            String vin = insuranceReq.getVin();
            try {
                // 创建领域对象
                InsuranceImportDomain domain = InsuranceImportDomain.create(
                    vin, 
                    insuranceReq.getLicenseno(), 
                    insuranceReq.getAssetExecWord()
                );
                
                // 验证车辆是否存在
                List<CarObjects> carObjects = insuranceImportRepository.findCarObjectsByVin(vin);
                if (!domain.isVehicleExists(carObjects)) {
                    notFoundVins.add(vin);
                    log.warn("车辆不存在，无法导入保险信息, vin: {}", vin);
                    continue;
                }
                
                // 查找资产记录
                List<CarAssets> carAssetsList = insuranceImportRepository.findCarAssetsByVin(vin);
                if (!domain.selectTargetAsset(carAssetsList)) {
                    notFoundVins.add(vin);
                    log.warn("未找到有效的车辆资产记录, vin: {}", vin);
                    continue;
                }
                
                // 处理保险信息和车牌号更新
                processInsuranceInfo(domain);
                successVins.add(vin);
                
            } catch (Exception e) {
                errorVins.add(vin);
                log.error("导入保险信息失败, vin: {}", vin, e);
            }
        }
        
        // 构建返回结果
        return InsuranceImportResultDTO.builder()
                .successCount(successVins.size())
                .notFoundCount(notFoundVins.size())
                .errorCount(errorVins.size())
                .successVins(successVins)
                .notFoundVins(notFoundVins)
                .errorVins(errorVins)
                .build();
    }
    
    /**
     * 处理单个车辆的保险信息
     */
    private void processInsuranceInfo(InsuranceImportDomain domain) {
        // 更新车牌号（如果提供）
        domain.updateCarObjectsLicenseNo();
        if (domain.getCarObjects() != null) {
            insuranceImportRepository.updateCarObjects(domain.getCarObjects());
        }
        
        // 查找现有的CarExecWord记录（通过VIN）
        List<CarExecWord> existingCarExecWordsByVin = insuranceImportRepository.findCarExecWordByVin(domain.getVin());
        
        // 处理CarExecWord记录
        CarExecWord processedCarExecWord = domain.processCarExecWord(null, existingCarExecWordsByVin);
        
        // 根据是否有id决定是新增还是更新
        if (processedCarExecWord.getId() == null) {
            // 新增记录
            insuranceImportRepository.saveCarExecWord(processedCarExecWord);
            log.info("创建新的CarExecWord记录, vin: {}", domain.getVin());
        } else {
            // 更新记录
            insuranceImportRepository.updateCarExecWord(processedCarExecWord);
            log.info("更新现有的CarExecWord记录, id: {}, vin: {}", processedCarExecWord.getId(), domain.getVin());
        }
        
        log.info("保险信息和车牌号处理成功, vin: {}", domain.getVin());
    }
} 