package com.sankuai.walle.rmanage.config.util.MQTT;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.nio.charset.StandardCharsets;

public class MyMqttCallBack implements MqttCallback {
    //连接丢失时触发（不包括主动disconnect）
    @Override
    public void connectionLost(Throwable throwable) {
        System.out.println("连接失败，请重新连接");
    }
    //接收到内容触发
    @Override
    public void messageArrived(String s, MqttMessage mqttMessage) throws Exception {
        System.out.println("接收到的主题为：" + s);
        System.out.println("内容为：" + new String(mqttMessage.getPayload(), StandardCharsets.UTF_8));
    }
    //发布完消息触发
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        System.out.println("发布消息成功");
    }

}
