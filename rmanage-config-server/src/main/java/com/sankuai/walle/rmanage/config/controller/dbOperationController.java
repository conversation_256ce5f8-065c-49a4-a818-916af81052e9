package com.sankuai.walle.rmanage.config.controller;

import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.DbOperationService;
import lombok.extern.log4j.Log4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/eve/cmdb/rest")
@Log4j
public class dbOperationController {

    @Resource
    DbOperationService dbOperationService;

    @GetMapping("/update/ba")
    public ResData updateAndInsert(int type){
        //type 0:表示读入外部数据插入且更新
        //type 1:表示使用内部数据更新
        //type 2:
        //type 3:查询需要给定数据中待插入的数量

        ResData resData = new ResData();
        List<String>  data = new ArrayList<>();
        switch(type){
            case 0:
                data = dbOperationService.updateAndInsertDbByOut();
                resData.setData(data);
                resData.setRows((long) data.size());
                break;
            case 1:
                data = dbOperationService.updateDbByIn();
                resData.setData(data);
                resData.setRows((long) data.size());
                break;
            case 2:
                data = dbOperationService.updateDbByOut();
                resData.setData(data);
                resData.setRows((long) data.size());
                break;
            case 3:
                data =  dbOperationService.checkInsertNum();
                resData.setData(data);
                resData.setRows((long) data.size());
                break;
            default:
                break;
        }

        return resData;


    }
}
