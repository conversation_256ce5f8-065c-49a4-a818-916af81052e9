package com.sankuai.walle.rmanage.config.exception;

public class DiskManagementException extends RuntimeException {

    private int code = -1;

    public DiskManagementException() {
        super();
    }

    public DiskManagementException(final String message) {
        super(message);
    }

    public DiskManagementException(final Throwable t) {
        super(t);
    }


    public DiskManagementException(final String message, final Throwable cause) {
        super(message, cause);
    }

    public DiskManagementException(final int code, final String message) {
        super(message);
        this.code = code;
    }

    public DiskManagementException(final int code, final String message, final Throwable cause) {
        super(message, cause);
        this.code = code;
    }

}
