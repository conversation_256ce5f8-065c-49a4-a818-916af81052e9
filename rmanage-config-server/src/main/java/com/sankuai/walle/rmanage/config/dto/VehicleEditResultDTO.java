package com.sankuai.walle.rmanage.config.dto;

import lombok.Data;

/**
 * 车辆编辑结果DTO
 */
@Data
public class VehicleEditResultDTO {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 车辆VIN
     */
    private String vin;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 构造函数
     */
    public VehicleEditResultDTO(boolean success, String vin, String errorMessage) {
        this.success = success;
        this.vin = vin;
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建成功结果
     */
    public static VehicleEditResultDTO success(String vin) {
        return new VehicleEditResultDTO(true, vin, null);
    }
    
    /**
     * 创建失败结果
     */
    public static VehicleEditResultDTO failure(String vin, String errorMessage) {
        return new VehicleEditResultDTO(false, vin, errorMessage);
    }
} 