package com.sankuai.walle.rmanage.config.controller;


import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import lombok.extern.log4j.Log4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping(path = {"/eve/cmdb/rest/auk"})
@Log4j
public class AukRequestController {


    @Resource
    ConfigService configService;

    @RequestMapping(path = {"/send/query/send"}, method = RequestMethod.GET)
    public ResData sendAukQuery(@RequestParam List<String> vins,String configName) throws Exception {
        if (vins==null || vins.isEmpty()) {
            return new ResData(){{setData("vin不能为空");setCode(CommonConstants.ERROR_CODE);setMsg("Bad Request");}};
        }
        HashMap<String,String> res = configService.queryConfigSync(vins,configName);
        long a = System.currentTimeMillis();
        configService.queryConfigAsync(vins);
        long b = System.currentTimeMillis();
        log.info("异步耗时："+(b-a));
        return ResData.successWithData(res);
    }

    // 读取配置回捞的结果列表
    @RequestMapping(path = {"/config/query/list"}, method = RequestMethod.GET)
    public ResData getConfigQueryList(@RequestParam int page,@RequestParam int pageSize,@RequestParam(required = false) List<String> vins
        ,@RequestParam(required = false) String configName
    ){
        return configService.configQueryList(page, pageSize, vins, configName);
    }

}
