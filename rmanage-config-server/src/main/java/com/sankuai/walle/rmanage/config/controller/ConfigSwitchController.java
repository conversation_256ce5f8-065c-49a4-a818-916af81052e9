package com.sankuai.walle.rmanage.config.controller;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.ResData;
import lombok.extern.log4j.Log4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@RestController
@RequestMapping("/eve/cmdb/rest/switch")
@Log4j
public class ConfigSwitchController {

    @MdpConfig("vehicle_switch_controles")
    private HashMap<String,Object> vehicleSwitchControles;

    @GetMapping("/controles")
    public ResData updateAndInsert(){
        // 获取开关项
        return ResData.successWithData(vehicleSwitchControles);
    }
}
