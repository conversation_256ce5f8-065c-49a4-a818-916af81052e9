package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walle.rmanage.config.service.AccidentVideoService;
import com.sankuai.walle.rmanage.config.thread.req.VideoReponse;
import com.sankuai.walle.rmanage.config.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class AccidentVideoServiceImpl implements AccidentVideoService,Serializable {

    @MdpConfig("sc.video.hostname")
    String HostName;
    @MdpConfig("sc.videoba.clientId")
    String clientId;
    @MdpConfig("sc.videoba.clientSecret")
    String clientSecret;
    @MdpConfig("sc.video.timeGap")
    private ArrayList<Integer> timeGap;

    @MdpConfig("check.group.video.timeGap")
    private ArrayList<Integer> checkGroupVideoTimeGap;

    private static final String path = "/autonomous-driving/rmanage/stream";
    @Override
    public void getAccidentVideo(Long accidentTime, String vin, List<String> position){

        Map<String, Object> param = new HashMap<>();
        param.put("vin",  vin);
        param.put("start_ts",accidentTime - timeGap.get(0));
        param.put("end_ts", accidentTime  + timeGap.get(1) );
        param.put("position", position );
        param.put("immediateRetrieve", true);

        String url = HostName + path;
        try {
            //Ba,设置请求头参数
            Map<String, String> headers = new HashMap<>();
            String authorization = CommonUtil.getAuthorization(path, new HttpPost().getMethod(), CommonUtil.getDate(), clientId, clientSecret);
            headers.put("Date", CommonUtil.getDate());
            headers.put("Authorization", authorization);
            String response = CommonUtil.doPost(url, param, headers);

            Gson gson = new Gson();
            JsonObject responseJson = gson.fromJson(response, JsonObject.class);
            if (responseJson.has("data")) {
                JsonElement dataElement = responseJson.get("data");
                if (!dataElement.isJsonNull()) {
                    VideoReponse videoResponse = gson.fromJson(dataElement, VideoReponse.class);
                    log.info("request video, vin = {}, videoResponse = {}", vin, videoResponse);
                }
            }
        } catch (Exception e) {
            log.error("getHDVideo is failed!", e);
        }

    }

    @Override
    public VideoReponse getAccidentVideoResponse(Long accidentTime, String vin, List<String> position) {
        Long startTime = accidentTime - timeGap.get(0);
        Long endTime   = accidentTime  + timeGap.get(1);
        VideoReponse res = getHDVideoResponse(startTime, endTime, vin, position, true);
        return res;
    }

    @Override
    public int getCollisionDetectVideoResponse(Long accidentTime, String vin) {
       //对于环视视频取（-15，30）， 高清环视是（-15，20）

        Long loopStartTime = accidentTime - checkGroupVideoTimeGap.get(0);
        Long loopEndTime   = accidentTime  + checkGroupVideoTimeGap.get(1);
        List<String> position = Arrays.asList("loop", "v_loop");

        List<String> finishFlag = new ArrayList<>();

        VideoReponse loopVideoReponse = getHDVideoResponse(loopStartTime, loopEndTime, vin, position, false);
        if(loopVideoReponse != null) {
            if(loopVideoReponse.getLoop() != null ){
                finishFlag.add("loop");
            }
            else if( loopVideoReponse.getStatus() == 3 || loopVideoReponse.getStatus_desc().equals("task in progress vehicle offline, task has been sent to vehicle-end ")){
                return -1;  //表示任务失败
            }
        }

        Long concatStartTime = accidentTime - checkGroupVideoTimeGap.get(2);
        Long concatEndTime   = accidentTime  + checkGroupVideoTimeGap.get(3);
        List<String> concatPosition = Arrays.asList("v_concat");

        VideoReponse concatVideoReponse = getHDVideoResponse(concatStartTime, concatEndTime, vin, concatPosition, true );
        if(concatVideoReponse != null) {
            if(concatVideoReponse.getV_concat() != null ){
                finishFlag.add("v_loop");
            }
            else if( concatVideoReponse.getStatus() == 3 || concatVideoReponse.getStatus_desc().equals("task in progress vehicle offline, task has been sent to vehicle-end ")){
                return -1;  //表示任务失败
            }

        }

        if(finishFlag.size() == 2){
            return 1;   //表示打捞成功
        }
        return  0;  //表示正在打捞中
    }

    public VideoReponse getHDVideoResponse(Long startTime, Long endTime, String vin, List<String> position, Boolean immediateRetrieve) {
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("vin",  vin);
        requestParam.put("start_ts", startTime);
        requestParam.put("end_ts", endTime );
        requestParam.put("position", position );
        requestParam.put("immediateRetrieve", immediateRetrieve);


        String url = HostName + path;
        VideoReponse videoReponse = new VideoReponse();
        try {
            //Ba,设置请求头参数
            Map<String, String> headers = new HashMap<>();
            String authorization = CommonUtil.getAuthorization(path, new HttpPost().getMethod(), CommonUtil.getDate(), clientId, clientSecret);
            headers.put("Date", CommonUtil.getDate());
            headers.put("Authorization", authorization);
            String response = CommonUtil.doPost(url, requestParam, headers);

            JSONObject responseJson = JSONObject.parseObject(response);
            Gson gson = new Gson();
            videoReponse = gson.fromJson(responseJson.getString("data"), VideoReponse.class);
            log.info("getHDVideo, requestParam = {}, responseParam = {}", requestParam, videoReponse);
            return videoReponse;
        } catch (Exception e) {
            log.error("getHDVideo is failed! requestParam = {}",requestParam);
        }
        return null;
    }

}
