package com.sankuai.walle.rmanage.config.thread;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.walle.rmanage.config.thread.dto.QueueParamDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RedisProductor {

    @Resource(name = "ThreadRedisBean")
    RedisStoreClient redisStoreClient;
    public void sendDateToRedisQueue(String category, String vin, Long accidentTime, String loopVideoMsg,String concatVideoMsg, Long groupId, String key ){
        StoreKey storeKey = new StoreKey(category, key);
        QueueParamDTO queueParamDTO = new QueueParamDTO();
        queueParamDTO.setVin(vin);
        queueParamDTO.setAccidentTime(accidentTime);
        queueParamDTO.setLoopVideoMsg(loopVideoMsg);
        queueParamDTO.setConcatVideoMsg(concatVideoMsg);
        queueParamDTO.setGroupId(groupId);
        redisStoreClient.rpush(storeKey, queueParamDTO);
    }
}
