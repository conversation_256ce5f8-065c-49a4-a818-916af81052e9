package com.sankuai.walle.rmanage.config.config;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meituan.org.opensdk.client.RemoteServiceClient;
import com.sankuai.meituan.org.opensdk.service.*;
import com.sankuai.meituan.org.opensdk.service.impl.*;
import com.sankuai.meituan.org.queryservice.domain.param.DataScope;
import com.sankuai.walle.objects.constants.CommonConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;


@Configuration
public class ORGOpenSdkConfig {
    private final static String REMOTE_AK = "com.sankuai.it.bsi.mdmgatewayservice";
    //TODO 替换成你的appkey（申请ORG资源后，返回的应用编码。）

    @MdpConfig(CommonConstants.cmdbOrgAppKeyConfig)
    private volatile static String CLIENT_AK;
    //TODO 不再使用，默认空字符即可。
    private final static String APP_SC = "";
    private final static Integer APP_TENANT_ID = 1;//如需查询其它租户数据，请设置成其它租户ID
    private final static String APP_SOURCE = "ALL";//设置租户下对应的source.如果设置为"ALL"，则查tenantId下所有sources。


    @Bean
    public RemoteServiceClient remoteServiceClient() throws Exception {

        // 设置App默认的数据访问范围。如下设置，App默认所有的请求是针对美团租户下“MT”数据域的ORG数据
        DataScope dataScope = new DataScope();
        dataScope.setTenantId(APP_TENANT_ID);
        dataScope.setSources(Arrays.asList(APP_SOURCE));

        RemoteServiceClient remoteServiceClient = new RemoteServiceClient(CLIENT_AK, APP_SC, REMOTE_AK, dataScope);
        return remoteServiceClient;
    }

    @Bean
    public CompService compService(RemoteServiceClient remoteServiceClient) {
        CompServiceImpl compService = new CompServiceImpl(remoteServiceClient);
        return compService;
    }

    @Bean
    public DictService dictService(RemoteServiceClient remoteServiceClient) {
        DictServiceImpl dictService = new DictServiceImpl(remoteServiceClient);
        return dictService;
    }

    @Bean
    public EmpService empService(RemoteServiceClient remoteServiceClient) {
        EmpServiceImpl empService = new EmpServiceImpl(remoteServiceClient);
        return empService;
    }

    @Bean
    public JobCodeService jobCodeService(RemoteServiceClient remoteServiceClient) {
        JobCodeServiceImpl jobCodeService = new JobCodeServiceImpl(remoteServiceClient);
        return jobCodeService;
    }

    @Bean
    public OrgService orgService(RemoteServiceClient remoteServiceClient) {
        OrgServiceImpl orgService = new OrgServiceImpl(remoteServiceClient);
        return orgService;
    }

    @Bean
    public SiteCodeService siteCodeService(RemoteServiceClient remoteServiceClient) {
        SiteCodeServiceImpl siteCodeService = new SiteCodeServiceImpl(remoteServiceClient);
        return siteCodeService;
    }
}