package com.sankuai.walle.rmanage.config.thrift;


import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.walle.cmdb.thrift.model.ResponseCommon;
import com.sankuai.walle.cmdb.thrift.service.CarDeviceModel;
import com.sankuai.walle.cmdb.thrift.service.CarDevicesThriftService;
import com.sankuai.walle.dal.eve.entity.CarDevices;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.rmanage.config.convertor.VehicleModelMapper;
import com.sankuai.walle.rmanage.config.service.VechicleService;
import com.sankuai.walle.rmanage.config.thrift.model.CarDeviceDTO;
import com.sankuai.walle.rmanage.config.thrift.model.ThriftResopnseCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TDeserializer;
import org.apache.thrift.TException;
import org.apache.thrift.TSerializer;
import org.apache.thrift.protocol.TCompactProtocol;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

@MdpThriftServer(
        serviceInterface = CarDevicesThriftService.Iface.class,
        useSinglePortMultiServiceMode = true,
        port = 9001
)
@Slf4j
public class CarDevicesThriftServiceImpl implements CarDevicesThriftService.Iface {

    @Resource
    VechicleService vechicleService;


    public ThriftResopnseCommon queryCarDeviceConfig(String vin) {
        ThriftResopnseCommon rep = new ThriftResopnseCommon();
        CarDevices carDevice = vechicleService.fetchCarDevices(vin);
        CarDeviceDTO res = null;
        if (carDevice == null) {
             res = CarDeviceDTO.builder().id(carDevice.getId()).sn(carDevice.getSn()).vin(carDevice.getVin()).type(carDevice.getType()).build();
             rep.setCode(CommonConstants.SUCCEED_CODE);
        }
         rep.setData(res);
        return rep;
    }



    @Override
    public ResponseCommon queryCarDevicesBySn(String sn) throws TException {
        ResponseCommon rep = new ResponseCommon();
        CarDevices carDevice = vechicleService.fetchCarDevicesBySn(sn);

        String data = null;
        if (carDevice != null ) {
            CarDeviceModel dto = VehicleModelMapper.MAPPER.carDeviceToThriftModel(carDevice);
            rep.setCode(CommonConstants.SUCCEED_CODE);
            TSerializer serializer = new TSerializer(new TCompactProtocol.Factory());
            data = serializer.toString(dto,"UTF-8");
        }
        rep.setData(data);
        return rep;
    }

    public static void main(String[] args)   {
        String data = "\u0016\u0004\u0018\u0003DMS\u0018\u0011LMTZSV016MC000107\u0018\u0003123\u0000";
        CarDeviceModel carDeviceModel = new CarDeviceModel();
        TDeserializer deserializer = new TDeserializer(new TCompactProtocol.Factory());
        try {
            deserializer.deserialize(carDeviceModel, data.getBytes("UTF-8"));
        } catch (TException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        System.out.println(carDeviceModel);
    }
}
