package com.sankuai.walle.rmanage.config.service.impl;

import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjectTags;
import com.sankuai.walle.dal.mrm_manage.entity.RemoteObjects;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectTagsExample;
import com.sankuai.walle.dal.mrm_manage.example.RemoteObjectsExample;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectTagsMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectsMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CarTypeService {

    @Resource
    RemoteObjectsMapper remoteObjectsMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Resource
    RemoteObjectTagsMapper remoteObjectTagsMapper;


    public List<String> fetchFirstTypeCars(String typeId){
        if (StringUtils.isBlank(typeId)){
            return new ArrayList<>();
        }
        CarObjectsExample example = new CarObjectsExample();
        example.createCriteria().andCarTypeEqualTo(typeId);
        List<CarObjects> firstCars = carObjectsMapper.selectByExample(example);
        return firstCars.stream().map(CarObjects::getVin).collect(Collectors.toList());
    }



    public List<String> fetchSecondTypeCars(Long typeId){
        if (typeId == null){
            return new ArrayList<>();
        }
        RemoteObjectTagsExample secondCarModelExample = new RemoteObjectTagsExample();
        secondCarModelExample.createCriteria().andTagIdEqualTo(typeId);
        List<RemoteObjectTags> secondCars = remoteObjectTagsMapper.selectByExample(secondCarModelExample);
        List<String> vins_second = new ArrayList<>();
        List<Long> objectIds = new ArrayList<>();
        for (RemoteObjectTags tag:secondCars){
            objectIds.add(tag.getRemoteObjectId());
        }
        RemoteObjectsExample objectsExample = new RemoteObjectsExample();
        objectsExample.createCriteria().andIdIn(objectIds);
        List<RemoteObjects> secondCar = remoteObjectsMapper.selectByExample(objectsExample);
        for (RemoteObjects object: secondCar){
            vins_second.add(object.getVin());
        }
        return vins_second;
    }
}
