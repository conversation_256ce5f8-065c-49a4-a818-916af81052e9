package com.sankuai.walle.rmanage.config.dto.accident;

import lombok.Data;

@Data
public class VehicleHistoryStatusDTO {
    private Long timestamp; //状态数据时间
    private Double speed; //速度，m/s
    private Integer gear; //档位
    private String gearDesc; //档位描述
    private Integer driveMode; //驾驶模式
    private String driveModeDesc; //驾驶模式描述
    private Integer cockpitStatus; //坐席状态
    private String cockpitStatusDesc; //坐席状态描述
    private String cockpitMisID; //坐席的MisID
    private Double longitude; //经度，坐标系wgs84
    private Double latitude; //维度，坐标系wgs84
    private String vin; //车架号
    private Double height; //海拔
}
