package com.sankuai.walle.rmanage.config.dto.rgOncallType;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collections;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 新排班模式解析消息类
 */
@Data
public class RgOncallNewModeDTO {

    private int code;
    private String message;
    private UserData data;

    @Data
    public static class UserData {

        @JsonProperty("displayname")
        private String displayName;
        private String email;
        private String identify;
    }

    /**
     * 解析新值班模式
     * @return
     */
    public List<String> getMisIdList() {
        if (this.getData() == null || StringUtils.isBlank(this.getData().getIdentify())) {
            return Collections.emptyList();
        }
        return Collections.singletonList(this.getData().getIdentify());
    }
}
