package com.sankuai.walle.rmanage.config.service.impl;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.example.CarAssetsExample;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.CarAssetsMapper;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import com.sankuai.walle.dal.walle_data_center.entity.VehicleInfo;
import com.sankuai.walle.dal.walle_data_center.entity.VehicleInfoWithBLOBs;
import com.sankuai.walle.dal.walle_data_center.example.VehicleInfoExample;
import com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper;
import com.sankuai.walle.rmanage.config.service.ActionLogService;
import com.sankuai.walle.rmanage.config.service.AssetService;
import com.sankuai.walle.rmanage.config.service.VehicleInfoService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VehicleInfoServiceImpl implements VehicleInfoService {

    @Autowired
    VehicleInfoMapper vehicleInfoMapper;
    @Autowired
    CarSelectsMapper carSelectsMapper;
    @Autowired
    CarAssetsMapper carAssetsMapper;
    @Autowired
    CarObjectsMapper carObjectsMapper;
    @Autowired
    AssetService assetService;
    @Resource
    ActionLogService actionLogService;

    @AllArgsConstructor
    @Getter
    public enum VehicleStatus{
        NOT_USE((byte)0),
        CAN_USE((byte)1),
        DELETE((byte)2);

        private final byte code;
    }
    @AllArgsConstructor
    @Getter
    public enum VehicleType{
        BIG((byte)1,3),
        MIDDLE((byte)2,1);

        private final byte vehicleType;
        private final Integer carSizeType;

        public static Byte getVehicleTypeByCarSizeType(Integer carSizeType){
            for(VehicleType type : VehicleType.values()){
                if(type.getCarSizeType() == carSizeType){
                    return type.getVehicleType();
                }
            }
            return null;
        }
    }
    
    @Override
    public void insertVehicleInfo(List<CarObjects> carObjectsList) {
        if (CollectionUtils.isEmpty(carObjectsList)) {
            return;
        }
        // 判断carObjectsList的vin在vehile_info中是否存在，是的话跳过，否的话插入
        List<String> vins = carObjectsList.stream().map(CarObjects::getVin).collect(Collectors.toList());
        VehicleInfoExample example = new VehicleInfoExample();
        example.createCriteria().andVinIn(vins);
        List<VehicleInfo> vehicleInfoList = vehicleInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(vehicleInfoList)) {
            insertVehicle(carObjectsList);
        } else {
            List<String> existVins = vehicleInfoList.stream().map(VehicleInfo::getVin).collect(Collectors.toList());
            List<CarObjects> insertTarget = carObjectsList.stream().filter(carObjects -> !existVins.contains(carObjects.getVin())).collect(Collectors.toList());
            insertVehicle(insertTarget);
        }
    }
    @Override
    public VehicleInfo fetchVehicleInfoByVin(String vin) {
        VehicleInfoExample example = new VehicleInfoExample();
        example.createCriteria().andVinEqualTo(vin);
        List<VehicleInfo> vehicleInfoList = vehicleInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(vehicleInfoList)) {
            return null;
        }
        return vehicleInfoList.get(0);
    }
    @Override
    public void updateVehicleInfo(String vin) {
        // 同步车辆，如果有则更新，没有则插入
        CarObjects carObjects = this.fetchCarObjectsByVin(vin);
        if(carObjects == null)return;
        CarAssets carAsset = assetService.fetchCarAssetsByVin(vin);
        VehicleInfoExample vehicleExample = new VehicleInfoExample();
        vehicleExample.createCriteria().andVinEqualTo(vin);
        List<VehicleInfo> vehicles = vehicleInfoMapper.selectByExampleWithBLOBs(vehicleExample);
        if(CollectionUtils.isEmpty(vehicles)){
            VehicleInfoWithBLOBs vehicleInfo = new VehicleInfoWithBLOBs();
            this.initVehicleInfo(vehicleInfo);
            this.pushColumn(carObjects, carAsset, vehicleInfo, vin);
            vehicleInfoMapper.insert(vehicleInfo);
        } else {
            VehicleInfo vehicleInfo = vehicles.get(0);
            this.pushColumn(carObjects, carAsset, vehicleInfo, vin);
            vehicleInfoMapper.updateByExampleWithBLOBs(vehicleInfo, vehicleExample);
        }
        actionLogService.insertCarManageActionLog(vin, UserUtils.getUser().getLogin(), "车辆信息同步到vehicle_info");
    }
    // 前提是对象存在
    @Override
    public void updateVehicleStatus(String vin){
        VehicleInfoExample vehicleExample = new VehicleInfoExample();
        vehicleExample.createCriteria().andVinEqualTo(vin);
        List<VehicleInfo> vehicles = vehicleInfoMapper.selectByExampleWithBLOBs(vehicleExample);
        if(CollectionUtils.isEmpty(vehicles))return;
        VehicleInfo vehicleInfo = vehicles.get(0);
        Byte status = this.fetchVehicleStatus(vin);
        if(status != null) {
            vehicleInfo.setStatus(status);
            vehicleInfoMapper.updateByExampleSelective(vehicleInfo, vehicleExample);
        }
    }
    @Override
    public Byte fetchVehicleStatus(String vin) {
        CarAssetsExample example = new CarAssetsExample();
        example.createCriteria().andVinEqualTo(vin);
        List<CarAssets> assets = carAssetsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(assets)) {
            return null;
        }
        // assets 如果都 scrap 如果都为1，则为删除, 其他状态不处理
        return assets.stream().allMatch(carAssets -> Objects.equals(carAssets.getScrap(),true)) ? VehicleStatus.DELETE.getCode(): null;
    }

    private void insertVehicle(List<CarObjects> carObjectsList) {
        List<VehicleInfoWithBLOBs> vehicleInfos = new ArrayList<>();
        carObjectsList.forEach(carObjects -> {
            VehicleInfoWithBLOBs vehicleInfo = new VehicleInfoWithBLOBs();
            this.initVehicleInfo(vehicleInfo);
            this.pushColumn(carObjects,null, vehicleInfo, carObjects.getVin());
            vehicleInfo.setStatus(VehicleStatus.CAN_USE.getCode()); // 新插入的，可用
            vehicleInfos.add(vehicleInfo);
        });
        if (vehicleInfos.size() > 0) {
            vehicleInfoMapper.batchInsert(vehicleInfos);
        }
    }

    private CarSelects fetchCarSelectsByType(String type) {
        CarSelectsExample example = new CarSelectsExample();
        example.createCriteria().andTypeEqualTo(type);
        List<CarSelects> carSelects = carSelectsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(carSelects)) {
            return null;
        }
        return carSelects.get(0);
    }

    private CarObjects fetchCarObjectsByVin(String vin) {
        CarObjectsExample example = new CarObjectsExample();
        example.createCriteria().andVinEqualTo(vin);
        List<CarObjects> carObjectsList = carObjectsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(carObjectsList)) {
            return null;
        }
        return carObjectsList.get(0);
    }

    private void initVehicleInfo(VehicleInfoWithBLOBs vehicleInfo){
        vehicleInfo.setPark("");
        vehicleInfo.setBrand("");
        vehicleInfo.setType("");
        vehicleInfo.setLicence("");
        vehicleInfo.setVehicleId("");
        vehicleInfo.setVehicleType((byte)0);
        vehicleInfo.setVehicleCategory("");
        vehicleInfo.setLicenseNumber("");
        vehicleInfo.setLicence("");
        vehicleInfo.setYear(0);
        vehicleInfo.setPowerType((byte)0);
        vehicleInfo.setOperationState((byte)1);
        vehicleInfo.setStatus((byte)1);
        vehicleInfo.setCreateTime(new Date());
        vehicleInfo.setUpdateTime(new Date());
        vehicleInfo.setOwner("");
        vehicleInfo.setProduct(0);
        vehicleInfo.setPurposeId((byte)0);
        vehicleInfo.setIsDedicated(false);
        vehicleInfo.setSubmitter("");
        vehicleInfo.setRemark("");
        vehicleInfo.setStartPostion("");
        vehicleInfo.setEndPostion("");
    }
    private void pushColumn(CarObjects carObjects, CarAssets carAsset, VehicleInfo vehicleInfo,String vin){
        vehicleInfo.setVin(carObjects.getVin());
        if(!Strings.isEmpty(carObjects.getName())){
            vehicleInfo.setName(carObjects.getName());
        }
        if(!Strings.isEmpty(carObjects.getLicenseno())){
            vehicleInfo.setLicence(carObjects.getLicenseno());
            vehicleInfo.setVehicleId(carObjects.getLicenseno());
        }
        if(!Strings.isEmpty(carObjects.getCarType())){
            String typeName = Objects.requireNonNull(this.fetchCarSelectsByType(carObjects.getCarType())).getName();
            if (!Strings.isEmpty(typeName)) {
                vehicleInfo.setType(typeName);
            }
        }
        if(carAsset != null){
            vehicleInfo.setBrand(carAsset.getBrand());
            vehicleInfo.setOwner(carAsset.getPersonmis());
            Byte vehicleType = VehicleType.getVehicleTypeByCarSizeType(carAsset.getCarSizeType());
            if (vehicleType!=null) {
                vehicleInfo.setVehicleType(vehicleType);
            }
        }

        // 同步状态
        Byte status = this.fetchVehicleStatus(vin);
        if (status != null) {
            vehicleInfo.setStatus(status);
        }
    }


}
