package com.sankuai.walle.rmanage.config.repository;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.walle.dal.battery.entity.BatteryCabinetCommand;
import com.sankuai.walle.dal.battery.entity.BatterySwapCabinetProperty;
import com.sankuai.walle.dal.battery.example.BatteryCabinetCommandExample;
import com.sankuai.walle.dal.battery.example.BatterySwapCabinetPropertyExample;
import com.sankuai.walle.dal.battery.mapper.BatteryCabinetCommandMapper;
import com.sankuai.walle.dal.battery.mapper.BatterySwapCabinetPropertyMapper;
import com.sankuai.walle.dal.battery.mapper.BatterySwapCabinetPropertyMapperMy;
import com.sankuai.walle.rmanage.config.constant.ChargingCarbinetConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class ChargingCabinetRepsitory {
    @Resource
    BatterySwapCabinetPropertyMapperMy batterySwapCabinetPropertyMapperMy;
    @Resource
    BatteryCabinetCommandMapper batteryCabinetCommandMapper;

    public BatterySwapCabinetProperty queryDaoByDeviceId(String deviceId) {
        BatterySwapCabinetProperty property = batterySwapCabinetPropertyMapperMy.selectLatestByDeviceKey(deviceId);
        return property;
    }

    // 开门指令写入数据库
    public void insertCommand(String deviceKey, String commandContent,String messageId) {
        String mis = UserUtils.getUser().getLogin();
        BatteryCabinetCommand dao = new BatteryCabinetCommand();
        dao.setDeviceKey(deviceKey);
        dao.setRequestId(messageId);
        dao.setStatus(1);
        dao.setMis(mis);
        dao.setModuleIdentifier(ChargingCarbinetConstant.Remote_Control);
        dao.setCommandIdentifier(ChargingCarbinetConstant.Remote_ELock_Control);
        dao.setCommandContent(commandContent);
        batteryCabinetCommandMapper.insert(dao);
    }

    // 开门指令更新数据库状态
    public void updateDao(String messageId,String data,String deviceKey) {
        // 查询数据库中此条数据是否存在
        BatteryCabinetCommandExample example = new BatteryCabinetCommandExample();
        example.createCriteria().andRequestIdEqualTo(messageId).andDeviceKeyEqualTo(deviceKey);
        List<BatteryCabinetCommand> list = batteryCabinetCommandMapper.selectByExample(example);
        if (list.isEmpty()) {
            // 等待1秒重试，一共重试3次
            for (int i = 0; i < 3; i++) {
                try {
                    Thread.sleep(1000);
                    list = batteryCabinetCommandMapper.selectByExample(example);
                    if (!list.isEmpty()) {
                        break;
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            if (list.isEmpty()) {
                log.error("开门指令更新数据库状态失败，数据不存在");
                return;
            }
        }
        BatteryCabinetCommand dao = list.get(0);
        dao.setStatus(2);
        dao.setStatusMsg(data);
        batteryCabinetCommandMapper.updateByPrimaryKey(dao);
    }

    public List<BatteryCabinetCommand> queryCommandByDeviceKey(String deviceKey) {
        BatteryCabinetCommandExample example = new BatteryCabinetCommandExample();
        example.createCriteria().andDeviceKeyEqualTo(deviceKey);
        example.setOrderByClause("id desc");
        example.limit(1);
        return batteryCabinetCommandMapper.selectByExample(example);
    }
}
