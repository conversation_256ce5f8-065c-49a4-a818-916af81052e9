package com.sankuai.walle.rmanage.config.Listener;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarConfigQuery;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfigExtend;
import com.sankuai.walle.dal.classify.example.CarConfigQueryExample;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExample;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExtendExample;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarConfigQueryMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigExtendMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.mrm_manage.entity.AutoConfigVehicleTaskStatus;
import com.sankuai.walle.dal.mrm_manage.example.AutoConfigVehicleTaskStatusExample;
import com.sankuai.walle.dal.mrm_manage.mapper.AutoConfigVehicleTaskStatusMapper;
import com.sankuai.walle.objects.bo.AukChargingCarbinetUpContentBO;
import com.sankuai.walle.objects.bo.AukUpCountent;
import com.sankuai.walle.objects.bo.RealTimeObj;
import com.sankuai.walle.objects.constants.CarConstant;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.rmanage.config.Listener.ThingStrategy.ThingModelHandlerFactory;
import com.sankuai.walle.rmanage.config.Listener.ThingStrategy.ThingModelHandlerStrategy;
import com.sankuai.walle.rmanage.config.Listener.strategy.MessageHandlerFactory;
import com.sankuai.walle.rmanage.config.Listener.strategy.MessageHandlerStrategy;
import com.sankuai.walle.rmanage.config.common.constant.AutoConfigTaskStatusEnum;
import com.sankuai.walle.rmanage.config.common.constant.AutoConfigTaskTypeEnum;
import com.sankuai.walle.rmanage.config.config.AukClientConfig;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.service.MafkaRealtimeService;
import com.sankuai.walle.rmanage.config.service.VehicleDataBusService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.util.MD5Util;
import com.sankuai.walle.rmanage.config.vto.VehicleDataBusVTO;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MafkaAukConsumer implements IMessageListener {

    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;

    @Resource
    CarDeviceConfigService carDeviceConfigService;
    @Resource
    CarConfigMapper carConfigMapper;

    @Resource
    MyAukService myAukService;

    @Resource
    CarDeviceConfigExtendMapper configExtendMapper;
    @Resource
    CarConfigQueryMapper carConfigQueryMapper;

    @Resource
    AutoConfigVehicleTaskStatusMapper autoConfigVehicleTaskStatusMapper;

    @Resource
    VehicleDataBusService vehicleDataBusService;

    @Resource
    MafkaRealtimeService mafkaRealtimeService;

    @Resource
    MessageHandlerFactory messageHandlerFactory;
    @Resource
    ThingModelHandlerFactory thingModelHandlerFactory;


    /**
     * 处理接收到的消息
     * @param message 消息对象
     * @param topic 消息主题
     */
    public void onMessage(AukUpCountent message, String topic) {
        try {
            // 根据消息主题获取对应的处理策略
            MessageHandlerStrategy strategy = messageHandlerFactory.getStrategy(topic);

            if (strategy == null) {
                log.error("未找到对应的消息处理策略，topic: {}", topic);
                return;
            }

            // 使用策略处理消息
            strategy.handle(message);

        } catch (Exception e) {
            log.error("处理消息时发生错误，topic: {}, error: {}", topic, e.getMessage(), e);
        }
    }

    private Object parseMessageBody(String messageBody) {
        JSONObject json = JSONObject.parseObject(messageBody);

        // 判断特征字段
        if (json.containsKey("outValue")) {
            // 充电柜上报
            /*
             * {"processDataCategory":10,"productKey":"H24BatterySwapCabinet","deviceKey":"H511220244700006","moduleIdentifier":"Remote_Control","funcIdentifier":"Remote_ELock_Control","funcName":"远程控制电控门锁指令","msgId":"MVKXP7uGaLt5Eiot7cxJtx","msgTime":"2025-05-20 17:22:30.000","outValue":"{\"failreason\":1,\"result\":0}","outValueValidate":1,"outValueErrMsg":"","inputValue":"{\"cmd\":0,\"time\":10,\"ServerTime\":1747732950231}","inputValueValidate":0,"inputValueErrMsg":""}
             * */
            return json.toJavaObject(AukChargingCarbinetUpContentBO.class);
        } else if (json.containsKey("message")) {
            // AukUpCountent
            return json.toJavaObject(AukUpCountent.class);
        } else {
            // 其他类型或无法识别
            throw new IllegalArgumentException("未知消息体结构");
        }
    }

    // device上线通知，上线时，查看配置是否已经下发完成，如果否则重新下发
    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        String messageBody = (String) mafkaMessage.getBody();
        log.info("接收规则引擎转发上行消息, content: {}", messageBody);
        try {
//            AukUpCountent message = JSONObject.parseObject(messageBody, AukUpCountent.class);
            Object parsedMessage = parseMessageBody(messageBody);
            if (parsedMessage instanceof AukUpCountent) {
                AukUpCountent message = (AukUpCountent) parsedMessage;
                onMessage(message, message.getTopic());
            } else if (parsedMessage instanceof AukChargingCarbinetUpContentBO) {
                AukChargingCarbinetUpContentBO message = (AukChargingCarbinetUpContentBO) parsedMessage;
                String topic = message.getModuleIdentifier()+"."+message.getFuncIdentifier();
                ThingModelHandlerStrategy strategy = thingModelHandlerFactory.getStrategy(topic);
                if (strategy != null) {
                    strategy.handle(message);
                }
            } else {
                log.error("未知的消息体结构");
            }


        }catch (Exception e){
            log.error("接收从auk的mafka消息出错：",e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


}


