package com.sankuai.walle.rmanage.config.dto.VehicleModelManage;

import com.sankuai.walle.objects.vo.VehicleModelInfoVO;
import com.sankuai.walle.objects.vo.request.VehicleModelCreateRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleModelAttributesDTO {

    /**
     * 车型品牌
     */
    String carBrand;

    /**
     * 车型尺寸
     */
    String carMedium;


    /**
     * 车型创建请求结构体转换为属性DTO
     * @param vehicleModelDTO 车型创建请求
     * @return 属性DTO
     */
    public static VehicleModelAttributesDTO from(VehicleModelDTO vehicleModelDTO) {
        if (Objects.isNull(vehicleModelDTO)) {
            return null;
        }
        return VehicleModelAttributesDTO.builder()
                .carBrand(vehicleModelDTO.getCarBrand())
                .carMedium(vehicleModelDTO.getCarMedium())
                .build();
    }

    /**
     * 车型属性DTO转换为车型信息VO
     * @param dto 车型属性DTO
     * @param infoVO 车型信息VO
     * @return 转换后的车型信息VO
     */
    public static VehicleModelInfoVO toVehicleModelInfoVO(VehicleModelAttributesDTO dto,
                                                          VehicleModelInfoVO infoVO) {
        if (Objects.isNull(dto) || Objects.isNull(infoVO)) {
            return null;
        }
        infoVO.setCarBrand(dto.getCarBrand());
        infoVO.setCarMedium(dto.getCarMedium());
        return infoVO;
    }

}
