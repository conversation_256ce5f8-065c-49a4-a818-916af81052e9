package com.sankuai.walle.rmanage.config.adapter;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.carosscan.request.DxUpdateGroupRoleNameRequest;
import com.sankuai.carosscan.request.GroupRoleName;
import com.sankuai.carosscan.response.DxUpdateGroupRoleNameResultVO;
import com.sankuai.carosscan.service.IThriftDxUpdateGroupService;
import com.sankuai.walle.rmanage.config.dto.accident.RGOncallUserDTO;
import com.sankuai.walle.rmanage.config.vto.RgId2RoleNameVTO;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


@Slf4j
@Component
public class DxGroupRoleNameUpdateAdapter {

    @MdpThriftClient(remoteAppKey = "com.sankuai.carosscan.common.output", timeout = 2000)
    private IThriftDxUpdateGroupService dxUpdateGroupService;

    // 值班组对应值班人员需要设置的角色名列表
    @MdpConfig("accident.tt.rgid.list:[]")
    private ArrayList<RgId2RoleNameVTO> rgId2RoleNameList = new ArrayList<>();

    /**
     * 更新大象群角色信息 不能异步，必须执行完了才能转移群主，否则如果先转移了群主，该接口没有机器人会失败
     *
     * @param groupId
     * @param rgId2MisIdList
     */
    public void updateGroupRoleName(Long groupId, List<RGOncallUserDTO> rgId2MisIdList) {
        if (groupId == null || CollectionUtils.isEmpty(rgId2MisIdList)) {
            log.error("updateGroupRoleName 参数不合法,groupId={},rgId2MisIdList={}", groupId, rgId2MisIdList);
            return;
        }
        //  获取值班组对应值班人员需要设置的角色名列表
        List<GroupRoleName> groupRoleNameList = getGroupRoleNameList(rgId2MisIdList);

        // 构建请求参数
        DxUpdateGroupRoleNameRequest request = DxUpdateGroupRoleNameRequest.builder()
                .gid(groupId)
                .groupRoleNames(groupRoleNameList)
                .build();
        log.info("dxUpdateGroupService.batchUpdateDxGroupRoleName, request = {}", request);

        // 调用修改大象群角色信息接口
        DxUpdateGroupRoleNameResultVO resultDTO = null;
        try {
            EveThriftResponse<DxUpdateGroupRoleNameResultVO> response = dxUpdateGroupService.batchUpdateDxGroupRoleName(
                    request);
            resultDTO = response.getData();
        } catch (Exception e) {
            log.error("更新大象群角色接口异常", e);
            return;
        }

        log.info("dxUpdateGroupService.batchUpdateDxGroupRoleName, response = {}", resultDTO);
    }

    /**
     * 获取需要修改的角色信息
     *
     * @param rgOncallUserDTOList
     * @return
     */
    private List<GroupRoleName> getGroupRoleNameList(List<RGOncallUserDTO> rgOncallUserDTOList) {
        // 构建映射rgId->roleName
        Map<Long, String> rgOncallDutyMap = rgId2RoleNameList.stream()
                .filter(Objects::nonNull)
                .filter(RgId2RoleNameVTO ->
                        StringUtils.isNotBlank(RgId2RoleNameVTO.getRoleName()) && RgId2RoleNameVTO.getRgId() != null)
                .collect(Collectors.toMap(RgId2RoleNameVTO::getRgId, RgId2RoleNameVTO::getRoleName, (v1, v2) -> v1));

        // 组装需要修改的{misId，roleName}
        List<GroupRoleName> groupRoleNameList = new ArrayList<>();
        rgOncallUserDTOList.stream()
                .filter(Objects::nonNull)
                .forEach(rgOncallUserDTO -> {
                    Long rgId = rgOncallUserDTO.getRgId();
                    String roleName = rgOncallDutyMap.get(rgId);
                    List<String> userMisIdList = rgOncallUserDTO.getOncallUserList();
                    if (StringUtils.isEmpty(roleName) || CollectionUtils.isEmpty(userMisIdList)) {
                        return;
                    }
                    // 将 rgId2MisIdList中 rgId->misId 根据映射 rgId->roleName，得到{misId，roleName}
                    userMisIdList.stream().filter(Objects::nonNull).forEach(misId -> {
                        groupRoleNameList.add(new GroupRoleName(misId, roleName));
                    });
                });
        return groupRoleNameList;
    }

}
