package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.fastjson.JSONObject;

import com.meituan.finerp.eam.dto.OpenAssetDTO;
import com.meituan.finerp.eam.response.OpenQueryWalleAssetsRes;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meituan.config.util.JsonUtils;
import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarOperationExample;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.CarOperationMapper;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import com.sankuai.walle.dal.walle_data_center.entity.BizPlaceInfo;
import com.sankuai.walle.dal.walle_data_center.example.BizPlaceInfoExample;
import com.sankuai.walle.dal.walle_data_center.mapper.BizPlaceInfoMapper;
import com.sankuai.walle.objects.constants.CarAssetsConstant;
import com.sankuai.walle.objects.constants.SelectBelongConstant;
import com.sankuai.walle.rmanage.config.component.ORGQueryService;
import com.sankuai.walle.rmanage.config.helper.convertor.OpenAssetDtoConvert;
import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarAssetsExample;
import com.sankuai.walle.carManage.mapper.CarAssetsMapper;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.rmanage.config.inter.OpenAssetQueryRemoteService;
import com.sankuai.walle.rmanage.config.service.AssetService;
import com.sankuai.walle.rmanage.config.service.RedisService;
import com.sankuai.walle.rmanage.config.service.VehicleInfoService;
import com.sankuai.walledelivery.basic.client.response.inner.businessstation.BusinessStationResponse;
import com.sankuai.walledelivery.basic.client.response.inner.deliverer.VehicleResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.RpcVehicleQueryThriftService;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AssetServiceImpl implements AssetService {

    @Resource
    OpenAssetQueryRemoteService openAssetQueryRemoteService;

    @Resource
    private ORGQueryService orgQueryService;

    @Resource
    CarObjectsMapper carObjectsMapper;

    @Autowired
    TTService ttService;

    @Resource
    CarAssetsMapper carAssetsMapper;

    @Resource
    VehicleInfoService vehicleInfoService;
//    @Resource
//    RpcDelivererQueryThriftService rpcDelivererQueryThriftService;
    @Resource
    RpcVehicleQueryThriftService rpcVehicleQueryThriftService;
    @Resource
    BizPlaceInfoMapper bizPlaceInfoMapper;
    /**
     *  需要进行资产更新对应的vin列表
     */
    @MdpConfig("update.asserts.vin.list.set")
    private HashSet<String> vinListSet;

    /**
     * ALL
     */
    private static final String ALL = "ALL";

    @Override
    public void syncRemoteObjectFromOpenAsset() throws Exception {

        List<String> smallTypeList = CarAssetsConstant.smallTypeList;
        int pageSize = CarAssetsConstant.pageSize;
        // OpenAssetDTO数据说明：scrap-是否报废、model-型号、status是资产状态（可不用）
        List<OpenAssetDTO> openAssetDTOS = null;
        String scrollId = null;
        do {
            OpenQueryWalleAssetsRes rep = openAssetQueryRemoteService.queryWalleAssets(smallTypeList, scrollId, pageSize);
            openAssetDTOS = rep.getDataList();
//            // 1、已有资产，2，新增的资产，3，已有资产，但没有编码，mock一下 d24-004,验证一下
            scrollId = rep.getScrollId();
            syncObject(openAssetDTOS);

        } while ( CollectionUtils.isNotEmpty(openAssetDTOS) );
    }

    @Resource
    Environment env;
    @Data
    static class Gid {
        private Boolean on;
        private String value;
    }
    @MdpConfig("assests.elephant.gid")
    private Gid gid;

    @Override
    public void checkVin(){
        CarAssetsExample query = new CarAssetsExample();
        query.createCriteria().andScrapEqualTo(false);
        List<CarAssets> assests = carAssetsMapper.selectByExample(query);
        RestTemplate restTemplate = new RestTemplate();
        ArrayList<String> list = new ArrayList<>();
        for(CarAssets carAssets: assests){

            boolean res = checkFun(carAssets.getVin(), carAssets.getSn());
            if (!res & carAssets.getLabel() != null) {
                list.add(carAssets.getLabel());
            }
        }
        if (gid.getOn() && list.size()>0) {
            // 抛出消息到大象
            String url = env.getProperty("evehost") + "eve/output/rest/push-message/gid?appkey=com.sankuai.caros.wallecmdb";
            String carDetail = String.format("{\"text\":\"该数据vin不符合标准，请修改海鸥sn，资产标签：%s\"}", list);
            // 创建请求体
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json;charset=UTF-8");
            // 使用Map来构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("messageType", "text");
            requestBody.put("bodyJson", carDetail);
            requestBody.put("gid", Long.valueOf(gid.getValue()));
            // 使用ObjectMapper将Map转为JSON字符串
            String requestJson = JSONObject.toJSONString(requestBody);
            HttpEntity<String> entity = new HttpEntity<>(requestJson, headers);
            System.out.println(url);
            System.out.println(requestJson);
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            System.out.println(response);
        }
    }

    public boolean checkFun(String vin, String sn){
        // 判断vin和sn是否为空
        if (vin == null || sn == null || vin.isEmpty() || sn.isEmpty()) {
            return true;
        }

        // 使用"-"切分sn
        String[] parts = sn.split("-");

        // 找出最长的部分
        String longestPart = Arrays.stream(parts)
                .max(Comparator.comparingInt(String::length))
                .orElse("");

        // 判断vin是否等于最长的部分
        if (!vin.equals(longestPart)) {
            return false;
        }

        // 判断vin是否包含特殊字符，这里假设特殊字符是非字母和数字
        if (!vin.matches("^[a-zA-Z0-9]*$")) {
            return false;
        }

        return true;
    }

    @Resource
    CarOperationMapper carOperationMapper;
    @Autowired
    RedisService redisService;
    @Autowired
    MafkaRealtimeServiceImpl realtimeService;
    @Resource
    CarSelectsMapper carSelectsMapper;

    @Override
    public void syncCarObject() throws Exception {
        // 取出所有中车的vin
        List<String> types = carSelectsMapper.selectByExample(new CarSelectsExample() {{
            createCriteria().andBelongEqualTo(SelectBelongConstant.CarType);

        }}).stream().map(CarSelects::getType).collect(Collectors.toList());
        List<String> vins = carObjectsMapper.selectByExample(new CarObjectsExample(){{
            createCriteria().andCarTypeIn(types);
        }}).stream().map(
                CarObjects::getVin
        ).collect(Collectors.toList());
        System.out.println(vins);
        byte[] value = JSONObject.toJSONBytes(vins);
//        log.info("存入的vins: {}",value);
        redisService.pushCarInRedisBytes("allvin", value);
    }

    // 获取所有车的vin
    @Override
    public List<String> queryAllVins(){
        byte[] value = redisService.queryValue("allvin");
        if (value==null || value.length==0){
            return Collections.emptyList();
        }
        List<String> res = JSONObject.parseArray(new String(value),String.class);
        return res;
    }

    /**
     * 批量更新资产owner所属部门信息
     */
    @Override
    public void batchUpdateAssetOwnerDepartment() {
        List<CarAssets> carAssetsList = carAssetsMapper.selectByExample(null);
        if (CollectionUtils.isEmpty(carAssetsList)) {
            return;
        }
        // 只更新灰度部分
        carAssetsList = carAssetsList.stream().filter(carAssets ->
                isNeedUpdate(carAssets.getVin())).collect(Collectors.toList());
        // 设置资产owner所属部门的信息
        setAssetOwnerDepartment(carAssetsList);
        // 分别记录成功和失败车辆列表
        List<String> successedList = new ArrayList<>();
        List<String> failedList = new ArrayList<>();
        // 更细资产
        carAssetsList.forEach(carAssets -> {
            try {
                carAssetsMapper.updateByPrimaryKeySelective(carAssets);
                successedList.add(carAssets.getVin());
            } catch (Exception e) {
                log.error("更新资产信息失败，资产信息: {}", carAssets, e);
                failedList.add(carAssets.getVin());
            }
        });
        log.info("更新资产信息成功列表: {}, 失败列表:{}", successedList, failedList);
        if(CollectionUtils.isNotEmpty(failedList)){
            log.error("更新资产信息失败列表: {}", failedList, new RuntimeException());
        }
    }

    // 从运力服务同步运力城市、场地、用途到车管中
    @Override
    public void asyncUpdateOperationData() {
        List<String> vinList = carObjectsMapper.selectByExample(null).stream().map(CarObjects::getVin).collect(Collectors.toList());
        this.asyncOperationFun(vinList);
    }
    private void asyncOperationFun(List<String> vinList){
        try {
            ThriftResponse<List<VehicleResponse>> deliverer = rpcVehicleQueryThriftService.batchQueryByVehicleVinList(vinList);
            if (deliverer.getCode() != 0) {
                log.error("从运力服务同步运力城市、场地、用途到车管中失败，失败原因: {}", deliverer.getMessage());
                return;
            }
            List<VehicleResponse> data = deliverer.getData();
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            for (VehicleResponse delivererResponse : data) {
                String vin = delivererResponse.getIdentifyNum();
                List<BusinessStationResponse> stationTypes = delivererResponse.getBusinessStationList();
                if (CollectionUtils.isEmpty(stationTypes)) {
                    log.error("从运力服务同步运力城市、场地、用途到车管中失败，失败原因: {}，stationType 为空：{}", delivererResponse, stationTypes);
                    continue;
                }
                BusinessStationResponse station = stationTypes.get(0);
                String stationType;
                switch ( station.getType()) {
                    case 1:
                        stationType = "业务运营";
                        break;
                    case 2:
                        stationType = "道路测试";
                        break;
                    default:
                        stationType = "其他";
                }
                String placeId = station.getPlaceId();
                BizPlaceInfoExample example= new BizPlaceInfoExample();
                example.createCriteria().andCodeEqualTo(placeId);
                List<BizPlaceInfo> places = bizPlaceInfoMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(places)) {
                    log.error("从运力服务同步运力城市、场地、用途到车管中失败，失败原因: {}，场地找不到，placeId为：{}", delivererResponse,placeId);
                    continue;
                }
                BizPlaceInfo place = places.get(0);
                String city = place.getCity();
                String placeName = place.getName();
                // 查询车辆信息
                CarOperationExample operationExample = new CarOperationExample();
                operationExample.createCriteria().andVinEqualTo(vin);
                List<CarOperation> operations = carOperationMapper.selectByExample(operationExample);
                if (CollectionUtils.isEmpty(operations)) {
                    log.error("从运力服务同步运力城市、场地、用途到车管中失败，失败原因: {}，找不到车辆：{}", delivererResponse, vin);
                    continue;
                }
                CarOperation operation = operations.get(0);
                operation.setCity(city);
                operation.setArea(placeName);
                operation.setAreaStr(place.getCode());
                operation.setCarUsedTargetStr(String.valueOf(station.getType()));
                operation.setCarUsedTarget(stationType);
                carOperationMapper.updateByPrimaryKey(operation);
            }
        } catch (BizThriftException e) {
            throw new RuntimeException(e);
        }
    }

    // 从海鸥同步车辆信息
    /* 策略
新增：
1、已有label，跳过。
2，新增label，
1）vin存在，对应的label为null，跳过。
2）vin存在，对应的label不是null，新增。
3）vin不存在，新增。

更新：
1、已有label，更新。
2、label是null
1）vin不存在，跳过
2）vin存在，更新*/
    @Override
    public void syncObject(List<OpenAssetDTO> openAssetDTOS) throws Exception {
        if (CollectionUtils.isEmpty(openAssetDTOS)) {
            return;
        }
        // 海鸥的sn，同自动车的vin，区别是，sn里面可能有-s，而vin里面没有-s
        //注意，这里改了DTO的值
//        openAssetDTOS.stream().forEach(assertDto -> assertDto.getSerialNumber().trim());
        openAssetDTOS.forEach(assertDto -> {
            String serialNumber = assertDto.getSerialNumber();
            if (serialNumber != null) {
                assertDto.setSerialNumber(serialNumber.trim());
            }
        });
        // 标签
        List<String> labels = openAssetDTOS.stream()
                .map(OpenAssetDTO::getLabel)
                .map(String::trim)
                .collect(Collectors.toList());
        // vin
        List<String> serialNumbers = openAssetDTOS.stream()
                .map(OpenAssetDTO::getSerialNumber)
                .map(String::trim)
                .collect(Collectors.toList());
        List<String> vins = serialNumbers.stream().map(OpenAssetDtoConvert::SnToVin).collect(Collectors.toList());

        CarAssetsExample example = new CarAssetsExample();
        // label 和 vin都存在
        example.createCriteria().andLabelIn(labels).andVinIn(vins);
        // 只有vin存在
        example.or().andVinIn(vins).andLabelIsNull();
        log.info("syncObject, serialNumbers:{}", JsonUtils.toJson(labels));
        List<CarAssets> existCars = carAssetsMapper.selectByExample(example);
        log.info("syncObject, existCars:{}", JsonUtils.toJson(existCars));

        addNewObject(openAssetDTOS, existCars); // 增加
        addNewAssetInfo(openAssetDTOS, existCars);// 增加没有的资产数据 、没有的车辆vin
        updateExistAssetInfo(openAssetDTOS, existCars);// 更新已有的资产数据
    }
    @Override
    public CarAssets fetchCarAssetsByVin(String vin) {
        CarAssetsExample example = new CarAssetsExample();
        example.createCriteria().andVinEqualTo(vin);
        List<CarAssets> carAssets = carAssetsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(carAssets)) {
            return null;
        }
        Optional<CarAssets> res = carAssets.stream()
            .filter(obj -> obj!=null && obj.getScrap() != null && !obj.getScrap())
            .findFirst();
        return res.orElse(null);
    }
    private void addNewObject(List<OpenAssetDTO> openAssetDTOS, List<CarAssets> existCars) throws IOException {
        List<String> labels = existCars.stream().map(CarAssets::getLabel).collect(Collectors.toList()); // 已入库的labels
        TreeSet<String> treeSet = new TreeSet<>();
        // 去掉labels中的null
        labels.removeIf(Objects::isNull);
        if (CollectionUtils.isNotEmpty(labels)){
            treeSet.addAll(labels);
        }
        List<OpenAssetDTO> addTarget = openAssetDTOS.stream().filter(openAssetDTO -> openAssetDTO.getLabel() != null &&
                !treeSet.contains(openAssetDTO.getLabel().trim())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addTarget)){
            return;
        }

        List<CarObjects> tempCarObjects = OpenAssetDtoConvert.toCarObject(addTarget);
        List<String> vinsIn = tempCarObjects.stream().map(CarObjects::getVin).collect(Collectors.toList());
        List<CarObjects> existObjects = carObjectsMapper.selectByExample(new CarObjectsExample() {{createCriteria().andVinIn(vinsIn);}});
        List<String> vinsOut = existObjects.stream().map(CarObjects::getVin).collect(Collectors.toList());

        TreeSet<String> treeSet2 = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
        treeSet2.addAll(vinsOut);
        List<CarObjects> addTargetObjects = tempCarObjects.stream().filter(carObjects -> carObjects.getVin() !=null &&
                !treeSet2.contains(carObjects.getVin())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addTargetObjects)){
            return;
        }
        log.info("<新增的车辆：{}>",JsonUtils.toJson(addTargetObjects));
        carObjectsMapper.batchInsert(addTargetObjects);
        // 判断是否需要插入vehicle_info表
        vehicleInfoService.insertVehicleInfo(addTargetObjects);
    }
    private void addNewAssetInfo(List<OpenAssetDTO> openAssetDTOS,List<CarAssets> existCars) throws IOException {
        // 需要新增的情况：1、CarAssets表中label/vin 都不存在，2、CarAssets表中 label不存在，vin存在，且已有数据的label不是null
        Set<String> labels = existCars.stream().map(CarAssets::getLabel).collect(Collectors.toSet());
        Set<String> vins = existCars.stream().map(CarAssets::getVin).collect(Collectors.toSet());
        TreeSet<String> treeSet = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
        labels.removeIf(Objects::isNull);
        if(CollectionUtils.isNotEmpty(labels)){
            treeSet.addAll(labels);
        }
        // existCars 组成 vin为key，相同vin的 CarAssets 组成List<CarAssets> 的map
        Map<String, List<CarAssets>> existCarMap = new HashMap<>();
        existCars.forEach(existCar -> {
           if (existCarMap.containsKey(existCar.getVin())){
               existCarMap.get(existCar.getVin()).add(existCar);
           }else {
               List<CarAssets> list = new ArrayList<>();
               list.add(existCar);
               existCarMap.put(existCar.getVin(), list);
           }
        });
        // label不存在
        List<OpenAssetDTO> addTarget = openAssetDTOS.stream().filter(
                openAssetDTO -> openAssetDTO.getLabel() != null && !treeSet.contains(openAssetDTO.getLabel().trim()))
                .collect(Collectors.toList());
        addTarget = addTarget.stream().filter(openAssetDTO -> {
            // 两种情况：1、label/vin 都不存在，
            String vin = OpenAssetDtoConvert.SnToVin(openAssetDTO.getSerialNumber());
            if(!vins.contains(vin)){
                return true;
            };
            // 2，vin包含，且vin对应的所有label都不是null，如果是null，则新增，不是null，则更新
            List<String> lables = existCarMap.get(vin).stream().map(CarAssets::getLabel).collect(Collectors.toList());
            boolean res = vins.contains(vin) && !lables.contains(null);
            return res;
        }
        ).collect(Collectors.toList());

        // 2、vin存在，但label不相同，且已有数据的label不是null
//        List<OpenAssetDTO> addTarget2 = openAssetDTOS.stream().filter(
//                openAssetDTO -> {
//                    String vin = OpenAssetDtoConvert.SnToVin(openAssetDTO.getSerialNumber());
//                    boolean res = vins.contains(vin);
//                    List<String> lables = existCarMap.get(vin).stream().map(CarAssets::getLabel).collect(Collectors.toList());
//                    res = res && !labels.contains(openAssetDTO.getLabel());
//                    return res;
//                }
//        ).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(addTarget)){
            return;
        }

        List<CarAssets> addTargetCarAssets = OpenAssetDtoConvert.toCarAssets(addTarget);
        // 设置资产所属的部门名称
        setAssetOwnerDepartment(addTargetCarAssets);
        log.info("<新增的资产：{}>",JsonUtils.toJson(addTargetCarAssets));
        carAssetsMapper.batchInsert(addTargetCarAssets);

        // 发送资产新增的TT提醒
        List<String> vinsTT = addTargetCarAssets.stream().map(CarAssets::getVin).collect(Collectors.toList());
        ttService.buildAndSend("新增车辆：vin号：" + vinsTT);
    }
    private void updateExistAssetInfo(List<OpenAssetDTO> openAssetDTOS, List<CarAssets> existCars) throws IOException {
        if (CollectionUtils.isEmpty(existCars)){
            return;
        }
        List<CarAssets> updateTarget = new ArrayList<>();
        for (CarAssets son: existCars) {
            updateTarget.add(son);
        }
        // 转换成map，key是label
        Map<String, OpenAssetDTO> openAssetDTOMap = openAssetDTOS.stream().collect(
                Collectors.toMap(OpenAssetDTO::getLabel, Function.identity(), (oldValue,newValue)->newValue)
        );
        // 转换成map，key是vin
        Map<String, OpenAssetDTO> openAssetDTOMapVin = openAssetDTOS.stream().collect(
                Collectors.toMap(obj -> OpenAssetDtoConvert.SnToVin(obj.getSerialNumber()), Function.identity(), (oldValue,newValue)->newValue)
        );
        TreeMap<String, OpenAssetDTO> treeMap = new TreeMap<>(openAssetDTOMap); // all data
        TreeMap<String, OpenAssetDTO> treeMapVin = new TreeMap<>(openAssetDTOMapVin); // all data
        Iterator<CarAssets> iterator = updateTarget.iterator(); // exits data
        while(iterator.hasNext()){
            CarAssets asset = iterator.next();
            OpenAssetDTO openAssetDTO = null;
            // 首先看label，有label就直接用了
            if(asset.getLabel()!=null){
                openAssetDTO = treeMap.get(asset.getLabel());
            } else if (asset.getVin()!=null) {
                // label为null，再看vin，把这个null的label更新掉
                openAssetDTO = treeMapVin.get(asset.getVin());
            }
            // 如果两个都是null，那这里openAssetDTO肯定是null
            if (openAssetDTO == null) {
                iterator.remove();
                continue;
            }
            // 最后判断是否需要更新
            if( !updateExistAssetInfo(openAssetDTO, asset) ){
                iterator.remove();
            }
            // 如果是报废，则更新vehicle_info的状态
            if (asset.getScrap() != null && asset.getScrap()){
                vehicleInfoService.updateVehicleStatus(asset.getVin());
            }
        }
        // 剩下需要更新的，按主键更新
        if(CollectionUtils.isNotEmpty(updateTarget)){
            // 设置资产所属的部门名称
            setAssetOwnerDepartment(updateTarget);
            log.info("updateExistSensorInfo  {}", JsonUtils.toJson(updateTarget));
            updateTarget.forEach(carAssets -> {
                carAssetsMapper.updateByPrimaryKeySelective(carAssets);
            });
        }
    }
    // 判断是否需要更新，如果需要更新，则更新对象属性，如果不需要更新，则返回false。
    public boolean updateExistAssetInfo(OpenAssetDTO openAssetDTO, CarAssets asset){
        String sn = openAssetDTO.getSerialNumber();

        // 什么情况要去更新: 品牌brand/personMis/scrap/SN/label 这几个字段有变化的时候，更新
        boolean updateFlag = !Objects.equals(openAssetDTO.getBrand(), asset.getBrand()) ||
                !Objects.equals(openAssetDTO.getPersonMis(), asset.getPersonmis()) ||
                !Objects.equals(openAssetDTO.getScrap(), asset.getScrap()) ||
                !Objects.equals(sn, asset.getSn()) ||
                !Objects.equals(openAssetDTO.getLabel(), asset.getLabel());
        // sn可能不存在，这时要避免覆盖已有的vin
        if (sn != null && !sn.isEmpty()) {
            sn = sn.trim();
            String vin = OpenAssetDtoConvert.SnToVin(sn);
            // 如果vin不相等，替换vin
            updateFlag = updateFlag || !Objects.equals(asset.getVin(), vin);
            asset.setVin(vin.trim());
        }
        if (updateFlag){
            asset.setBrand(openAssetDTO.getBrand());
            asset.setPersonmis(openAssetDTO.getPersonMis());
            asset.setScrap(openAssetDTO.getScrap());
            asset.setSn(sn);
            asset.setUpdateTime(new Date());
            asset.setLabel(openAssetDTO.getLabel());
            return true;
        }else{
            return false;
        }
    }

    /**
     * 设置资产owner的所属部门
     * @param carAssetsList 资产列表
     */
    private void setAssetOwnerDepartment(List<CarAssets> carAssetsList) {
        if (CollectionUtils.isEmpty(carAssetsList)){
            return;
        }
        log.info("setAssetOwnerDepartment carAssetsList:{}", carAssetsList);
        // 1 获取新增资产的责任人MIS列表
        List<String> misIds = carAssetsList.stream()
                .map(CarAssets::getPersonmis)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 2 调用ORG服务查询资产责任人的组织名称
        Map<String,String> misIdOrgNameMap = orgQueryService.batchGetMisIdOrgNameMap(misIds);
        if(Objects.isNull(misIdOrgNameMap)){
            return;
        }
        // 3 设置资产的所属组织名称
        carAssetsList.forEach(carAssets -> {
            String misId = carAssets.getPersonmis();
            if (misIdOrgNameMap.containsKey(misId)){
                carAssets.setOwnerDepartment(misIdOrgNameMap.get(misId));
            }
        });
        log.info("setAssetOwnerDepartment updated carAssetsList:{}", carAssetsList);
    }

    /**
     * 判断是否需要更新资产
     * @param vin 车架号
     * @return 是否需要更新
     */
    private Boolean isNeedUpdate(String vin){
        if(StringUtils.isBlank(vin) || CollectionUtils.isEmpty(vinListSet)){
            return false;
        }

        return vinListSet.contains(ALL) || vinListSet.contains(vin);
    }
}
