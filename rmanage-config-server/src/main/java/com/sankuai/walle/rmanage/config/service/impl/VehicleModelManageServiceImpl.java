package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.entity.VehicleModelAttributes;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import com.sankuai.walle.dal.eve.entity.CarDevices;
import com.sankuai.walle.dal.eve.entity.DeviceTypes;
import com.sankuai.walle.dal.eve.entity.VehicleModel;
import com.sankuai.walle.dal.eve.example.CarDevicesExample;
import com.sankuai.walle.dal.eve.example.VehicleModelExample;
import com.sankuai.walle.dal.eve.mapper.CarDevicesMapper;
import com.sankuai.walle.dal.eve.mapper.VehicleModelMapper;
import com.sankuai.walle.objects.constants.CarDeviceTypeEnum;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.RelatedDeviceInfoVO;
import com.sankuai.walle.objects.vo.request.VehicleModelDeviceRelationRequest;
import com.sankuai.walle.rmanage.config.common.exception.DuplicateCategoryException;
import com.sankuai.walle.rmanage.config.constant.enums.VehicleModelAddTypeEnum;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelAttributesDTO;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelDTO;
import com.sankuai.walle.rmanage.config.service.DeviceTypesService;
import com.sankuai.walle.rmanage.config.service.VehicleModelAttributesService;
import com.sankuai.walle.rmanage.config.service.VehicleModelManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.util.*;



import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VehicleModelManageServiceImpl implements VehicleModelManageService {

    @Resource
    private VehicleModelAttributesService vehicleModelAttributesService;

    @Resource
    private CarSelectsMapper carSelectsMapper;

    @Resource
    private DeviceTypesService deviceTypesService;

    @Resource
    private VehicleModelMapper vehicleModelMapper;
    @Resource
    CarDevicesMapper carDevicesMapper;

    /**
     * 插入车辆类型
     * @param carSelects 车辆类型对象
     * @return 返回插入后的车辆类型ID
     */
    @Override
    public Long insertCarSelect(CarSelects carSelects){
        carSelectsMapper.insertSelective(carSelects);
        return carSelects.getId();
    }

    /**
     * 关联车辆类型和设备类型
     * @param request 关联请求
     */
    @Override
    public void relateVehicleModelDevice(VehicleModelDeviceRelationRequest request) throws DuplicateCategoryException {

        Long vehicleModelId = request.getVehicleModelId();
        List<Long> deviceIds = request.getDeviceIds();
        Integer level = request.getVehicleModelLevel();

        // 根据车型Id 和 level 查询绑定的设备列表
        List<VehicleModel> relatedDeviceList = getRelationByVehicleModelIdAndLevel(vehicleModelId, level);
        List<Long> relatedDeviceIds = relatedDeviceList.stream().map(VehicleModel::getDeviceId).collect(Collectors.toList());
        List<DeviceTypes>  relatedDevicelist = deviceTypesService.batchGetDeviceTypesByIds(relatedDeviceIds);
        log.info("relateVehicleModelDevice, relatedDevicelist = {}", relatedDevicelist);
        // 使用 Set 来确保 category 的唯一性
        Set<String> uniqueCategories = new HashSet<>();
        // 车型当前已关联的设备列表
        for (DeviceTypes device : relatedDevicelist) {
            // 兼容设备分类为空的场景（历史数据）,对于没有绑定分类的涉笔可以关联多个
            if (StringUtils.isNotBlank(device.getCategory()) && !uniqueCategories.add(device.getCategory())) {
                String err = String.format("相同设备分类不允许重复添加, 重复设备分类: %s", device.getCategory());
                throw new DuplicateCategoryException(err);
            }
        }

        // 根据设备Id列表获取设备列表的详情
        List<DeviceTypes> addDeviceList = deviceTypesService.batchGetDeviceTypesByIds(deviceIds);
        for (DeviceTypes device : addDeviceList) {
            if (StringUtils.isNotBlank(device.getCategory()) && !uniqueCategories.add(device.getCategory())) {
                String err = String.format("相同设备分类不允许重复添加, 重复设备分类: %s", device.getCategory());
                throw new DuplicateCategoryException(err);
            }
        }
        log.info("relateVehicleModelDevice, addDeviceList = {}", addDeviceList);

        List<VehicleModel> addVehicleModelList = new ArrayList<>();
        // 获取编辑人
        User user = UserUtils.getUser();
        String misId = "";
        if(StringUtils.isNotBlank(user.getLogin())){
            misId = user.getLogin();
        }
        for (Long deviceId : deviceIds) {
            addVehicleModelList.add(VehicleModel.builder().deviceId(deviceId)
                    .vehicleId(vehicleModelId)
                    .editor(misId)
                    .isDeleted(false)
                    .level(level).build());
        }
        log.info("relateVehicleModelDevice, addVehicleModelList = {}", addVehicleModelList);
        vehicleModelMapper.batchInsert(addVehicleModelList);

    }

    /**
     * 删除车辆类型和设备类型关联关系
     * @param relatedId 关联关系ID
     */
    @Override
    public void deleteVehicleModelDeviceRelation(Long relatedId) {
        vehicleModelMapper.deleteByPrimaryKey(relatedId);
    }

    /**
     * 根据车辆类型查询关联设备信息
     * @param vehicleModelId 车辆类型ID
     * @param level 车辆类型等级
     * @return 返回关联设备信息列表
     */
    @Override
    public List<RelatedDeviceInfoVO> queryDeviceInfoByDeviceType(Long vehicleModelId, Integer level) {
        List<VehicleModel> relationList = getRelationByVehicleModelIdAndLevel(vehicleModelId, level);
        if(CollectionUtils.isEmpty(relationList)){
            return Collections.emptyList();
        }
        Map<Long, Long> deviceIdRelatedIdMap = relationList.stream().collect(Collectors.toMap(
                VehicleModel::getDeviceId,
                VehicleModel::getId,
                (existingValue, newValue) -> existingValue // 处理重复 key 的策略，这里选择保留现有值
        ));
        List<DeviceTypes> relatedDevicelist = deviceTypesService.batchGetDeviceTypesByIds(
                new ArrayList<>(deviceIdRelatedIdMap.keySet()));

        // 响应格式转化 + 升序排序
        return relatedDevicelist.stream()
                .map(deviceTypes ->  deviceTypesService.trance2RelatedDeviceInfoVO(deviceTypes,
                                deviceIdRelatedIdMap.getOrDefault(deviceTypes.getId(), null)))
                .collect(Collectors.toList());
    }

    /**
     * 获取车辆类型列表
     * @param carSelects 车辆类型查询对象
     * @return 返回车辆类型列表
     */
    @Override
    public List<CarSelects> getCarSelects(CarSelects carSelects){
        CarSelectsExample example = new CarSelectsExample();
        CarSelectsExample.Criteria criteria = example.createCriteria().
                andIsDeletedEqualTo(false).andBelongEqualTo(CommonConstants.CAR_TYPE);
        if(StringUtils.isNotBlank(carSelects.getName())){
            criteria.andNameEqualTo(carSelects.getName());
        }
        if(!Objects.isNull(carSelects.getFatherId())){
            criteria.andFatherIdEqualTo(carSelects.getFatherId());
        }
        log.info("getCarSelects, carSelects = {}", carSelects);
        return carSelectsMapper.selectByExample(example);
    }

    /**
     * 插入车型信息
     * @param fatherId 父级车型ID
     * @param vehicleModelDTO 车型创建请求
     * @param misId 用户ID
     * @param typeEnum 车型添加类型
     */
    public void insertVehicleModelInfo(Long fatherId, VehicleModelDTO vehicleModelDTO, String misId, VehicleModelAddTypeEnum typeEnum){
        log.info("insertVehicleModelInfo, typeEnum = {}, vehicleModelDTO = {}", typeEnum, vehicleModelDTO);
        try{
            // 插入车型信息
            String carType = addVehicleModelByType(fatherId, misId, vehicleModelDTO, typeEnum);
            if(StringUtils.isBlank(carType)){
                log.error("insertVehicleModelInfo,get carType error, request = {}", vehicleModelDTO);
                throw new RuntimeException("get vehicleModelId error");
            }
            // 插入车型属性信息
            insertVehicleModelAttributes(vehicleModelDTO, misId, carType);
        }
        catch (Exception e){
            log.error("insertVehicleModelInfo error,request = {}", vehicleModelDTO);
            throw new RuntimeException(e);
        }
    }

    /**
     * 插入车型
     * @param fatherId 父级车型ID
     * @param misId 用户ID
     * @param vehicleModelDTO 车型创建请求
     * @param typeEnum 车型添加类型
     * @return 返回插入后的车型type
     */
    private String addVehicleModelByType(Long fatherId, String misId, VehicleModelDTO vehicleModelDTO, VehicleModelAddTypeEnum typeEnum){

        String carType = "";
        switch (typeEnum){
            case ADD_SON_CAR_TYPE:
                // 插入二级车型
                insertCarSelect(CarSelects.builder().name(vehicleModelDTO.getSonTypeName())
                        .type(vehicleModelDTO.getSonType())
                        .fatherId(fatherId)
                        .editor(misId)
                        .belong(CommonConstants.CAR_TYPE).build());
                carType = vehicleModelDTO.getSonType();
                break;
            case ADD_FATHER_CAR_TYPE:
                // 插入一级车型
                insertCarSelect(CarSelects.builder().name(vehicleModelDTO.getFatherTypeName())
                        .type(vehicleModelDTO.getFatherType())
                        .fatherId(CommonConstants.DEFAULT_FATHER_ID)
                        .editor(misId)
                        .belong(CommonConstants.CAR_TYPE).build());
                carType = vehicleModelDTO.getFatherType();
                break;
            case ADD_ALL_CAR_TYPE:
                // 插入一级车型
                fatherId = insertCarSelect(CarSelects.builder().name(vehicleModelDTO.getFatherTypeName())
                        .type(vehicleModelDTO.getFatherType())
                        .fatherId(CommonConstants.DEFAULT_FATHER_ID)
                        .editor(misId)
                        .belong(CommonConstants.CAR_TYPE).build());
                // 插入二级车型
                insertCarSelect(CarSelects.builder().name(vehicleModelDTO.getSonTypeName())
                        .type(vehicleModelDTO.getSonType())
                        .fatherId(fatherId)
                        .editor(misId)
                        .belong(CommonConstants.CAR_TYPE).build());
                carType = vehicleModelDTO.getSonType();
                break;
        }
        return carType;
    }

    /**
     * 插入车型属性
     * @param vehicleModelDTO 车型创建请求
     * @param misId 用户ID
     */
    private void insertVehicleModelAttributes(VehicleModelDTO vehicleModelDTO, String misId, String carType){
        log.info("insertVehicleModelAttributes, vehicleModelDTO = {}", vehicleModelDTO);
        VehicleModelAttributesDTO attributesDTO = VehicleModelAttributesDTO.from(vehicleModelDTO);
        if(Objects.isNull(attributesDTO)){
            return;
        }
        // 将 VehicleModelAttributesDTO 转化成 List<VehicleModelAttributes>
        List<VehicleModelAttributes> vehicleModelAttributes = new ArrayList<>();
        Field[] fields = attributesDTO.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(attributesDTO);
                // 过滤不为空的字段
                if (value != null) {
                    VehicleModelAttributes attribute = new VehicleModelAttributes();
                    attribute.setAttributeName(field.getName());
                    attribute.setAttributeValue(value.toString());
                    attribute.setIsDeleted(false);
                    attribute.setEditor(misId);
                    attribute.setCarType(carType);
                    vehicleModelAttributes.add(attribute);
                }
            } catch (IllegalAccessException e) {
                log.error("VehicleModelAttributesDTO to List<VehicleModelAttributes> error", e);
            }
        }
        vehicleModelAttributesService.batchInsertVehicleModelAttributes(vehicleModelAttributes);
    }

    /**
     * 根据车型Id和等级获取绑定的设备列表
     * @param vehicleModelId 车型ID
     * @param level 车型等级
     * @return 返回绑定的设备列表
     */
    private List<VehicleModel> getRelationByVehicleModelIdAndLevel(Long vehicleModelId, Integer level){
        VehicleModelExample example = new VehicleModelExample();
        example.createCriteria().andIsDeletedEqualTo(false).andVehicleIdEqualTo(vehicleModelId).andLevelEqualTo(level);
        List<VehicleModel> vehicleModels = vehicleModelMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(vehicleModels)){
            return new ArrayList<>();
        }
        log.info("getDeviceIdsByVehicleModelIdAndLevel, vehicleModels = {}", vehicleModels);
        return vehicleModels;
    }

    /**
     * 绑定车辆vin和设备sn
     * */
    @Override
    public void relateVehicleModelDevice(String vin, String sn, CarDeviceTypeEnum type, String mac){
        // 如果没有vin，则查找历史绑定，并解绑。然后返回；
        // 如果有vin：
        // 先查询有无历史sn绑定，如果有，且历史vin和当前vin不同，则先解绑；如果相同，则不处理，直接返回
        // 如果有vin，则将当前vin和sn绑定
        if (StringUtils.isBlank(sn)){
            return;
        }
        CarDevicesExample example = new CarDevicesExample();
        example.createCriteria().andSnEqualTo(sn);
        List<CarDevices> devices = carDevicesMapper.selectByExample(example);
        if (StringUtils.isBlank(vin)){
            this.unbindHistoryDevice(devices);
            return;
        }
        if (!CollectionUtils.isEmpty(devices)){
            if (!vin.equals(devices.get(0).getVin())){
                this.unbindHistoryDevice(devices);
            }else{
                return;
            }
        }
        this.bindDevice(vin, sn, type, mac);
    }

    @Override
    public List<CarDevices> queryDevicesByVin(String vin){
        CarDevicesExample example = new CarDevicesExample();
        example.createCriteria().andVinEqualTo(vin);
        return carDevicesMapper.selectByExample(example);
    }

    private void bindDevice(String vin, String sn, CarDeviceTypeEnum type, String mac){
        String stype = type.name();
        CarDevicesExample example = new CarDevicesExample();
        example.createCriteria().andVinEqualTo(vin).andTypeEqualTo(stype);
        List<CarDevices> obj = carDevicesMapper.selectByExample(example);

        CarDevices carDevices = new CarDevices();
        carDevices.setVin(vin);
        carDevices.setSn(sn);
        carDevices.setType(stype);
        carDevices.setMac(mac);
        User user = UserUtils.getUser();
        if (Objects.nonNull(user)){
            carDevices.setOperatorId(user.getLogin());
        }else {
            carDevices.setOperatorId("");
        }
        if (CollectionUtils.isEmpty(obj)){
            carDevicesMapper.insert(carDevices);
        }else {
            carDevicesMapper.updateByExampleSelective(carDevices, example);
        }
    }
    private void unbindHistoryDevice(List<CarDevices> devices){
        devices.forEach(
                device -> {
                    device.setSn(null);
                    device.setMac(null);
                    carDevicesMapper.updateByPrimaryKey(device);
                }
        );
    }

}
