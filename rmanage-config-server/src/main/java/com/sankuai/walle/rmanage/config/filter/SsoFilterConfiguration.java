package com.sankuai.walle.rmanage.config.filter;


import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.it.sso.sdk.spring.FilterFactoryBean;
import com.sankuai.meituan.uac.sdk.filter.UacFilterFactoryBean;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.meituan.uac.sdk.service.UacResourceRemoteService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.Filter;
import java.util.Map;

import static javax.servlet.DispatcherType.FORWARD;
import static javax.servlet.DispatcherType.REQUEST;

@Configuration
public class SsoFilterConfiguration {


//    @Value("${uac.appKey}")
    String appKey;

//    @Value("${uac.secret}")
    String appSecret;

//    @Value("${uac.host}")
    String uacHost;

    String ssoSecret;

    String clientId;

    SsoFilterConfiguration (){
        ConfigRepository config = Lion.getConfigRepository();
        appKey = config.get("uacAppkey");
        appSecret = config.get("uacAppSecret");
        uacHost = config.get("uacHost");
        ssoSecret = config.get("ssoSecret");
        clientId = config.get("clientId");
    }

    @Bean
    public UacAuthRemoteService uacAuthRemoteService() {
        UacAuthRemoteService uacAuthRemoteService = new UacAuthRemoteService();
        uacAuthRemoteService.setUacHost(this.uacHost);
        uacAuthRemoteService.setUacClientId(this.appKey);
        uacAuthRemoteService.setUacSecret(this.appSecret);
        return uacAuthRemoteService;
    }

    @Bean
    public UacResourceRemoteService uacResourceRemoteService() {
        UacResourceRemoteService uacResourceRemoteService = new UacResourceRemoteService();
        uacResourceRemoteService.setUacHost(this.uacHost);
        uacResourceRemoteService.setUacClientId(this.appKey);
        uacResourceRemoteService.setUacSecret(this.appSecret);
        return uacResourceRemoteService;
    }

    @Bean
    public FilterRegistrationBean mtFilter(@Qualifier("mtFilterBean") Filter mtFilterBean) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(mtFilterBean);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(REQUEST);
        registration.setName("mtFilter");
        registration.setOrder(1);
        return registration;
    }

    @Bean
    public FilterRegistrationBean uacFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("uacFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(FORWARD);
        registration.setName("uacFilter");

        //注意顺序应设置在SSO filter之后
        registration.setOrder(2);
        return registration;
    }

    /**
     * uacFilterBean配置
     */
    @Bean
    public UacFilterFactoryBean uacFilterBean() {
        UacFilterFactoryBean filterFactoryBean = new UacFilterFactoryBean();

        //必须配置，以下二者需先到开放平台(http://open.sankuai.com)申请接入UAC后颁发，对应企平开放平台的AppKey和AppSecret
        filterFactoryBean.setAppKey(appKey);
        filterFactoryBean.setSecret(appSecret);

        //接入的UAC HOST地址，线下为：http://uac.it.beta.sankuai.com 线上为：http://uac.vip.sankuai.com
        filterFactoryBean.setHost(uacHost);

        //------------------------以下配置为可选配置--------------------------------------

        //表示需要经过UAC鉴权的uri，多个之间以','分割,支持Ant风格路径表达式。
        //includedUriList中的uri必须经过SSO进行过滤，否则UAC无法获取当前登录人信息进行鉴权。
//        filterFactoryBean.setIncludedUriList("/eve/**,/api/**");
//        filterFactoryBean.setIncludedUriList("\"/eve/cmdb/rest/api/**,\n" +
//                "/eve/cmdb/rest/auk/**,\n" +
//                "/eve/cmdb/rest/switch/**,\n" +
//                "/eve/cmdb/rest/equipment/**,\n" +
//                "/eve/cmdb/rest/output/**,\n" +
//                "/eve/cmdb/rest/vehicle/**,\n" +
//                "/eve/cmdb/rest/update/**\",/api/**");
//
//        //表示不需要经过UAC鉴权的uri。两者都配置的情况下，excludedUriList会失效，includedUriList优先级更高。
        filterFactoryBean.setExcludedUriList("/static/**,/octo/checkAlive,/monitor/**,/eve/cmdb/rest/output/**,/eve/cmdb/rest/access/**");

        //UAC授权失败后返回的错误信息
        filterFactoryBean.setAuthFailedResponse("{\"status\":501,\"message\":\"您没有权限访问,请通过uac开通\",\"version\":\"1.0.0-SNAPSHOT\",\"data\":{\"message\":\"您没有权限访问\"}}");

        //是否关闭UAC日志功能（URI鉴权过程打印一些日志，方便调试），true表示关闭，默认为false，即开启日志功能。
        filterFactoryBean.setLogClosed(true);
        return filterFactoryBean;
    }


    /**
     * mtFilter配置
     可在本地配置具体的值，每一配置的含义参考第三节
     */
    @Bean
    public FilterFactoryBean mtFilterBean() {
        ConfigRepository config = Lion.getConfigRepository();
        Map<String, String> configs = config.getConfigs();

        FilterFactoryBean filterFactoryBean = new FilterFactoryBean();
        filterFactoryBean.setClientId(clientId);
        filterFactoryBean.setSecret(ssoSecret);

        /**
         * 不需要 SSO 检查的 Url 配置, 多个以逗号分隔，允许换行
         * 单独配 includedUriList，includedUriList 以外的链接都不检查sso登录
         * 单独配 excludedUriList，excludedUriList 以外的链接都会检查sso登录
         * includedUriList，excludedUriList 都有的时候，includedUriList 优先级更高
         **/
        filterFactoryBean.setExcludedUriList("/static/**,/octo/checkAlive,/monitor/**,/eve/cmdb/rest/output/**,/eve/cmdb/rest/access/**");

        //---------------以下配置请业务方系统阅读其使用方法再行接入----------------
        //表示需要经过SSO过滤的uri，多个uri以','分割。
        //两者都配置的情况下，includedUriList优先级更高
//        filterFactoryBean.setIncludedUriList("/test/**,/api/**,/eve/cmdb/rest/api/**,/eve/cmdb/rest/equipment/**,/eve/cmdb/rest/vehicle/**");
//        filterFactoryBean.setIncludedUriList("\"/eve/cmdb/rest/api/**,\n" +
//                "/eve/cmdb/rest/auk/**,\n" +
//                "/eve/cmdb/rest/switch/**,\n" +
//                "/eve/cmdb/rest/equipment/**,\n" +
//                "/eve/cmdb/rest/output/**,\n" +
//                "/eve/cmdb/rest/vehicle/**,\n" +
//                "/eve/cmdb/rest/update/**\",/api/**");
        /**
         * 根据实际情况指定接入SSO线下(取值test)或线上(取值prod)环境。
         * ##请不要接入SSO的dev和staging环境，不提供稳定服务##
         * 默认不需要配置，SDK将根据客户端环境自动对齐，
         * 即dev、test对齐到SSO线下环境，staging和prod对齐到SSO线上环境
         */
//        filterFactoryBean.setAccessEnv("test");//test或prod

        /**
         * 根据实际情况指定该监听器，用于业务方需要进行额外的一些扩展功能。关于监听器，请参考：第五节
         */
//        filterFactoryBean.setSsoListener("com.sankuai.it.iam.dataentrance.listener.MySSOListener");// or MySSOListener.class.getName()

        /**
         * 根据实际情况指定，如果nginx配置Location时进行了rewrite抹除，
         * 请在这里填写该location，SDK2.0会在跳转时拼接回正确的url。否则不用填写。
         * 例如：/locationA/uriB经nginx转发后，到后端被重写成/uriB，则/locationA就是rewriteLocation，
         * 参考nginx配置：
         * location /locationA/ {
         *    rewrite /locationA/?(.*)$ /$1 break;
         * }
         * 是否配置rewriteLocation属性请参考：第四节，配置项说明-Location的特别说明
         */
//        filterFactoryBean.setRewriteLocation("/123");

        //可不配置，默认预留/sso/logout作为登出地址，业务方可以直接使用该uri
//        filterFactoryBean.setLogoutUri("/logout");

        //可不配置，表示本应用使用的http或https协议，
        //默认从header "X-Forwarded-Proto"中取值，除非用户自己指定
//        filterFactoryBean.setSchema("https");
        return filterFactoryBean;
    }


//    @Bean
//    public UacMenuRemoteService uacMenuRemoteService() {
//        return new UacMenuRemoteService();
//    }
//
//    @Bean
//    public UacSystemRemoteService uacSystemRemoteService() {
//        return new UacSystemRemoteService();
//    }
}