package com.sankuai.walle.rmanage.config.config;

import com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ThreadRedisConfiguration {

    @Bean(name = "ThreadRedisBean")
    public RedisClientBeanFactory redisClientBeanFactory(@Value("${redis-safe.cluster-name}") String clusterName,
                                                         @Value("${redis-safe.read-timeout}") int readTimeout,
                                                         @Value("${redis-safe.router-type}") String routerType,
                                                         @Value("${redis-safe.pool-max-idle}") int poolMaxIdle,
                                                         @Value("${redis-safe.pool-max-total}") int poolMaxTotal,
                                                         @Value("${redis-safe.pool-wait-millis}") int poolWaitMillis,
                                                         @Value("${redis-safe.pool-min-idle}") int poolMinIdle,
                                                         @Value("${redis-safe.serialize-type}") String serializeType) {
        RedisClientBeanFactory factory = new RedisClientBeanFactory();
        factory.setClusterName(clusterName);
        factory.setReadTimeout(readTimeout);
        factory.setRouterType(routerType);
        factory.setPoolMaxIdle(poolMaxIdle);
        factory.setPoolMaxTotal(poolMaxTotal);
        factory.setPoolWaitMillis(poolWaitMillis);
        factory.setPoolMinIdle(poolMinIdle);
        factory.setSerializeType(serializeType);
        return factory;
    }
}
