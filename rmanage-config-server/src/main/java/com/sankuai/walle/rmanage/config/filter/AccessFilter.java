package com.sankuai.walle.rmanage.config.filter;


import com.sankuai.oceanus.http.filter.InfFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

@Configuration
@Slf4j
public class AccessFilter {

    @Bean
    public FilterRegistrationBean<Filter> infFilter() {
        // 创建InfFilter
        InfFilter filter = new InfFilter();
        // 创建过滤器注册Bean
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/eve/cmdb/rest/access/*");
        registration.addInitParameter("access.auth", "true"); // 将降级开关设置为true，否则无法正常使用鉴权功能
        // 排除探活接口，根据服务实际情况配置
        registration.addInitParameter("oceanus-http.inffilter.auth-exclude-uri", "/monitor/alive");
        registration.setName("inf-filter");
        return registration;
    }
}
