package com.sankuai.walle.rmanage.config.infrastructure.leaf;

import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Created on 2021/12/14
 */
@EnableRetry
@Slf4j
@Service
public class LeafIdService {
    @Resource
    private IDGen.Iface idGenThriftService;

    /**
     * 根据leafKey获取snowflake方式发号id
     *
     * @param leafKey
     * @return
     */
    /*
     * Retryable的参数说明：
     * value：抛出指定异常才会重试
     * include：和value一样，默认为空，当exclude也为空时，默认所以异常
     * exclude：指定不处理的异常
     * maxAttempts：最大重试次数，默认3次
     * backoff：重试等待策略，默认使用@Backoff，@Backoff的value默认为1000L，设置为1L；multiplier（指定延迟倍数）默认为0，
     * delay: 表示固定暂停1ms后进行重试，如果把multiplier设置为2，则第一次重试为1ms，第二次为2ms，第三次为4ms，以此类推。
     */
    @Retryable(value = Exception.class, maxAttempts = 5, backoff = @Backoff(delay = 1L, multiplier = 2))
    public long getSnowFlackId(String leafKey) throws Exception {
        try {
            Result result = idGenThriftService.getSnowFlake(leafKey);
            if (result != null && result.getStatus() == Status.SUCCESS) {
                log.info("next SnowFlackId leafKey:{} id:{}", leafKey, result.getId());
                return result.getId();
            } else {
                log.error("leaf getSnowFlackId result error, leafKey:{} result:{}", leafKey, result);
            }
        } catch (TException e) {
            log.error("leaf getSnowFlackId error, leafKey:{}", leafKey, e);
        }
        throw new RuntimeException("leaf id生成错误, leafKey=" + leafKey);
    }
}
