package com.sankuai.walle.rmanage.config.controller;


import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.walle.carManage.entity.*;
import com.sankuai.walle.carManage.example.CarExecWordExample;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarOperationExample;
import com.sankuai.walle.carManage.mapper.CarAssetsMapper;
import com.sankuai.walle.carManage.mapper.CarExecWordMapper;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.carManage.mapper.CarOperationMapper;
import com.sankuai.walle.dal.eve.mapper.VehicleBaseInfoMapper;
import com.sankuai.walle.objects.bo.RealTimeObj;
import com.sankuai.walle.objects.constants.CarConstant;
import com.sankuai.walle.objects.constants.CarDeviceTypeEnum;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.CarDeviceVO;
import com.sankuai.walle.objects.vo.request.CarAllocateReq;
import com.sankuai.walle.objects.vo.request.CarEditReq;
import com.sankuai.walle.objects.vo.request.ImportCarReq;
import com.sankuai.walle.objects.vo.request.ImportInsuranceReq;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.application.InsuranceImportApplicationService;
import com.sankuai.walle.rmanage.config.application.VehicleEditApplicationService;
import com.sankuai.walle.rmanage.config.dto.InsuranceImportResultDTO;
import com.sankuai.walle.rmanage.config.dto.VehicleEditResultDTO;
import com.sankuai.walle.rmanage.config.service.*;
import com.sankuai.walle.rmanage.config.service.impl.VehicleInfoServiceImpl;
import com.sankuai.walle.rmanage.config.service.VehicleRealtimeService;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.sankuai.walle.rmanage.config.common.constant.ConfigForVehicleTypeConstants.ConfigForVehicleTypeList;

/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping
@Slf4j
public class VechicleEditController {

    @Resource
    CarAssetsMapper carAssetsMapper;
    @Resource
    CarOperationMapper carOperationMapper;

    @Resource
    CarExecWordMapper carExecWordMapper;

    @Resource
    CarObjectsMapper carObjectsMapper;

    @Resource
    AssetService assetService;
    @Resource
    ConfigService configService;

//    @MethodDoc(
//            displayName = "判断vin是否唯一"
//    )
//    @RequestMapping(path = {"/api/cmdb/vehicle/1.3/car/validator/unique"}, method = RequestMethod.GET)
//    public ResData validatorVin(HttpServletRequest request, @RequestParam(required = false) String vin,
//                                @RequestParam(required = false) String sn) {
//        ResData rep = new ResData();
//        rep.code = Constants.errorCode;
//
//        try {
//            int size = 0;
//            if(vin!=null) {
//                size = carObjectsMapper.selectByExample(new CarObjectsExample(){{
//                    createCriteria().andVinEqualTo(vin);
//                }}).size();
//            } else if (sn!=null){
//                size = carAssetsMapper.selectByExample(new CarAssetsExample(){{
//                    createCriteria().andSnEqualTo(sn);
//                }}).size();
//            }
//            rep.data = size;
//            if(size>0){ rep.msg = "已存在";}
//            else{ rep.msg = "可用"; }
//            rep.code = Constants.succeedCode;
//            return rep;
//        } catch (Exception e) {
//            rep.msg = e.getMessage();
//            log.error(vin+"<获取车辆详情> {}",e);
//            return rep;
//        }
//    }
    @Autowired
    MafkaRealtimeService realtimeService;
    @Resource
    VehicleInfoService vehicleInfoService;
    @Resource
    ActionLogService actionLogService;
    @Resource
    VehicleModelManageService vehicleModelManageService;

    @Resource
    InsuranceImportApplicationService insuranceImportApplicationService;
    
    @Resource
    VehicleEditApplicationService vehicleEditApplicationService;
    
    @Resource
    VehicleRealtimeService vehicleRealtimeService;

    @MethodDoc(
            displayName = "新增car"
    )
    @Transactional
    @RequestMapping(path = {"/api/cmdb/vehicle/1.3/car/add"}, method = RequestMethod.POST)
    public ResData AddCar(HttpServletRequest request, @RequestBody CarEditReq body) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;

        CarObjects carObject = body.getCarObjects();
        String vin = carObject.getVin();
        try {
            this.addCar(body);
            rep.code = CommonConstants.SUCCEED_CODE;
            return rep;
        } catch (Exception e) {
            rep.msg = e.getMessage();
            log.error(vin+"<新增车辆> {}",e);
            return rep;
        }
    }

    // 导入车辆信息
    @RequestMapping(path = {"/api/cmdb/vehicle/1.3/car/import"}, method = RequestMethod.POST)
    public ResData importCar(HttpServletRequest request, @RequestBody List<ImportCarReq> body) {
        // 必传字段：carObject: vin,name,carType,assemblyParts   carAssets: smallType,personmis,scrap,carSizeType
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        // 判断所有参数都不能为空
        List<String> exitVin = new ArrayList<>();
        body.forEach(importCarReq -> {
            String vin = importCarReq.getVin();
            CarObjects carObject = new CarObjects();
            carObject.setVin(vin);
            carObject.setName(importCarReq.getName());
            carObject.setCarType(importCarReq.getCarType());
            carObject.setAssemblyParts(importCarReq.getAssemblyParts());
            carObject.setAddTime(new Date());
            carObject.setUpdateTime(new Date());
            carObject.setLicenseno(importCarReq.getLicenseno());

            CarAssets carAssets = new CarAssets();
            carAssets.setVin(vin);
            carAssets.setSmallType(importCarReq.getSmallType());
            carAssets.setPersonmis(importCarReq.getPersonmis());
            carAssets.setScrap(importCarReq.getScrap());
            carAssets.setCarSizeType(importCarReq.getCarSizeType());


            List<CarObjects> searchObjects = carObjectsMapper.selectByExample(new CarObjectsExample() {{
                createCriteria().andVinEqualTo(vin);
            }});
            if(searchObjects.size()==0) {
                vehicleInfoService.insertVehicleInfo(Lists.newArrayList(carObject));
                carObjectsMapper.insertSelective(carObject);
                carAssetsMapper.insertSelective(carAssets);
                actionLogService.insertCarManageActionLog(vin, UserUtils.getUser().getLogin(), "新增车辆");
            } else {
                exitVin.add(vin);
            }
            // 不论是否存在，都触发消息发送
            try {
                vehicleRealtimeService.sendRealTime(vin, null, carObject.getName(), carObject.getCarType());
            } catch (Exception e) {
                log.error("发送实时消息失败, vin: {}" ,vin, e);
            }
        });
        log.info("导入车辆信息成功, 已存在的车辆: {}", exitVin);
        rep.msg = "导入车辆信息成功, 已存在的车辆: "+exitVin;
        rep.data = exitVin;
        rep.code = CommonConstants.SUCCEED_CODE;
        return rep;
    }

    // 导入保险日期信息 - DDD重构版本
    @MethodDoc(
            displayName = "导入保险日期信息"
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/1.3/car/import/insurance"}, method = RequestMethod.POST)
    public ResData importInsurance(HttpServletRequest request, @RequestBody List<ImportInsuranceReq> body) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        
        try {
            // 调用应用服务处理业务逻辑
            InsuranceImportResultDTO result = insuranceImportApplicationService.importInsuranceBatch(body);
            
            // 记录操作日志
//            for (String vin : result.getSuccessVins()) {
//                actionLogService.insertCarManageActionLog(vin, UserUtils.getUser().getLogin(), "更新保险信息");
//            }
            
            rep.data = result;
            rep.msg = result.buildMessage();
            rep.code = CommonConstants.SUCCEED_CODE;
            
        } catch (Exception e) {
            rep.msg = "导入保险信息失败: " + e.getMessage();
            log.error("导入保险信息失败", e);
        }
        
        return rep;
    }

    private void addCar(CarEditReq body) throws Exception {
        CarObjects carObject = body.getCarObjects();
        String vin = carObject.getVin();
        CarSelects cartype = body.getCarType();
        carObject.setCarType(cartype.getType());
        carObject.setAddTime(new Date());
        carObject.setUpdateTime(new Date());

        CarAssets carasset = body.getCarAssets();
        carasset.setVin(vin);
        carasset.setSmallType("cmdb新增");
        carasset.setAddTime(new Date());
        carasset.setUpdateTime(new Date());

        CarOperation carOperation = body.getCarOperation();
        carOperation.setVin(vin);
        carOperation.setAddTime(new Date());
        carOperation.setUpdateTime(new Date());

        CarEditReq.CarExecWordConvertor carExecWordConvertor = body.getCarExecWord();
        carExecWordConvertor.setVin(vin);
        carExecWordConvertor.setAddTime(new Date());
        carExecWordConvertor.setUpdateTime(new Date());
        CarExecWord carExecWord = CarEditReq.CarExecWordConvertor.objExecToJson(carExecWordConvertor);

        List<CarObjects> searchObjects = carObjectsMapper.selectByExample(new CarObjectsExample() {{
            createCriteria().andVinEqualTo(vin);
        }});
        if(searchObjects.size()!=0) {
            throw new Exception("车辆已存在");
        }
        carObjectsMapper.insertSelective(carObject);
        vehicleInfoService.insertVehicleInfo(Lists.newArrayList(carObject));
        carAssetsMapper.insertSelective(carasset);
        carOperationMapper.insertSelective(carOperation);
        carExecWordMapper.insertSelective(carExecWord);
        actionLogService.insertCarManageActionLog(vin, UserUtils.getUser().getLogin(), "新增车辆");
        
        // 使用实时消息服务发送消息
        vehicleRealtimeService.sendRealTime(vin, carOperation, carObject.getName(), cartype.getType());
    }

    @MethodDoc(
            displayName = "修改车辆信息"
    )
    @Transactional
    @RequestMapping(path = {"/api/cmdb/vehicle/1.3/car/edit"}, method = RequestMethod.POST)
    public ResData EditCar(HttpServletRequest request, @RequestBody CarEditReq body) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        
        try {
            // 使用应用服务处理车辆编辑业务逻辑
            VehicleEditResultDTO result = vehicleEditApplicationService.editVehicle(body);
            if (result.isSuccess()) {
                rep.code = CommonConstants.SUCCEED_CODE;
                rep.msg = "车辆编辑成功";
            } else {
                rep.code = CommonConstants.ERROR_CODE;
                rep.msg = result.getErrorMessage();
            }
            return rep;
        } catch (Exception e) {
            rep.msg = e.getMessage();
            log.error("修改车辆失败, vin: {}", body.getCarObjects().getVin(), e);
            return rep;
        }
    }

    // 预判更新 car_object 数据到 vehicle_info 后，vehicle_info 中车辆的status
    @RequestMapping(path = {"/eve/cmdb/rest/vehicle/async/judge-status"}, method = RequestMethod.GET)
    public ResData judgeAsyncVehicleStatus(@RequestParam String vin){
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        HashMap<String, Object> data = new HashMap<>();
        data.put("status", vehicleInfoService.fetchVehicleStatus(vin));
        CarAssets carAsset = assetService.fetchCarAssetsByVin(vin);
        if(carAsset!=null) {
            Byte vehicleType = VehicleInfoServiceImpl.VehicleType.getVehicleTypeByCarSizeType(carAsset.getCarSizeType());
            data.put("vehicleType", vehicleType);
            data.put("owner", carAsset.getPersonmis());
        }
        rep.data = data;
        rep.code = CommonConstants.SUCCEED_CODE;
        return rep;
    }

    // 触发更新car_object 数据到 vehicle_info
    @RequestMapping(path = {"/eve/cmdb/rest/vehicle/async/vehicleinfo"}, method = RequestMethod.GET)
    public ResData updateCarToVehicle(@RequestParam String vin){
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        vehicleInfoService.updateVehicleInfo(vin);
        rep.code = CommonConstants.SUCCEED_CODE;
        return rep;
    }

    @Transactional
    @RequestMapping(path = {"/eve/cmdb/rest/vehicle/1.3/car/allocate"}, method = RequestMethod.POST)
    public ResData allocateCar(HttpServletRequest request, @RequestBody List<CarAllocateReq> body) {
        ResData res = new ResData();
        res.setCode(CommonConstants.ERROR_CODE);
        List<String> vins = new ArrayList<>();
        HashMap<String, CarAllocateReq> map = new HashMap<>();
        for(CarAllocateReq obj: body){
            vins.add(obj.getVin());
            map.put(obj.getVin(), obj);
        }
        List<CarOperation> targets = carOperationMapper.selectByExample(new CarOperationExample() {{
            createCriteria().andVinIn(vins);
        }});
        for(CarOperation operation : targets){
            CarAllocateReq req = map.get(operation.getVin());

            if(!StringUtil.isNullOrEmpty(req.getIncity()) | !StringUtil.isNullOrEmpty(req.getInpark()) |
                    !StringUtil.isNullOrEmpty(req.getInUseType()) ) {
                operation.setArea(req.getInpark());
                operation.setCarUsedTarget(req.getInUseType());
                operation.setCity(req.getIncity());
                operation.setUpdateTime(new Date());
                if(req.getInparkStr()!=null){operation.setAreaStr(req.getInparkStr());}
                if(req.getInUseTypeStr()!=null){operation.setCarUsedTargetStr(req.getInUseTypeStr());}
                carOperationMapper.updateByPrimaryKeySelective(operation);
            }
        }
        res.setCode(CommonConstants.SUCCEED_CODE);
        res.setMsg("success");
        return res;

    }

    @MdpConfig
    ArrayList<String> configS3Vname;



}
