package com.sankuai.walle.rmanage.config.controller;


import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.*;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.constants.SelectBelongConstant;
import com.sankuai.walle.objects.vo.res.ResData;
import lombok.extern.log4j.Log4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping
@Log4j
public class HomePageController {

    @Resource
    CarSelectsMapper carSelectsMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;

    @MethodDoc(
            displayName = "车辆统计",
            description = "车辆统计"
    )
    @RequestMapping(path = {"/api/cmdb/vehicle/1.4/home/<USER>"}, method = RequestMethod.GET)
    public ResData getCarSelects(HttpServletRequest request) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        ArrayList<HashMap<Object, Object>> data = new ArrayList<HashMap<Object, Object>>();
        carSelectsMapper.selectByExample(new CarSelectsExample(){{
            createCriteria().andBelongEqualTo(SelectBelongConstant.CarType);
        }}).forEach(carSelects -> {
            HashMap<Object, Object> child = new HashMap<>();
            int number = carObjectsMapper.selectByExample(new CarObjectsExample() {{
                createCriteria().andCarTypeEqualTo(carSelects.getType());
            }}).size();
            child.put("type", carSelects.getName());
            child.put("flow", number);
            data.add(child);
        });
        rep.data = data;
        rep.code = CommonConstants.SUCCEED_CODE;
        return rep;
    }
    // 车辆用途接口
    @RequestMapping(path = {"/autocar/sre/cmdb/rest/home/<USER>/used_types/get"}, method = RequestMethod.GET)
    public ResData getCarUsedTypes(HttpServletRequest request) {
        ResData rep = new ResData();
        rep.code = CommonConstants.ERROR_CODE;
        ArrayList<HashMap<Object, Object>> data = new ArrayList<HashMap<Object, Object>>();
        carSelectsMapper.selectByExample(new CarSelectsExample(){{
            createCriteria().andBelongEqualTo(SelectBelongConstant.CarType);
        }}).forEach(carSelects -> {
            HashMap<Object, Object> child = new HashMap<>();
            int number = carObjectsMapper.selectByExample(new CarObjectsExample() {{
                createCriteria().andCarTypeEqualTo(carSelects.getType());
            }}).size();
            child.put("type", carSelects.getName());
            child.put("flow", number);
            data.add(child);
        });
        rep.data = data;
        rep.code = CommonConstants.SUCCEED_CODE;
        return rep;
    }
}
