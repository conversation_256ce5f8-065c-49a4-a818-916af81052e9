package com.sankuai.walle.rmanage.config.constant;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Create 2025-04-28 15:00
 */

@Component
public class ConfigEnumConstant {
    @MdpConfig("config.enum")
    private ArrayList<String> configs;

    public ArrayList<String> getConfigs() {
        return configs;
    }

    public Boolean contains(String configName) {
        return configs.contains(configName);
    }
}

//        IPMI,//impi
//        QIANXUN,// 千寻和六分定位
//        USB_WHITELIST,// usb白名单
//        VSREPORT_OBU,// 状态监控obu的配置
//        SIM_MANAGE,// SIM卡管理
//        HVI, HVI_UI_V1,// 车机配置
//        RTK,// 新的千寻和六分定位
//        HOSTNAME,//一个历史的配置，用来配置车辆名称
//        UDS_VCU,// 底盘vcu的配置
//        FluentBit, // 混沌工程配置项
//        FluentBitIPC,
//        FluentBitParsers,
//        SwiningConfig, // 双通道配置
//        RTK,SSH_PUB_KEY,USB_WHITE_LIST,OBU_UPLOAD,RFID_IP,SIM_CONFIG
//        SECTY, // 安全组证书配置
//        FluentBitJ3A,FluentBitJ3B,FluentBitJ3C, // J3上的 flunentbit配置
//        NET_TFC_STATS,NET_URL_SOURCE,NET_LIMIT,NET_RECOGNITIONS, // 1、车辆流量统计的配置 2、车端所有的域名信息 3、车端网络限速配置信息 4、车端流量识别配置信息
//        FluentbitMADCOrinA, FluentbitMADCOrinB, FluentbitDRB, // MADC, DRB的fluentbit配置
//        CAR_INFO,  // 车型信息
//        SYSMON_CXX_CONFIG, // sysmon_CXX配置
//        SOFT_GRAY_SWITCH, // 替代SYSMON_CXX_CONFIG，功能扩展为所有车的灰度控制开关
//        SSH_CONFIG, // ssh到tbu的配置项
//        DELIVERY_TAG, // 运力标签配置项
//
//        SCRIPT,
//        TROUBLE_TREE, // 故障树结构配置
//        UDS_XCU, // UDS_XCU配置