package com.sankuai.walle.rmanage.config.service.impl;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.sankuai.walle.rmanage.config.service.infrastructureService.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.List;

@Service
@Slf4j
public class S3ServiceImpl implements S3Service {

    @Autowired
    @Qualifier("s3PlusClient0")
    private AmazonS3 amazonS3;

    @Override
    public void pushFileToS3(String bucketName, String objectName, byte[] input) {
        PutObjectResult data = amazonS3.putObject(bucketName, objectName, new ByteArrayInputStream(input), null);
    }

    // 查询文件列表，看目标目录，以及目标文件是否存在
    @Override
    public List<S3ObjectSummary> listObjects(String bucketName, String prefix) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        listObjectsRequest.setBucketName(bucketName);
        listObjectsRequest.setPrefix(prefix);
        ObjectListing objectListing = amazonS3.listObjects(listObjectsRequest);
        return objectListing.getObjectSummaries();
    }

    // 获取元信息
    @Override
    public ObjectMetadata getMetaData(String bucketName, String objectName) {
        try {
            ObjectMetadata objectMetadata = amazonS3.getObjectMetadata(bucketName, objectName);
            return objectMetadata;
        } catch (AmazonS3Exception e){
            if (e.getStatusCode() == 404){
                return null;
            }
            throw e;
        }
    }

    // 写入S3
    @Override
    public void writeFileToS3(String bucketName, String objectName, String content) {
        // 如果文件存在，则删除，再新增
        if (getMetaData(bucketName, objectName) != null) {
            deleteObjectExample(bucketName, objectName);
        }
        amazonS3.putObject(bucketName,objectName,new ByteArrayInputStream(content.getBytes()),null);
    }

    // 获取object
    @Override
    public String getObjectAsString(String bucketName, String objectName) throws IOException {
        S3Object s3object = null;
        try{
            //bucketName是桶名（服务秘钥要用桶别名）
            //objectName是文件名
            s3object = amazonS3.getObject(new GetObjectRequest(
                    bucketName, objectName));
            InputStream content = s3object.getObjectContent();
            BufferedReader reader = new BufferedReader(new InputStreamReader(content));
            StringBuilder res = new StringBuilder();
            while (true) {
                String line = reader.readLine();
                if (line == null) break;
                System.out.println("\n" + line);
                res.append(line+"\n");
            }
            // 如果res存在，去掉最后一个\n
            if (res.length() > 0) {
                res.deleteCharAt(res.length() - 1);
            }
            return res.toString();
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            log.error("Caught an ServiceException.");
            log.error("Error Message: " + ase.getMessage());
            log.error("HTTP Status Code: " + ase.getStatusCode());
            log.error("Error Code: " + ase.getErrorCode());
            log.error("Error Type: " + ase.getErrorType());
            log.error("Request ID: " + ase.getRequestId());
            Assert.assertEquals(true, true);
        } catch (AmazonClientException ace) {
            // 客户端处理异常
            log.error("Caught an ClientException.");
            log.error("Error Message: " + ace.getMessage());
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 获取object后需要close(),释放连接
            if (s3object != null){
                s3object.close();
            }
        }
        return "";
    }

    @Override
    public void deleteObjectExample(String bucketName, String objectName){
        try{
            amazonS3.deleteObject(new DeleteObjectRequest(bucketName, objectName));
        }catch (AmazonServiceException ase) {
            //存储服务端处理异常
            log.error("Caught an ServiceException.");
            log.error("Error Message: " + ase.getMessage());
            log.error("HTTP Status Code: " + ase.getStatusCode());
            log.error("Error Code: " + ase.getErrorCode());
            log.error("Error Type: " + ase.getErrorType());
            log.error("Request ID: " + ase.getRequestId());
            Assert.assertEquals(true, true);
        }catch (AmazonClientException ace) {
            //客户端处理异常
            log.error("Caught an ClientException.");
            log.error("Error Message: " + ace.getMessage());
        }
    }

}
