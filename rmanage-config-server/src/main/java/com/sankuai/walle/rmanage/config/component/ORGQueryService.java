package com.sankuai.walle.rmanage.config.component;

import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class ORGQueryService {

    @Autowired
    EmpService empService;

    /**
     * "是否在职 [15-在职|16离职]"
     */
    private static final Integer JOB_STATUS_ID_ON_JOB = 15;

    /**
     * 根据mis查询EMP信息接口的mis最大允许数量
     */
    private static final Integer BATCH_QUERY_EMP_MAX_NUM = 200;

    /**
     * 批量查询mis号对应的组织名称信息
     * @param misIdList mis列表
     * @return mis列表对应的组织名称信息
     */
    public Map<String, String> batchGetMisIdOrgNameMap(List<String> misIdList) {
        log.info("ORGQueryService# batchGetUidMisMap, misList = {}", misIdList);
        // 存储misId - 组织名称的映射关系
        Map<String, String> misIdOrgNameMap = new HashMap<>();
        try {
            // 查询过滤状态不是在职的员工，每批次不得大于200
            int batchSize = BATCH_QUERY_EMP_MAX_NUM;
            int batchCount = (misIdList.size() + batchSize - 1) / batchSize;
            for (int i = 0; i < batchCount; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, misIdList.size());
                List<String> batchList = misIdList.subList(start, end);
                List<Emp> userList = batchQueryByMis(batchList);
                for (Emp emp : userList) {
                    if (JOB_STATUS_ID_ON_JOB.equals(emp.getJobStatusId())) { // 15表示在职
                        misIdOrgNameMap.put(emp.getMis(), emp.getOrgName());
                    }
                }
            }
            log.info("ORGQueryService# batchGetUidMisMap, misIdOrgNameMap = {}", misIdOrgNameMap);
        } catch (Exception e) {
            log.error("ORGQueryService# batchGetUidMisMap error", e);
        }
        return misIdOrgNameMap;
    }

    /**
     * 批量查询mis号对应的Emp信息（ + 重试）
     *
     * @param batchList mis列表
     * @return mis列表对应的ORG信息列表
     */
    private List<Emp> batchQueryByMis(List<String> batchList) {
        int retryNum = 3;
        while (retryNum > 0) {
            try {
                List<Emp> empList = empService.batchQueryByMis(batchList, null);
                log.info("ORGQueryService# batchQueryByMis, empList = {}", empList);
                return empList;
            } catch (Exception e) {
                log.error("ORGQueryService# batchQueryByMis is error", e);
                retryNum--;
            }
        }
        return new ArrayList<>();
    }
}
