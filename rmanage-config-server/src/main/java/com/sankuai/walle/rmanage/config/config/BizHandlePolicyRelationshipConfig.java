package com.sankuai.walle.rmanage.config.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import com.sankuai.walle.objects.constants.CommonConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lijun131
 * @Date: 2023/11/13
 */

@Configuration
@Slf4j
public class BizHandlePolicyRelationshipConfig {
    @MdpConfig(CommonConstants.bizHandlePolicyRelationshipKey)
    public static volatile String bizHandlePolicyRelationshipConfig;

    @MdpConfig(CommonConstants.handleMethodAliasKey)
    public static volatile String handleMethodAliasConfig;

    public static Map<String, String> matchHandleMethod(String handleMethods) throws Exception {
        Map<String, String> handleMethodMap = new HashMap<>();
        try {
            JSONObject handleMethodsJson = JSON.parseObject(handleMethods);
            JSONObject handleMethodAlias = JSON.parseObject(handleMethodAliasConfig);
            for (String handleMethod : handleMethodsJson.keySet()) {
                for (String handleMethodAliasKey : handleMethodAlias.keySet()) {
                    if (handleMethodAlias.getString(handleMethodAliasKey).equals(handleMethod)) {
                        handleMethodMap.put(handleMethodAliasKey, handleMethodsJson.getString(handleMethod));
                    }
                }
            }
            return handleMethodMap;
        } catch (Exception e) {
            log.error("matchHandleMethod error, handleMethods:{}, e: {}", handleMethods, e.toString());
            throw new Exception("matchHandleMethod error");
        }
    }
}