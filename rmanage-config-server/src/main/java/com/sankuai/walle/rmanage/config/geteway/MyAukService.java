package com.sankuai.walle.rmanage.config.geteway;

import com.sankuai.auk.open.api.contracts.service.DeviceEnrichment;
import com.sankuai.banma.auk.server.sdk.callback.AukCallback;
import com.sankuai.banma.auk.server.sdk.request.DeviceRequest;
import com.sankuai.banma.auk.server.sdk.request.thing.ThingInvokeServiceRequest;
import com.sankuai.banma.auk.server.sdk.response.*;
import com.sankuai.banma.auk.server.sdk.response.thing.ThingInvokeServiceResponse;
import com.sankuai.walle.dal.mrm_manage.entity.AukCert;

import java.util.ArrayList;
import java.util.List;

public interface MyAukService {

//    DeviceCreateResponse createDevice(DeviceCreateRequest request);
//
//    DeviceResponse queryDevice(DeviceRequest request);
//
//    DeviceSecretResponse queryDeviceSecret(DeviceSecretRequest request);
//
//    void publishMessageAsync(DownLinkMsgAsyncRequest request);
//
//    DeviceRemoteRebootResponse rebootDevice(DeviceRemoteRebootRequest request);
    public void sendConfigToMqtt(String vin, byte[] config, AukCallback callback);

//    void sendQueryReq(String vin);
//
//    void queryFromMqtt(String vin, byte[] config, AukCallback callback);

    void queryFromMqtt(String vin, byte[] config, AukCallback callback);

    byte[] queryFromMqttSync(String vin, byte[] config );

    List<String> batchCreateAukDevices(List<String> deviceKeys) ;

    // 调用物模型
    ThingInvokeServiceResponse invokeThingModel(ThingInvokeServiceRequest request);

    // 自动为auk生成device key
    public DeviceCreateResponse createAukDevice(String DeviceKey);

    // 让客户端通过vin查询SK
    public DeviceSecretResponse queryDeviceSecret(String vin);

    public AukCert queryDeviceCert(String vin);

    // 查询设备的响应信息
    public DeviceResponse queryDevice(DeviceRequest request);

    public DeviceListResponse queryDeviceList(ArrayList<String> vins);

    // 查询设备列表，使用切片，因为一次最多支持查50个
    List<DeviceEnrichment> queryDeviceListWithSlice(List<String> vins, int slice);
}
