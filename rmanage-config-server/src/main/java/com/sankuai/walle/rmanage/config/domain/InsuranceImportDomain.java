package com.sankuai.walle.rmanage.config.domain;

import com.sankuai.walle.carManage.entity.CarAssets;
import com.sankuai.walle.carManage.entity.CarExecWord;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.objects.execDo.AssetExecWord;
import com.sankuai.walle.objects.execDo.OperationExecWord;
import com.sankuai.walle.objects.vo.request.CarEditReq;
import com.sankuai.walle.objects.vo.request.ImportInsuranceReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.logging.log4j.util.Strings;

import java.util.Date;
import java.util.List;

/**
 * 保险导入领域对象
 * 包含保险导入的核心业务逻辑
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsuranceImportDomain extends BaseVehicleDomain {
    
    private String licenseNo;
    private AssetExecWord assetExecWord;
    
    /**
     * 创建保险导入领域对象
     */
    public static InsuranceImportDomain create(String vin, String licenseNo, AssetExecWord assetExecWord) {
        InsuranceImportDomain domain = new InsuranceImportDomain();
        domain.setVin(vin);
        domain.setLicenseNo(licenseNo);
        domain.setAssetExecWord(assetExecWord);
        return domain;
    }
    
    /**
     * 验证车辆是否存在
     */
    public boolean isVehicleExists(List<CarObjects> carObjectsList) {
        if (carObjectsList == null || carObjectsList.isEmpty()) {
            return false;
        }
        this.carObjects = carObjectsList.get(0);
        return true;
    }
    
    /**
     * 选择目标资产记录（优先选择未报废的）
     */
    public boolean selectTargetAsset(List<CarAssets> carAssetsList) {
        if (carAssetsList == null || carAssetsList.isEmpty()) {
            return false;
        }
        
        if (carAssetsList.size() == 1) {
            this.carAssets = carAssetsList.get(0);
        } else {
            // 多条记录时，优先选择未报废的
            this.carAssets = carAssetsList.stream()
                    .filter(assets -> assets.getScrap() == null || !assets.getScrap())
                    .findFirst()
                    .orElse(carAssetsList.get(0)); // 如果都报废了，选择第一条
        }
        return true;
    }
    
    /**
     * 更新CarObjects的车牌号
     */
    public void updateCarObjectsLicenseNo() {
        if (this.carObjects != null && Strings.isNotBlank(this.licenseNo)) {
            this.carObjects.setLicenseno(this.licenseNo);
            this.carObjects.setUpdateTime(new Date());
        }
    }
    
    /**
     * 处理CarExecWord记录
     * 判断id是否存在，如果不存在则通过vin查找，如果存在则更新，如果都不存在则新增
     */
    public CarExecWord processCarExecWord(CarExecWord existingCarExecWord, List<CarExecWord> existingCarExecWordsByVin) {
        // 如果传入的CarExecWord有id，直接使用
        if (existingCarExecWord != null && existingCarExecWord.getId() != null) {
            // 更新现有的CarExecWord记录
            updateExistingCarExecWord(existingCarExecWord);
            return existingCarExecWord;
        }
        
        // 如果传入的CarExecWord没有id，通过vin查找
        if (existingCarExecWordsByVin != null && !existingCarExecWordsByVin.isEmpty()) {
            // 使用找到的第一条记录
            CarExecWord foundCarExecWord = existingCarExecWordsByVin.get(0);
            updateExistingCarExecWord(foundCarExecWord);
            return foundCarExecWord;
        }
        
        // 如果都不存在，创建新的
        return createNewCarExecWord();
    }
    
    /**
     * 创建新的CarExecWord记录
     */
    public CarExecWord createNewCarExecWord() {
        // 创建CarExecWordConvertor，参考addCar方法
        CarEditReq.CarExecWordConvertor carExecWordConvertor = new CarEditReq.CarExecWordConvertor();
        carExecWordConvertor.setVin(this.vin);
        carExecWordConvertor.setAddTime(new Date());
        carExecWordConvertor.setUpdateTime(new Date());
        
        // 设置保险信息
        if (this.assetExecWord == null) {
            this.assetExecWord = new AssetExecWord();
            this.assetExecWord.setVin(this.vin);
        }
        carExecWordConvertor.setAssetexecWord(this.assetExecWord);
        
        // 设置空的OperationExecWord
        OperationExecWord operationExecWord = new OperationExecWord();
        operationExecWord.setVin(this.vin);
        carExecWordConvertor.setOperationExecWord(operationExecWord);
        
        // 转换为CarExecWord
        return CarEditReq.CarExecWordConvertor.objExecToJson(carExecWordConvertor);
    }
    
    /**
     * 更新现有的CarExecWord记录
     */
    public void updateExistingCarExecWord(CarExecWord existingCarExecWord) {
        // 解析现有的AssetExecWord
        AssetExecWord existingAssetExecWord = parseAssetExecWord(existingCarExecWord.getCarAssetsExecWords());
        if (existingAssetExecWord == null) {
            existingAssetExecWord = new AssetExecWord();
            existingAssetExecWord.setVin(this.vin);
        }
        
        // 更新保险信息
        if (this.assetExecWord != null) {
            // 只更新保险相关字段，保留其他字段
            if (this.assetExecWord.getHeavyTrafficInsurance() != null) {
                existingAssetExecWord.setHeavyTrafficInsurance(this.assetExecWord.getHeavyTrafficInsurance());
            }
            if (this.assetExecWord.getBusinessInsuranceDate() != null) {
                existingAssetExecWord.setBusinessInsuranceDate(this.assetExecWord.getBusinessInsuranceDate());
            }
            if (this.assetExecWord.getEquipmentInsuranceDate() != null) {
                existingAssetExecWord.setEquipmentInsuranceDate(this.assetExecWord.getEquipmentInsuranceDate());
            }
            if (this.assetExecWord.getPriorityInsurance() != null) {
                existingAssetExecWord.setPriorityInsurance(this.assetExecWord.getPriorityInsurance());
            }
            if (this.assetExecWord.getThirdLiabilityInsurance() != null) {
                existingAssetExecWord.setThirdLiabilityInsurance(this.assetExecWord.getThirdLiabilityInsurance());
            }
        }
        
        // 序列化并更新
        String assetExecWordJson = new com.google.gson.Gson().toJson(existingAssetExecWord);
        existingCarExecWord.setCarAssetsExecWords(assetExecWordJson);
        existingCarExecWord.setUpdateTime(new Date());
        
        this.carExecWord = existingCarExecWord;
    }
    
    /**
     * 解析AssetExecWord JSON字符串
     */
    private AssetExecWord parseAssetExecWord(String assetExecWordJson) {
        if (assetExecWordJson == null || assetExecWordJson.trim().isEmpty()) {
            return null;
        }
        return new com.google.gson.Gson().fromJson(assetExecWordJson, AssetExecWord.class);
    }
} 