package com.sankuai.walle.rmanage.config.crane.datatransfer;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.walle.rmanage.config.service.OverallVersionDraftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@CraneConfiguration
public class OtaVersionTask {

    @Autowired
    OverallVersionDraftService overallVersionDraftService;

    @Crane("cmdb-push-ota-allVersion-redis")
    public void transferCarUsedTarget() throws Exception {
        overallVersionDraftService.pushOverallVersionInRedis();
    }
}
