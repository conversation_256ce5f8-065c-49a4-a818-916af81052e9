package com.sankuai.walle.rmanage.config.service;

import com.sankuai.walle.carManage.entity.CarSelects;
import com.sankuai.walle.carManage.example.CarSelectsExample;
import com.sankuai.walle.carManage.mapper.CarSelectsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


public interface CarSelectsQueryService {


    List<CarSelects> fetchCarSelectsByBelone(String belong);

    String getSelect(String key, String type_id);
}
