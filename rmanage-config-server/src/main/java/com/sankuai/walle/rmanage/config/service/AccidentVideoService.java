package com.sankuai.walle.rmanage.config.service;


import com.sankuai.walle.rmanage.config.thread.req.VideoReponse;

import java.util.List;

public interface AccidentVideoService {
    public void getAccidentVideo(Long accidentTime, String vin , List<String> position);
    public VideoReponse getAccidentVideoResponse(Long accidentTime, String vin , List<String> position);

    public int getCollisionDetectVideoResponse(Long accidentTime, String vin);

}
