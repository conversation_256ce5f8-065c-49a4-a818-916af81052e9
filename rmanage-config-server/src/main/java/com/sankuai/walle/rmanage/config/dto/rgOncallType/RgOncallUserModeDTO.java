package com.sankuai.walle.rmanage.config.dto.rgOncallType;


import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * 接收分页查询rg-oncall值班组的userList，格式来自TT值班接口文档，解析用
 */
@Data
public class RgOncallUserModeDTO {

    private int code;
    private String message;
    private Content data;

    @Data
    public static class Content {

        private List<Item> items;
    }

    @Data
    public static class Item {

        private String identify;

        // 传递来的json字符串有is开头，需要序列化is
        @JsonProperty("isOncall")
        private boolean isOncall;
    }

    /**
     * 解析用户模式
     * @return
     */
    public List<String> getMisIdList() {
        if (this.getData() == null || CollectionUtils.isEmpty(this.getData().getItems())) {
            return Collections.emptyList();
        }
        return this.getData().getItems().stream()
                .filter(Objects::nonNull)
                .filter(Item::isOncall)
                .map(Item::getIdentify)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }
}