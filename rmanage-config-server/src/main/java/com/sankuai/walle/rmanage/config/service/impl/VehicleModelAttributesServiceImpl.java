package com.sankuai.walle.rmanage.config.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.sankuai.walle.carManage.entity.VehicleModelAttributes;
import com.sankuai.walle.carManage.example.VehicleModelAttributesExample;
import com.sankuai.walle.carManage.mapper.VehicleModelAttributesMapper;
import com.sankuai.walle.rmanage.config.dto.VehicleModelManage.VehicleModelAttributesDTO;
import com.sankuai.walle.rmanage.config.service.VehicleModelAttributesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.apache.commons.beanutils.BeanUtils;


import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VehicleModelAttributesServiceImpl implements VehicleModelAttributesService {

    @Resource
    VehicleModelAttributesMapper vehicleModelAttributesMapper;

    /**
     * 批量插入车型属性
     *
     * @param vehicleModelAttributes 车辆型号属性列表
     */
    @Override
    public void batchInsertVehicleModelAttributes(List<VehicleModelAttributes> vehicleModelAttributes) {
        vehicleModelAttributesMapper.batchInsert(vehicleModelAttributes);
    }

    /**
     * 获取车型属性
     *
     * @param vehicleModelAttributes 车辆型号属性条件
     * @return 车辆型号属性列表
     */
    @Override
    public List<VehicleModelAttributes> getVehicleModelAttributes(VehicleModelAttributes vehicleModelAttributes) {
        VehicleModelAttributesExample example = new VehicleModelAttributesExample();

        VehicleModelAttributesExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(false);
        if (StringUtils.isNotBlank(vehicleModelAttributes.getCarType())) {
            criteria.andCarTypeEqualTo(vehicleModelAttributes.getCarType());
        }

        if (StringUtils.isNotBlank(vehicleModelAttributes.getAttributeName())) {
            criteria.andAttributeNameEqualTo(vehicleModelAttributes.getAttributeName());
        }

        if (StringUtils.isNotBlank(vehicleModelAttributes.getAttributeValue())) {
            criteria.andAttributeNameEqualTo(vehicleModelAttributes.getAttributeValue());
        }
        List<VehicleModelAttributes> vehicleModelAttributesList = vehicleModelAttributesMapper.selectByExample(example);
        log.info("getVehicleModelAttributes:{}", vehicleModelAttributesList);
        return vehicleModelAttributesList;
    }

    /**
     * 获取车型属性DTO
     * @param carType 车辆型号type
     * @return 车辆型号属性DTO列表
     */
    @Override
    public VehicleModelAttributesDTO getVehicleModelAttributesDTOByCarType(String carType) {
        List<VehicleModelAttributes> attributesList = getVehicleModelAttributes(
                VehicleModelAttributes.builder().carType(carType).build());
        if(CollectionUtils.isEmpty(attributesList)){
            return null;
        }
        Map<String, String> attributeMap = attributesList.stream().collect(Collectors.toMap(
                VehicleModelAttributes::getAttributeName,
                VehicleModelAttributes::getAttributeValue));

        try {
            VehicleModelAttributesDTO dto = new VehicleModelAttributesDTO();
            BeanUtils.populate(dto, attributeMap);
            return dto;
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("getVehicleModelAttributesDTO error", e);
        }
        return null;
    }
}
