package com.sankuai.walle.rmanage.config.aop;


import com.dianping.cat.Cat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mtrace.Tracer;
import com.sankuai.walle.common.Status;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.common.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import org.slf4j.Logger;

import java.lang.reflect.Field;

import static com.sankuai.walle.rmanage.config.common.ErrorCode.SERVICE_INTERNAL_ERROR;

@Aspect
@Component
@Slf4j
public class ControllerAspect {


    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Pointcut("execution(public * com.sankuai.walle.rmanage.config.controller..*.*(..))")
    public void controllerPointCut() {

    }

    @Around("controllerPointCut()")
    public Object handleThriftMethod(ProceedingJoinPoint pjp) {
        try {
            // 打印入参
            logInput(pjp);

            Object proceed = pjp.proceed();

            // 打印结果
            logOutput(pjp, proceed);

            return proceed;
        } catch (Throwable e) {
            // 异常通知
            log.error("系统异常：", e);
            Cat.logError(e);

            Object failureResp = buildFailureResp(pjp, SERVICE_INTERNAL_ERROR.getCode(),
                    SERVICE_INTERNAL_ERROR.getDefaultMsg());
            logOutput(pjp, failureResp);

            return failureResp;
        }
    }

    private Object buildFailureResp(ProceedingJoinPoint pjp, int errorCode, String msg) {
        ResData resp = new ResData();
        resp.setCode(errorCode);
        resp.setMsg(msg);

        return resp;

    }


    private void logOutput(ProceedingJoinPoint pjp, Object proceed) {
        String className = pjp.getTarget().getClass().getName();
        String methodName = pjp.getSignature().getName();
        String argsString = buildArgsString(pjp.getArgs());
        String outputStr = "";
        try {
            // 尝试使用 Jackson 序列化
            outputStr = objectMapper.writeValueAsString(proceed);
        } catch (Exception e) {
            outputStr = String.valueOf(proceed);
        }

        log.info("aspect [({},{})]: {} {} input=[{}] output = {}",
                Tracer.id(), Cat.getCurrentMessageId(), className, methodName, argsString, outputStr);
    }

    private void logInput(ProceedingJoinPoint pjp) {
        String className = pjp.getTarget().getClass().getName();
        String methodName = pjp.getSignature().getName();
        String argsString = buildArgsString(pjp.getArgs());

        log.info("aspect [({},{})]: {} {} input=[{}]", Tracer.id(), Cat.getCurrentMessageId(), className, methodName, argsString);
    }

    private String buildArgsString(Object[] args) {
        if (args == null || args.length == 0) {
            return "";
        }

        StringBuilder argsString = new StringBuilder();
        for (Object arg : args) {
            argsString.append(arg).append(" ");
        }
        if (argsString.length() > 0) {
            argsString.deleteCharAt(argsString.length() - 1);
        }
        return argsString.toString();
    }
}
