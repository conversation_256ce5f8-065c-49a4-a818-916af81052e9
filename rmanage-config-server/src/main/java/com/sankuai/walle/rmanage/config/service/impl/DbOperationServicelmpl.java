package com.sankuai.walle.rmanage.config.service.impl;

import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;


import com.sankuai.walle.carManage.entity.CarOperation;
import com.sankuai.walle.carManage.example.CarOperationExample;
import com.sankuai.walle.carManage.mapper.CarOperationMapper;
import com.sankuai.walle.rmanage.config.service.DbOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
@Service
@Slf4j
public class DbOperationServicelmpl implements DbOperationService {
    @MdpConfig("inputData")
    HashMap<String, ArrayList<String>> inputData;
    @MdpConfig("area_name_reflect")
    HashMap<String, String> area_name_reflect;
    @MdpConfig("usage_name_reflect")
    HashMap<String, String> usage_name_reflect;
    @MdpConfig("usagereflect")
    HashMap<String, String> usagereflect;

    @Resource
    CarOperationMapper carOperationMapper;

    @Override
    public List<String> updateAndInsertDbByOut() {
        //先查询vin，有则更新，无则插入
        log.info("updateAndInsertDbByOut is start");

        List<String> updatedvins = new ArrayList<>();
        if(inputData == null || !isValid()){
            return updatedvins;
        }
        List<String> vinList = new ArrayList<>();
        List<String> areaList = new ArrayList<>();
        List<String> usageList = new ArrayList<>();

        for(String key: inputData.keySet()) {
            if(key.equals("vin")){
                vinList = inputData.get(key);
            }
            else if(key.equals("area")){
                areaList = inputData.get(key);
            }
            else if(key.equals("usage")){
                usageList = inputData.get(key);
            }
        }
        for(int i = 0; i< vinList.size(); i++){
            CarOperationExample carOperationExample = new CarOperationExample();
            carOperationExample.createCriteria().andVinEqualTo(vinList.get(i));
            List<CarOperation> result  = carOperationMapper.selectByExample(carOperationExample);

            int size = result.size();
            if(size >= 1){
                for(CarOperation carOperation : result){

                    CarOperation newData = new CarOperation();
                    newData.setId(carOperation.getId());
                    String area = carOperation.getArea();
                    String usage = carOperation.getCarUsedTarget();
                    if( area == null || (area != null && !area.equals(areaList.get(i)))){
                        newData.setArea(areaList.get(i));
                        newData.setAreaStr("");
                    }
                    if(usage == null || (usage != null && !usage.equals(usageList.get(i)))) {
                        newData.setCarUsedTarget(usageList.get(i));
                        newData.setCarUsedTargetStr("");
                    }

                    if(newData.getArea() == null && newData.getCarUsedTarget() == null){
                        continue;
                    }
                    int res  = carOperationMapper.updateByPrimaryKeySelective(newData);
                    if(res <1){
                       log.info("updateAndInsertDbByOut, updateByExample is failed, vin = {}",vinList.get(i) );
                    }
                    else{
                        log.info("index = {}, vin = {}, oldArea = {}, newArea = {}, oldUsage = {}, newUsage = {}",i ,vinList.get(i) , carOperation.getArea() , areaList.get(i) , carOperation.getCarUsedTarget() , usageList.get(i));
                        updatedvins.add(vinList.get(i));
                    }
                }
            }
            else{
                CarOperation newData = new CarOperation();
                newData.setVin(vinList.get(i));
                newData.setArea(areaList.get(i));
                newData.setCarUsedTarget(usageList.get(i));
                int res = carOperationMapper.insert(newData);
                if(res <1){
                    log.info("updateAndInsertDbByOut, carOperationMapper is failed, vin = {}",vinList.get(i) );
                }
            }
        }

        return updatedvins;
    }

    @Override
    public List<String> updateDbByIn() {
        //1、 中文的映射，将履约用车 映射成 业务保障

        List<String> updatedVins = new ArrayList<>();
        CarOperationExample example = new CarOperationExample();
        List<CarOperation> result = carOperationMapper.selectByExample(example);


        for(int i = 0; i< result.size() ;i++){
            String usage = result.get(i).getCarUsedTarget();
            String vin   = result.get(i).getVin();
            String usageStr = result.get(i).getCarUsedTargetStr();
            if(usage == null || vin == null ){
                continue;
            }
            //如果包含了需要更新的用途 则需要做转换
            if(usagereflect.containsKey(usage)){
                if(usageStr != null && usageStr == usagereflect.get(usage)){
                    continue;
                }
                CarOperation newData = new CarOperation();
                newData.setId(result.get(i).getId());
                newData.setCarUsedTarget(usagereflect.get(usage));
                int res  = carOperationMapper.updateByPrimaryKeySelective(newData);
                if(res <1){
                    log.info("updateDbByIn, updateByExample is failed, vin = {}", (Object) null);
                }
                else{
                    updatedVins.add(vin);
                }
            }
        }

        return updatedVins;
    }

    @Override
    public List<String> updateDbByOut() {
        // 2、将中文 映射成 字符串
        List<String> updatedVins = new ArrayList<>();
        CarOperationExample example = new CarOperationExample();
        List<CarOperation> result = carOperationMapper.selectByExample(example);
        System.out.println(" "+ result.size());

        for(int i = 0; i< result.size() ;i++){
            String area =  result.get(i).getArea();
            String usage = result.get(i).getCarUsedTarget();
            String vin   = result.get(i).getVin();
            String areaStr = result.get(i).getAreaStr();
            String usageStr = result.get(i).getCarUsedTargetStr();
            if(vin == null ){
                continue;
            }

            CarOperation newData = new CarOperation();
            newData.setId(result.get(i).getId());

            //如果包含 area信息 则需要做转换
            if(area != null && area_name_reflect.containsKey(area)){
                if(areaStr == null || !areaStr.equals(area_name_reflect.get(area))){
                    newData.setAreaStr(area_name_reflect.get(area));
                }
            }
            //如果包含 车辆用途信息 则需要做转换
            if(usage != null && usage_name_reflect.containsKey(usage)){
                if(usageStr == null || !usageStr.equals(usage_name_reflect.get(usage))) {
                    newData.setCarUsedTargetStr(usage_name_reflect.get(usage));
                }
            }
            if(newData.getAreaStr() == null && newData.getCarUsedTargetStr() == null){
                continue;
            }
            int temp  = carOperationMapper.updateByPrimaryKeySelective(newData);
            if(temp <1){
                log.info("updateDbByOut, updateByExample is failed, vin = {}", vin);
            }
            else{
                updatedVins.add(vin);
            }
        }
        return updatedVins;
    }

    @Override
    public List<String> checkInsertNum() {
        List<String> vins = new ArrayList<>();
        if (inputData == null || !isValid()) {
            return vins;
        }
        List<String> vinList = new ArrayList<>();

        for (String key : inputData.keySet()) {
            if (key.equals("vin")) {
                vinList = inputData.get(key);
            }
        }

        for (int i = 0; i < vinList.size(); i++) {
            CarOperationExample carOperationExample = new CarOperationExample();
            carOperationExample.createCriteria().andVinEqualTo(vinList.get(i));
            List<CarOperation> result = carOperationMapper.selectByExample(carOperationExample);
            int Size = result.size();
            if(Size == 0){
                vins.add( vinList.get(i) );
            }
        }

        return vins;
    }

    Boolean isValid(){
        int size = -1;
        for(String key: inputData.keySet()){
            int temp = inputData.get(key).size();

            if(size == -1){
                size = temp;
            }
            else {
                if(temp != size){
                    return false;
                }
            }

        }

        if(size == 0){
            return false;
        }
        return true;
    }
}
