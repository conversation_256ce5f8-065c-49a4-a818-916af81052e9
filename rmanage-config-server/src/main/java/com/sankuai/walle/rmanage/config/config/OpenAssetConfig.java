package com.sankuai.walle.rmanage.config.config;

import com.meituan.finerp.eam.service.OpenAssetQueryService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenAssetConfig {

    // 单例连接
    @Bean(name = "OpenAssetQueryService")
    public OpenAssetQueryService queryService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
//        proxy.setServerIpPorts("************:9003");
        proxy.setRemoteAppkey("com.sankuai.it.finerp.eam");
        proxy.setServiceInterface(Class.forName("com.meituan.finerp.eam.service.OpenAssetQueryService"));
        proxy.setNettyIO(true);
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();  //初始化实例

        OpenAssetQueryService openAssetQueryService = (OpenAssetQueryService)proxy.getObject(); //获取代理对象
        return openAssetQueryService;
    }
}
