package com.sankuai.walle.rmanage.config.service.impl;

import cn.hutool.core.date.DateUtil;
import com.dianping.cat.util.MetricHelper;
import com.google.protobuf.ByteString;
import com.sankuai.banma.auk.server.sdk.callback.AukCallback;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.uac.sdk.entity.menu.MenuNode;
import com.sankuai.meituan.uac.sdk.entity.menu.UserMenu;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.walle.carManage.entity.CarObjects;
import com.sankuai.walle.carManage.example.CarObjectsExample;
import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.classify.entity.CarConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfig;
import com.sankuai.walle.dal.classify.entity.CarDeviceConfigExtend;
import com.sankuai.walle.dal.classify.example.CarConfigExample;
import com.sankuai.walle.dal.classify.example.CarDeviceConfigExtendExample;
import com.sankuai.walle.dal.classify.mapper.CarConfigMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigExtendMapper;
import com.sankuai.walle.dal.classify.mapper.CarDeviceConfigMapper;
import com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper;
import com.sankuai.walle.objects.constants.ConfigConstant;
import com.sankuai.walle.objects.vo.res.CarDeviceConfigRes;
import com.sankuai.walle.rmanage.config.geteway.REApi;
import com.sankuai.walle.rmanage.config.geteway.req.ReCmdbReq;
import com.sankuai.walle.rmanage.config.service.ActionLogService;
import com.sankuai.walle.rmanage.config.service.appService.CarDeviceConfigService;
import com.sankuai.walle.rmanage.config.geteway.MyAukService;
import com.sankuai.walle.rmanage.config.util.ObjectUtil;
import com.sankuai.walle.rmanage.proto.target.CloudCgf;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CarDeviceConfigServiceImpl implements CarDeviceConfigService {
    // 配置分发

//    @Resource
//    RemoteDeviceTypeMapper remoteDeviceTypeMapper;

    @Resource
    VehicleInfoMapper vehicleInfoMapper;

    @Resource
    CarDeviceConfigExtendMapper carDeviceConfigExtendMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Resource
    ActionLogService actionLogService;
    @Resource
    REApi reApi;

    @Override
    public void setStatus(CarDeviceConfigRes child, CarDeviceConfig carDeviceConfig){
        if(carDeviceConfig.getStatus()==0)child.setStatus(
                new HashMap(){{
                    put("code", ConfigConstant.SEND);
                    put("msg", ConfigConstant.SEND_STATUS);
                }}
        );
        if(carDeviceConfig.getStatus()==1)child.setStatus(
                new HashMap(){{
                    put("code", ConfigConstant.FINISH);
                    put("msg", ConfigConstant.FINISH_STATUS);
                }}
        );
        if(carDeviceConfig.getStatus()==2) {
            List<CarDeviceConfigExtend> configextends = carDeviceConfigExtendMapper.selectByExample(new CarDeviceConfigExtendExample() {{
                createCriteria().andConfigIdEqualTo(carDeviceConfig.getId());
            }});
            if (configextends.size()>0) {
                child.setStatus(
                        new HashMap(){{
                            put("code", ConfigConstant.ERROR);
                            put("msg",configextends.get(0).getErrorStatus());
                        }}
                );
            }
        }
    }

    // 组装车辆、配置、设备内容
    @Override
    public CarDeviceConfigRes createRes(CarDeviceConfig carDeviceConfig){
        CarDeviceConfigRes child = new CarDeviceConfigRes();
        child.setConfig_id(carDeviceConfig.getId());
        child.setConfig_name(carDeviceConfig.getName());
        child.setConfig(carDeviceConfig.getConfig());
        child.setCreater(carDeviceConfig.getCreateUser());
        child.setAddTime(carDeviceConfig.getAddTime());
        child.setUpdater(carDeviceConfig.getUpdateUser());
        child.setUpdateTime(carDeviceConfig.getUpdateTime());
        child.setNow_config_id(carDeviceConfig.getConfigId());
        child.setLast_config_id(carDeviceConfig.getConfigIdLast());
        child.setVin(carDeviceConfig.getVin());
        this.setStatus(child, carDeviceConfig);
        String car_vin = carDeviceConfig.getVin();
        CarObjectsExample carObjectsExample = new CarObjectsExample();
        carObjectsExample.createCriteria().andVinEqualTo(car_vin);
        List<CarObjects> vehicles = carObjectsMapper.selectByExample(carObjectsExample);
        if (vehicles.size()>0) {
            child.setCarName(vehicles.get(0).getName());
        }
        return child;
    }

    @Autowired
    UacAuthRemoteService uacAuthRemoteService;
    @Autowired
    MyAukService myAukClient;
    @Resource
    CarConfigMapper carConfigMapper;
    // 根据权限，筛选可以使用的配置项
    private List<String> filterConfigItem(){
        User user = UserUtils.getUser();
        UserMenu usermenu = uacAuthRemoteService.getUserMenus(String.valueOf(user.getId()));
        List<MenuNode> folders = usermenu.getMenuFolderList();
        List<String> names = new ArrayList<>();
        for(MenuNode node: folders){
            if(Objects.equals(node.getNodeCode(), "cmdb")){
                List<MenuNode> childMenus = node.getChildrenMenuList();
                for (MenuNode childMenu:childMenus){
                    if (Objects.equals(childMenu.getNodeCode(), "config_items")){
                        List<MenuNode> items = childMenu.getChildrenMenuList();
                        for (MenuNode item:items){
                            names.add(item.getNodeName());
                        }
                    }
                }
            }
        }
        return names;
    }

    @Override
    public List<Long> filterConfigIds(){
        List<String> names = this.filterConfigItem();
        CarConfigExample query = new CarConfigExample();
        CarConfigExample.Criteria query2 = new CarConfigExample().createCriteria();
        if(names.size()>0) {
            query.createCriteria().andMasterIdIsNull().andNameIn(names);
            query2.andMasterIdEqualTo(ConfigConstant.DEV_VERSION).andNameIn(names);
        }else{
            query.createCriteria().andMasterIdIsNull();
            query2.andMasterIdEqualTo(ConfigConstant.DEV_VERSION);
        }
        query.or(query2);
        List<Long> ids = carConfigMapper.selectByExample(query).stream().map(CarConfig::getId).collect(Collectors.toList());
        return ids;
    };

    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;

    private CloudCgf.CloudToVehicleReleaseConfig.Builder buildConfig(CarDeviceConfig target, CarConfig carConfig){
        CloudCgf.CloudToVehicleReleaseConfig.Builder config = CloudCgf.CloudToVehicleReleaseConfig.newBuilder();
        config.setSVin(target.getVin());
        config.setSUuid(UUID.randomUUID().toString().replace("-", ""));
        if(carConfig!=null && carConfig.getConfigVersion()!=null) {
            config.setSVersion(String.valueOf(carConfig.getConfigVersion()));
        }
        // 通过excel下发的，版本号设为 -1
        if(Objects.equals(target.getConfigId(), ConfigConstant.EXCEL_CONFIG_ID)){
            config.setSVersion(String.valueOf(ConfigConstant.EXCEL_VERSION));
        }
        config.setSConfigName(target.getName());
        config.setBConf(ByteString.copyFromUtf8(target.getConfig()));
        return config;
    }

    // 把下发的信息转换成pb
    @Override
    public CloudCgf.CloudToVehicleReleaseConfig.Builder handleTargetData( CarDeviceConfig target ){
        // 处理目标数据, 配置文件类型来自CarConfig
        CarConfig carConfig = carConfigMapper.selectByPrimaryKey(target.getConfigId());
        CloudCgf.CloudToVehicleReleaseConfig.Builder config = this.buildConfig(target , carConfig);
        if(target.getName()!=null && carConfig!=null && carConfig.getFileType()!=null){
            config.setSFileName(target.getName()+"."+carConfig.getFileType());
        }
        if(!ObjectUtil.isAllFieldNull(config)){
            //日志、报警
            log.error("config 的属性值不能为空::"+target);
            MetricHelper.build().name("wallcmdb.auk.config").tag("error", "config 的属性值不能为空::" + target).count(1);
            throw new RuntimeException("config 的属性值不能为空");
        }
        return config;
    }
    public CloudCgf.CloudToVehicleReleaseConfig.Builder handleTargetData(CarDeviceConfig target, String fileType){
        // 处理目标数据，配置文件类型来自传参
        CarConfig carConfig = carConfigMapper.selectByPrimaryKey(target.getConfigId());
        CloudCgf.CloudToVehicleReleaseConfig.Builder config = this.buildConfig(target, carConfig);
        if(target.getName()!=null && fileType!=null){
            config.setSFileName(target.getName()+"."+fileType);
        }
        if(!ObjectUtil.isAllFieldNull(config)){
            //日志、报警
            log.error("config 的属性值不能为空::"+target);
            MetricHelper.build().name("wallcmdb.auk.config").tag("error", "config 的属性值不能为空::" + target).count(1);
            throw new RuntimeException("config 的属性值不能为空");
        }
        return config;
    }
    
    // 通过auk的mqtt下发配置
    // 通过mqtt下发的属性和配置项，都会走 car_device_config 表
    @Override
    public void configToMqtt(CarDeviceConfig target) {
        // 处理数据
        CloudCgf.CloudToVehicleReleaseConfig.Builder config = this.handleTargetData(target);
        this.configToMqtt(target, config, false);
    }
    @Override
    public void configToMqtt(CarDeviceConfig target, String fileType) {
        // 处理数据
        CloudCgf.CloudToVehicleReleaseConfig.Builder config = this.handleTargetData(target, fileType);
        this.configToMqtt(target, config, false);
    }

    @Override
    public void retryConfigToMqtt(CarDeviceConfig target) {
        CloudCgf.CloudToVehicleReleaseConfig.Builder config;
        if(Objects.equals(target.getConfigId(), ConfigConstant.EXCEL_CONFIG_ID)){
            config = this.handleTargetData(target, target.getFileType());
        }else {
            config = this.handleTargetData(target);
        }
        this.configToMqtt(target, config, true);
    }

    private void configToMqtt( CarDeviceConfig target, CloudCgf.CloudToVehicleReleaseConfig.Builder config, boolean retry ){
        // 下发数据，配置分发层
        User user = UserUtils.getUser();
        String mis = user!=null ? user.getLogin() : Strings.isNotBlank(target.getUpdateUser()) ? target.getUpdateUser() : "mafka";
        AukCallback callback = new AukCallback() {
            @Override
            public void onSuccess(Object o) {
                log.info("配置下发成功：【{}】", config.toString());
                MetricHelper.build().name("wallcmdb.auk.config").tag("status", "success").count(1);
                if(!retry) {
                    reApi.sendConfigChangeMsg(ReCmdbReq.builder()
                            .vin(target.getVin())
                            .changeTime(System.currentTimeMillis())
                            .configName(target.getName())
                            .mis(mis)
                            .state(ConfigConstant.FINISH).build());
                }
            }
            @Override
            public void onFailure(Object o, Throwable throwable) {
                // 配置下发失败，什么也不做
                log.info("配置下发失败：【{}】\n【{}】\n配置内容：【{}】", o, throwable, config.toString());
                MetricHelper.build().name("wallcmdb.auk.config").tag("status", "fail").count(1);
            }
        };
        myAukClient.sendConfigToMqtt(target.getVin() , config.build().toByteArray(), callback);
        if(!retry) {
            actionLogService.insertConfigActionLog(target.getVin(), mis, "下发配置："+target.getName()+"，配置内容："+target.getConfigId());
            reApi.sendConfigChangeMsg(ReCmdbReq.builder()
                    .vin(target.getVin())
                    .changeTime(System.currentTimeMillis())
                    .configName(target.getName())
                    .mis(mis)
                    .state(ConfigConstant.SEND).build());
        }

    }

}
