package com.sankuai.walle.rmanage.config.vto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Optional;
import lombok.Data;


/**
 * 接收数据总线的Json结构体
 */
@Data
public class VehicleDataBusVTO {

    // 预留车辆信息
    @JsonProperty("reserve_vehicle")
    private ReserveVehicle reserveVehicle;

    // 车辆识别码
    private String vin;

    // 标签信息
    private Label label;

    private Monitor monitor;

    private Issue issue;

    /**
     * 状态监控加工数据
     */
    @JsonProperty("monitor_compute")
    private MonitorCompute monitorCompute;

    /**
     * 车辆基础信息
     */
    @JsonProperty("vehicle_manage")
    private VehicleManage vehicleManage;

    /**
     * 获取云控VHR值
     * @return
     */
    public String getTelecontrolVHRValue() {
        return Optional.ofNullable(label)
                .map(Label::getTags)
                .map(Tags::getTelecontrolVHR)
                .map(VhrInfo::getValue)
                .orElse("");
    }

    /**
     * 获取近场VHR值
     * @return
     */
    public String getNearbyRescueVHRValue() {
        return Optional.ofNullable(label)
                .map(Label::getTags)
                .map(Tags::getNearbyRescueVHR)
                .map(VhrInfo::getValue)
                .orElse("");
    }


    @Data
    public static  class MonitorCompute {

        @JsonProperty("online_status")
        private Integer onlineStatus;

    }
    @Data
    public static class Issue {
        @JsonProperty("error_list")
        private List<UnExpectedInfo> errorList;

        @JsonProperty("fatal_list")
        private List<UnExpectedInfo> fatalList;

        @JsonProperty("warn_list")
        private List<UnExpectedInfo> warnList;
    }

    @Data
    public static class UnExpectedInfo {
        private Integer code;
        private String text; // 错误描述
        private String level; // 错误等级
        private String id; // 错误key
        private Integer item; // item id
        @JsonProperty("item_name")
        private String itemName; // item 名称
        private String action; // 操作描述
    }

    @Data
    public static class Monitor {
        @JsonProperty("slab_sn")
        private String sn;
    }

    @Data
    public static class ReserveVehicle {

        // 预留车辆列表
        @JsonProperty("vresv_list")
        private List<VresvItem> vresvList;

        // 更新时间
        @JsonProperty("update_time")
        private long updateTime;
    }

    @Data
    public static class VresvItem {

        // 开始时间
        @JsonProperty("start_time")
        private long startTime;

        // 地点名称
        @JsonProperty("location_name")
        private String locationName;

        // 是否批准
        private boolean approved;

        // 是否删除
        private int deleted;

        // 云控指令
        private String telecontrol;

        // 结束时间
        @JsonProperty("end_time")
        private long endTime;

        // 替代者
        private String substitute;

        // 预留ID
        @JsonProperty("resv_id")
        private String resvId;
    }

    @Data
    public static class Label {

        // 提示信息
        private String tip;

        // 标签
        private Tags tags;
    }

    @Data
    public static class Tags {

        // 业务类型
        private BusinessType businessType;

        /**
         * 云控VHR
         */
        @JsonProperty("telecontrolVHR")
        private VhrInfo telecontrolVHR;

        /**
         * 近场VHR
         */
        @JsonProperty("nearbyRescueVHR")
        private VhrInfo nearbyRescueVHR;
    }

    @Data
    public static class BusinessType {

        // 业务类型值
        private String value;
    }

    /**
     * 车辆基础信息
     */
    @Data
    public static class VehicleManage {

        /**
         * 车辆名称
         */
        @JsonProperty("vehicle_name")
        private String vehicleName;

        /**
         * 一级车型
         */
        @JsonProperty("first_class_model")
        private String firstClassModel;

        /**
         * 二级车型
         */
        @JsonProperty("second_class_model")
        private String secondClassModel;

    }

    /**
     * 标签系统中的vhr取值
     */
    @Data
    public static class VhrInfo  {

        /**
         * appkey列表
         */
        @JsonProperty("appkeys")
        private List<String> appkeys;

        /**
         * 取值
         */
        private String value;

    }
}
