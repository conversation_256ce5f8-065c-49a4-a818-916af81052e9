package com.sankuai.walle.rmanage.config.thread;

import com.sankuai.walle.rmanage.config.component.DxGroupHandler;
import com.sankuai.walle.rmanage.config.service.AccidentVideoService;
import com.sankuai.walle.rmanage.config.thread.dto.QueueParamDTO;

public interface RedisQueueConsumer {


    // 获取队列名称
    String getQueueName();

    // 获取监听器返回的消息
    void getMessage(QueueParamDTO queueParamDTO,
                    AccidentVideoService accidentVideoService,
                    DxGroupHandler dxGroupHandler);

    // 获取监听器返回的错误消息
    void error(String error);
}