package com.sankuai.walle.rmanage.config.geteway;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.sankuai.auk.open.api.contracts.enums.AukResultCode;
import com.sankuai.auk.open.api.contracts.service.DeviceEnrichment;
import com.sankuai.banma.auk.server.sdk.AukClient;
import com.sankuai.banma.auk.server.sdk.callback.AukCallback;
import com.sankuai.banma.auk.server.sdk.enums.QoSLevel;
import com.sankuai.banma.auk.server.sdk.request.*;
import com.sankuai.banma.auk.server.sdk.request.thing.ThingInvokeServiceRequest;
import com.sankuai.banma.auk.server.sdk.response.*;
import com.sankuai.banma.auk.server.sdk.response.thing.ThingInvokeServiceResponse;
import com.sankuai.banma.auk.server.sdk.service.thing.ThingModelService;
import com.sankuai.walle.dal.mrm_manage.entity.AukCert;
import com.sankuai.walle.dal.mrm_manage.example.AukCertExample;
import com.sankuai.walle.dal.mrm_manage.mapper.AukCertMapper;
import com.sankuai.walle.rmanage.config.config.AukClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.google.common.base.Preconditions.checkArgument;
import static com.sankuai.walle.rmanage.config.constant.ChargingCarbinetConstant.*;
import static com.sankuai.walle.rmanage.config.util.DateUtils.daysBetween;
import static io.netty.util.internal.StringUtil.isNullOrEmpty;
import static lombok.Lombok.checkNotNull;

@Service
@Slf4j
public class MyAukServiceImpl implements MyAukService {

    @Resource(name = "aukClient")
    AukClient aukClient;



    // 将配置发送到mqtt
    /* deviceKey 一般用 车辆vin
    * */
    @Override
    public void sendConfigToMqtt(String vin, byte[] config, AukCallback callback) {
        DownLinkMsgAsyncRequest request = new DownLinkMsgAsyncRequest();
        request.setTimeout(AukClientConfig.TIMEOUT);
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        request.setQos(QoSLevel.AT_LEAST_ONCE);
        request.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        request.setTopic(AukClientConfig.TOPIC);
        request.setDeviceKey(vin);
        request.setPayload(config);
        aukClient.publishMessageAsync(request,callback);
    }
    @Override
    public void queryFromMqtt(String vin, byte[] config, AukCallback callback) {
        DownLinkMsgAsyncRequest request = new DownLinkMsgAsyncRequest();
        request.setTimeout(AukClientConfig.TIMEOUT);
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        request.setQos(QoSLevel.AT_LEAST_ONCE);
        request.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        request.setTopic(AukClientConfig.QUERY_TOPIC);
        request.setDeviceKey(vin);
        request.setPayload(config);
        aukClient.publishMessageAsync(request,callback);
    }

    @Override
    public byte[] queryFromMqttSync(String vin, byte[] config ) {
        DownLinkRRpcMsgRequest request = new DownLinkRRpcMsgRequest();
        request.setTimeout(AukClientConfig.TIMEOUT);
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        request.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        request.setTopic(AukClientConfig.QUERY_TOPIC);
        request.setDeviceKey(vin);
        request.setPayload(config);
        DownLinkRRpcMsgResponse rep = aukClient.invokeRRpc(request);
        if (rep.getCode()==AukResultCode.OK.code){
            return rep.getPayload();
        }else {
            return null;
        }
    }

    @Override
    public List<String> batchCreateAukDevices(List<String> deviceKeys) {
        List<String> failedCreateAukVinList = new ArrayList<>();
        for(String deviceKey : deviceKeys) {
            DeviceCreateResponse response = createAukDevice(deviceKey);
            if(!"OK".equals(response.getMsg())) {
                failedCreateAukVinList.add(deviceKey);
            }
        }
        return failedCreateAukVinList;
    }

    // 调用物模型
    @Override
    public ThingInvokeServiceResponse invokeThingModel(ThingInvokeServiceRequest request) {
        ThingModelService service = aukClient.getThingModelService();
        ThingInvokeServiceResponse res = service.invokeThingService(request);
        log.info("调用物模型返回结果：{}",res);
        return res;
    }


//    @Override
//    public void sendQueryReq(String vin){
//        List<CarDeviceConfig> configs = carDeviceConfigMapper.selectByExample(new CarDeviceConfigExample() {{
//            createCriteria().andVinEqualTo(vin).andStatusEqualTo(ConfigConstant.FINISH);
//        }});
//        Set<String> names = configs.stream().map(CarDeviceConfig::getName).collect(Collectors.toSet());
//        log.info("将要查询的车辆,{},配置项：{}",vin, names);
//        CloudCgf.CloudToVehicleQueryConfig.Builder builder = CloudCgf.CloudToVehicleQueryConfig.newBuilder();
//        builder.setSVin(vin);
//
//        for(int i=0; i< configs.size(); i++) {
//            CarDeviceConfig config = configs.get(i);
//            CarConfig carConfig = carConfigMapper.selectByPrimaryKey(config.getConfigId());
//
//            if(carConfig!=null && carConfig.getFileType()!=null){
//                builder.setSFileName( config.getName()+"."+carConfig.getFileType() );
//                builder.setSConfigName( config.getName() );
//            }else{
//                builder.setSFileName( config.getName()+"."+config.getFileType() );
//                builder.setSConfigName( config.getName() );
//            }
//            // 主动下发查询请求，查询哪个车的哪些配置
//            queryFromMqtt(vin, builder.build().toByteArray(), new AukCallback() {
//                @Override
//                public void onSuccess(Object o) {
//                    log.info("SUCCESS:下发回捞请求成功，{},配置项：{}, 配置内容：{}",vin,config.getName(),builder);
//                }
//
//                @Override
//                public void onFailure(Object o, Throwable throwable) {
//                    log.error("ERROR:下发回捞请求失败，{},配置项：{}",vin,config.getName());
//                }
//            });
//        }
//    }

//    @Override
//    public void queryFromMqtt(String vin, byte[] config, AukCallback callback) {
//        DownLinkMsgAsyncRequest request = new DownLinkMsgAsyncRequest();
//        request.setTimeout(AukClientConfig.TIMEOUT);
//        request.setProductKey(AukClientConfig.PRODUCT_KEY);
//        request.setQos(QoSLevel.AT_LEAST_ONCE);
//        request.setMessageId(UUID.randomUUID().toString().replace("-", ""));
//        request.setTopic(AukClientConfig.QUERY_TOPIC);
//        request.setDeviceKey(vin);
//        request.setPayload(config);
//        aukClient.publishMessageAsync(request,callback);
//    }


    @Override
    public DeviceCreateResponse createAukDevice(String DeviceKey) {
        // 前置校验参数
        checkNotNull(DeviceKey, "创建设备vin不能为空");
        DeviceCreateRequest request = new DeviceCreateRequest();
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        request.setDeviceKey(DeviceKey);
        request.setDeviceName(DeviceKey);
//        checkArgument(!isNullOrEmpty(request.getProductKey()), "产品Key必填");
//        checkArgument(!isNullOrEmpty(request.getDeviceKey()), "设备Key必填");
//        checkArgument(!isNullOrEmpty(request.getDeviceName()), "设备Name必填");

        DeviceCreateResponse response = null;
        try {
            response = aukClient.createDevice(request);
            log.info("创建设备的响应信息：【{}】", JSONObject.toJSON(response));
        } catch (Exception exception) {
            log.error("Auk create device Exception:", exception);
        }
        return response;
    }

    @Override
    public DeviceSecretResponse queryDeviceSecret(String vin) {
//        checkNotNull(request, "查询设备密钥请求为空");
//        checkArgument(!isNullOrEmpty(request.getProductKey()), "产品Key必填");
//        checkArgument(!isNullOrEmpty(request.getDeviceKey()), "设备Key必填");
        DeviceSecretRequest request = new DeviceSecretRequest();
        request.setDeviceKey(vin);
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        DeviceSecretResponse response = null;
        try {
            response = aukClient.queryDeviceSecret(request);
            log.info("查询设备密钥的响应信息：【{}】", JSONObject.toJSON(!response.getData().getDeviceKey().isEmpty()));
        } catch (Exception exception) {
            log.error("vin: {},Auk query device Exception:", vin , exception);
        }
        return response;
    }


    @Resource
    AukCertMapper aukCertMapper;
    @Override
    public AukCert queryDeviceCert(String vin) {
        checkArgument(!isNullOrEmpty(vin), "vin必填");
        QueryDeviceCertRequest request = new QueryDeviceCertRequest();
        request.setDeviceKey(vin);
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        IssueDeviceCertResponse response = null;
        try {
            // 1.查询证书、私钥发送到车端
            List<AukCert> certs = aukCertMapper.selectByExampleWithBLOBs(new AukCertExample() {{
                createCriteria().andDeviceKeyEqualTo(vin);
            }});
            // 2.如果查询不到，则创建
            // 3.如果查询到，看是否过期，如果过期，需要重新创建
            AukCert cert = validDate(certs);
            if( certs.isEmpty() | cert==null ){
                QueryRootCertExpireDateResponse expireDateResponse = aukClient.queryRootCertExpireDate(new QueryRootCertExpireDateRequest(){{
                    setProductKey(AukClientConfig.PRODUCT_KEY);
                }});
                int days = daysBetween(new Date(), new Date(expireDateResponse.getData()));
                if (days > AukClientConfig.CertDays){
                    days = AukClientConfig.CertDays;
                }
                IssueDeviceCertRequest req = new IssueDeviceCertRequest() {{
                    setDeviceKey(vin);
                    setProductKey(AukClientConfig.PRODUCT_KEY);
                }};
                req.setValidityPeriod(days);
                response = aukClient.issueDeviceCert(req);
                // 如果创建失败，则获取证书可创建天数，重新创建
                // 创建成功，将结果保存入库
                IssueDeviceCertResponse finalResponse = response;
                // 如果创建失败，则报警
                if(finalResponse.getCode() != AukResultCode.OK.getCode()){
                    MetricHelper.build().name("wallcmdb.auk.cert").tag("status", "error").count(1);
                    log.error("Auk query device Exception:{}",finalResponse);
                    return null;
                }
                MetricHelper.build().name("wallcmdb.auk.cert").tag("status", "success").count(1);
                Date expire = new Date(finalResponse.getData().getExpire());
                cert = new AukCert(){{
                    setAddTime(new Date());
                    setDeviceKey(vin);
                    setCertid(finalResponse.getData().getCertId());
                    setPem(finalResponse.getData().getPem());
                    setPrivatekey(finalResponse.getData().getPrivateKey());
                    setExpireTime(expire);
                    setValidityPeriod(AukClientConfig.CertDays);
                }};
                aukCertMapper.insert(cert);
            }

            log.info("查询设备密钥的响应信息：【{}】", !cert.getDeviceKey().isEmpty());
            return cert;
        } catch (Exception exception) {
            MetricHelper.build().name("wallcmdb.auk.cert").tag("status", "error").count(1);
            log.error("Auk query device Exception:", exception);
            return null;
        }
    }

    private AukCert validDate( List<AukCert> certs){
        AukCert res = null;
        for (AukCert cert : certs){
            // 有效天数大于10
            if(daysBetween(new Date(), cert.getExpireTime())>10){
                res = cert;
            }
        }
        return res;
    }


    @Override
    public DeviceResponse queryDevice(DeviceRequest request) {
        checkNotNull(request, "查询设备请求为空");
        checkArgument(!isNullOrEmpty(request.getProductKey()), "产品Key必填");
        checkArgument(!isNullOrEmpty(request.getDeviceKey()), "设备Key必填");
        DeviceResponse response = null;
        try {
            response = aukClient.queryDevice(request);
            log.info("查询设备的响应信息：【{}】", JSONObject.toJSON(response));
        } catch (Exception exception) {
            log.error("Auk query device Exception:", exception);
        }
        return response;
    }
    @Override
    public DeviceListResponse queryDeviceList(ArrayList<String> vins) {
        DeviceListRequest request = new DeviceListRequest();
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        request.setDeviceKeyList(vins);
        DeviceListResponse response = null;
        try {
            response = aukClient.queryDeviceList(request);
            log.info("查询设备的响应信息：【{}】", JSONObject.toJSON(response));
        } catch (Exception exception) {
            log.error("Auk query device Exception:", exception);
        }
        return response;
    }

    @Override
    public List<DeviceEnrichment> queryDeviceListWithSlice(List<String> vins, int slice) {
        int batch = vins.size() / slice;
        List<DeviceEnrichment> result = new ArrayList<>();
        for (int i = 0; i < batch; i++) {
            DeviceListResponse deviceListResponse = queryList(new ArrayList<>( vins.subList(i * slice, (i + 1) * slice)));
            result.addAll(deviceListResponse.getData());
        }
        if(!(vins.size() % slice == 0)) {
            //查询集合的设备不能为空
            result.addAll(queryList(new ArrayList<>( vins.subList(batch * slice, vins.size() ))).getData());
        }

        return result;
    }

    private DeviceListResponse queryList(List<String> vins) {
        DeviceListRequest request = new DeviceListRequest();
        request.setProductKey(AukClientConfig.PRODUCT_KEY);
        request.setDeviceKeyList(vins);
        DeviceListResponse response = null;
        try {
            response = aukClient.queryDeviceList(request);
            log.info("查询设备的响应信息：【{}】", JSONObject.toJSON(response));
        } catch (Exception exception) {
            log.error("Auk query device Exception:", exception);
        }
        return response;
    }
}
