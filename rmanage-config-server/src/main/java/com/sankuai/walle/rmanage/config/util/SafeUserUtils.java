package com.sankuai.walle.rmanage.config.util;

import com.sankuai.meituan.auth.util.UserUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户工具类
 * 安全地获取用户信息，处理null情况
 */
@Slf4j
public class SafeUserUtils {
    
    /**
     * 安全获取用户登录名
     * 如果获取失败，返回默认值
     */
    public static String getSafeUserLogin() {
        try {
            if (UserUtils.getUser() != null) {
                return UserUtils.getUser().getLogin();
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败", e);
        }
        return "";
    }
    
    /**
     * 安全获取用户登录名，支持自定义默认值
     */
    public static String getSafeUserLogin(String defaultValue) {
        try {
            if (UserUtils.getUser() != null) {
                return UserUtils.getUser().getLogin();
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败", e);
        }
        return defaultValue != null ? defaultValue : "";
    }
} 