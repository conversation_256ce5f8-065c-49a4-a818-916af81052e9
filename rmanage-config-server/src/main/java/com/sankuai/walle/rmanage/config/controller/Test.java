package com.sankuai.walle.rmanage.config.controller;


import com.meituan.finerp.eam.dto.OpenAssetDTO;
import com.sankuai.walle.rmanage.config.service.AssetService;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.infrastructureService.LexicalAnalyzerService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(path = {"/eve/cmdb/rest/test"})
@Log4j
public class Test {

    @Resource
    AssetService service;
    @Autowired
    LexicalAnalyzerService lexicalAnalyzerService;
    @Resource
    ConfigService configService;
    @Resource
    AssetService assetService;

    // hack资产同步的数据，用于验证
    @RequestMapping(path = {"/sync/carobject"}, method = RequestMethod.POST)
    public String asyncAssetData(@RequestBody List<OpenAssetDTO> body) throws Exception {
        assetService.syncObject(body);
        return "请查看数据";
    }

    @RequestMapping(path = {"/sync/car/object"}, method = RequestMethod.GET)
    public String sendAukMessageTest() throws Exception {
        assetService.asyncUpdateOperationData();

        String str = "{\n" +
                "\t\"vinfo\": {\n" +
                "\t  \"model\": \"`${#type}`\",\n" +
                "    \"phase\": \"`${#phase}`\",\n" +
                "    \"compute_name\": \"`${#compute_name}`\",\n" +
                "\t\t\"switch_name\": \"`${#switch_name}`\",\n" +
                "    \"car_name\": \"`${#name}`\",\n" +
                "    \"vin\":\"`${#vin}`\",\n" +
                "    \"type\":\"`${#size}`\",\n" +
                "    \"type-model\": \"`${#vehicleType}`\", // 父子车型组成的列表[D01,D23,EP1]\n" +
                "\t\t\"equipments\": [\n" +
                "    \t{\n" +
                "      \t\"compute_name\": \"`${#compute_name}`\",\n" +
                "\t\t\t\t\"switch_name\": \"`${#switch_name}`\",\n" +
                "      }\n" +
                "    ],\n" +
                "\t}\n" +
                "}";
        return "hello world";
//        String data = lexicalAnalyzerService.getConfigContent(str, "LMTZSV016MC000107");
//        System.out.println(data);
//        str = "vin: \"`${#vin}`\" \n" +
//                "\n" +
//                "name: \"`${#name}`\" \n" +
//                "\n" +
//                "type: \"`${#size}`\" \n" +
//                "\n" +
//                "vendor: \"`${#vendor}`\" \n" +
//                "\n" +
//                "batch: \"`${#phase}`\"";
//        data = lexicalAnalyzerService.getConfigContent(str, "LMTZSV016MC000107");
//        System.out.println(data);
//        String vin = "LMTZSV016MC000107";
//        String carName = "S20-107";
//        configService.sendCarInfoConfigToS3(vin, carName);
    }
}
