package com.sankuai.walle.rmanage.config.controller;


import com.sankuai.walle.carManage.mapper.CarObjectsMapper;
import com.sankuai.walle.dal.classify.mapper.*;
import com.sankuai.walle.dal.eve.entity.ConfigPermission;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteCarTypeMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.RemoteObjectTagsMapper;
import com.sankuai.walle.dal.mrm_manage.mapper.TagsMapper;
import com.sankuai.walle.dal.walle_data_center.mapper.BizPlaceInfoMapper;
import com.sankuai.walle.dal.walle_data_center.mapper.VehicleInfoMapper;
import com.sankuai.walle.dal.wallevresv.mapper.VresvCityParkMapper;
import com.sankuai.walle.objects.constants.CommonConstants;
import com.sankuai.walle.objects.vo.res.ResData;
import com.sankuai.walle.rmanage.config.dto.config.ConfigPermissionDTO;
import com.sankuai.walle.rmanage.config.service.ConfigService;
import com.sankuai.walle.rmanage.config.service.impl.CarTypeService;
import com.sankuai.walle.rmanage.config.service.impl.VehicleDeviceServiceImpl;
import com.sankuai.walle.rmanage.config.service.infrastructureService.LexicalAnalyzerService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Created on 2022/09/14
 */
@RestController
@RequestMapping(path = {"/eve/cmdb/rest/api/cmdb/device/car/config"})
@Log4j
public class ConfigS3Controller {

    @Resource
    RemoteCarTypeMapper remoteCarTypeMapper;

    @Resource
    RemoteObjectTagsMapper remoteObjectTagsMapper;
    @Resource
    CarObjectsMapper carObjectsMapper;
    @Resource
    TagsMapper tagsMapper;
    @Resource
    VehicleDeviceServiceImpl carTagDeviceService;
    @Resource
    BizPlaceInfoMapper bizPlaceInfoMapper;
    // 三级分类：1 城市 2 运营区域 3 运营组别 4 车辆编号
    @Resource
    VresvCityParkMapper vresvCityParkMapper;
    @Resource
    VehicleInfoMapper vehicleInfoMapper;
//    @Resource
//    RemoteDeviceTypeMapper remoteDeviceTypeMapper;
    @Resource
    CarClassifyMapper carClassifyMapper;
    @Resource
    CarClassifyThirdModelMapper carClassifyThirdModelMapper;
    @Resource
    CarConfigMapper carConfigMapper;
    @Resource
    CarClassifyDeviceConfigViewMapper carClassifyDeviceConfigViewMapper;
    @Resource
    CarConfigMyMapper configMyMapper;
    @Resource
    CarTypeService carTypeService;
    @Resource
    CarDeviceConfigMapper carDeviceConfigMapper;
    @Resource
    CarConfigTaskMapper carConfigTaskMapper;
    @Resource
    ConfigService configService;
    @Resource
    LexicalAnalyzerService lexicalAnalyzerService;

    @Autowired
    Environment env;


    // todo:配置到期时间

    // 读取autocar S3的配置
    @RequestMapping(path = {"/autocar/get"},method = RequestMethod.GET)
    public ResData getAutoCarConfig(@RequestParam String s3Key) throws IOException {
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        String data = configService.getAutocarConfigFromS3(s3Key);
        res.data = data;
        return res;
    }

    // 读取某个carName的S3配置列表
    @RequestMapping(path = {"/autocar/list"},method = RequestMethod.GET)
    public ResData getAutoCarConfigList(@RequestParam String carName){
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        Object data = configService.getAutocarConfigListFromS3(carName);
        res.data = data;
        return res;
    }

    // 发送配置到S3
    @RequestMapping(path = {"/autocar/post"},method = RequestMethod.POST)
    public ResData sendAutoCarConfigToS3(@RequestBody Map<String,String> body, HttpServletRequest request ) throws IOException {
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        String objName = body.get("objName");
        String data = body.get("data");
        configService.sendConfigToS3(objName, data);
        return res;
    }

    // 删除s3的配置
    @RequestMapping(path = {"/autocar/delete"},method = RequestMethod.GET)
    public ResData deleteAutoCarConfig(@RequestParam String objName) throws IOException {
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        configService.deleteConfigFromS3(objName);
        return res;
    }

    // 获取配置权限列表
    @RequestMapping(path = {"/permission/list"},method = RequestMethod.GET)
    public ResData queryConfigAuthList(){
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        Map<String, List<String>> map = configService.getConfigAuthList();
        List<ConfigPermissionDTO> data = new ArrayList<>();
        for (Map.Entry<String,List<String>> entry:map.entrySet()){
            ConfigPermissionDTO dto = new ConfigPermissionDTO();
            dto.setFile(entry.getKey());
            dto.setMis(entry.getValue());
            data.add(dto);
        }
        res.data = data;
        return res;
    }

    // 新增配置权限
    @RequestMapping(path = {"/permission/add"},method = RequestMethod.POST)
    public ResData addConfigAuth(@RequestBody ConfigPermission configPermission){
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        configService.addConfigAuth(configPermission);
        return res;
    }
    // 更新配置权限
    @RequestMapping(path = {"/permission/update"},method = RequestMethod.POST)
    public ResData updateConfigAuth(@RequestBody ConfigPermissionDTO body) {
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        configService.updateConfigAuth(body);
        return res;
    }

    // 获取当前用户有权限的配置
    @RequestMapping(path = {"/permission/user"},method = RequestMethod.GET)
    public ResData getUserAuthorizedConfig() {
        ResData res = new ResData();
        res.code = CommonConstants.SUCCEED_CODE;
        res.msg = CommonConstants.succeedMsg;
        List<String> authList = configService.getConfigAuth();
        res.data = authList;
        return res;
    }
}
