<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="goods" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 数据库关键字冲突,自动处理 用反引号`` -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 生成的entity实体引入lombok注解 Getter,Setter,Builder -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!--生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin
        3个插件，根据诉求决定使用哪个插件，具体区别见 https://docs.sankuai.com/dp/hbar/mdp-docs/master/mbg/ -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>-->
        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：
        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>
        <!-- 需要生成使用 RowBounds 的分页查询语句-->
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>
        <!--分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator
        type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>
        <!-- targetRuntime="Mybatis3"时需要，Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.walle.rmanage.config.dal.classify.example"/>
        </plugin>
        <!-- 从数据库中的字段的comment做为生成entity的属性注释 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--使用前替换数据库名,账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*********************************************************************************************************************"
                        userId="mb_generator"
                        password="m2tWrNusl6cnul">
        </jdbcConnection>
        <!--        connectionURL="*******************************************************************************************************************"-->
        <!--        userId="mb_generator"-->
        <!--        password="m2tWrNusl6cnul">-->


        <!--替换成自己数据库的jdbcRef -->
        <!--        <zebra jdbcRef="walle_mrm_manage_test"/>-->

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sankuai.walle.rmanage.config.dal.classify.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mappers" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.walle.rmanage.config.dal.classify.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--配置需要生成的表-->
        <!--        车辆管理的表  -->
                <table tableName="auto_config_vehicle_task_status" />
        <!--        <table tableName="car_assets" />-->
        <!--        <table tableName="car_selects"/>-->
        <!--        <table tableName="car_operation" />-->
        <!--        <table tableName="car_exec_word" />-->
        <!--        配置管理的表 -->
        <!--        <table tableName="car_device_config_extend" />-->

        <!--<table tableName="remote_custom_config" domainObjectName="RemoteCustomConfigDO">
            <columnOverride column="config_value" jdbcType="VARCHAR"/>
        </table>-->
        <!--<table tableName="remote_custom_config_subject" domainObjectName="RemoteCustomConfigSubjectDO"/>-->
        <!--<table tableName="operation_log" domainObjectName="OperationLogDO"/>-->
        <!--<table tableName="remote_objects" domainObjectName="RemoteObjectsDO"/>-->
        <!--<table tableName="remote_car_type" domainObjectName="RemoteCarTypeDO"/>-->
        <!--<table tableName="tags" domainObjectName="TagsDO"/>-->
        <!--<table tableName="tag_type" domainObjectName="TagTypeDO"/>-->
        <!--<table tableName="remote_object_tags" domainObjectName="RemoteObjectTagsDO"/>-->
        <!--<table tableName="remote_object_type" domainObjectName="RemoteObjectTypeDO"/>-->
        <!--<table tableName="remote_device_type" domainObjectName="RemoteDeviceTypeDO"/>-->

    </context>

</generatorConfiguration>