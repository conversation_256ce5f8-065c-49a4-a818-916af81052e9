<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="clientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.walle.cmdb.thrift.service.CarThriftService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.caros.wallecmdb"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="clusterManager" value="OCTO"/>
        <property name="serverIpPorts" value="***********:9001" />
    </bean>

<!--    <bean id="clientProxy2" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
<!--        <property name="serviceInterface" value="com.sankuai.walle.SendMessageService"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
<!--        <property name="remoteAppkey" value="com.sankuai.carosscan.realtimeinfo"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
<!--        <property name="nettyIO" value="true"/>-->
<!--        <property name="filterByServiceName" value="true"/>  &lt;!&ndash; 通过服务名称进行服务发现 &ndash;&gt;-->
<!--        <property name="clusterManager" value="OCTO"/>-->
<!--        <property name="serverIpPorts" value="127.0.0.1:9001" />-->
<!--    </bean>-->

</beans>