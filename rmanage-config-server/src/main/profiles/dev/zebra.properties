#以下配置由mdp生成，可以自由更改
mdp.zebra[0].basePackage=com.sankuai.walle.rmanage.config.dal.mrm_manage
mdp.zebra[0].jdbcRef=walle_mrm_manage_test
mdp.zebra[0].mapperLocations=classpath:mappers/*.xml

#以下配置由mdp生成，可以自由更改
mdp.zebra[1].basePackage=com.sankuai.walle.rmanage.config.dal.walle_data_center
mdp.zebra[1].jdbcRef=walleops_walle_data_center_dev
mdp.zebra[1].mapperLocations=classpath:mappers/*.xml

#以下配置由mdp生成，可以自由更改
mdp.zebra[2].basePackage=com.sankuai.walle.rmanage.config.dal.wallevresv
mdp.zebra[2].jdbcRef=walleops_wallevresv_dev
mdp.zebra[2].mapperLocations=classpath:mappers/*.xml

#以下配置由mdp生成，可以自由更改
mdp.zebra[3].basePackage=com.sankuai.walle.rmanage.config.dal.classify
mdp.zebra[3].jdbcRef=walle_mrm_manage_test
mdp.zebra[3].mapperLocations=classpath:mappers/*.xml
#mdp.zebra[3].datasource.url=*********************************
#mdp.zebra[3].datasource.username=walle
#mdp.zebra[3].datasource.password=walle123
#mdp.zebra[3].mapperLocations=classpath:mappers/*.xml

mdp.zebra[4].basePackage=com.sankuai.walle.rmanage.config.dal.old
mdp.zebra[4].jdbcRef=walle_mrm_manage_test
mdp.zebra[4].mapperLocations=classpath:mappers/*.xml