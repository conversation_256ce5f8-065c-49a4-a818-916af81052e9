#健康检测相关配置,如需改动请同步修改check.sh文件TEST_URL
management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive
#server相关
server.port=8080


create.vehicle.visit.strategy.url=https://aim.adp.test.sankuai.com/v1/sys/policies/acl/
create.vehicle.sso.url=https://aim.adp.test.sankuai.com/v1/auth/approle/role/
import.secret.key.url=https://aim.adp.test.sankuai.com/v1/keys-kv/data/
query.generate.key.result.url=https://aim.adp.test.sankuai.com/v1/keys-kv/data/

x.vault.token=$KMS{x.vault.token}

# config.properties
can.auth.key=xxxxxxxxxxxxxxx
SRV_Clp43oAL6QEjgnb8hEsFOm5CPJL4cxrh=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
HDMapCollectionTrajectoryUploadKey=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
HDMapCollectionTrajectoryUploadKey_Test=xxxxxxxxxxxxxxxxxxxxxxxxxxxx
ADMapCollectionTrajectoryUploadKey=xxxxxxxxxxxxxxxxxxxxxxxxxx
PostProcessQXUploadSecret=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
PostProcessQXUploadKey=xxxxxxxxxxxxxxxxxxxxxxxxxxx
J3C_maebctl_client_secret=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
J3_mediastream_client_secret=xxxxxxxxxxxxxxxxxxxxxxxxxx
J3_teleop_client_secret=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
slab_broker_access_secret=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx