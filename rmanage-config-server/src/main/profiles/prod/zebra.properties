##以下配置由mdp生成，可以自由更改
#mdp.zebra[0].basePackage=com.sankuai.walle.rmanage.config.dal.mrm_manage
#mdp.zebra[0].jdbcRef=walle_mrm_manage_product
#mdp.zebra[0].mapperLocations=classpath:mappers/*.xml
#
##以下配置由mdp生成，可以自由更改
#mdp.zebra[1].basePackage=com.sankuai.walle.rmanage.config.dal.walle_data_center
#mdp.zebra[1].jdbcRef=walle_data_center_product.sdw
#mdp.zebra[1].mapperLocations=classpath:mappers/*.xml
#
##以下配置由mdp生成，可以自由更改
#mdp.zebra[2].basePackage=com.sankuai.walle.rmanage.config.dal.wallevresv
#mdp.zebra[2].jdbcRef=wallevresv_product.sdw
#mdp.zebra[2].mapperLocations=classpath:mappers/*.xml
#
##以下配置由mdp生成，可以自由更改
#mdp.zebra[3].basePackage=com.sankuai.walle.rmanage.config.dal.classify
#mdp.zebra[3].jdbcRef=walle_mrm_manage_product
#mdp.zebra[3].mapperLocations=classpath:mappers/*.xml
#
#mdp.zebra[4].basePackage=com.sankuai.walle.rmanage.config.dal.old
#mdp.zebra[4].jdbcRef=walle_mrm_manage_product
#mdp.zebra[4].mapperLocations=classpath:mappers/*.xml

#以下配置由mdp生成，可以自由更改
mdp.zebra[0].basePackage=com.sankuai.walle.dal.mrm_manage
mdp.zebra[0].jdbcRef=walle_mrm_manage_product
mdp.zebra[0].mapperLocations=classpath*:mappers/dal/*.xml
mdp.zebra[0].useTransactionAsDefault=true

#以下配置由mdp生成，可以自由更改
mdp.zebra[1].basePackage=com.sankuai.walle.dal.walle_data_center
mdp.zebra[1].jdbcRef=walle_data_center_product
mdp.zebra[1].mapperLocations=classpath*:mappers/dal/*.xml

#以下配置由mdp生成，可以自由更改
mdp.zebra[2].basePackage=com.sankuai.walle.dal.wallevresv
mdp.zebra[2].jdbcRef=wallevresv_product
mdp.zebra[2].mapperLocations=classpath*:mappers/dal/*.xml

#以下配置由mdp生成，可以自由更改
mdp.zebra[3].basePackage=com.sankuai.walle.dal.classify
mdp.zebra[3].jdbcRef=walle_mrm_manage_product
mdp.zebra[3].mapperLocations=classpath*:mappers/dal/*.xml

mdp.zebra[4].basePackage=com.sankuai.walle.dal.old
mdp.zebra[4].jdbcRef=walle_mrm_manage_product
mdp.zebra[4].mapperLocations=classpath*:mappers/dal/*.xml

mdp.zebra[5].basePackage=com.sankuai.walle.carManage
mdp.zebra[5].jdbcRef=walle_mrm_manage_product
mdp.zebra[5].mapperLocations=classpath*:mappers/carManage/*.xml
mdp.zebra[5].transactionName=mdpTM1

mdp.zebra[6].basePackage=com.sankuai.walle.dal.eve
mdp.zebra[6].jdbcRef=eve_eve_product
mdp.zebra[6].mapperLocations=classpath*:mappers/dal/*.xml

mdp.zebra[7].basePackage=com.sankuai.walle.dal.battery
mdp.zebra[7].jdbcRef=eve_battery_swap_cabinet_product
mdp.zebra[7].mapperLocations=classpath*:mappers/dal/*.xml