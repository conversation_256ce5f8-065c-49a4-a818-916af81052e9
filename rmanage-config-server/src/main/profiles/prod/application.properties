#健康检测相关配置,如需改动请同步修改check.sh文件TEST_URL
spring.config.location=classpath:/META-INF/

management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive
#server相关
server.port=8080
mybatis.configuration.log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


#uac.appKey=48d18087f0
#uac.secret=c73cf9cfa4f544268568495b1f3af6da
#uac.host=http://uac.it.beta.sankuai.com

auk.aukClientAK=1620709674808688680
auk.aukClientSK=7XL675WU8vSGwXkviGR4i

mafka.namespace=waimai
mafka.mafkaAK=com.sankuai.caros.wallecmdb
mafka.topic=com.sankuai.caros.wallecmdb
mafka.SubscribeGroup=cmdb

mafka2.namespace=waimai
mafka2.mafkaAK=com.sankuai.carosscan.realtimeinfo
mafka2.topic=mad-vehicle.real.status.use
tt.oncallUrlPrefix=https://cti.vip.sankuai.com/
tt.createUrl=https://ticket.vip.sankuai.com/api/1.0/ticket/fast/create
tt.itemId=32043
tt.manageMis=zhaojianfeng05
tt.Authorization=Basic ZdeEt/BJydhl8OIvX+oOGWngo8wRT5lBvlUQlpWgUcU=

es.Index=battery

s3.bucket.name=battery-management
s3-bucketName=hardware
# 给车机的S3
# 给配置管理的S3
config-bucketName=vehicle-config-files

redis-safe.cluster-name=redis-eve-system_product
redis-safe.read-timeout=400
redis-safe.router-type=slave-only
redis-safe.pool-max-idle=16
redis-safe.pool-max-total=32
redis-safe.pool-wait-millis=500
redis-safe.pool-min-idle=3
redis-safe.serialize-type=hessian

evehost=https://eve.sankuai.com/

autocar.s3.bucketName=vehicle-config-files

create.vehicle.visit.strategy.url=https://aim.sankuai.com/v1/sys/policy/
query.vehicle.visit.strategy.url=https://aim.sankuai.com/v1/sys/policy/
create.vehicle.sso.url=https://aim.sankuai.com/v1/auth/approle/role/
import.secret.key.url=https://aim.sankuai.com/v1/keys-kv/data/
query.generate.key.result.url=https://aim.sankuai.com/v1/keys-kv/data/

x.vault.token=$KMS{x.vault.token}

# config.properties
can_auth_key=$KMS{can_auth_key}
SRV_Clp43oAL6QEjgnb8hEsFOm5CPJL4cxrh=$KMS{SRV_Clp43oAL6QEjgnb8hEsFOm5CPJL4cxrh}
HDMapCollectionTrajectoryUploadKey=$KMS{HDMapCollectionTrajectoryUploadKey}
HDMapCollectionTrajectoryUploadKey_Test=$KMS{HDMapCollectionTrajectoryUploadKey_Test}
ADMapCollectionTrajectoryUploadKey=$KMS{ADMapCollectionTrajectoryUploadKey}
PostProcessQXUploadSecret=$KMS{PostProcessQXUploadSecret}
PostProcessQXUploadKey=$KMS{PostProcessQXUploadKey}
J3C_maebctl_client_secret=$KMS{J3C_maebctl_client_secret}
J3_mediastream_client_secret=$KMS{J3_mediastream_client_secret}
J3_teleop_client_secret=$KMS{J3_teleop_client_secret}
slab_broker_access_secret=$KMS{slab_broker_access_secret}
J3C_maeb_ssh_passwd=$KMS{J3C_maeb_ssh_passwd}

re.host=https://eve.sankuai.com