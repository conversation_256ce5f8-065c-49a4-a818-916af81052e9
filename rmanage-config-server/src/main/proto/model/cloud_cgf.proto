syntax = "proto3";

package walle.vehicle.slab;

option java_package = "com.sankuai.walle.rmanage.proto.target";

// --云端配置下发协议(车端向云端请求协议)

// --下行协议 云端向车端
message CloudToVehicleReleaseConfig{
  string sUuid = 1;           // 全局唯一id
  string sVin = 2;            // 车架号
  string sFileName = 3;       // 文件名称
  string sConfigName = 4;     // 配置项目名称
  string sVersion = 5;        // 版本号
  bytes bConf = 6;            // 文件内容
};

message VehicleToCloudReleaseConfigRsp{
  string sUuid = 1;           // 全局唯一id
  string sVin = 2;            // 车架号
  string sFileName = 3;       // 文件名称
  string sConfigName = 4;     // 配置项目名称
  string sVersion = 5;        // 版本号
  Status status = 6;
};
// 云端请求车端，获取车端配置
message CloudToVehicleQueryConfig{
  string sUuid = 1;           // 全局唯一id
  string sVin = 2;            // 车架号
  string sFileName = 3;       // 文件名称
  string sConfigName = 4;     // 配置项目名称
  string sVersion = 5;        // 版本号
};
// 对应的返回消息
message VehicleToCloudQueryConfigRsp{
  string sUuid = 1;           // 全局唯一id
  string sVin = 2;            // 车架号
  string sFileName = 3;       // 文件名称
  string sConfigName = 4;     // 配置项目名称
  string sVersion = 5;        // 版本号
  bytes bConf = 6;            // 文件内容
  Status status = 7;
};

message Status{
  int64   nRet = 1;           // 错误码 0 表示成功 < 0表示失败
  string  sMsg = 2;            // 错误信息
}