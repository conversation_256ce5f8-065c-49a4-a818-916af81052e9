<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rmanage-cmdb</artifactId>
        <groupId>com.sankuai.walle</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>schedule</artifactId>
    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.sankuai.dolphin</groupId>
                <artifactId>thrift-maven-plugin</artifactId>
                <version>1.2.4</version>
                <executions>
                    <execution>
                        <id>generate</id>
                        <phase>generate-sources</phase>  <!-- 根据IDL文件生成Java代码 -->
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>archive</id>   <!-- 生成服务文档 -->
                        <phase>deploy</phase>
                        <goals>
                            <goal>archive</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <appkey>com.sankuai.caros.wallecmdb</appkey>     <!-- 这里替换成项目的appkey -->
                    <codeDir>${project.basedir}/target/generated-sources/java</codeDir>   <!-- 生成代码目标路径。多模块项目请务必保留 ${project.basedir}，否则可能导致编译失败 -->
                    <thriftVersion>0.9.3</thriftVersion>			<!--Thrift编译器版本，需要与项目依赖的libthrift版本一致，否则可能不兼容，也可以依赖美团优化版libthrift，解决兼容问题。可以不填写，默认0.8.0 -->
                    <language>java</language>			<!--目标语言。可以不填写。默认java-->
                    <idlDir>${project.basedir}/src/main/thrift</idlDir>		<!-- idl文件所在路径。可以不填写。默认src/main/resources/thrift。 多模块项目请务必保留 ${project.basedir}，否则可能导致编译失败 -->
                    <compatible>false</compatible> 		 <!-- 参考 3.1. 兼容模式与非兼容模式 ，除非之前使用了genthrift插件的项目，否则推荐设置为false -->
                    <supportClasspath>false</supportClasspath>  <!-- 扫描类路径中的Thrift文件，默认关闭，建议需要时再开启 -->
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>