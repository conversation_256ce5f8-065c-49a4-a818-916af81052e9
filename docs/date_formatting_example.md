# 日期格式化配置说明

## 概述

为了确保前端能够正确显示日期信息，我们对项目中的日期字段进行了格式化配置。

## 配置方式

### 1. 字段级注解配置

在实体类的日期字段上添加 `@JsonFormat` 注解：

```java
// 保险相关日期字段 - 只显示日期
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
Date heavyTrafficInsurance;

@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
Date businessInsuranceDate;

// 时间戳字段 - 显示日期和时间
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
Date addTime;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
Date updateTime;
```

### 2. 全局配置

## 已配置的实体类

### AssetExecWord 类
- `equipmentInsuranceDate`: 设备险到期日期 → `yyyy-MM-dd`
- `businessInsuranceDate`: 商业险到期日期 → `yyyy-MM-dd`
- `heavyTrafficInsurance`: 交强险到期日期 → `yyyy-MM-dd`
- `annualInspection`: 年检到期日期 → `yyyy-MM-dd`
- `registrationDate`: 注册时间 → `yyyy-MM-dd`
- `thirdLiabilityInsurance`: 三方责任险到期日期 → `yyyy-MM-dd`
- `priorityInsurance`: 财产险到期日期 → `yyyy-MM-dd`
- `indecatorStart/End`: 指标租用时长 → `yyyy-MM-dd`
- `leasingStart/End`: 融租时长 → `yyyy-MM-dd`
- `comeSchoolDate`: 到校日期 → `yyyy-MM-dd`

### CarObjects 类
- `addTime`: 添加时间 → `yyyy-MM-dd HH:mm:ss`
- `updateTime`: 更新时间 → `yyyy-MM-dd HH:mm:ss`

### CarExecWord 类
- `addTime`: 添加时间 → `yyyy-MM-dd HH:mm:ss`
- `updateTime`: 更新时间 → `yyyy-MM-dd HH:mm:ss`

### CarAssets 类
- `addTime`: 添加时间 → `yyyy-MM-dd HH:mm:ss`
- `updateTime`: 更新时间 → `yyyy-MM-dd HH:mm:ss`

### CarOperation 类
- `addTime`: 添加时间 → `yyyy-MM-dd HH:mm:ss`
- `updateTime`: 更新时间 → `yyyy-MM-dd HH:mm:ss`

## 前端显示效果

### 保险信息导入接口返回示例

```json
{
  "code": 200,
  "msg": "导入完成: 成功1条, 未找到0条, 错误0条",
  "data": {
    "successCount": 1,
    "notFoundCount": 0,
    "errorCount": 0,
    "successVins": ["TEST123456789"],
    "notFoundVins": [],
    "errorVins": []
  }
}
```

### 车辆详情查询返回示例

```json
{
  "carObjects": {
    "vin": "TEST123456789",
    "name": "测试车辆",
    "licenseno": "京A12345",
    "addTime": "2024-01-15 10:30:00",
    "updateTime": "2024-01-15 14:20:00"
  },
  "carExecWords": [
    {
      "vin": "TEST123456789",
      "carAssetsExecWords": {
        "vin": "TEST123456789",
        "heavyTrafficInsurance": "2024-12-31",
        "businessInsuranceDate": "2024-12-31",
        "equipmentInsuranceDate": "2024-12-31",
        "annualInspection": "2024-06-30"
      },
      "addTime": "2024-01-15 10:30:00",
      "updateTime": "2024-01-15 14:20:00"
    }
  ]
}
```

## 使用说明

1. **保险日期字段**: 使用 `yyyy-MM-dd` 格式，只显示日期，适合保险到期日等场景
2. **时间戳字段**: 使用 `yyyy-MM-dd HH:mm:ss` 格式，显示完整的日期和时间
3. **时区设置**: 统一使用 `GMT+8` 时区，确保中国时区的正确显示

## 注意事项

1. 前端接收到的日期已经是格式化后的字符串，无需额外处理
2. 前端发送日期时，可以使用 `yyyy-MM-dd` 或 `yyyy-MM-dd HH:mm:ss` 格式
3. 时区统一为 `GMT+8`，确保数据一致性 