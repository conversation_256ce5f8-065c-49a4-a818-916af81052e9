# 保险日期导入接口文档

## 接口信息

- **接口路径**: `/api/cmdb/vehicle/1.3/car/import/insurance`
- **请求方法**: `POST`
- **功能描述**: 导入车辆保险日期信息

## 请求参数

### 请求体结构
```json
[
  {
    "vin": "车辆VIN号",
    "assetExecWord": {
      "vin": "车辆VIN号",
      "heavyTrafficInsurance": "交强险到期日期",
      "businessInsuranceDate": "商业险到期日期", 
      "equipmentInsuranceDate": "设备险到期日期",
      "priorityInsurance": "财产险到期日期",
      "thirdLiabilityInsurance": "三方责任险到期日期"
    }
  }
]
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| vin | String | 是 | 车辆VIN号 |
| assetExecWord | Object | 是 | 保险信息对象 |
| assetExecWord.heavyTrafficInsurance | Date | 否 | 交强险到期日期 |
| assetExecWord.businessInsuranceDate | Date | 否 | 商业险到期日期 |
| assetExecWord.equipmentInsuranceDate | Date | 否 | 设备险到期日期 |
| assetExecWord.priorityInsurance | Date | 否 | 财产险到期日期 |
| assetExecWord.thirdLiabilityInsurance | Date | 否 | 三方责任险到期日期 |

## 响应参数

### 成功响应
```json
{
  "code": 0,
  "msg": "导入完成: 成功2条, 未找到1条, 错误0条",
  "data": {
    "successCount": 2,
    "notFoundCount": 1,
    "errorCount": 0,
    "successVins": ["VIN001", "VIN002"],
    "notFoundVins": ["VIN003"],
    "errorVins": []
  }
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应码，0表示成功 |
| msg | String | 响应消息 |
| data | Object | 响应数据 |
| data.successCount | Integer | 成功导入的车辆数量 |
| data.notFoundCount | Integer | 未找到的车辆数量 |
| data.errorCount | Integer | 导入失败的车辆数量 |
| data.successVins | Array | 成功导入的VIN列表 |
| data.notFoundVins | Array | 未找到的VIN列表 |
| data.errorVins | Array | 导入失败的VIN列表 |

## 业务逻辑

1. **车辆存在性检查**: 根据VIN查找车辆，如果不存在则记录到notFoundVins
2. **资产记录查找**: 查找对应的CarAssets记录，优先选择未报废的车辆
3. **保险信息更新**: 
   - 如果CarExecWord记录不存在，创建新的记录
   - 如果存在，更新现有的保险信息
4. **数据存储**: 使用carExecWordMapper.insertSelective()方法插入数据

## 使用示例

### 请求示例
```bash
curl -X POST "http://localhost:8080/api/cmdb/vehicle/1.3/car/import/insurance" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "vin": "LSVAM4187C2184841",
      "assetExecWord": {
        "vin": "LSVAM4187C2184841",
        "heavyTrafficInsurance": "2024-12-31T00:00:00.000Z",
        "businessInsuranceDate": "2024-12-31T00:00:00.000Z",
        "equipmentInsuranceDate": "2024-12-31T00:00:00.000Z"
      }
    }
  ]'
```

### 响应示例
```json
{
  "code": 0,
  "msg": "导入完成: 成功1条, 未找到0条, 错误0条",
  "data": {
    "successCount": 1,
    "notFoundCount": 0,
    "errorCount": 0,
    "successVins": ["LSVAM4187C2184841"],
    "notFoundVins": [],
    "errorVins": []
  }
}
```

## 注意事项

1. VIN号必须存在于系统中，否则无法导入保险信息
2. 如果同一VIN有多条资产记录，优先更新未报废的车辆
3. 保险日期字段为可选，只更新提供的字段
4. 接口支持批量导入，建议单次请求不超过100条记录
5. 所有操作都会记录操作日志 